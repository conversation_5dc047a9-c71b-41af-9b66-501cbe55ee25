import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddQASubmissionTrackingFields1748318319482 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add only the essential revision tracking columns
        await queryRunner.query(`
            ALTER TABLE "qa_submission"
            ADD COLUMN IF NOT EXISTS "current_revision" integer DEFAULT 1,
            ADD COLUMN IF NOT EXISTS "total_revisions" integer DEFAULT 1;
        `);

        // Update existing records to have consistent values
        await queryRunner.query(`
            UPDATE "qa_submission"
            SET total_revisions = current_revision
            WHERE total_revisions IS NULL OR total_revisions != current_revision;
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "qa_submission"
            DROP COLUMN IF EXISTS "current_revision",
            DROP COLUMN IF EXISTS "total_revisions";
        `);
    }
}
