import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { DiaryEntry } from './diary-entry.entity';

@Entity()
export class DiaryShare extends AuditableBaseEntity {
  @Column({ name: 'diary_entry_id' })
  diaryEntryId: string;

  @ManyToOne(() => DiaryEntry, (entry) => entry.shares)
  @JoinColumn({ name: 'diary_entry_id' })
  diaryEntry: DiaryEntry;

  @Column({ name: 'share_token' })
  shareToken: string;

  @Column({ name: 'share_date', type: 'timestamp' })
  shareDate: Date;

  @Column({ name: 'expiry_date', type: 'timestamp', nullable: true })
  expiryDate: Date;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;
}
