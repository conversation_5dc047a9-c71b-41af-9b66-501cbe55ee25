import { <PERSON><PERSON><PERSON>, Column, <PERSON>To<PERSON>ne, Join<PERSON><PERSON>umn } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { WaterfallSet } from './waterfall-set.entity';

@Entity()
export class WaterfallQuestion extends AuditableBaseEntity {
  @Column({ name: 'question_text', type: 'text' })
  questionText: string;

  @Column({ name: 'question_text_plain', type: 'text' })
  questionTextPlain: string;

  @Column({ name: 'correct_answers', type: 'text', array: true })
  correctAnswers: string[];

  @Column({ name: 'options', type: 'text', array: true })
  options: string[];

  @Column({ name: 'set_id' })
  setId: string;

  @Column({ name: 'time_limit_in_seconds', nullable: true })
  timeLimitInSeconds: number;

  @Column({ name: 'level', nullable: true })
  level: number;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  // Relationship removed to allow both admin and tutor set IDs
  // @ManyToOne(() => WaterfallSet, (set) => set.questions, { onDelete: 'CASCADE' })
  // @JoinColumn({ name: 'set_id' })
  // set: WaterfallSet;
}
