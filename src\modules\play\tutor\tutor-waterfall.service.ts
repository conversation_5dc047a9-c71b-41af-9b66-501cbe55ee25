import { Injectable, NotFoundException, ForbiddenException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { TutorWaterfallSet, WaterfallQuestionType } from '../../../database/entities/tutor-waterfall-set.entity';
import { WaterfallQuestion } from '../../../database/entities/waterfall-question.entity';
import { WaterfallTrueFalseQuestion } from '../../../database/entities/waterfall-true-false-question.entity';
import { WaterfallMultipleChoiceQuestion } from '../../../database/entities/waterfall-multiple-choice-question.entity';

@Injectable()
export class TutorWaterfallService {
  private readonly logger = new Logger(TutorWaterfallService.name);

  constructor(
    @InjectRepository(TutorWaterfallSet)
    private readonly tutorSetRepository: Repository<TutorWaterfallSet>,
    @InjectRepository(WaterfallQuestion)
    private readonly questionRepository: Repository<WaterfallQuestion>,
    @InjectRepository(WaterfallTrueFalseQuestion)
    private readonly trueFalseRepository: Repository<WaterfallTrueFalseQuestion>,
    @InjectRepository(WaterfallMultipleChoiceQuestion)
    private readonly multipleChoiceRepository: Repository<WaterfallMultipleChoiceQuestion>,
    private readonly dataSource: DataSource,
  ) {}

  async bulkCreateSets(tutorId: string, sets: any[]): Promise<any[]> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const createdSets = [];

      for (const setData of sets) {
        // Create tutor set with MIXED type to allow all question types
        const tutorSet = queryRunner.manager.create(TutorWaterfallSet, {
          title: setData.title,
          tutorId,
          studentId: setData.student_id,
          moduleType: setData.module_type,
          entryId: setData.entry_id,
          questionType: WaterfallQuestionType.MIXED,
          totalQuestions: setData.questions.length,
          totalScore: setData.total_score || 100,
          createdBy: tutorId,
          updatedBy: tutorId,
        });

        const savedSet = await queryRunner.manager.save(tutorSet);
        
        // Create questions based on type
        await this.createQuestions(queryRunner, savedSet.id, WaterfallQuestionType.MIXED, setData.questions, tutorId);

        createdSets.push(savedSet);
      }

      await queryRunner.commitTransaction();
      return createdSets;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Failed to bulk create sets: ${error.message}`, error.stack);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async bulkUpdateSets(tutorId: string, sets: any[]): Promise<any[]> {
    const updatedSets = [];

    for (const setData of sets) {
      const tutorSet = await this.tutorSetRepository.findOne({
        where: { id: setData.id, tutorId },
      });

      if (!tutorSet) {
        throw new NotFoundException(`Set ${setData.id} not found or not owned by tutor`);
      }

      Object.assign(tutorSet, {
        title: setData.title,
        totalScore: setData.total_score,
        updatedBy: tutorId,
      });

      const updated = await this.tutorSetRepository.save(tutorSet);
      updatedSets.push(updated);
    }

    return updatedSets;
  }

  async bulkDeleteSets(tutorId: string, setIds: string[]): Promise<void> {
    for (const setId of setIds) {
      const tutorSet = await this.tutorSetRepository.findOne({
        where: { id: setId, tutorId },
      });

      if (!tutorSet) {
        throw new NotFoundException(`Set ${setId} not found or not owned by tutor`);
      }

      tutorSet.isActive = false;
      tutorSet.updatedBy = tutorId;
      await this.tutorSetRepository.save(tutorSet);
    }
  }

  async addQuestion(tutorId: string, setId: string, questionData: any): Promise<any> {
    const tutorSet = await this.tutorSetRepository.findOne({
      where: { id: setId, tutorId },
    });

    if (!tutorSet) {
      throw new NotFoundException('Set not found or not owned by tutor');
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const question = await this.createSingleQuestion(
        queryRunner,
        setId,
        tutorSet.questionType,
        questionData,
        tutorId
      );

      // Update set question count
      tutorSet.totalQuestions += 1;
      await queryRunner.manager.save(tutorSet);

      await queryRunner.commitTransaction();
      return question;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async updateQuestion(tutorId: string, questionId: string, questionData: any): Promise<any> {
    // Implementation would check ownership and update question
    throw new Error('Method not implemented');
  }

  async deleteQuestion(tutorId: string, questionId: string): Promise<void> {
    // Implementation would check ownership and soft delete question
    throw new Error('Method not implemented');
  }

  async getTutorSets(tutorId: string): Promise<any[]> {
    const sets = await this.tutorSetRepository.find({
      where: { tutorId, isActive: true },
      order: { createdAt: 'DESC' },
    });

    return sets.map(set => ({
      id: set.id,
      title: set.title,
      total_score: set.totalScore,
      total_questions: set.totalQuestions,
      question_type: set.questionType,
      student_id: set.studentId,
      module_type: set.moduleType,
      entry_id: set.entryId,
      is_active: set.isActive,
      created_at: set.createdAt,
      updated_at: set.updatedAt,
    }));
  }

  async getTutorSetWithQuestions(tutorId: string, setId: string): Promise<any> {
    const set = await this.tutorSetRepository.findOne({
      where: { id: setId, tutorId, isActive: true },
    });

    if (!set) {
      throw new NotFoundException('Set not found or not owned by tutor');
    }

    const questions = await this.getAllQuestionTypes(setId);

    return {
      id: set.id,
      title: set.title,
      total_score: set.totalScore,
      total_questions: set.totalQuestions,
      question_type: set.questionType,
      student_id: set.studentId,
      module_type: set.moduleType,
      entry_id: set.entryId,
      is_active: set.isActive,
      created_at: set.createdAt,
      updated_at: set.updatedAt,
      questions,
    };
  }

  private async getAllQuestionTypes(setId: string): Promise<any[]> {
    const [fillInBlankQuestions, trueFalseQuestions, mcQuestions] = await Promise.all([
      this.questionRepository.find({
        where: { setId, isActive: true },
        order: { createdAt: 'ASC' },
      }),
      this.trueFalseRepository.find({
        where: { setId, isActive: true },
        order: { createdAt: 'ASC' },
      }),
      this.multipleChoiceRepository.find({
        where: { setId, isActive: true },
        order: { createdAt: 'ASC' },
      }),
    ]);

    const allQuestions = [];

    // Add fill-in-blank questions
    allQuestions.push(...fillInBlankQuestions.map(q => ({
      id: q.id,
      type: 'fill_in_blank',
      question_text: q.questionText,
      question_text_plain: q.questionTextPlain,
      options: q.options,
      correct_answers: q.correctAnswers,
      time_limit_in_seconds: q.timeLimitInSeconds,
      level: q.level,
      is_active: q.isActive,
      created_at: q.createdAt,
      updated_at: q.updatedAt,
    })));

    // Add true/false questions
    allQuestions.push(...trueFalseQuestions.map(q => ({
      id: q.id,
      type: 'true_false',
      statement: q.statement,
      correct_answer: q.correctAnswer,
      time_limit_in_seconds: q.timeLimitInSeconds,
      level: q.level,
      is_active: q.isActive,
      created_at: q.createdAt,
      updated_at: q.updatedAt,
    })));

    // Add multiple choice questions
    allQuestions.push(...mcQuestions.map(q => ({
      id: q.id,
      type: q.allowMultipleSelection ? 'multiple_choice_multiple' : 'multiple_choice_single',
      question_text: q.questionText,
      options: q.options,
      allow_multiple_selection: q.allowMultipleSelection,
      min_selections: q.minSelections,
      max_selections: q.maxSelections,
      correct_option_indices: q.correctOptionIndices,
      time_limit_in_seconds: q.timeLimitInSeconds,
      level: q.level,
      is_active: q.isActive,
      created_at: q.createdAt,
      updated_at: q.updatedAt,
    })));

    // Sort by creation date to maintain consistent order
    return allQuestions.sort((a, b) => {
      const aQuestion = fillInBlankQuestions.find(q => q.id === a.id) || 
                       trueFalseQuestions.find(q => q.id === a.id) || 
                       mcQuestions.find(q => q.id === a.id);
      const bQuestion = fillInBlankQuestions.find(q => q.id === b.id) || 
                       trueFalseQuestions.find(q => q.id === b.id) || 
                       mcQuestions.find(q => q.id === b.id);
      return new Date(aQuestion.createdAt).getTime() - new Date(bQuestion.createdAt).getTime();
    });
  }

  private async createQuestions(queryRunner: any, setId: string, questionType: WaterfallQuestionType, questions: any[], tutorId: string): Promise<void> {
    for (const questionData of questions) {
      await this.createSingleQuestion(queryRunner, setId, questionType, questionData, tutorId);
    }
  }

  private async createSingleQuestion(queryRunner: any, setId: string, questionType: WaterfallQuestionType, questionData: any, tutorId: string): Promise<any> {
    // For MIXED type, determine actual question type from questionData.type
    const actualType = questionType === WaterfallQuestionType.MIXED ? questionData.type : questionType;
    
    switch (actualType) {
      case 'fill_in_blank':
        const fibQuestion = queryRunner.manager.create(WaterfallQuestion, {
          setId,
          questionText: questionData.question_text,
          questionTextPlain: questionData.question_text_plain,
          correctAnswers: questionData.correct_answers,
          options: questionData.options,
          timeLimitInSeconds: questionData.time_limit_in_seconds,
          level: questionData.level,
          createdBy: tutorId,
          updatedBy: tutorId,
        });
        return await queryRunner.manager.save(fibQuestion);

      case 'true_false':
        const tfQuestion = queryRunner.manager.create(WaterfallTrueFalseQuestion, {
          setId,
          statement: questionData.statement,
          correctAnswer: questionData.correct_answer,
          timeLimitInSeconds: questionData.time_limit_in_seconds,
          level: questionData.level,
          createdBy: tutorId,
          updatedBy: tutorId,
        });
        return await queryRunner.manager.save(tfQuestion);

      case 'multiple_choice_single':
      case 'multiple_choice_multiple':
        const mcQuestion = queryRunner.manager.create(WaterfallMultipleChoiceQuestion, {
          setId,
          questionText: questionData.question_text,
          options: questionData.options,
          correctOptionIndices: questionData.correct_option_indices || [questionData.correct_option_index],
          allowMultipleSelection: actualType === 'multiple_choice_multiple',
          minSelections: questionData.min_selections,
          maxSelections: questionData.max_selections,
          timeLimitInSeconds: questionData.time_limit_in_seconds,
          level: questionData.level,
          createdBy: tutorId,
          updatedBy: tutorId,
        });
        return await queryRunner.manager.save(mcQuestion);

      default:
        throw new Error(`Unsupported question type: ${actualType}`);
    }
  }
}