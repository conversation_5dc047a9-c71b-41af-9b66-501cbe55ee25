import { DataSource } from 'typeorm';
import { getTypeOrmConfig } from '../config/database.config';
import { ConfigService } from '@nestjs/config';
import { TranscriptionSeeder } from '../config/seeds/transcription-seeder';
import { Book } from '../database/entities/book.entity';
import { Sentence } from '../database/entities/sentence.entity';

async function runTranscriptionSeeder() {
  const configService = new ConfigService();
  const dataSourceOptions = await getTypeOrmConfig(configService);
  
  const dataSource = new DataSource(dataSourceOptions);
  
  try {
    await dataSource.initialize();
    console.log('📊 Database connection established');
    
    const bookRepository = dataSource.getRepository(Book);
    const sentenceRepository = dataSource.getRepository(Sentence);
    const seeder = new TranscriptionSeeder(bookRepository, sentenceRepository);
    await seeder.run();
    
    console.log('🎉 Transcription seeding completed successfully!');
  } catch (error) {
    console.error('❌ Error during transcription seeding:', error);
    process.exit(1);
  } finally {
    await dataSource.destroy();
    console.log('🔌 Database connection closed');
  }
}

runTranscriptionSeeder();