import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, ILike, In, Not, IsNull } from 'typeorm';
import { EssayMissionTasks } from 'src/database/entities/essay-mission-tasks.entity';
import { EssayTaskSubmissions } from 'src/database/entities/essay-task-submissions.entity';
import { EssayTaskSubmissionHistory } from 'src/database/entities/essay-task-submission-history.entity';
import { EssayMission, MissionFrequency } from 'src/database/entities/essay-mission.entity';
import { MissionResponseDto, MissionPaginationDto, CreateMissionDto, UpdateMissionDto, EssayTaskSubmissionDto } from 'src/database/models/mission.dto';
import { PagedListDto } from 'src/common/models/paged-list.dto';
import { PaginationDto } from 'src/common/models/pagination.dto';
import { UsersService } from '../users/users.service';
import { EssayModuleSkinPreference, SkinScopeType } from 'src/database/entities/essay-preferences.entity';
import { UserType } from 'src/database/entities/user.entity';
import { TutorMatchingService } from '../tutor-matching/tutor-matching.service';
import { PlanFeaturesService } from '../plans/plan-features.service';
import { FeatureType } from 'src/database/entities/plan-feature.entity';
import { SubmissionStatus } from 'src/constants/submission.enum';

@Injectable()
export class EssayMissionService {
  private readonly logger = new Logger(EssayMissionService.name);

  constructor(
    @InjectRepository(EssayMission)
    private readonly missionRepository: Repository<EssayMission>,
    @InjectRepository(EssayMissionTasks)
    private readonly missionTasksRepository: Repository<EssayMissionTasks>,
    @InjectRepository(EssayTaskSubmissions)
    private readonly essayTaskSubmissionsRepository: Repository<EssayTaskSubmissions>,
    @InjectRepository(EssayTaskSubmissionHistory)
    private readonly essayTaskSubmissionHistoryRepository: Repository<EssayTaskSubmissionHistory>,
    @InjectRepository(EssayModuleSkinPreference)
    private readonly essayModuleSkinPreferenceRepository: Repository<EssayModuleSkinPreference>,
    private readonly dataSource: DataSource,
    private readonly userService: UsersService,
    private readonly tutorStudentMatchingService: TutorMatchingService,
    private readonly planFeatureService: PlanFeaturesService,
  ) {}

  private toMissionResponseDto(mission: EssayMission, tasks: EssayMissionTasks[], userId?: string): MissionResponseDto {
    const taskProgress = tasks.flatMap((task) => {
      if (!userId) {
        return [
          {
            id: task.id,
            progress: 0,
            status: null,
          },
        ];
      }
      const firstRevisionSubmission = task.submissions?.find((submission) => (submission.isFirstRevision || submission.currentRevision === 1) && submission.createdBy === userId);

      if (firstRevisionSubmission) {
        return [
          {
            id: task.id,
            progress: firstRevisionSubmission.firstRevisionProgress || 0,
            status: firstRevisionSubmission.status,
          },
        ];
      }
      return [
        {
          id: task.id,
          progress: 0,
          status: null,
        },
      ];
    });

    return {
      id: mission.id,
      timeFrequency: mission.timeFrequency,
      isActive: mission.isActive,
      sequenceNumber: mission.sequenceNumber,
      tasks: tasks.map((task) => ({
        id: task.id,
        title: task.title,
        description: task.description,
        wordLimitMinimum: task.wordLimitMinimum,
        wordLimitMaximum: task.wordLimitMaximum,
        isActive: task.isActive,
        timePeriodUnit: task.timePeriodUnit,
        deadline: task.deadline,
        instructions: task.instructions,
        metaData: task.metaData,
      })),
      taskProgress,
    };
  }

  async findAll(paginationDto?: MissionPaginationDto, userId?: string): Promise<PagedListDto<MissionResponseDto>> {
    const options: any = {
      relations: ['tasks', 'tasks.submissions'],
      where: { isActive: true },
      order: {},
    };

    if (paginationDto) {
      const { page = 1, limit = 10, sortBy, sortDirection, timeFrequency, weekOrMonth, title } = paginationDto;
      if (timeFrequency) {
        options.where.timeFrequency = timeFrequency;
      }
      options.skip = (page - 1) * limit;
      options.take = limit;

      if (sortBy) {
        if (sortBy == 'week' || sortBy == 'month') {
          options.where.sequenceNumber = weekOrMonth;
        } else if (sortBy == 'title') {
          options.where.tasks = { title: ILike(`%${title}%`) };
        }
        if (sortBy == 'createdAt' || sortBy == 'updatedAt') {
          if (sortDirection) {
            options.order = { [sortBy]: sortDirection.toUpperCase() };
          } else {
            options.order = { [sortBy]: 'DESC' };
          }
        }
      }
    }

    options.order.sequenceNumber = 'ASC'; // Default sort by sequence number
    const [missions, totalCount] = await this.missionRepository.findAndCount(options);

    if (userId) {
      const missionDtos = missions.map((mission) => this.toMissionResponseDto(mission, mission.tasks, userId));
      return new PagedListDto(missionDtos, totalCount);
    }

    const missionDtos = missions.map((mission) => this.toMissionResponseDto(mission, mission.tasks));

    return new PagedListDto(missionDtos, totalCount);
  }

  async findById(id: string): Promise<MissionResponseDto> {
    const mission = await this.missionRepository.findOne({
      where: { id },
      relations: ['tasks'],
    });

    if (!mission) {
      throw new NotFoundException(`Mission with ID ${id} not found`);
    }

    return this.toMissionResponseDto(mission, mission.tasks);
  }

  async findByIdWithTasks(id: string): Promise<MissionResponseDto> {
    const missionTask = await this.missionTasksRepository.findOne({
      where: { id },
      relations: ['mission'],
    });
    if (!missionTask) {
      throw new NotFoundException(`Mission with ID ${id} not found`);
    }
    return this.toMissionResponseDto(missionTask.mission, [missionTask]);
  }

  async create(missionData: CreateMissionDto): Promise<MissionResponseDto> {
    try {
      const { timeFrequency, tasks } = missionData;

      const lastMission = await this.missionRepository.findOne({
        order: { sequenceNumber: 'DESC' },
        where: { isActive: true, timeFrequency: timeFrequency },
      });

      const lastSequenceNumber = lastMission ? lastMission.sequenceNumber + 1 : 1;

      if (timeFrequency == MissionFrequency.WEEKLY && lastSequenceNumber > 52) {
        throw new BadRequestException('Cannot create more than 52 missions ( yearly ) for weekly frequencies');
      }

      if (timeFrequency == MissionFrequency.MONTHLY && lastSequenceNumber > 12) {
        throw new BadRequestException('Cannot create more than 12 missions for monthly ( yearly ) frequencies');
      }

      const mission = this.missionRepository.create({
        timeFrequency,
        isActive: true,
        sequenceNumber: lastSequenceNumber,
      });

      const savedMission = await this.missionRepository.save(mission);

      const missionTasks = tasks.map((task) =>
        this.missionTasksRepository.create({
          ...task,
          mission: savedMission,
          missionId: savedMission.id,
          isActive: true,
        }),
      );

      const savedTasks = await this.missionTasksRepository.save(missionTasks);

      return this.toMissionResponseDto(savedMission, savedTasks);
    } catch (error) {
      this.logger.error(`Failed to create mission: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to create mission. ${error.message}`);
    }
  }

  async update(id: string, missionData: UpdateMissionDto): Promise<MissionResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const mission = await queryRunner.manager.findOne(EssayMission, {
        where: { id },
        relations: ['tasks'],
      });

      if (!mission) {
        throw new NotFoundException(`Mission with ID ${id} not found`);
      }

      const { tasks: incomingTasksData, ...missionProperties } = missionData;
      Object.assign(mission, missionProperties);
      await queryRunner.manager.save(EssayMission, mission);

      if (incomingTasksData && incomingTasksData.length > 0) {
        const incomingTaskIds = incomingTasksData.filter((task) => task.id).map((task) => task.id);

        const tasksToDelete = mission.tasks?.filter((task) => !incomingTaskIds.includes(task.id)) || [];

        if (tasksToDelete.length > 0) {
          await queryRunner.manager.delete(
            EssayMissionTasks,
            tasksToDelete.map((task) => task.id),
          );
        }

        for (const taskData of incomingTasksData) {
          if (taskData.id) {
            await queryRunner.manager.update(
              EssayMissionTasks,
              { id: taskData.id },
              {
                ...taskData,
                mission: { id: mission.id },
                missionId: mission.id,
              },
            );
          } else {
            const newTask = queryRunner.manager.create(EssayMissionTasks, {
              ...taskData,
              mission: { id: mission.id },
              missionId: mission.id,
            });
            await queryRunner.manager.save(EssayMissionTasks, newTask);
          }
        }
      }

      const updatedMission = await queryRunner.manager.findOne(EssayMission, {
        where: { id: mission.id },
        relations: ['tasks'],
      });

      await queryRunner.commitTransaction();

      return this.toMissionResponseDto(updatedMission, updatedMission?.tasks || []);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Failed to update mission ${id}: ${error.message}`, error.stack);

      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to update mission: ${error.message || 'Please check input data'}`);
    } finally {
      await queryRunner.release();
    }
  }

  async softDelete(id: string): Promise<void> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const mission = await queryRunner.manager.findOne(EssayMission, {
        where: { id },
        relations: ['tasks'],
      });

      if (!mission) {
        throw new NotFoundException(`Mission with ID ${id} not found`);
      }

      mission.isActive = false;
      await queryRunner.manager.save(EssayMission, mission);

      if (mission.tasks && mission.tasks.length > 0) {
        await queryRunner.manager.update(EssayMissionTasks, { missionId: mission.id }, { isActive: false });
      }

      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Failed to soft delete mission ${id}: ${error.message}`, error.stack);

      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to soft delete mission: ${error.message}`);
    } finally {
      await queryRunner.release();
    }
  }

  async hardDelete(id: string): Promise<void> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const mission = await queryRunner.manager.findOne(EssayMission, {
        where: { id },
        relations: ['tasks'],
      });

      if (!mission) {
        throw new NotFoundException(`Mission with ID ${id} not found`);
      }

      if (mission.tasks && mission.tasks.length > 0) {
        await queryRunner.manager.delete(
          EssayMissionTasks,
          mission.tasks.map((task) => task.id),
        );
      }

      await queryRunner.manager.delete(EssayMission, id);

      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Failed to delete mission ${id}: ${error.message}`, error.stack);

      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to delete mission: ${error.message}`);
    } finally {
      await queryRunner.release();
    }
  }

  async findByFrequency(frequency: MissionFrequency): Promise<MissionResponseDto[]> {
    const missions = await this.missionRepository.find({
      where: { timeFrequency: frequency, isActive: true },
      relations: ['tasks'],
    });

    return missions.map((mission) => this.toMissionResponseDto(mission, mission.tasks));
  }

  async findAllSubmittedEssaysByMission(paginationDto?: MissionPaginationDto, userId?: string): Promise<PagedListDto<EssayTaskSubmissionDto>> {
    const { page = 1, limit = 10, timeFrequency } = paginationDto;

    const where = {
      task: Not(IsNull()),
      status: In([SubmissionStatus.SUBMITTED, SubmissionStatus.RESUBMITTED]),
      firstSubmittedAt: Not(IsNull()),
    }

    if (userId) {
      const user = await this.userService.findById(userId);
      if (!user) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }
      if (user.type === UserType.STUDENT) {
        where['createdBy'] = userId;
      }
      if (user.type === UserType.TUTOR) {
        const feature = await this.planFeatureService.findByType(FeatureType.ENGLISH_ESSAY);
        const tutorStudentIds = await this.tutorStudentMatchingService.getTutorStudents(userId, feature?.id);
        where['createdBy'] = In(tutorStudentIds.map((student) => student.id));
      }
    }

    const missionSubmissions = await this.essayTaskSubmissionsRepository.find({
      where: where,
      // relations: ['task'],
    })

    const firstSubmissions: EssayTaskSubmissions[] = [];
    const skip = (page - 1) * limit;
    const paginatedMinDates = missionSubmissions.slice(skip, skip + limit);

    for (const { taskId, firstSubmittedAt } of paginatedMinDates) {
      const submission = await this.essayTaskSubmissionsRepository.findOne({
        where: {
          taskId: taskId,
          firstSubmittedAt: firstSubmittedAt,
          status: In([SubmissionStatus.SUBMITTED, SubmissionStatus.REVIEWED, SubmissionStatus.RESUBMITTED]),
        },
        relations: ['submissionSkin', 'task', 'task.mission', 'submissionMark'],
      });

      if (submission) {
        const submissionHistory = await this.essayTaskSubmissionHistoryRepository.find({
          where: {
            id: submission.latestSubmissionId,
          },
          relations: ['submissionMark'],
        });
        submission.submissionHistory = submissionHistory;
        firstSubmissions.push(submission);
      }
    }
    const totalCount = missionSubmissions.length;
    const firstSubmissionsDto = await Promise.all(firstSubmissions.map((submission) => this.toEssayTaskSubmissionDto(submission)));
    return new PagedListDto(firstSubmissionsDto, totalCount);
  }

  async findAllSubmittedEssays(paginationDto?: PaginationDto, userId?: string): Promise<PagedListDto<EssayTaskSubmissionDto>> {
    const { page = 1, limit = 10 } = paginationDto;

    const where = {
      task: IsNull(),
      status: In([SubmissionStatus.SUBMITTED, SubmissionStatus.RESUBMITTED]),
      firstSubmittedAt: Not(IsNull()),
    };
    if (userId) {
      const user = await this.userService.findById(userId);
      if (!user) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }
      if (user.type === UserType.STUDENT) {
        where['createdBy'] = userId;
      }
      if (user.type === UserType.TUTOR) {
        const feature = await this.planFeatureService.findByType(FeatureType.ENGLISH_ESSAY);
        const tutorStudentIds = await this.tutorStudentMatchingService.getTutorStudents(userId, feature?.id);
        where['createdBy'] = In(tutorStudentIds.map((student) => student.id));
      }
    }
    const submissionWithoutTask = await this.essayTaskSubmissionsRepository.find({
      where: where,
      order: {
        firstSubmittedAt: 'ASC',
      },
    });

    const firstSubmissions: EssayTaskSubmissions[] = [];
    const skip = (page - 1) * limit;
    const paginatedMinDates = submissionWithoutTask.slice(skip, skip + limit);

    for (const { taskId, id } of paginatedMinDates) {
      const submission = await this.essayTaskSubmissionsRepository.findOne({
        where: {
          id: id,
          taskId: taskId,
        },
        relations: ['submissionSkin', 'task', 'studentDiarySkin'],
      });

      if (submission) {
        const submissionHistory = await this.essayTaskSubmissionHistoryRepository.find({
          where: {
            id: submission.latestSubmissionId,
          },
          relations: ['submissionMark'],
        });
        submission.submissionHistory = submissionHistory;
        firstSubmissions.push(submission);
      }
    }
    const totalCount = submissionWithoutTask.length;
    const firstSubmissionsDto = await Promise.all(firstSubmissions.map((submission) => this.toEssayTaskSubmissionDto(submission)));
    return new PagedListDto(firstSubmissionsDto, totalCount);
  }

  async findSubmittedEssayById(id: string, userId?: string): Promise<EssayTaskSubmissionDto> {
    const where = { id };
    if (userId) {
      where['createdBy'] = userId;
    }
    const submission = await this.essayTaskSubmissionsRepository.findOne({
      where: where,
      relations: ['submissionSkin', 'task', 'studentDiarySkin'],
    });

    if (!submission) {
      throw new NotFoundException(`Submitted essay with ID ${id} not found`);
    }

    const submissionHistory = await this.essayTaskSubmissionHistoryRepository.find({
      where: {
        submissionId: submission.id,
        isSubmitted: true,
      },
      relations: ['submissionMark'],
    });
    submission.submissionHistory = submissionHistory;

    const essayTaskSubmissionDto = await this.toEssayTaskSubmissionDto(submission);
    return essayTaskSubmissionDto;
  }

  private async toEssayTaskSubmissionDto(submission: EssayTaskSubmissions): Promise<EssayTaskSubmissionDto> {
    const submissionUpdatedBy = await this.userService.findById(submission.updatedBy);
    const submissionCreatedBy = await this.userService.findById(submission.createdBy);

    const submissionHistoryPromises = submission.submissionHistory.map(async (history) => {
      const historyUpdatedBy = await this.userService.findById(history.updatedBy);
      const historyCreatedBy = await this.userService.findById(history.createdBy);

      let submissionMark = null;
      if (history.submissionMark) {
        const markUpdatedBy = await this.userService.findById(history.submissionMark.updatedBy);

        submissionMark = {
          id: history.submissionMark.id,
          points: history.submissionMark.points,
          submissionFeedback: history.submissionMark.submissionFeedback,
          taskRemarks: history.submissionMark.taskRemarks,
          createdAt: history.submissionMark.createdAt,
          updatedAt: history.submissionMark.updatedAt,
          updatedBy: markUpdatedBy?.name || null,
          updatedById: markUpdatedBy?.id || null,
        };
      }

      return {
        id: history.id,
        content: history.content,
        metaData: history.metaData,
        createdAt: history.createdAt,
        updatedAt: history.updatedAt,
        submissionMark,
        wordCount: history.wordCount,
        submissionDate: history.submissionDate,
        sequenceNumber: history.sequenceNumber,
        updatedBy: historyUpdatedBy?.name || null,
        updatedById: historyUpdatedBy?.id || null,
        createdBy: historyCreatedBy?.name || null,
        createdById: historyCreatedBy?.id || null,
        status: history.status,
      };
    });

    const processedHistory = await Promise.all(submissionHistoryPromises);

    const defaultSkinPreference = await this.essayModuleSkinPreferenceRepository.findOne({
      where: {
        scopeType: SkinScopeType.MODULE_DEFAULT,
        isActive: true,
        createdBy: submission.createdBy,
      },
      relations: ['skin', 'studentSkin'],
    });

    const diarySkin =
      (submission.submissionSkin || submission.studentDiarySkin) ??
      (defaultSkinPreference?.skin || defaultSkinPreference?.studentSkin);

    const response: EssayTaskSubmissionDto = {
      id: submission.id,
      title: submission.title,
      status: submission.status,
      currentRevision: submission.currentRevision,
      task: submission.task
        ? {
            id: submission.task.id,
            wordLimitMinimum: submission.task.wordLimitMinimum,
            wordLimitMaximum: submission.task.wordLimitMaximum,
            title: submission.task.title,
            instructions: submission.task.instructions,
          }
        : null,
      updatedBy: submissionUpdatedBy?.name || null,
      updatedById: submissionUpdatedBy?.id || null,
      createdAt: submission.createdAt,
      updatedAt: submission.updatedAt,
      submissionHistory: processedHistory,
      isActive: submission.isActive,
      diarySkin: diarySkin,
      createdBy: submissionCreatedBy?.name || null,
      createdById: submissionCreatedBy?.id || null,
    };

    return response;
  }
}
