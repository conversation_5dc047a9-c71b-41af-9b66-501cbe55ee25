import { PipeTransform, Injectable, BadRequestException } from '@nestjs/common';
import { TimezoneService } from '../services/timezone.service';
import { JwtService } from '@nestjs/jwt';

@Injectable()
export class TimezoneRangePipe implements PipeTransform {
  constructor(
    private readonly timezoneService: TimezoneService,
    private readonly jwtService: JwtService,
  ) {}

  async transform(value: any): Promise<{ start: Date; end: Date } | null> {
    if (!value) {
      return null;
    }

    try {
      // Get user timezone (simplified - in real implementation, extract from request context)
      const userTimezone = 'Asia/Seoul'; // Default fallback

      // Handle different range types
      if (typeof value === 'string') {
        return this.parseRangeString(value, userTimezone);
      }

      if (typeof value === 'object' && value.start && value.end) {
        return {
          start: this.timezoneService.convertUserTimezoneToUTC(value.start, userTimezone),
          end: this.timezoneService.convertUserTimezoneToUTC(value.end, userTimezone)
        };
      }

      throw new BadRequestException(`Invalid range format: ${value}`);
    } catch (error) {
      throw new BadRequestException(`Invalid date range: ${error.message}`);
    }
  }

  private parseRangeString(value: string, timezone: string): { start: Date; end: Date } {
    const today = new Date();
    
    switch (value.toLowerCase()) {
      case 'today':
        return this.timezoneService.getUserDateRange(today.toISOString().split('T')[0], timezone);
      
      case 'yesterday':
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);
        return this.timezoneService.getUserDateRange(yesterday.toISOString().split('T')[0], timezone);
      
      case 'this_week':
        return this.getWeekRange(today, timezone);
      
      case 'this_month':
        return this.getMonthRange(today, timezone);
      
      default:
        // Try to parse as date string
        if (/^\d{4}-\d{2}-\d{2}$/.test(value)) {
          return this.timezoneService.getUserDateRange(value, timezone);
        }
        throw new BadRequestException(`Unsupported range: ${value}`);
    }
  }

  private getWeekRange(date: Date, timezone: string): { start: Date; end: Date } {
    const start = new Date(date);
    const day = start.getDay();
    const diff = start.getDate() - day + (day === 0 ? -6 : 1); // Monday as first day
    start.setDate(diff);
    
    const end = new Date(start);
    end.setDate(start.getDate() + 6);
    
    return {
      start: this.timezoneService.convertUserTimezoneToUTC(start.toISOString().split('T')[0] + 'T00:00:00', timezone),
      end: this.timezoneService.convertUserTimezoneToUTC(end.toISOString().split('T')[0] + 'T23:59:59', timezone)
    };
  }

  private getMonthRange(date: Date, timezone: string): { start: Date; end: Date } {
    const start = new Date(date.getFullYear(), date.getMonth(), 1);
    const end = new Date(date.getFullYear(), date.getMonth() + 1, 0);
    
    return {
      start: this.timezoneService.convertUserTimezoneToUTC(start.toISOString().split('T')[0] + 'T00:00:00', timezone),
      end: this.timezoneService.convertUserTimezoneToUTC(end.toISOString().split('T')[0] + 'T23:59:59', timezone)
    };
  }
}