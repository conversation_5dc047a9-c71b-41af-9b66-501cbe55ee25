import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PlansService } from './plans.service';
import { PlansController } from './plans.controller';
import { PlanFeaturesService } from './plan-features.service';
import { PlanFeaturesController } from './plan-features.controller';
import { Plan } from '../../database/entities/plan.entity';
import { UserPlan } from '../../database/entities/user-plan.entity';
import { User } from '../../database/entities/user.entity';
import { PlanFeature } from '../../database/entities/plan-feature.entity';
import { JwtService } from '@nestjs/jwt';
import { DiaryModule } from '../diary/diary.module';
import { AuthModule } from '../auth/auth.module';
import { ConfigModule } from '@nestjs/config';
import { TutorMatchingModule } from '../tutor-matching/tutor-matching.module';
import { NotificationModule } from '../notification/notification.module';
import { PromotionsModule } from '../promotions/promotions.module';
import { PaymentModule } from '../payment/payment.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Plan, UserPlan, User, PlanFeature]),
    forwardRef(() => DiaryModule),
    forwardRef(() => AuthModule),
    ConfigModule,
    TutorMatchingModule,
    NotificationModule,
    forwardRef(() => PromotionsModule),
    forwardRef(() => PaymentModule),
  ],
  controllers: [PlansController, PlanFeaturesController],
  providers: [PlansService, PlanFeaturesService, JwtService],
  exports: [PlansService, PlanFeaturesService],
})
export class PlansModule {}
