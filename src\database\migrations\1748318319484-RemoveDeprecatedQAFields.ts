import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveDeprecatedQAFields1748318319484 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // Remove the deprecated columns
        await queryRunner.query(`
            ALTER TABLE qa_task_submissions 
            DROP COLUMN IF EXISTS is_first_revision,
            DROP COLUMN IF EXISTS first_submitted_at,
            DROP COLUMN IF EXISTS last_submitted_at;
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Add back the deprecated columns if needed to rollback
        await queryRunner.query(`
            ALTER TABLE qa_task_submissions 
            ADD COLUMN IF NOT EXISTS is_first_revision boolean DEFAULT true,
            ADD COLUMN IF NOT EXISTS first_submitted_at timestamp with time zone,
            ADD COLUMN IF NOT EXISTS last_submitted_at timestamp with time zone;
        `);
    }
}
