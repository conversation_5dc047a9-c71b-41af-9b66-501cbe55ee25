/**
 * Enum for notification related entity types
 * Used to categorize what type of entity a notification is related to
 */
export enum RelatedEntityType {
  // Friendship related
  FRIENDSHIP = 'friendship',
  
  // Diary follow related
  DIARY_FOLLOW_REQUEST = 'diary_follow_request',
  DIARY_FOLLOW_ACCEPTED = 'diary_follow_accepted',
  DIARY_FOLLOW_REJECTED = 'diary_follow_rejected',
  
  // Chat related
  MESSAGE = 'message',
  CHAT_MESSAGE = 'chat_message',
  CONVERSATION = 'conversation',
  
  // Diary related
  DIARY_ENTRY = 'diary_entry',
  DIARY_SUBMISSION = 'diary_submission',
  
  // Mission related
  MISSION = 'mission',
  MISSION_SUBMISSION = 'mission_submission',
  MISSION_DIARY_ENTRY = 'mission_diary_entry',
  
  // QA related
  QA_SUBMISSION = 'qa_submission',
  QA_ASSIGNMENT = 'qa_assignment',
  
  // Essay related
  ESSAY_SUBMISSION = 'essay_submission',
  
  // Novel related
  NOVEL = 'novel',
  NOVEL_ENTRY = 'novel_entry',
  NOVEL_SUBMISSION = 'novel_submission',
  
  // Story maker related
  STORY_MAKER_SUBMISSION = 'story_maker_submission',
  STORY_MAKER_EVALUATION = 'story_maker_evaluation',
  
  // Plan related
  USER_PLAN = 'user_plan',
  
  // Tutor matching related
  STUDENT_TUTOR_MAPPING = 'student_tutor_mapping',
  STUDENT_TUTOR_CHANGE = 'student_tutor_change',
  
  // QA Mission related
  QA_MISSION_SUBMISSION = 'qa_mission_submission',
  
  // Essay related
  ESSAY_MISSION_SUBMISSION = 'essay_mission_submission',
  
  // Tutor related
  TUTOR = 'tutor',
  TUTOR_ASSIGNMENT = 'tutor_assignment',
  
  // Award related
  AWARD = 'award',
  AWARD_WINNERS = 'award_winners',
  
  // Diary related (additional)
  DIARY = 'diary',
  DIARY_FRIEND_SHARE = 'diary_friend_share',
  
  // General
  SYSTEM = 'system',
}
