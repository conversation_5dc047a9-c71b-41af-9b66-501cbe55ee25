import { Entity, Column, OneToMany, ManyTo<PERSON>ne, Jo<PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { User } from './user.entity';

export enum WaterfallQuestionType {
  FILL_IN_BLANK = 'fill_in_blank',
  TRUE_FALSE = 'true_false',
  MULTIPLE_CHOICE_SINGLE = 'multiple_choice_single',
  MULTIPLE_CHOICE_MULTIPLE = 'multiple_choice_multiple',
  MIXED = 'mixed'
}

export enum ModuleType {
  DIARY = 'diary',
  NOVEL = 'novel',
  ESSAY = 'essay',
  QA = 'qa'
}

@Entity()
export class TutorWaterfallSet extends AuditableBaseEntity {
  @Column()
  title: string;

  @Column({ name: 'total_score' })
  totalScore: number;

  @Column({ name: 'total_questions' })
  totalQuestions: number;

  @Column({ name: 'question_type', type: 'enum', enum: WaterfallQuestionType })
  questionType: WaterfallQuestionType;

  @Column({ name: 'tutor_id' })
  tutorId: string;

  @Column({ name: 'student_id' })
  studentId: string;

  @Column({ name: 'module_type', type: 'enum', enum: ModuleType })
  moduleType: ModuleType;

  @Column({ name: 'entry_id' })
  entryId: string;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'tutor_id' })
  tutor: User;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'student_id' })
  student: User;
}