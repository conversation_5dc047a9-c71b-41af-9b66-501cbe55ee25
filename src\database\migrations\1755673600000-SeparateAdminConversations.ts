import { MigrationInterface, QueryRunner } from 'typeorm';

export class SeparateAdminConversations1755673600000 implements MigrationInterface {
  name = 'SeparateAdminConversations1755673600000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create admin_conversation table
    await queryRunner.query(`
      CREATE TABLE "admin_conversation" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP DEFAULT now(),
        "created_by" character varying(36),
        "updated_by" character varying(36),
        "user_id" uuid NOT NULL,
        "virtual_admin_id" uuid NOT NULL,
        "status" character varying NOT NULL DEFAULT 'active',
        "last_message_at" TIMESTAMP,
        "last_message_text" text,
        "last_message_sender_id" uuid,
        "user_unread_count" integer NOT NULL DEFAULT 0,
        "admin_unread_count" integer NOT NULL DEFAULT 0,
        CONSTRAINT "PK_admin_conversation" PRIMARY KEY ("id"),
        CONSTRAINT "FK_admin_conversation_user_id" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE CASCADE,
        CONSTRAINT "FK_admin_conversation_virtual_admin_id" FOREIGN KEY ("virtual_admin_id") REFERENCES "user"("id") ON DELETE CASCADE
      )
    `);

    // Create unique index on user_id
    await queryRunner.query(`
      CREATE UNIQUE INDEX "IDX_admin_conversation_user_id" ON "admin_conversation" ("user_id")
    `);

    // Add admin_conversation_id column to message table
    await queryRunner.query(`
      ALTER TABLE "message" ADD COLUMN "admin_conversation_id" uuid
    `);

    // Add foreign key constraint
    await queryRunner.query(`
      ALTER TABLE "message" 
      ADD CONSTRAINT "FK_message_admin_conversation_id" 
      FOREIGN KEY ("admin_conversation_id") REFERENCES "admin_conversation"("id") ON DELETE CASCADE
    `);

    // Make conversation_id nullable
    await queryRunner.query(`
      ALTER TABLE "message" ALTER COLUMN "conversation_id" DROP NOT NULL
    `);

    // Migrate existing admin conversations
    console.log('Migrating existing admin conversations...');
    
    // Get virtual admin user ID
    const virtualAdminResult = await queryRunner.query(`
      SELECT id FROM "user" WHERE user_id = 'virtual-admin' LIMIT 1
    `);
    
    if (virtualAdminResult.length === 0) {
      throw new Error('Virtual admin user not found. Please run the virtual admin migration first.');
    }
    
    const virtualAdminId = virtualAdminResult[0].id;

    // Get all existing admin conversations
    const adminConversations = await queryRunner.query(`
      SELECT id, participant2_id as user_id, last_message_at, last_message_text, 
             last_message_sender_id, participant2_unread_count as user_unread_count,
             participant1_unread_count as admin_unread_count, status, created_at, updated_at
      FROM conversation 
      WHERE is_admin_conversation = true
    `);

    for (const conv of adminConversations) {
      // Create new admin conversation
      const newAdminConvResult = await queryRunner.query(`
        INSERT INTO "admin_conversation" 
        (user_id, virtual_admin_id, status, last_message_at, last_message_text, 
         last_message_sender_id, user_unread_count, admin_unread_count, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        RETURNING id
      `, [
        conv.user_id, virtualAdminId, conv.status, conv.last_message_at,
        conv.last_message_text, conv.last_message_sender_id, 
        conv.user_unread_count, conv.admin_unread_count, conv.created_at, conv.updated_at
      ]);

      const newAdminConvId = newAdminConvResult[0].id;

      // Move messages to admin conversation
      await queryRunner.query(`
        UPDATE "message" 
        SET admin_conversation_id = $1, conversation_id = NULL
        WHERE conversation_id = $2
      `, [newAdminConvId, conv.id]);

      // Delete old admin conversation
      await queryRunner.query(`
        DELETE FROM conversation WHERE id = $1
      `, [conv.id]);
    }

    // Remove admin-specific columns from conversation table
    await queryRunner.query(`
      ALTER TABLE "conversation" DROP CONSTRAINT IF EXISTS "FK_conversation_admin_conversation_user_id"
    `);
    await queryRunner.query(`
      ALTER TABLE "conversation" DROP COLUMN IF EXISTS "admin_conversation_user_id"
    `);
    await queryRunner.query(`
      ALTER TABLE "conversation" DROP COLUMN IF EXISTS "is_admin_conversation"
    `);

    // Drop admin conversation participant table as it's no longer needed
    await queryRunner.query(`
      DROP TABLE IF EXISTS "admin_conversation_participant"
    `);

    console.log('Admin conversation separation completed.');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // This is a complex migration to reverse, so we'll keep it simple
    // In production, you'd want to implement a proper rollback
    
    // Add back admin columns to conversation table
    await queryRunner.query(`
      ALTER TABLE "conversation" ADD COLUMN "is_admin_conversation" boolean DEFAULT false
    `);
    await queryRunner.query(`
      ALTER TABLE "conversation" ADD COLUMN "admin_conversation_user_id" uuid
    `);

    // Remove admin_conversation_id from message table
    await queryRunner.query(`
      ALTER TABLE "message" DROP CONSTRAINT IF EXISTS "FK_message_admin_conversation_id"
    `);
    await queryRunner.query(`
      ALTER TABLE "message" DROP COLUMN IF EXISTS "admin_conversation_id"
    `);

    // Make conversation_id required again
    await queryRunner.query(`
      ALTER TABLE "message" ALTER COLUMN "conversation_id" SET NOT NULL
    `);

    // Drop admin_conversation table
    await queryRunner.query(`
      DROP TABLE IF EXISTS "admin_conversation"
    `);

    console.log('Rollback completed.');
  }
}