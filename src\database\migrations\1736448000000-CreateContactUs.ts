import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateContactUs1736448000000 implements MigrationInterface {
  name = 'CreateContactUs1736448000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TYPE "public"."contact_us_status_enum" AS ENUM('pending', 'responded', 'closed')
    `);
    
    await queryRunner.query(`
      CREATE TABLE "contact_us" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" character varying NOT NULL,
        "email" character varying NOT NULL,
        "phone" character varying,
        "subject" character varying NOT NULL,
        "message" text NOT NULL,
        "status" "public"."contact_us_status_enum" NOT NULL DEFAULT 'pending',
        "admin_response" text,
        "responded_by" character varying,
        "responded_at" TIMESTAMP,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        "created_by" character varying,
        "updated_by" character varying,
        CONSTRAINT "PK_contact_us" PRIMARY KEY ("id")
      )
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "contact_us"`);
    await queryRunner.query(`DROP TYPE "public"."contact_us_status_enum"`);
  }
}