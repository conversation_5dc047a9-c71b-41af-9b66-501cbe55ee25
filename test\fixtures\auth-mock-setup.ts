import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { JwtService } from '@nestjs/jwt';
import { DataSource } from 'typeorm';
import { AuthService } from '../../src/modules/auth/auth.service';
import { UsersService } from '../../src/modules/users/users.service';
import EmailService from '../../src/common/services/email.service';
import { ProfilePictureService } from '../../src/common/services/profile-picture.service';
import { DiaryService } from '../../src/modules/diary/diary.service';
import { AsyncNotificationHelperService } from '../../src/modules/notification/async-notification-helper.service';
import { DeeplinkService } from '../../src/common/utils/deeplink.service';
import { TokenBlacklistService } from '../../src/common/services/token-blacklist.service';
import { EmailTemplateService } from '../../src/common/services/email-template.service';
import { PlansService } from '../../src/modules/plans/plans.service';
import { Role } from '../../src/database/entities/role.entity';
import { User } from '../../src/database/entities/user.entity';
import { UserPlan } from '../../src/database/entities/user-plan.entity';
import { PasswordReset } from '../../src/database/entities/password-reset.entity';
import { EmailVerification } from '../../src/database/entities/email-verification.entity';
import { TutorApproval } from '../../src/database/entities/tutor-approval.entity';
import { Diary } from '../../src/database/entities/diary.entity';

export interface AuthTestMocks {
  service: AuthService;
  usersService: jest.Mocked<UsersService>;
  jwtService: jest.Mocked<JwtService>;
  emailService: jest.Mocked<EmailService>;
  profilePictureService: jest.Mocked<ProfilePictureService>;
  userRepository: any;
  emailVerificationRepository: any;
  passwordResetRepository: any;
  tutorApprovalRepository: any;
  dataSource: any;
}

export class AuthTestSetup {
  static createMockRepositories() {
    const mockUserRepository = {
      findOne: jest.fn(),
      find: jest.fn(),
      create: jest.fn(),
      save: jest.fn(),
    };

    const mockEmailVerificationRepository = {
      findOne: jest.fn(),
      save: jest.fn(),
      delete: jest.fn(),
    };

    const mockPasswordResetRepository = {
      findOne: jest.fn(),
      save: jest.fn(),
      delete: jest.fn(),
    };

    const mockTutorApprovalRepository = {
      findOne: jest.fn(),
      save: jest.fn(),
    };

    const mockDataSource = {
      query: jest.fn(),
      createQueryRunner: jest.fn().mockReturnValue({
        connect: jest.fn(),
        startTransaction: jest.fn(),
        commitTransaction: jest.fn(),
        rollbackTransaction: jest.fn(),
        release: jest.fn(),
        manager: {
          getRepository: jest.fn().mockImplementation((entity) => {
            if (entity === User) return mockUserRepository;
            if (entity === Role) return { findOne: jest.fn(), create: jest.fn(), save: jest.fn() };
            return { save: jest.fn(), create: jest.fn(), delete: jest.fn() };
          }),
        },
      }),
    };

    return {
      mockUserRepository,
      mockEmailVerificationRepository,
      mockPasswordResetRepository,
      mockTutorApprovalRepository,
      mockDataSource,
    };
  }

  static async createTestingModule(): Promise<TestingModule> {
    const {
      mockUserRepository,
      mockEmailVerificationRepository,
      mockPasswordResetRepository,
      mockTutorApprovalRepository,
      mockDataSource,
    } = this.createMockRepositories();

    return Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: UsersService,
          useValue: {
            findByUserId: jest.fn(),
            findByEmail: jest.fn(),
            getAllAdminUsers: jest.fn(),
          },
        },
        {
          provide: EmailService,
          useValue: {
            sendVerificationLink: jest.fn(),
            sendPasswordResetLink: jest.fn(),
            sendUserId: jest.fn(),
            sendPasswordChangeNotification: jest.fn(),
          },
        },
        {
          provide: JwtService,
          useValue: {
            sign: jest.fn(),
            decode: jest.fn(),
          },
        },
        {
          provide: ProfilePictureService,
          useValue: {
            hasProfilePicture: jest.fn(),
            getProfilePictureDirectUrl: jest.fn(),
          },
        },
        {
          provide: DataSource,
          useValue: mockDataSource,
        },
        {
          provide: DiaryService,
          useValue: {
            createDiary: jest.fn(),
            getOrCreateDiary: jest.fn(),
          },
        },
        {
          provide: AsyncNotificationHelperService,
          useValue: {
            notifyAsync: jest.fn(),
            notifyManyAsync: jest.fn(),
          },
        },
        {
          provide: DeeplinkService,
          useValue: {
            getLinkHtml: jest.fn(),
          },
        },
        {
          provide: TokenBlacklistService,
          useValue: {
            blacklistToken: jest.fn(),
          },
        },
        {
          provide: EmailTemplateService,
          useValue: {
            generateSubscriptionWelcomeTemplate: jest.fn(),
            generatePaymentConfirmationTemplate: jest.fn(),
          },
        },
        {
          provide: PlansService,
          useValue: {
            subscribeWithFreePayment: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(Role),
          useValue: { findOne: jest.fn(), find: jest.fn(), create: jest.fn(), save: jest.fn() },
        },
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
        {
          provide: getRepositoryToken(UserPlan),
          useValue: { findOne: jest.fn(), find: jest.fn(), create: jest.fn(), save: jest.fn() },
        },
        {
          provide: getRepositoryToken(PasswordReset),
          useValue: mockPasswordResetRepository,
        },
        {
          provide: getRepositoryToken(EmailVerification),
          useValue: mockEmailVerificationRepository,
        },
        {
          provide: getRepositoryToken(TutorApproval),
          useValue: mockTutorApprovalRepository,
        },
        {
          provide: getRepositoryToken(Diary),
          useValue: { findOne: jest.fn(), find: jest.fn(), create: jest.fn(), save: jest.fn() },
        },
      ],
    }).compile();
  }

  static extractMocks(module: TestingModule): AuthTestMocks {
    return {
      service: module.get<AuthService>(AuthService),
      usersService: module.get(UsersService),
      jwtService: module.get(JwtService),
      emailService: module.get(EmailService),
      profilePictureService: module.get(ProfilePictureService),
      userRepository: module.get(getRepositoryToken(User)),
      emailVerificationRepository: module.get(getRepositoryToken(EmailVerification)),
      passwordResetRepository: module.get(getRepositoryToken(PasswordReset)),
      tutorApprovalRepository: module.get(getRepositoryToken(TutorApproval)),
      dataSource: module.get(DataSource),
    };
  }

  static setupCommonMocks(mocks: AuthTestMocks) {
    mocks.jwtService.sign.mockReturnValue('<jwt-token>');
    mocks.jwtService.decode.mockReturnValue({ exp: Math.floor(Date.now() / 1000) + 3600 });
    mocks.profilePictureService.hasProfilePicture.mockResolvedValue(false);
    mocks.dataSource.query.mockResolvedValue([]);
    mocks.emailService.sendVerificationLink.mockResolvedValue(true);
    mocks.emailService.sendPasswordResetLink.mockResolvedValue(true);
    mocks.emailService.sendPasswordChangeNotification.mockResolvedValue(true);
  }
}