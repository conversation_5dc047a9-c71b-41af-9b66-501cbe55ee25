import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddUserTimezone1737975200000 implements MigrationInterface {
  name = 'AddUserTimezone1737975200000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'user',
      new TableColumn({
        name: 'timezone',
        type: 'varchar',
        length: '50',
        default: "'Asia/Seoul'",
        isNullable: false,
      }),
    );

    // Create index for timezone queries
    await queryRunner.query(`CREATE INDEX "idx_user_timezone" ON "user" ("timezone")`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "idx_user_timezone"`);
    await queryRunner.dropColumn('user', 'timezone');
  }
}