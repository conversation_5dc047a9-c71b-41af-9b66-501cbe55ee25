import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveBlockGameSentenceForeignKey1734567890005 implements MigrationInterface {
  name = 'RemoveBlockGameSentenceForeignKey1734567890005';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop the foreign key constraint
    await queryRunner.query(`ALTER TABLE "block_game_sentence" DROP CONSTRAINT IF EXISTS "FK_block_game_sentence_block_game"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Re-add the foreign key constraint (only if needed for rollback)
    await queryRunner.query(`ALTER TABLE "block_game_sentence" ADD CONSTRAINT "FK_block_game_sentence_block_game" FOREIGN KEY ("block_game_id") REFERENCES "block_game"("id") ON DELETE CASCADE`);
  }
}