# QA Modules API Testing Flow

This document outlines the testing flow for both QA and QA Mission Module API endpoints.

## Prerequisites

Before testing the QA APIs:

1. Ensure the HEC backend is running
2. Have valid authentication tokens for students
3. Set up your API testing tool (<PERSON><PERSON> recommended)
4. Ensure test QA assignments and missions are available

## QA Module Testing Flow

### QA Assignment Management

#### Test Case 1: Get Latest Assignment

1. Authenticate with a student token
2. Send a GET request to `/api/qa/latest-assignments/{id}`
3. Verify HTTP status code is 200 OK
4. Verify response contains latest assignment set
5. Verify assignment includes questions and metadata
6. Verify only student's own assignments are returned

#### Test Case 2: Assignment Access Control

1. Test accessing assignment with different student tokens
2. Verify proper authorization checks
3. Test with non-existent assignment ID
4. Verify appropriate error responses

### QA Submission Management

#### Test Case 1: Create Submission Draft

1. Authenticate with a student token
2. Send a POST request to `/api/qa/submissions`:
   ```json
   {
     "assignmentId": "123e4567-e89b-12d3-a456-426614174000",
     "answers": [
       {
         "questionId": "q1",
         "answer": "This is my answer to question 1",
         "confidence": 4
       },
       {
         "questionId": "q2",
         "answer": "This is my answer to question 2",
         "confidence": 3
       }
     ],
     "isDraft": true
   }
   ```
3. Verify HTTP status code is 200 OK
4. Verify submission is created as draft
5. Verify answers are saved correctly

#### Test Case 2: Update Submission Draft

1. Create a submission draft
2. Send another POST request with updated answers
3. Verify existing draft is updated
4. Verify new answers replace old ones
5. Verify draft status is maintained

#### Test Case 3: Submit Final Submission

1. Create and update submission draft
2. Send a PUT request to `/api/qa/submissions/{id}/submit`
3. Verify HTTP status code is 200 OK
4. Verify submission status changes to "SUBMITTED"
5. Verify submission timestamp is recorded
6. Verify tutor assignment notification is triggered

#### Test Case 4: Submission Validation

1. Test submitting without required answers
2. Test submitting with invalid question IDs
3. Test submitting already submitted assignment
4. Test submitting with malformed answer data
5. Verify appropriate validation errors

## QA Mission Module Testing Flow

### QA Mission Discovery

#### Test Case 1: Get Available QA Missions

1. Authenticate with a student token
2. Send a GET request to `/api/student-qa-mission/getMissionList` with pagination:
   ```json
   {
     "page": 1,
     "limit": 10,
     "sortBy": "createdAt",
     "sortDirection": "DESC"
   }
   ```
3. Verify HTTP status code is 200 OK
4. Verify response contains paginated list of QA missions
5. Verify missions include title, description, difficulty
6. Verify only active missions are returned
7. Verify student's progress is included

#### Test Case 2: Mission Filtering and Sorting

1. Test different sorting options (title, difficulty, createdAt)
2. Test pagination with different page sizes
3. Verify sorting works correctly
4. Test edge cases (empty results, large page numbers)

### QA Task Management

#### Test Case 1: Start QA Task

1. Authenticate with a student token
2. Send a POST request to `/api/student-qa-mission/start/task`:
   ```json
   {
     "taskId": "123e4567-e89b-12d3-a456-426614174000"
   }
   ```
3. Verify HTTP status code is 200 OK
4. Verify response contains task submission details
5. Verify task status is set to "STARTED"
6. Verify task is associated with the authenticated student

#### Test Case 2: Task Start Validation

1. Test starting a task that doesn't exist
2. Test starting a task that's already started
3. Test starting a task without proper permissions
4. Verify appropriate error responses for each case

#### Test Case 3: Get Specific QA Task

1. Send a GET request to `/api/student-qa-mission/task/{id}`
2. Verify HTTP status code is 200 OK
3. Verify response contains complete task details
4. Verify questions and answer options are included
5. Test with non-existent task ID and verify 404 response

### QA Task Submission Process

#### Test Case 1: Submit QA Task

1. Start a QA task
2. Send a POST request to `/api/student-qa-mission/submit/task`:
   ```json
   {
     "taskId": "123e4567-e89b-12d3-a456-426614174000",
     "content": "This is my QA submission with detailed answers",
     "wordCount": 75,
     "metaData": {
       "timeSpent": 120,
       "questionsAnswered": 5,
       "confidenceLevel": 4
     }
   }
   ```
3. Verify HTTP status code is 200 OK
4. Verify submission is recorded
5. Verify word count is calculated correctly
6. Verify metadata is stored

#### Test Case 2: Update QA Task Submission

1. Submit a QA task
2. Send a PATCH request to `/api/student-qa-mission/submit/task` with updated content
3. Verify HTTP status code is 200 OK
4. Verify submission is updated with new content
5. Verify word count is recalculated
6. Verify metadata is updated

#### Test Case 3: Auto-Save QA Content

1. Start a QA task
2. Send a POST request to `/api/student-qa-mission/submit/task/update`:
   ```json
   {
     "submissionId": "123e4567-e89b-12d3-a456-426614174000",
     "content": "Auto-saved content during writing...",
     "wordCount": 45
   }
   ```
3. Verify HTTP status code is 200 OK
4. Verify content is auto-saved
5. Verify draft status is maintained
6. Test rapid successive auto-saves

#### Test Case 4: Submission Validation

1. Test submitting without required content
2. Test submitting with invalid task ID
3. Test submitting with negative word count
4. Test submitting with malformed metadata
5. Verify appropriate validation errors

### QA Task Status Management

#### Test Case 1: Get Active QA Task

1. Authenticate with a student token
2. Start a QA task
3. Send a GET request to `/api/student-qa-mission/activeTask`
4. Verify HTTP status code is 200 OK
5. Verify response contains current active task details
6. Verify draft content is included if available

#### Test Case 2: Multiple Active Tasks

1. Start multiple QA tasks
2. Verify only the most recent active task is returned
3. Test task switching behavior
4. Verify proper task state management

## Integration Testing Flow

### Test Case 1: Complete QA Workflow

1. **Assignment Discovery**
   - Get available QA assignments
   - Select appropriate assignment

2. **Answer Process**
   - Create submission draft
   - Update answers multiple times
   - Save draft content

3. **Final Submission**
   - Submit final answers
   - Verify submission confirmation
   - Check assignment completion status

4. **Review Process**
   - Wait for tutor review
   - Check for feedback notifications
   - View reviewed assignment with scores

### Test Case 2: Complete QA Mission Workflow

1. **Mission Discovery**
   - Browse available QA missions
   - Filter by difficulty or topic

2. **Task Execution**
   - Start QA mission task
   - Write comprehensive answers
   - Use auto-save functionality

3. **Submission Process**
   - Submit task for evaluation
   - Update submission if needed
   - Verify final submission

4. **Progress Tracking**
   - Check mission completion status
   - View performance metrics
   - Track improvement over time

### Test Case 3: Cross-Module Integration

1. QA submissions integrate with award system
2. Performance metrics feed into analytics
3. Notifications integrate with notification system
4. Progress tracking integrates with dashboard

## Performance Testing Flow

### Test Case 1: Auto-Save Performance

1. Measure auto-save response times
2. Test with large content submissions
3. Verify performance remains acceptable
4. Test rapid successive auto-saves

### Test Case 2: Mission List Performance

1. Create large number of missions (100+)
2. Test pagination performance
3. Verify search and filter performance
4. Test sorting with large datasets

### Test Case 3: Concurrent Submissions

1. Multiple students submit simultaneously
2. Verify system handles concurrent load
3. Test database locking mechanisms
4. Verify data consistency

## Security Testing Flow

### Test Case 1: Access Control

1. Attempt to access other students' QA submissions
2. Verify proper authorization checks
3. Test task access without proper permissions
4. Verify assignment isolation between students

### Test Case 2: Data Validation

1. Test XSS prevention in QA content
2. Test SQL injection in search parameters
3. Verify input sanitization
4. Test malicious content submission

### Test Case 3: Submission Security

1. Test submission tampering attempts
2. Verify answer integrity checks
3. Test time-based attack prevention
4. Verify secure data transmission

## Error Handling Testing Flow

### Test Case 1: Network Interruption

1. Start QA task session
2. Simulate network interruption during submission
3. Verify graceful error handling
4. Verify content recovery when connection restored

### Test Case 2: Server Errors

1. Simulate server errors during task start
2. Test submission failures
3. Verify appropriate error messages
4. Test retry mechanisms

### Test Case 3: Validation Errors

1. Test various invalid input scenarios
2. Verify comprehensive error messages
3. Test error recovery workflows
4. Verify user guidance for corrections

## Edge Cases Testing Flow

### Test Case 1: Boundary Conditions

1. Test with maximum content length
2. Test with minimum valid answers
3. Test rapid task switching
4. Verify system limits are enforced

### Test Case 2: Timing Scenarios

1. Submit QA exactly at deadline
2. Test auto-save during submission
3. Test task timeout handling
4. Verify timestamp accuracy

### Test Case 3: Data Consistency

1. Test concurrent answer updates
2. Verify submission state consistency
3. Test task state during interruptions
4. Verify data integrity across operations

## Reporting and Analytics Testing

### Test Case 1: Progress Tracking

1. Complete multiple QA tasks
2. Verify progress metrics are accurate
3. Test performance trend calculations
4. Verify completion statistics

### Test Case 2: Score Calculations

1. Submit QA with various answer qualities
2. Verify scoring algorithms work correctly
3. Test score aggregation across tasks
4. Verify ranking calculations

### Test Case 3: Analytics Integration

1. Verify QA data feeds into dashboard
2. Test performance analytics accuracy
3. Verify trend analysis functionality
4. Test comparative performance metrics

This comprehensive testing flow ensures both QA and QA Mission modules function correctly and provide reliable assessment capabilities for students while maintaining data integrity and security.