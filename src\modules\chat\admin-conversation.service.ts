import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, EntityManager, In } from 'typeorm';
import { AdminConversation, AdminConversationStatus } from '../../database/entities/admin-conversation.entity';
import { User, UserType } from '../../database/entities/user.entity';
import { VirtualAdminService } from './virtual-admin.service';

@Injectable()
export class AdminConversationService {
  private readonly logger = new Logger(AdminConversationService.name);

  constructor(
    @InjectRepository(AdminConversation)
    private readonly adminConversationRepository: Repository<AdminConversation>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly dataSource: DataSource,
    private readonly virtualAdminService: VirtualAdminService,
  ) {}

  /**
   * Get or create admin conversation for a user
   */
  async getOrCreateAdminConversation(userId: string): Promise<AdminConversation> {
    return await this.dataSource.transaction(async (manager: EntityManager) => {
      let conversation = await manager.findOne(AdminConversation, {
        where: { userId },
      });

      if (conversation) {
        return conversation;
      }

      const virtualAdminId = await this.virtualAdminService.getVirtualAdminUserId();
      conversation = manager.create(AdminConversation, {
        userId,
        virtualAdminId,
        status: AdminConversationStatus.ACTIVE,
      });

      return await manager.save(conversation);
    });
  }

  /**
   * Get admin conversation by user ID
   */
  async getAdminConversationByUserId(userId: string): Promise<AdminConversation | null> {
    return await this.adminConversationRepository.findOne({
      where: { userId },
      relations: ['user', 'virtualAdmin'],
    });
  }

  /**
   * Get admin conversation by ID
   */
  async getAdminConversation(conversationId: string): Promise<AdminConversation | null> {
    return await this.adminConversationRepository.findOne({
      where: { id: conversationId },
      relations: ['user', 'virtualAdmin'],
    });
  }

  /**
   * Get all admin conversations for admin users
   */
  async getAdminConversations(page: number = 1, limit: number = 10) {
    const skip = (page - 1) * limit;
    
    const [conversations, total] = await this.adminConversationRepository.findAndCount({
      relations: ['user'],
      order: { lastMessageAt: 'DESC', createdAt: 'DESC' },
      skip,
      take: limit,
    });

    return { conversations, total, page, limit };
  }

  /**
   * Get admin conversations by user IDs
   */
  async getAdminConversationsByUserIds(userIds: string[]): Promise<AdminConversation[]> {
    if (userIds.length === 0) return [];
    
    return await this.adminConversationRepository.find({
      where: { userId: In(userIds) },
      relations: ['user'],
    });
  }



  /**
   * Update unread counts
   */
  async updateUnreadCounts(
    conversationId: string,
    userUnreadCount?: number,
    adminUnreadCount?: number
  ): Promise<void> {
    const updateData: any = {};
    if (userUnreadCount !== undefined) updateData.userUnreadCount = userUnreadCount;
    if (adminUnreadCount !== undefined) updateData.adminUnreadCount = adminUnreadCount;

    if (Object.keys(updateData).length > 0) {
      await this.adminConversationRepository.update(conversationId, updateData);
    }
  }

  /**
   * Increment unread count for user or admin
   */
  async incrementUnreadCount(conversationId: string, isForUser: boolean): Promise<void> {
    const field = isForUser ? 'userUnreadCount' : 'adminUnreadCount';
    await this.adminConversationRepository.increment({ id: conversationId }, field, 1);
  }

  /**
   * Reset unread count for user or admin
   */
  async resetUnreadCount(conversationId: string, isForUser: boolean): Promise<void> {
    const updateData = isForUser 
      ? { userUnreadCount: 0 } 
      : { adminUnreadCount: 0 };
    
    await this.adminConversationRepository.update(conversationId, updateData);
  }

  /**
   * Mark messages as read for user
   */
  async markMessagesAsReadForUser(conversationId: string, userId: string): Promise<void> {
    // Reset user unread count
    await this.resetUnreadCount(conversationId, true);
  }

  /**
   * Update last message info
   */
  async updateLastMessage(
    conversationId: string,
    messageText: string,
    messageTime: Date,
    senderId: string
  ): Promise<void> {
    await this.adminConversationRepository.update(conversationId, {
      lastMessageText: messageText,
      lastMessageSenderId: senderId,
      lastMessageAt: messageTime,
    });
  }
}