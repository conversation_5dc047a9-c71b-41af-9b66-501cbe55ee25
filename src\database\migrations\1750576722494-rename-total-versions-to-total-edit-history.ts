import { MigrationInterface, QueryRunner } from 'typeorm';

export class RenameTotalVersionsToTotalEditHistory1750576722494 implements MigrationInterface {
  name = 'RenameTotalVersionsToTotalEditHistory1750576722494';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Rename total_versions column to total_edit_history in diary_entry table
    await queryRunner.query(`
      ALTER TABLE "diary_entry" 
      RENAME COLUMN "total_versions" TO "total_edit_history"
    `);

    // Rename total_versions column to total_edit_history in novel_entry table
    await queryRunner.query(`
      ALTER TABLE "novel_entry" 
      RENAME COLUMN "total_versions" TO "total_edit_history"
    `);

    // Rename total_versions column to total_edit_history in mission_diary_entry table
    await queryRunner.query(`
      ALTER TABLE "mission_diary_entry" 
      RENAME COLUMN "total_versions" TO "total_edit_history"
    `);

    // Update the values to reflect actual history counts
    // For diary entries
    await queryRunner.query(`
      UPDATE "diary_entry" 
      SET "total_edit_history" = (
        SELECT COUNT(*) 
        FROM "diary_entry_history" 
        WHERE "diary_entry_history"."diary_entry_id" = "diary_entry"."id"
      )
    `);

    // For novel entries
    await queryRunner.query(`
      UPDATE "novel_entry" 
      SET "total_edit_history" = (
        SELECT COUNT(*) 
        FROM "novel_entry_history" 
        WHERE "novel_entry_history"."novel_entry_id" = "novel_entry"."id"
      )
    `);

    // For mission diary entries
    await queryRunner.query(`
      UPDATE "mission_diary_entry" 
      SET "total_edit_history" = (
        SELECT COUNT(*) 
        FROM "mission_diary_entry_history" 
        WHERE "mission_diary_entry_history"."mission_entry_id" = "mission_diary_entry"."id"
      )
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Rename back to total_versions in diary_entry table
    await queryRunner.query(`
      ALTER TABLE "diary_entry" 
      RENAME COLUMN "total_edit_history" TO "total_versions"
    `);

    // Rename back to total_versions in novel_entry table
    await queryRunner.query(`
      ALTER TABLE "novel_entry" 
      RENAME COLUMN "total_edit_history" TO "total_versions"
    `);

    // Rename back to total_versions in mission_diary_entry table
    await queryRunner.query(`
      ALTER TABLE "mission_diary_entry" 
      RENAME COLUMN "total_edit_history" TO "total_versions"
    `);
  }
}
