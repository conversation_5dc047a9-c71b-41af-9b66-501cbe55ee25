import { Injectable, BadRequestException, NotFoundException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { TutorBlockGame } from '../../../database/entities/tutor-block-game.entity';
import { BlockGameSentence } from '../../../database/entities/block-game-sentence.entity';
import { User, UserType } from '../../../database/entities/user.entity';

@Injectable()
export class TutorBlockGameService {
  private readonly logger = new Logger(TutorBlockGameService.name);

  constructor(
    @InjectRepository(TutorBlockGame)
    private readonly tutorBlockGameRepository: Repository<TutorBlockGame>,
    @InjectRepository(BlockGameSentence)
    private readonly blockGameSentenceRepository: Repository<BlockGameSentence>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * Get all tutor block games
   */
  async getTutorGames(tutorId: string): Promise<any[]> {
    const games = await this.tutorBlockGameRepository.find({
      where: { tutorId, isActive: true },
      order: { createdAt: 'DESC' },
    });

    return games.map(game => ({
      id: game.id,
      title: game.title,
      student_id: game.studentId,
      module_type: game.moduleType,
      entry_id: game.entryId,
      score: game.score,
      is_active: game.isActive,
      created_at: game.createdAt,
      updated_at: game.updatedAt,
    }));
  }

  /**
   * Get tutor block game with sentences
   */
  async getTutorGameWithSentences(tutorId: string, gameId: string): Promise<any> {
    const game = await this.tutorBlockGameRepository.findOne({
      where: { id: gameId, tutorId, isActive: true },
    });

    if (!game) {
      throw new NotFoundException('Block game not found');
    }

    // Get sentences for the tutor game
    const sentences = await this.blockGameSentenceRepository.find({
      where: { blockGameId: gameId },
      order: { sentenceOrder: 'ASC' },
    });

    return {
      id: game.id,
      title: game.title,
      student_id: game.studentId,
      module_type: game.moduleType,
      entry_id: game.entryId,
      score: game.score,
      is_active: game.isActive,
      sentences: sentences.map(s => ({
        id: s.id,
        starting_part: s.startingPart,
        expanding_part: s.expandingPart,
        starting_part_answers: s.startingPartAnswers,
        starting_part_distractors: s.startingPartDistractors,
        expanding_part_answers: s.expandingPartAnswers,
        expanding_part_distractors: s.expandingPartDistractors,
        sentence_order: s.sentenceOrder,
      })),
      created_at: game.createdAt,
      updated_at: game.updatedAt,
    };
  }

  /**
   * Bulk create block games
   */
  async bulkCreateGames(tutorId: string, games: any[]): Promise<any> {
    if (!games || games.length === 0) {
      throw new BadRequestException('At least one game must be provided');
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const createdGames = [];
      const errors = [];

      for (let i = 0; i < games.length; i++) {
        const gameData = games[i];
        
        try {
          // Validate student exists
          const student = await this.userRepository.findOne({
            where: { id: gameData.student_id, type: UserType.STUDENT },
          });

          if (!student) {
            throw new Error(`Student with ID ${gameData.student_id} not found`);
          }

          // Create tutor block game
          const game = queryRunner.manager.create(TutorBlockGame, {
            tutorId,
            studentId: gameData.student_id,
            title: gameData.title,
            moduleType: gameData.module_type,
            entryId: gameData.entry_id,
            score: gameData.score || 100,
            isActive: true,
          });

          const savedGame = await queryRunner.manager.save(game);

          // Validate and create sentences for the tutor block game
          if (!gameData.sentences || gameData.sentences.length === 0) {
            throw new Error('Game must have at least one sentence');
          }

          for (const sentenceData of gameData.sentences) {
            const sentence = queryRunner.manager.create(BlockGameSentence, {
              blockGameId: savedGame.id,
              startingPart: sentenceData.starting_part,
              expandingPart: sentenceData.expanding_part,
              startingPartAnswers: sentenceData.starting_part_answers || [],
              startingPartDistractors: sentenceData.starting_part_distractors || [],
              expandingPartAnswers: sentenceData.expanding_part_answers || [],
              expandingPartDistractors: sentenceData.expanding_part_distractors || [],
              sentenceOrder: sentenceData.sentence_order,
            });
            await queryRunner.manager.save(sentence);
          }

          createdGames.push({
            id: savedGame.id,
            title: savedGame.title,
            student_id: savedGame.studentId,
            module_type: savedGame.moduleType,
            entry_id: savedGame.entryId,
            score: savedGame.score,
            sentences_count: gameData.sentences?.length || 0,
          });

        } catch (error) {
          errors.push({
            index: i,
            title: gameData.title,
            error: error.message,
          });
        }
      }

      await queryRunner.commitTransaction();

      const result = {
        created_games: createdGames,
        errors,
        success_count: createdGames.length,
        error_count: errors.length,
      };

      // If there are errors, throw an exception to return error response
      if (errors.length > 0) {
        throw new BadRequestException(result);
      }

      return result;

    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Failed to bulk create block games: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to create block games');
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Bulk update block games
   */
  async bulkUpdateGames(tutorId: string, games: any[]): Promise<any> {
    if (!games || games.length === 0) {
      throw new BadRequestException('At least one game must be provided');
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const updatedGames = [];
      const errors = [];

      for (let i = 0; i < games.length; i++) {
        const gameData = games[i];
        
        try {
          const game = await queryRunner.manager.findOne(TutorBlockGame, {
            where: { id: gameData.id, tutorId, isActive: true },
          });

          if (!game) {
            throw new Error(`Block game with ID ${gameData.id} not found`);
          }

          // Update game metadata
          if (gameData.title !== undefined) game.title = gameData.title;
          if (gameData.score !== undefined) game.score = gameData.score;

          const savedGame = await queryRunner.manager.save(game);

          // TODO: Update sentences when TutorBlockGameSentence entity is implemented

          updatedGames.push({
            id: savedGame.id,
            title: savedGame.title,
            score: savedGame.score,
            updated_sentences: gameData.sentences?.length || 0,
          });

        } catch (error) {
          errors.push({
            index: i,
            id: gameData.id,
            error: error.message,
          });
        }
      }

      await queryRunner.commitTransaction();

      return {
        updated_games: updatedGames,
        errors,
        success_count: updatedGames.length,
        error_count: errors.length,
      };

    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Failed to bulk update block games: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to update block games');
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Bulk delete (soft delete) block games
   */
  async bulkDeleteGames(tutorId: string, gameIds: string[]): Promise<void> {
    if (!gameIds || gameIds.length === 0) {
      throw new BadRequestException('At least one game ID must be provided');
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      await queryRunner.manager.update(
        TutorBlockGame,
        { id: { $in: gameIds } as any, tutorId },
        { isActive: false }
      );

      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Failed to bulk delete block games: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to delete block games');
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Add sentence to block game
   */
  async addSentence(tutorId: string, gameId: string, sentenceData: any): Promise<any> {
    const game = await this.tutorBlockGameRepository.findOne({
      where: { id: gameId, tutorId, isActive: true },
    });

    if (!game) {
      throw new NotFoundException('Block game not found');
    }

    // TODO: Implement sentence creation when TutorBlockGameSentence entity is created
    // For now, return a placeholder response

    return {
      message: 'Sentence functionality will be implemented when TutorBlockGameSentence entity is created',
      game_id: gameId,
      sentence_data: sentenceData,
    };
  }
}