import { Injectable, Logger, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In, Not, Equal, DataSource, EntityManager } from 'typeorm';
import { Conversation, ConversationStatus, ConversationType } from '../../database/entities/conversation.entity';
import { Message, MessageStatus, MessageType } from '../../database/entities/message.entity';
import { MessageAttachment } from '../../database/entities/message-attachment.entity';
import { MessageRegistry } from '../../database/entities/message-registry.entity';
import { User, UserType } from '../../database/entities/user.entity';
import { StudentTutorMapping, MappingStatus } from '../../database/entities/student-tutor-mapping.entity';
import { StudentFriendship, FriendshipStatus } from '../../database/entities/student-friendship.entity';
import { AsyncNotificationHelperService } from '../notification/async-notification-helper.service';
import { NotificationType } from '../../database/entities/notification.entity';
import { FileRegistryService } from '../../common/services/file-registry.service';
import { FileEntityType } from '../../common/enums/file-entity-type.enum';
import { DeeplinkService } from '../../common/utils/deeplink.service';
import { RelatedEntityType } from '../../common/enums/related-entity-type.enum';
import {
  ConversationDto,
  MessageDto,
  CreateMessageDto,
  ConversationFilterDto,
  MessageFilterDto,
  PagedConversationListDto,
  PagedMessageListDto,
  ConversationParticipantDto,
  MessageAttachmentDto,
  ChatFileUploadResponseDto,
  ContactFilterDto,
} from '../../database/models/chat.dto';
import { getCurrentUTCDate } from '../../common/utils/date-utils';
import { ConfigService } from '@nestjs/config';
import { MulterFile } from '../../common/interfaces/multer-file.interface';
import { VirtualAdminService } from './virtual-admin.service';
import { AdminConversationService } from './admin-conversation.service';
import { ChatValidationUtil } from './chat-validation.util';

@Injectable()
export class ChatService {
  private readonly logger = new Logger(ChatService.name);

  constructor(
    @InjectRepository(Conversation)
    private readonly conversationRepository: Repository<Conversation>,
    @InjectRepository(Message)
    private readonly messageRepository: Repository<Message>,
    @InjectRepository(MessageAttachment)
    private readonly messageAttachmentRepository: Repository<MessageAttachment>,
    @InjectRepository(MessageRegistry)
    private readonly messageRegistryRepository: Repository<MessageRegistry>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(StudentTutorMapping)
    private readonly studentTutorMappingRepository: Repository<StudentTutorMapping>,
    @InjectRepository(StudentFriendship)
    private readonly studentFriendshipRepository: Repository<StudentFriendship>,
    private readonly dataSource: DataSource,
    private readonly virtualAdminService: VirtualAdminService,
    private readonly adminConversationService: AdminConversationService,
    private readonly asyncNotificationHelper: AsyncNotificationHelperService,
    private readonly fileRegistryService: FileRegistryService,
    private readonly deeplinkService: DeeplinkService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Check if two users can chat with each other
   */
  async canUsersChat(userId1: string, userId2: string): Promise<boolean> {
    try {
      const [user1, user2] = await Promise.all([
        this.userRepository.findOne({ where: { id: userId1 } }),
        this.userRepository.findOne({ where: { id: userId2 } })
      ]);

      if (!user1 || !user2) return false;

      // Admin can chat with anyone
      if (user1.type === UserType.ADMIN || user2.type === UserType.ADMIN) {
        return true;
      }

      // Student-Tutor: Check if they are mapped
      if ((user1.type === UserType.STUDENT && user2.type === UserType.TUTOR) || 
          (user1.type === UserType.TUTOR && user2.type === UserType.STUDENT)) {
        const studentId = user1.type === UserType.STUDENT ? user1.id : user2.id;
        const tutorId = user1.type === UserType.TUTOR ? user1.id : user2.id;

        const mapping = await this.studentTutorMappingRepository.findOne({
          where: { studentId, tutorId, status: MappingStatus.ACTIVE },
        });

        return !!mapping;
      }

      // Student-Student: Check if they are friends
      if (user1.type === UserType.STUDENT && user2.type === UserType.STUDENT) {
        const friendship = await this.studentFriendshipRepository.findOne({
          where: [
            { requesterId: userId1, requestedId: userId2, status: FriendshipStatus.ACCEPTED },
            { requesterId: userId2, requestedId: userId1, status: FriendshipStatus.ACCEPTED },
          ],
        });

        return !!friendship;
      }

      return false;
    } catch (error) {
      this.logger.error(`Error checking if users can chat: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Get or create a conversation between two users
   */
  async getOrCreateConversation(userId1: string, userId2: string): Promise<Conversation> {
    try {
      const canChat = await this.canUsersChat(userId1, userId2);
      if (!canChat) {
        throw new ForbiddenException('Users cannot chat with each other');
      }

      // Try to find existing conversation
      let conversation = await this.conversationRepository.findOne({
        where: [
          { participant1Id: userId1, participant2Id: userId2 },
          { participant1Id: userId2, participant2Id: userId1 },
        ],
      });

      if (!conversation) {
        // Create new conversation
        conversation = this.conversationRepository.create({
          participant1Id: userId1,
          participant2Id: userId2,
          participant1UnreadCount: 0,
          participant2UnreadCount: 0,
        });
        conversation = await this.conversationRepository.save(conversation);
        this.logger.log(`Created new conversation ${conversation.id} between users ${userId1} and ${userId2}`);
      }

      return conversation;
    } catch (error) {
      this.logger.error(`Error getting/creating conversation between ${userId1} and ${userId2}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get available chat contacts for a user
   */
  async getChatContacts(userId: string, filter?: ContactFilterDto): Promise<ConversationParticipantDto[]> {
    try {
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      const contactsMap = new Map<string, User>();

      // Get contacts based on user type
      if (user.type === UserType.ADMIN) {
        // Admin can only chat with tutors and students, not other admins
        const tutorsAndStudents = await this.userRepository.find({
          where: {
            id: Not(Equal(userId)),
            type: In([UserType.TUTOR, UserType.STUDENT]),
          },
        });
        tutorsAndStudents.forEach(contact => contactsMap.set(contact.id, contact));
      } else if (user.type === UserType.STUDENT) {
        // Get assigned tutors
        const mappings = await this.studentTutorMappingRepository.find({
          where: { studentId: userId, status: MappingStatus.ACTIVE },
          relations: ['tutor'],
        });
        mappings.forEach(mapping => {
          if (mapping.tutor) {
            contactsMap.set(mapping.tutor.id, mapping.tutor);
          }
        });

        // Get student friends
        const friendships = await this.studentFriendshipRepository.find({
          where: [
            { requesterId: userId, status: FriendshipStatus.ACCEPTED },
            { requestedId: userId, status: FriendshipStatus.ACCEPTED },
          ],
          relations: ['requester', 'requested'],
        });
        friendships.forEach(friendship => {
          const friendId = friendship.requesterId === userId ? friendship.requestedId : friendship.requesterId;
          const friend = friendship.requesterId === userId ? friendship.requested : friendship.requester;
          if (friend && !contactsMap.has(friendId)) {
            contactsMap.set(friendId, friend);
          }
        });
      } else if (user.type === UserType.TUTOR) {
        // Get assigned students
        const mappings = await this.studentTutorMappingRepository.find({
          where: { tutorId: userId, status: MappingStatus.ACTIVE },
          relations: ['student'],
        });
        mappings.forEach(mapping => {
          if (mapping.student) {
            contactsMap.set(mapping.student.id, mapping.student);
          }
        });
      }

      // Convert map to array
      let contacts = Array.from(contactsMap.values());

      // Apply filters if provided
      if (filter) {
        if (filter.name) {
          contacts = contacts.filter((contact) => contact.name.toLowerCase().includes(filter.name.toLowerCase()));
        }
        if (filter.email) {
          contacts = contacts.filter((contact) => contact.email.toLowerCase().includes(filter.email.toLowerCase()));
        }
        if (filter.phone) {
          contacts = contacts.filter((contact) => contact.phoneNumber && contact.phoneNumber.includes(filter.phone));
        }
      }

      // Get virtual admin user ID
      const virtualAdmin = await this.virtualAdminService.getVirtualAdmin();
      const virtualAdminUserId = virtualAdmin.userId;

      // Add virtual admin to contacts for non-admin users
      if (user.type !== UserType.ADMIN) {
        contacts.push({
          id: virtualAdminUserId,
          name: virtualAdmin.displayName,
          userId: virtualAdmin.userId,
          email: '<EMAIL>',
          type: UserType.ADMIN,
          profilePicture: null,
        } as User);
      }

      // Pre-fetch conversations based on user type
      type ConversationMapEntry = Conversation | {
        id: string;
        lastMessageText?: string;
        lastMessageAt?: Date;
        adminUnreadCount?: number;
        userUnreadCount?: number;
        isAdminConversation: true;
      };
      const conversationMap = new Map<string, ConversationMapEntry>();
      const contactIds = contacts.map(c => c.id).filter(id => id !== virtualAdminUserId);
      
      if (user.type === UserType.ADMIN && contactIds.length > 0) {
        // For admin users, fetch admin conversations
        const adminConversations = await this.adminConversationService.getAdminConversationsByUserIds(contactIds);
        adminConversations.forEach(conv => {
          const adminConvEntry: ConversationMapEntry = {
            id: `admin-${conv.id}`,
            lastMessageText: conv.lastMessageText,
            lastMessageAt: conv.lastMessageAt,
            adminUnreadCount: conv.adminUnreadCount,
            userUnreadCount: conv.userUnreadCount,
            isAdminConversation: true,
          };
          conversationMap.set(conv.userId, adminConvEntry);
        });
      } else if (contactIds.length > 0) {
        // For regular users, fetch regular conversations
        const existingConversations = await this.conversationRepository.find({
          where: [
            { participant1Id: userId, participant2Id: In(contactIds) },
            { participant1Id: In(contactIds), participant2Id: userId },
          ],
        });
        
        existingConversations.forEach(conv => {
          const otherParticipantId = conv.participant1Id === userId ? conv.participant2Id : conv.participant1Id;
          conversationMap.set(otherParticipantId, conv);
        });
      }

      // Process contacts with optimized conversation lookup
      const contactDtos = await Promise.all(
        contacts.map(async (contact) => {
          try {
            let conversation: ConversationMapEntry | null = null;

            // Handle virtual admin contact specially
            if (contact.id === virtualAdminUserId) {
              // Get or create admin conversation for this user
              const adminConversation = await this.adminConversationService.getOrCreateAdminConversation(userId);
              
              // Create a temporary conversation-like object for compatibility
              conversation = {
                id: `admin-${adminConversation.id}`,
                lastMessageText: adminConversation.lastMessageText,
                lastMessageAt: adminConversation.lastMessageAt,
                participant1UnreadCount: adminConversation.userUnreadCount,
                participant2UnreadCount: adminConversation.adminUnreadCount,
              } as any;
            } else {
              // Use pre-fetched conversation from map
              conversation = conversationMap.get(contact.id) || null;

              // If no conversation exists and user is admin, create admin conversation
              if (!conversation && user.type === UserType.ADMIN) {
                try {
                  const adminConversation = await this.adminConversationService.getOrCreateAdminConversation(contact.id);
                  const adminConvEntry: ConversationMapEntry = {
                    id: `admin-${adminConversation.id}`,
                    lastMessageText: adminConversation.lastMessageText,
                    lastMessageAt: adminConversation.lastMessageAt,
                    adminUnreadCount: adminConversation.adminUnreadCount,
                    userUnreadCount: adminConversation.userUnreadCount,
                    isAdminConversation: true,
                  };
                  conversation = adminConvEntry;
                } catch (error) {
                  this.logger.error(`Error creating admin conversation for user ${contact.id}: ${error.message}`);
                  conversation = null;
                }
              } else if (!conversation && user.type !== UserType.ADMIN) {
                // For regular users, try to create regular conversation
                const canChat = await this.canUsersChat(userId, contact.id);
                if (canChat) {
                  try {
                    conversation = await this.getOrCreateConversation(userId, contact.id);
                    this.logger.log(ChatValidationUtil.sanitizeLogMessage(`Created/found conversation ${conversation.id} between users ${userId} and ${contact.id}`));
                  } catch (error) {
                    this.logger.error(ChatValidationUtil.sanitizeLogMessage(`Error creating conversation between ${userId} and ${contact.id}: ${error.message}`));
                    conversation = null;
                  }
                } else {
                  this.logger.warn(ChatValidationUtil.sanitizeLogMessage(`Users ${userId} and ${contact.id} cannot chat with each other`));
                  return null;
                }
              }
            }

            // Get last message and time if conversation exists
            let lastMessage = null;
            let lastMessageTime = null;

            if (conversation) {
              lastMessage = conversation.lastMessageText;
              lastMessageTime = conversation.lastMessageAt;

              // If there's no last message in the conversation entity, try to fetch the latest message
              if (!lastMessage && conversation.id) {
                try {
                  let latestMessage;
                  
                  if (conversation.id.startsWith('admin-')) {
                    // For admin conversations, use adminConversationId
                    const adminConversationId = conversation.id.replace('admin-', '');
                    latestMessage = await this.messageRepository.findOne({
                      where: { adminConversationId },
                      order: { createdAt: 'DESC' },
                    });
                  } else {
                    // For regular conversations, use conversationId
                    latestMessage = await this.messageRepository.findOne({
                      where: { conversationId: conversation.id },
                      order: { createdAt: 'DESC' },
                    });
                  }

                  if (latestMessage) {
                    lastMessage = latestMessage.content;
                    lastMessageTime = latestMessage.createdAt;
                  }
                } catch (error) {
                  this.logger.error(`Error fetching latest message for conversation ${conversation.id}: ${error.message}`, error.stack);
                }
              }
            }

            // Calculate unread count based on conversation type and user role
            let unreadCount = 0;
            if (conversation) {
              if (contact.id === virtualAdminUserId) {
                // For virtual admin contact, show user's unread count
                const regularConv = conversation as Conversation;
                unreadCount = regularConv.participant1UnreadCount || 0;
              } else if (user.type === UserType.ADMIN && ('isAdminConversation' in conversation || conversation.id?.startsWith('admin-'))) {
                // For admin users viewing admin conversations, show admin's unread count
                const adminConv = conversation as { adminUnreadCount?: number };
                unreadCount = adminConv.adminUnreadCount || 0;
              } else if ('participant1Id' in conversation) {
                // For regular conversations, get unread count for current user
                unreadCount = conversation.participant1Id === userId 
                  ? conversation.participant1UnreadCount 
                  : conversation.participant2UnreadCount;
              }
            }

            return {
              id: contact.id,
              name: contact.name,
              userId: contact.userId,
              email: contact.email,
              type: contact.type,
              profilePicture: contact.profilePicture,
              conversationId: conversation?.id,
              lastMessage: lastMessage,
              lastMessageTime: lastMessageTime,
              unreadCount: unreadCount,
              isOnline: false,
            };
          } catch (error) {
            this.logger.error(`Error processing contact ${contact.id}: ${error.message}`, error.stack);
            return null;
          }
        }),
      );

      // Filter out null values (contacts that couldn't get a conversation)
      return contactDtos.filter((dto) => dto !== null);
    } catch (error) {
      this.logger.error(`Error getting chat contacts: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Send a message
   */
  async sendMessage(senderId: string, createMessageDto: CreateMessageDto): Promise<MessageDto> {
    try {
      const { recipientId, conversationId, type = MessageType.TEXT, content, metadata, attachmentIds } = createMessageDto;

      // Handle virtual admin messages specially
      const virtualAdminUserId = await this.virtualAdminService.getVirtualAdminUserId();
      if (recipientId === virtualAdminUserId || (conversationId && conversationId.startsWith('admin-'))) {
        return await this.sendMessageToVirtualAdmin(senderId, createMessageDto);
      }

      // Check if users can chat
      const canChat = await this.canUsersChat(senderId, recipientId);
      if (!canChat) {
        throw new ForbiddenException('Users cannot chat with each other');
      }

      // Get or create conversation
      const conversation = await this.getOrCreateConversation(senderId, recipientId);

      // Create message
      const message = this.messageRepository.create({
        conversationId: conversation.id,
        senderId,
        recipientId,
        type,
        content,
        metadata,
        status: MessageStatus.SENT,
      });

      const savedMessage = await this.messageRepository.save(message);

      // Process attachments if any
      if (attachmentIds && attachmentIds.length > 0) {
        await this.processMessageAttachments(savedMessage.id, attachmentIds);
      }

      // Update conversation with last message info
      conversation.lastMessageAt = savedMessage.createdAt;
      conversation.lastMessageText = content;
      conversation.lastMessageSenderId = senderId;

      // Update unread counts
      if (conversation.participant1Id === recipientId) {
        conversation.participant1UnreadCount += 1;
      } else {
        conversation.participant2UnreadCount += 1;
      }

      await this.conversationRepository.save(conversation);

      // Get message with attachments
      const messageWithAttachments = await this.messageRepository.findOne({
        where: { id: savedMessage.id },
        relations: ['sender', 'attachments'],
      });

      // Send notification to recipient
      await this.sendMessageNotification(messageWithAttachments);

      return await this.mapMessageToDto(messageWithAttachments, false);
    } catch (error) {
      this.logger.error(`Error sending message: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Send message to virtual admin (admin conversation)
   */
  private async sendMessageToVirtualAdmin(senderId: string, createMessageDto: CreateMessageDto): Promise<MessageDto> {
    try {
      // Get or create admin conversation
      const adminConversation = await this.adminConversationService.getOrCreateAdminConversation(senderId);
      
      // Get virtual admin user ID
      const virtualAdminUserId = await this.virtualAdminService.getVirtualAdminUserId();
      
      // Create message in admin conversation
      const message = this.messageRepository.create({
        adminConversationId: adminConversation.id,
        senderId,
        recipientId: virtualAdminUserId,
        type: createMessageDto.type || MessageType.TEXT,
        content: createMessageDto.content,
        metadata: createMessageDto.metadata,
        status: MessageStatus.SENT,
      });

      const savedMessage = await this.messageRepository.save(message);

      // Process attachments if any
      if (createMessageDto.attachmentIds && createMessageDto.attachmentIds.length > 0) {
        await this.processMessageAttachments(savedMessage.id, createMessageDto.attachmentIds);
      }

      // Update admin conversation
      await this.adminConversationService.updateLastMessage(
        adminConversation.id,
        savedMessage.content,
        savedMessage.createdAt,
        savedMessage.senderId
      );

      // Get message with attachments
      const messageWithAttachments = await this.messageRepository.findOne({
        where: { id: savedMessage.id },
        relations: ['sender', 'attachments'],
      });

      const result = messageWithAttachments;
      
      // Set the conversation ID to the admin conversation format for consistency
      const messageDto = await this.mapMessageToDto(result, true);
      messageDto.conversationId = `admin-${adminConversation.id}`;
      
      return messageDto;
    } catch (error) {
      this.logger.error(`Error sending message to virtual admin: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get messages for a conversation
   */
  async getMessages(conversationId: string, userId: string, filter: MessageFilterDto): Promise<PagedMessageListDto> {
    try {
      // Check if this is an admin conversation ID
      if (conversationId.startsWith('admin-')) {
        const adminConversationId = conversationId.replace('admin-', '');
        
        // Get messages from admin conversation
        const page = filter.page || 1;
        const limit = filter.limit || 20;
        const skip = (page - 1) * limit;

        const queryBuilder = this.messageRepository
          .createQueryBuilder('message')
          .leftJoinAndSelect('message.sender', 'sender')
          .leftJoinAndSelect('message.attachments', 'attachments')
          .where('message.adminConversationId = :adminConversationId', { adminConversationId })
          .orderBy('message.createdAt', 'DESC');

        const total = await queryBuilder.getCount();
        const messages = await queryBuilder.skip(skip).take(limit).getMany();

        // Mark messages as read
        await this.adminConversationService.markMessagesAsReadForUser(adminConversationId, userId);

        const messageDtos = await Promise.all(
          messages.map(message => this.mapMessageToDto(message, true))
        );

        return {
          items: messageDtos,
          total,
          page,
          limit,
        };
      }

      // Handle regular conversation
      const conversation = await this.conversationRepository.findOne({
        where: { id: conversationId },
      });

      if (!conversation) {
        return {
          items: [],
          total: 0,
          page: filter.page || 1,
          limit: filter.limit || 20,
        };
      }

      // Check if user is a participant
      if (conversation.participant1Id !== userId && conversation.participant2Id !== userId) {
        throw new ForbiddenException('User is not a participant in this conversation');
      }

      const page = filter.page || 1;
      const limit = filter.limit || 20;
      const skip = (page - 1) * limit;

      // Build query
      const queryBuilder = this.messageRepository
        .createQueryBuilder('message')
        .leftJoinAndSelect('message.sender', 'sender')
        .leftJoinAndSelect('message.attachments', 'attachments')
        .where('message.conversationId = :conversationId', { conversationId })
        .orderBy('message.createdAt', 'DESC');

      // Apply filters
      if (filter.type) {
        queryBuilder.andWhere('message.type = :type', { type: filter.type });
      }

      if (filter.status) {
        queryBuilder.andWhere('message.status = :status', { status: filter.status });
      }

      if (filter.search) {
        queryBuilder.andWhere('message.content ILIKE :search', { search: `%${filter.search}%` });
      }

      const total = await queryBuilder.getCount();
      const messages = await queryBuilder.skip(skip).take(limit).getMany();

      // Mark messages as read
      await this.markMessagesAsRead(conversationId, userId);

      const messageDtos = await Promise.all(
        messages.map(message => this.mapMessageToDto(message, false))
      );

      return {
        items: messageDtos,
        total,
        page,
        limit,
      };
    } catch (error) {
      this.logger.error(`Error getting messages: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Mark messages as read
   */
  async markMessagesAsRead(conversationId: string, userId: string): Promise<void> {
    try {
      // Check if this is an admin conversation ID
      if (conversationId.startsWith('admin-')) {
        const adminConversationId = conversationId.replace('admin-', '');
        await this.adminConversationService.markMessagesAsReadForUser(adminConversationId, userId);
        return;
      }

      // Handle regular conversation
      const conversation = await this.conversationRepository.findOne({
        where: { id: conversationId },
      });

      if (!conversation) {
        throw new NotFoundException('Conversation not found');
      }

      // Check if user is a participant
      if (conversation.participant1Id !== userId && conversation.participant2Id !== userId) {
        throw new ForbiddenException('User is not a participant in this conversation');
      }

      // Update unread messages
      await this.messageRepository.update(
        {
          conversationId,
          recipientId: userId,
          status: MessageStatus.SENT,
        },
        {
          status: MessageStatus.READ,
          readAt: getCurrentUTCDate(),
        },
      );

      // Reset unread count for the user
      if (conversation.participant1Id === userId) {
        conversation.participant1UnreadCount = 0;
      } else {
        conversation.participant2UnreadCount = 0;
      }

      await this.conversationRepository.save(conversation);
    } catch (error) {
      this.logger.error(`Error marking messages as read: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Upload a file for a message
   */
  async uploadFile(userId: string, file: MulterFile): Promise<ChatFileUploadResponseDto> {
    try {
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      const result = await this.fileRegistryService.uploadFile(
        FileEntityType.MESSAGE_ATTACHMENT, 
        file, 
        userId, 
        { userId, isTemporary: true }
      );

      const fileUrl = await this.fileRegistryService.getFileUrl(FileEntityType.MESSAGE_ATTACHMENT, result.registry.id);

      return {
        id: result.registry.id,
        filePath: fileUrl,
        fileName: result.registry.fileName,
        mimeType: result.registry.mimeType,
        fileSize: result.registry.fileSize,
        fileUrl,
      };
    } catch (error) {
      this.logger.error(`Error uploading file: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Process message attachments
   */
  private async processMessageAttachments(messageId: string, attachmentIds: string[]): Promise<void> {
    try {
      const registryEntries = await this.messageRegistryRepository.find({
        where: { id: In(attachmentIds), isTemporary: true },
      });

      if (registryEntries.length === 0) {
        return;
      }

      const attachments = registryEntries.map((registry) => {
        return this.messageAttachmentRepository.create({
          messageId,
          filePath: registry.filePath,
          fileName: registry.fileName,
          mimeType: registry.mimeType,
          fileSize: registry.fileSize,
          thumbnailPath: registry.thumbnailPath,
        });
      });

      await this.messageAttachmentRepository.save(attachments);

      await Promise.all(
        registryEntries.map(async (registry) => {
          registry.isTemporary = false;
          registry.messageId = messageId;
          return this.messageRegistryRepository.save(registry);
        }),
      );
    } catch (error) {
      this.logger.error(`Error processing message attachments: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Send notification for a new message
   */
  private async sendMessageNotification(message: Message): Promise<void> {
    try {
      const sender = message.sender || (await this.userRepository.findOne({ where: { id: message.senderId } }));
      if (!sender) {
        return;
      }

      const notificationTitle = `New message from ${sender.name}`;
      let notificationContent = message.content;

      if (message.type === MessageType.IMAGE) {
        notificationContent = 'Sent you an image';
      } else if (message.type === MessageType.FILE) {
        notificationContent = 'Sent you a file';
      }

      if (notificationContent.length > 100) {
        notificationContent = notificationContent.substring(0, 97) + '...';
      }

      await this.asyncNotificationHelper.notifyAsync(
        message.recipientId, 
        NotificationType.CHAT_MESSAGE, 
        notificationTitle, 
        notificationContent, 
        {
          relatedEntityId: message.conversationId || message.adminConversationId,
          relatedEntityType: RelatedEntityType.CONVERSATION,
          sendEmail: false,
          sendPush: true,
          sendInApp: true,
          sendRealtime: true,
        }
      );
    } catch (error) {
      this.logger.error(`Error sending message notification: ${error.message}`, error.stack);
    }
  }

  /**
   * Get conversations for a user (backward compatibility)
   */
  async getConversations(userId: string, filter: ConversationFilterDto): Promise<PagedConversationListDto> {
    // For now, return empty list as conversations are handled differently
    return {
      items: [],
      total: 0,
      page: filter.page || 1,
      limit: filter.limit || 10,
    };
  }

  /**
   * Get a conversation by ID (backward compatibility)
   */
  async getConversation(conversationId: string, userId: string): Promise<ConversationDto> {
    // Handle admin conversations
    if (conversationId.startsWith('admin-')) {
      const adminConversationId = conversationId.replace('admin-', '');
      const adminConversation = await this.adminConversationService.getAdminConversation(adminConversationId);
      
      if (!adminConversation) {
        throw new NotFoundException('Admin conversation not found');
      }

      // Check if user is the participant in admin conversation or is an admin
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Allow access if user is the conversation owner OR if user is an admin
      if (adminConversation.userId !== userId && user.type !== UserType.ADMIN) {
        throw new ForbiddenException('User is not a participant in this admin conversation');
      }

      // Return a conversation-like DTO for admin conversation
      const virtualAdmin = await this.virtualAdminService.getVirtualAdmin();
      return {
        id: conversationId, // Keep the admin- prefix for client consistency
        type: ConversationType.DIRECT,
        status: ConversationStatus.ACTIVE,
        participant: {
          id: virtualAdmin.userId,
          name: virtualAdmin.displayName,
          userId: virtualAdmin.userId,
          email: '<EMAIL>',
          type: UserType.ADMIN,
          profilePicture: null,
        },
        lastMessageAt: adminConversation.lastMessageAt,
        lastMessageText: adminConversation.lastMessageText,
        unreadCount: user.type === UserType.ADMIN ? (adminConversation.adminUnreadCount || 0) : (adminConversation.userUnreadCount || 0),
        createdAt: adminConversation.createdAt,
        isAdminConversation: true,
      } as ConversationDto;
    }

    // Handle regular conversations
    const conversation = await this.conversationRepository.findOne({
      where: { id: conversationId },
      relations: ['participant1', 'participant2'],
    });

    if (!conversation) {
      throw new NotFoundException('Conversation not found');
    }

    // Check if user is a participant
    if (conversation.participant1Id !== userId && conversation.participant2Id !== userId) {
      throw new ForbiddenException('User is not a participant in this conversation');
    }

    return this.mapConversationToDto(conversation, userId);
  }

  /**
   * Get file by ID (backward compatibility)
   */
  async getFile(fileId: string): Promise<{ buffer: Buffer; fileName: string; mimeType: string }> {
    return await this.fileRegistryService.getFileBuffer(FileEntityType.MESSAGE_ATTACHMENT, fileId);
  }

  /**
   * Mark messages as delivered (backward compatibility)
   */
  async markMessagesAsDelivered(conversationId: string, userId: string): Promise<void> {
    // Handle admin conversations
    if (conversationId.startsWith('admin-')) {
      const adminConversationId = conversationId.replace('admin-', '');
      
      // Validate admin conversation access
      const adminConversation = await this.adminConversationService.getAdminConversation(adminConversationId);
      if (!adminConversation) {
        throw new NotFoundException('Admin conversation not found');
      }

      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Allow access if user is the conversation owner OR if user is an admin
      if (adminConversation.userId !== userId && user.type !== UserType.ADMIN) {
        throw new ForbiddenException('User is not a participant in this admin conversation');
      }
      
      // Update sent messages to delivered for admin conversation
      await this.messageRepository.update(
        {
          adminConversationId,
          recipientId: userId,
          status: MessageStatus.SENT,
        },
        {
          status: MessageStatus.DELIVERED,
          deliveredAt: getCurrentUTCDate(),
        },
      );
      return;
    }

    // Handle regular conversations
    const conversation = await this.conversationRepository.findOne({
      where: { id: conversationId },
    });

    if (!conversation) {
      throw new NotFoundException('Conversation not found');
    }

    // Check if user is a participant
    if (conversation.participant1Id !== userId && conversation.participant2Id !== userId) {
      throw new ForbiddenException('User is not a participant in this conversation');
    }

    // Update sent messages to delivered
    await this.messageRepository.update(
      {
        conversationId,
        recipientId: userId,
        status: MessageStatus.SENT,
      },
      {
        status: MessageStatus.DELIVERED,
        deliveredAt: getCurrentUTCDate(),
      },
    );
  }

  /**
   * Get user by ID (helper method)
   */
  async getUserById(userId: string): Promise<User | null> {
    return await this.userRepository.findOne({ where: { id: userId } });
  }

  /**
   * Map conversation entity to DTO
   */
  private async mapConversationToDto(conversation: Conversation, currentUserId: string): Promise<ConversationDto> {
    const otherParticipantId = conversation.participant1Id === currentUserId 
      ? conversation.participant2Id 
      : conversation.participant1Id;
    
    const otherParticipant = await this.userRepository.findOne({ where: { id: otherParticipantId } });
    if (!otherParticipant) {
      throw new NotFoundException('Other participant not found');
    }

    const unreadCount = conversation.participant1Id === currentUserId 
      ? conversation.participant1UnreadCount 
      : conversation.participant2UnreadCount;

    return {
      id: conversation.id,
      type: ConversationType.DIRECT,
      status: ConversationStatus.ACTIVE,
      participant: {
        id: otherParticipant.id,
        name: otherParticipant.name,
        userId: otherParticipant.userId,
        email: otherParticipant.email,
        type: otherParticipant.type,
        profilePicture: otherParticipant.profilePicture,
      },
      lastMessageAt: conversation.lastMessageAt,
      lastMessageText: conversation.lastMessageText,
      unreadCount: unreadCount || 0,
      createdAt: conversation.createdAt,
    };
  }

  /**
   * Map message entity to DTO
   */
  private async mapMessageToDto(message: Message, isAdminMessage: boolean): Promise<MessageDto> {
    const sender = message.sender;
    let senderProfilePicture: string | null = null;
    
    // Check if sender is virtual admin
    const virtualAdminUserId = await this.virtualAdminService.getVirtualAdminUserId();
    const isSenderVirtualAdmin = message.senderId === virtualAdminUserId;

    if (sender?.id) {
      senderProfilePicture = this.deeplinkService.getProfilePictureUrl(sender.id);
    }

    // Process attachments with proper file URLs
    const attachmentDtos: MessageAttachmentDto[] = [];
    if (message.attachments && message.attachments.length > 0) {
      // Get MessageRegistry entries for this message to map attachments to registry IDs
      const messageRegistries = await this.messageRegistryRepository.find({
        where: { messageId: message.id, isTemporary: false },
      });

      for (const attachment of message.attachments) {
        try {
          // Find the corresponding MessageRegistry entry by matching file path
          const registry = messageRegistries.find(r => r.filePath === attachment.filePath);
          const registryId = registry?.id;
          
          let fileUrl = attachment.filePath; // fallback
          let thumbnailUrl = attachment.thumbnailPath; // fallback
          
          if (registryId) {
            // Get proper file URL from file registry service using registry ID
            fileUrl = await this.fileRegistryService.getFileUrl(FileEntityType.MESSAGE_ATTACHMENT, registryId);
            if (attachment.thumbnailPath) {
              thumbnailUrl = await this.fileRegistryService.getFileUrl(FileEntityType.MESSAGE_ATTACHMENT, registryId);
            }
          }

          attachmentDtos.push({
            id: attachment.id,
            filePath: attachment.filePath,
            fileName: attachment.fileName,
            mimeType: attachment.mimeType,
            fileSize: attachment.fileSize,
            thumbnailPath: attachment.thumbnailPath,
            fileUrl: fileUrl,
            thumbnailUrl: thumbnailUrl,
          });
        } catch (error) {
          this.logger.error(`Error getting file URL for attachment ${attachment.id}: ${error.message}`);
          // Fallback to file path if URL generation fails
          attachmentDtos.push({
            id: attachment.id,
            filePath: attachment.filePath,
            fileName: attachment.fileName,
            mimeType: attachment.mimeType,
            fileSize: attachment.fileSize,
            thumbnailPath: attachment.thumbnailPath,
            fileUrl: attachment.filePath,
            thumbnailUrl: attachment.thumbnailPath,
          });
        }
      }
    }

    return {
      id: message.id,
      conversationId: message.conversationId,
      senderId: message.senderId,
      senderName: sender?.name || 'Unknown',
      senderProfilePicture,
      recipientId: message.recipientId,
      type: message.type,
      content: message.content,
      status: message.status,
      readAt: message.readAt,
      deliveredAt: message.deliveredAt,
      metadata: message.metadata,
      attachments: attachmentDtos,
      createdAt: message.createdAt,
      isAdminMessage,
      isSenderVirtualAdmin,
    };
  }
}
