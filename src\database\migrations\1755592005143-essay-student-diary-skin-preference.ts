import { MigrationInterface, QueryRunner } from "typeorm";

export class EssayStudentDiarySkinPreference1755592005143 implements MigrationInterface {
    name = 'EssayStudentDiarySkinPreference1755592005143'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "essay_module_skin_preference" ADD "student_skin_id" uuid`);
        await queryRunner.query(`ALTER TABLE "essay_module_skin_preference" ADD CONSTRAINT "FK_055a11f3508da64c38ef1eab212" FOREIGN KEY ("student_skin_id") REFERENCES "student_diary_skin"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "essay_module_skin_preference" DROP CONSTRAINT "FK_055a11f3508da64c38ef1eab212"`);
        await queryRunner.query(`ALTER TABLE "essay_module_skin_preference" DROP COLUMN "student_skin_id"`);
    }

}
