import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsNumber, IsPositive, IsOptional, Min, ValidateNested, IsBoolean, IsEnum, IsArray, ArrayMinSize, Max } from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { WaterfallQuestionType } from '../../entities/tutor-waterfall-set.entity';

/**
 * DTO for creating a new waterfall set
 */
export class CreateWaterfallSetDto {
  @ApiProperty({
    description: 'The title of the waterfall set',
    example: 'Basic Grammar Set 1',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'The total score for the waterfall set',
    example: 50,
  })
  @IsNumber()
  @IsPositive()
  total_score: number;
}

/**
 * Wrapper DTO for validating a waterfall set creation
 * This ensures proper validation of the set creation object
 */
export class CreateWaterfallSetWrapperDto {
  @ApiProperty({
    description: 'Set creation data',
    type: CreateWaterfallSetDto,
    example: {
      title: 'Basic Grammar Set 1',
      total_score: 50,
    },
  })
  @ValidateNested()
  @Type(() => CreateWaterfallSetDto)
  set: CreateWaterfallSetDto;
}

/**
 * DTO for updating a waterfall set
 */
export class UpdateWaterfallSetDto {
  @ApiProperty({
    description: 'The title of the waterfall set',
    example: 'Basic Grammar Set 1',
    required: false,
  })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiProperty({
    description: 'The total score for the waterfall set',
    example: 50,
    required: false,
  })
  @IsNumber()
  @IsPositive()
  @IsOptional()
  total_score?: number;
}

/**
 * Wrapper DTO for validating a waterfall set update
 * This ensures proper validation of the set update object
 */
export class UpdateWaterfallSetWrapperDto {
  @ApiProperty({
    description: 'Set update data',
    type: UpdateWaterfallSetDto,
    example: {
      title: 'Updated Grammar Set 1',
      total_score: 60,
    },
  })
  @ValidateNested()
  @Type(() => UpdateWaterfallSetDto)
  set: UpdateWaterfallSetDto;
}

/**
 * DTO for filtering waterfall sets
 */
export class WaterfallFilterDto {
  @ApiProperty({
    description: 'Page number (1-based)',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  page?: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  limit?: number;

  @ApiProperty({
    description: 'Field to sort by',
    example: 'createdAt',
    required: false,
  })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @ApiProperty({
    description: 'Sort direction',
    example: 'DESC',
    enum: ['ASC', 'DESC'],
    required: false,
  })
  @IsOptional()
  @IsString()
  sortDirection?: 'ASC' | 'DESC';

  @ApiProperty({
    description: 'Filter sets by title (partial match)',
    example: 'Grammar',
    required: false,
  })
  @IsOptional()
  @IsString()
  title?: string;
}

/**
 * DTO for waterfall set response
 */
export class WaterfallSetResponseDto {
  @ApiProperty({
    description: 'The ID of the waterfall set',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'The title of the waterfall set',
    example: 'Basic Grammar Set 1',
  })
  title: string;

  @ApiProperty({
    description: 'The total score for the waterfall set',
    example: 50,
  })
  total_score: number;

  @ApiProperty({
    description: 'The total number of questions in the set',
    example: 10,
  })
  total_questions: number;

  @ApiProperty({
    description: 'Whether the waterfall set is active and available to students',
    example: true,
    required: false,
  })
  is_active?: boolean;

  @ApiProperty({
    description: 'The date the set was created',
    example: '2023-07-25T12:34:56.789Z',
  })
  created_at: Date;

  @ApiProperty({
    description: 'The date the set was last updated',
    example: '2023-07-25T12:34:56.789Z',
  })
  updated_at: Date;
}

/**
 * DTO for toggling waterfall set active status
 */
export class ToggleWaterfallStatusDto {
  @ApiProperty({
    description: 'Whether the waterfall set should be active',
    example: true,
  })
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return undefined;
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean()
  is_active: boolean;
}

// New question type DTOs

export class CreateWaterfallSetWithTypeDto {
  @ApiProperty({ description: 'Set title' })
  @IsString()
  title: string;

  @ApiProperty({ description: 'Total score for the set' })
  @IsNumber()
  @Min(1)
  total_score: number;

  @ApiProperty({ description: 'Question type for this set', enum: WaterfallQuestionType })
  @IsEnum(WaterfallQuestionType)
  question_type: WaterfallQuestionType;
}

export class CreateWaterfallSetWithTypeWrapperDto {
  @ApiProperty({ type: CreateWaterfallSetWithTypeDto })
  @ValidateNested()
  @Type(() => CreateWaterfallSetWithTypeDto)
  set: CreateWaterfallSetWithTypeDto;
}

export class CreateTrueFalseQuestionDto {
  @ApiProperty({ description: 'The true/false statement' })
  @IsString()
  statement: string;

  @ApiProperty({ description: 'The correct answer (true or false)' })
  @IsBoolean()
  correct_answer: boolean;

  @ApiProperty({ description: 'Time limit in seconds', required: false })
  @IsOptional()
  @IsNumber()
  @Min(1)
  time_limit_in_seconds?: number;

  @ApiProperty({ description: 'Difficulty level', required: false })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10)
  level?: number;
}

export class CreateMultipleChoiceQuestionDto {
  @ApiProperty({ description: 'The question text' })
  @IsString()
  question_text: string;

  @ApiProperty({ description: 'Array of answer options', type: [String] })
  @IsArray()
  @ArrayMinSize(2)
  @IsString({ each: true })
  options: string[];

  @ApiProperty({ description: 'Indices of correct options', type: [Number] })
  @IsArray()
  @ArrayMinSize(1)
  @IsNumber({}, { each: true })
  correct_option_indices: number[];

  @ApiProperty({ description: 'Allow multiple selections' })
  @IsBoolean()
  allow_multiple_selection: boolean;

  @ApiProperty({ description: 'Minimum selections required', required: false })
  @IsOptional()
  @IsNumber()
  @Min(1)
  min_selections?: number;

  @ApiProperty({ description: 'Maximum selections allowed', required: false })
  @IsOptional()
  @IsNumber()
  @Min(1)
  max_selections?: number;

  @ApiProperty({ description: 'Time limit in seconds', required: false })
  @IsOptional()
  @IsNumber()
  @Min(1)
  time_limit_in_seconds?: number;

  @ApiProperty({ description: 'Difficulty level', required: false })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10)
  level?: number;
}

export class CreateTrueFalseQuestionWrapperDto {
  @ApiProperty({ type: CreateTrueFalseQuestionDto })
  @ValidateNested()
  @Type(() => CreateTrueFalseQuestionDto)
  question: CreateTrueFalseQuestionDto;
}

export class CreateMultipleChoiceQuestionWrapperDto {
  @ApiProperty({ type: CreateMultipleChoiceQuestionDto })
  @ValidateNested()
  @Type(() => CreateMultipleChoiceQuestionDto)
  question: CreateMultipleChoiceQuestionDto;
}

export class CreateTrueFalseQuestionsWrapperDto {
  @ApiProperty({ type: [CreateTrueFalseQuestionDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateTrueFalseQuestionDto)
  questions: CreateTrueFalseQuestionDto[];
}

export class CreateMultipleChoiceQuestionsWrapperDto {
  @ApiProperty({ type: [CreateMultipleChoiceQuestionDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateMultipleChoiceQuestionDto)
  questions: CreateMultipleChoiceQuestionDto[];
}

export class WaterfallSetWithTypeResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  title: string;

  @ApiProperty()
  total_score: number;

  @ApiProperty()
  total_questions: number;

  @ApiProperty({ enum: WaterfallQuestionType })
  question_type: WaterfallQuestionType;

  @ApiProperty()
  is_active: boolean;

  @ApiProperty()
  created_at: Date;

  @ApiProperty()
  updated_at: Date;
}
