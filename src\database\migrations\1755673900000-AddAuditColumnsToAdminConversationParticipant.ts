import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddAuditColumnsToAdminConversationParticipant1755673900000 implements MigrationInterface {
  name = 'AddAuditColumnsToAdminConversationParticipant1755673900000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "admin_conversation_participant" 
      ADD COLUMN "created_by" varchar(36),
      ADD COLUMN "updated_by" varchar(36)
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "admin_conversation_participant" 
      DROP COLUMN "created_by",
      DROP COLUMN "updated_by"
    `);
  }
}