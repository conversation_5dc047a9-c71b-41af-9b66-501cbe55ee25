import { Controller, Get, Post, Put, Delete, Body, Param, UseGuards, Req } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiParam, ApiBody, ApiBearerAuth } from '@nestjs/swagger';
import { TutorGuard } from '../../../common/guards/tutor.guard';
import { ApiResponse } from '../../../common/dto/api-response.dto';
import { TutorWaterfallService } from './tutor-waterfall.service';

@ApiTags('Tutor Waterfall Games')
@ApiBearerAuth('JWT-auth')
@Controller('play/tutor/waterfall')
@UseGuards(TutorGuard)
export class TutorWaterfallController {
  constructor(private readonly tutorWaterfallService: TutorWaterfallService) {}

  @Get('sets')
  @ApiOperation({ 
    summary: 'Get all tutor waterfall sets',
    description: 'Retrieves all waterfall sets created by the authenticated tutor with basic information.'
  })
  async getTutorSets(@Req() req: any): Promise<ApiResponse<any>> {
    const tutorId = req.user.sub;
    const result = await this.tutorWaterfallService.getTutorSets(tutorId);
    return ApiResponse.success(result, 'Tutor waterfall sets retrieved successfully');
  }

  @Get('sets/:setId')
  @ApiOperation({ 
    summary: 'Get tutor waterfall set with all question types',
    description: 'Retrieves a specific waterfall set with ALL question types (fill-in-blank, true/false, multiple choice) regardless of the original set configuration.'
  })
  @ApiParam({ 
    name: 'setId', 
    description: 'Waterfall set ID', 
    example: '123e4567-e89b-12d3-a456-426614174000' 
  })
  async getTutorSetWithQuestions(@Req() req: any, @Param('setId') setId: string): Promise<ApiResponse<any>> {
    const tutorId = req.user.sub;
    const result = await this.tutorWaterfallService.getTutorSetWithQuestions(tutorId, setId);
    return ApiResponse.success(result, 'Tutor waterfall set with questions retrieved successfully');
  }

  @Post('bulk-create')
  @ApiOperation({ 
    summary: 'Create multiple waterfall sets with mixed question types',
    description: 'Creates multiple waterfall sets for students. Each set can contain any combination of fill-in-blank, true/false, and multiple choice questions.'
  })
  @ApiBody({
    description: 'Bulk create waterfall sets with mixed question types',
    schema: {
      type: 'object',
      properties: {
        sets: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              title: { type: 'string', example: 'English Grammar Quiz' },
              student_id: { type: 'string', example: '123e4567-e89b-12d3-a456-426614174000' },
              module_type: { type: 'string', enum: ['diary', 'novel', 'essay', 'qa'], example: 'essay' },
              entry_id: { type: 'string', example: '456e7890-e89b-12d3-a456-426614174001' },
              total_score: { type: 'number', example: 100 },
              questions: {
                type: 'array',
                description: 'Mixed array of different question types',
                items: {
                  oneOf: [
                    {
                      type: 'object',
                      description: 'Fill-in-blank question',
                      properties: {
                        type: { type: 'string', enum: ['fill_in_blank'] },
                        question_text: { type: 'string', example: 'The cat <span class="blank-highlight">___</span> on the mat.' },
                        question_text_plain: { type: 'string', example: 'The cat [[gap]] on the mat.' },
                        correct_answers: { type: 'array', items: { type: 'string' }, example: ['sat'] },
                        options: { type: 'array', items: { type: 'string' }, example: ['sits', 'standing', 'lying'] },
                        time_limit_in_seconds: { type: 'number', example: 30 },
                        level: { type: 'number', example: 2 }
                      }
                    },
                    {
                      type: 'object',
                      description: 'True/false question',
                      properties: {
                        type: { type: 'string', enum: ['true_false'] },
                        statement: { type: 'string', example: 'The Earth is round.' },
                        correct_answer: { type: 'boolean', example: true },
                        time_limit_in_seconds: { type: 'number', example: 15 },
                        level: { type: 'number', example: 1 }
                      }
                    },
                    {
                      type: 'object',
                      description: 'Multiple choice question',
                      properties: {
                        type: { type: 'string', enum: ['multiple_choice_single', 'multiple_choice_multiple'] },
                        question_text: { type: 'string', example: 'Which of the following are programming languages?' },
                        options: { type: 'array', items: { type: 'string' }, example: ['JavaScript', 'HTML', 'Python', 'CSS'] },
                        correct_option_indices: { type: 'array', items: { type: 'number' }, example: [0, 2] },
                        allow_multiple_selection: { type: 'boolean', example: true },
                        min_selections: { type: 'number', example: 1 },
                        max_selections: { type: 'number', example: 3 },
                        time_limit_in_seconds: { type: 'number', example: 45 },
                        level: { type: 'number', example: 3 }
                      }
                    }
                  ]
                }
              }
            },
            required: ['title', 'student_id', 'module_type', 'entry_id', 'questions']
          }
        }
      },
      required: ['sets']
    },
    examples: {
      mixedQuestionSet: {
        summary: 'Mixed question types example',
        value: {
          sets: [
            {
              title: 'English Grammar Quiz',
              student_id: '123e4567-e89b-12d3-a456-426614174000',
              module_type: 'essay',
              entry_id: '456e7890-e89b-12d3-a456-426614174001',
              total_score: 100,
              questions: [
                {
                  type: 'fill_in_blank',
                  question_text: 'The cat <span class="blank-highlight">___</span> on the mat.',
                  question_text_plain: 'The cat [[gap]] on the mat.',
                  correct_answers: ['sat'],
                  options: ['sits', 'standing', 'lying'],
                  time_limit_in_seconds: 30,
                  level: 2
                },
                {
                  type: 'true_false',
                  statement: 'English is a Germanic language.',
                  correct_answer: true,
                  time_limit_in_seconds: 15,
                  level: 1
                },
                {
                  type: 'multiple_choice_multiple',
                  question_text: 'Which of the following are parts of speech?',
                  options: ['Noun', 'Color', 'Verb', 'Adjective'],
                  correct_option_indices: [0, 2, 3],
                  allow_multiple_selection: true,
                  min_selections: 2,
                  max_selections: 4,
                  time_limit_in_seconds: 45,
                  level: 3
                }
              ]
            }
          ]
        }
      }
    }
  })
  async bulkCreateSets(@Req() req: any, @Body() dto: any): Promise<ApiResponse<any>> {
    const tutorId = req.user.sub;
    const result = await this.tutorWaterfallService.bulkCreateSets(tutorId, dto.sets);
    return ApiResponse.success(result, 'Waterfall sets created successfully');
  }

  @Put('bulk-update')
  @ApiOperation({ 
    summary: 'Update multiple waterfall sets with questions in bulk',
    description: 'Updates waterfall sets including their metadata (title, total_score) and questions. Can update, add, or modify questions within each set.'
  })
  @ApiBody({
    description: 'Bulk update waterfall sets with questions',
    schema: {
      type: 'object',
      properties: {
        sets: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string', example: '123e4567-e89b-12d3-a456-426614174000' },
              title: { type: 'string', example: 'Updated English Grammar Quiz' },
              total_score: { type: 'number', example: 150 },
              questions: {
                type: 'array',
                description: 'Updated questions for the set (mixed types supported)',
                items: {
                  oneOf: [
                    {
                      type: 'object',
                      description: 'Fill-in-blank question',
                      properties: {
                        id: { type: 'string', description: 'Question ID (optional for new questions)' },
                        type: { type: 'string', enum: ['fill_in_blank'] },
                        question_text: { type: 'string' },
                        question_text_plain: { type: 'string' },
                        correct_answers: { type: 'array', items: { type: 'string' } },
                        options: { type: 'array', items: { type: 'string' } },
                        time_limit_in_seconds: { type: 'number' },
                        level: { type: 'number' }
                      }
                    },
                    {
                      type: 'object',
                      description: 'True/false question',
                      properties: {
                        id: { type: 'string', description: 'Question ID (optional for new questions)' },
                        type: { type: 'string', enum: ['true_false'] },
                        statement: { type: 'string' },
                        correct_answer: { type: 'boolean' },
                        time_limit_in_seconds: { type: 'number' },
                        level: { type: 'number' }
                      }
                    },
                    {
                      type: 'object',
                      description: 'Multiple choice question',
                      properties: {
                        id: { type: 'string', description: 'Question ID (optional for new questions)' },
                        type: { type: 'string', enum: ['multiple_choice_single', 'multiple_choice_multiple'] },
                        question_text: { type: 'string' },
                        options: { type: 'array', items: { type: 'string' } },
                        correct_option_indices: { type: 'array', items: { type: 'number' } },
                        allow_multiple_selection: { type: 'boolean' },
                        min_selections: { type: 'number' },
                        max_selections: { type: 'number' },
                        time_limit_in_seconds: { type: 'number' },
                        level: { type: 'number' }
                      }
                    }
                  ]
                }
              }
            },
            required: ['id']
          }
        }
      },
      required: ['sets']
    },
    examples: {
      updateSetsWithQuestions: {
        summary: 'Update sets with mixed question types',
        value: {
          sets: [
            {
              id: '123e4567-e89b-12d3-a456-426614174000',
              title: 'Advanced English Grammar Quiz',
              total_score: 150,
              questions: [
                {
                  id: 'q1-existing-id',
                  type: 'fill_in_blank',
                  question_text: 'Updated: The cat <span class="blank-highlight">___</span> on the mat.',
                  question_text_plain: 'Updated: The cat [[gap]] on the mat.',
                  correct_answers: ['sat', 'sleeps'],
                  options: ['sits', 'standing', 'lying'],
                  time_limit_in_seconds: 35,
                  level: 2
                },
                {
                  type: 'true_false',
                  statement: 'English has more than 26 letters in its alphabet.',
                  correct_answer: false,
                  time_limit_in_seconds: 20,
                  level: 1
                },
                {
                  id: 'q3-existing-id',
                  type: 'multiple_choice_multiple',
                  question_text: 'Which are parts of speech?',
                  options: ['Noun', 'Color', 'Verb', 'Adjective', 'Number'],
                  correct_option_indices: [0, 2, 3],
                  allow_multiple_selection: true,
                  min_selections: 2,
                  max_selections: 3,
                  time_limit_in_seconds: 45,
                  level: 3
                }
              ]
            }
          ]
        }
      },
      updateMetadataOnly: {
        summary: 'Update only set metadata',
        value: {
          sets: [
            {
              id: '123e4567-e89b-12d3-a456-426614174000',
              title: 'Renamed Quiz Title',
              total_score: 200
            },
            {
              id: '456e7890-e89b-12d3-a456-426614174001',
              title: 'Basic Math Quiz'
            }
          ]
        }
      }
    }
  })
  async bulkUpdateSets(@Req() req: any, @Body() dto: any): Promise<ApiResponse<any>> {
    const tutorId = req.user.sub;
    const result = await this.tutorWaterfallService.bulkUpdateSets(tutorId, dto.sets);
    return ApiResponse.success(result, 'Waterfall sets updated successfully');
  }

  @Delete('bulk-delete')
  @ApiOperation({ 
    summary: 'Soft delete multiple waterfall sets in bulk',
    description: 'Soft deletes (deactivates) multiple waterfall sets owned by the tutor. Sets are marked as inactive rather than permanently deleted.'
  })
  @ApiBody({
    description: 'Array of set IDs to delete',
    schema: {
      type: 'object',
      properties: {
        set_ids: { 
          type: 'array', 
          items: { type: 'string' },
          example: ['123e4567-e89b-12d3-a456-426614174000', '456e7890-e89b-12d3-a456-426614174001']
        }
      },
      required: ['set_ids']
    },
    examples: {
      deleteSets: {
        summary: 'Delete multiple sets',
        value: {
          set_ids: [
            '123e4567-e89b-12d3-a456-426614174000',
            '456e7890-e89b-12d3-a456-426614174001'
          ]
        }
      }
    }
  })
  async bulkDeleteSets(@Req() req: any, @Body() dto: { set_ids: string[] }): Promise<ApiResponse<any>> {
    const tutorId = req.user.sub;
    await this.tutorWaterfallService.bulkDeleteSets(tutorId, dto.set_ids);
    return ApiResponse.success(null, 'Waterfall sets deleted successfully');
  }

  @Post(':setId/questions')
  @ApiOperation({ 
    summary: 'Add any type of question to waterfall set',
    description: 'Adds a new question (fill-in-blank, true/false, or multiple choice) to an existing waterfall set.'
  })
  @ApiParam({ name: 'setId', description: 'Waterfall set ID', example: '123e4567-e89b-12d3-a456-426614174000' })
  @ApiBody({
    description: 'Question data for any supported question type',
    schema: {
      oneOf: [
        {
          type: 'object',
          description: 'Fill-in-blank question',
          properties: {
            type: { type: 'string', enum: ['fill_in_blank'] },
            question_text: { type: 'string' },
            question_text_plain: { type: 'string' },
            correct_answers: { type: 'array', items: { type: 'string' } },
            options: { type: 'array', items: { type: 'string' } },
            time_limit_in_seconds: { type: 'number' },
            level: { type: 'number' }
          },
          required: ['type', 'question_text', 'question_text_plain', 'correct_answers']
        },
        {
          type: 'object',
          description: 'True/false question',
          properties: {
            type: { type: 'string', enum: ['true_false'] },
            statement: { type: 'string' },
            correct_answer: { type: 'boolean' },
            time_limit_in_seconds: { type: 'number' },
            level: { type: 'number' }
          },
          required: ['type', 'statement', 'correct_answer']
        },
        {
          type: 'object',
          description: 'Multiple choice question',
          properties: {
            type: { type: 'string', enum: ['multiple_choice_single', 'multiple_choice_multiple'] },
            question_text: { type: 'string' },
            options: { type: 'array', items: { type: 'string' } },
            correct_option_indices: { type: 'array', items: { type: 'number' } },
            allow_multiple_selection: { type: 'boolean' },
            min_selections: { type: 'number' },
            max_selections: { type: 'number' },
            time_limit_in_seconds: { type: 'number' },
            level: { type: 'number' }
          },
          required: ['type', 'question_text', 'options', 'correct_option_indices']
        }
      ]
    },
    examples: {
      fillInBlank: {
        summary: 'Fill-in-blank question',
        value: {
          type: 'fill_in_blank',
          question_text: 'The capital of France is <span class="blank-highlight">___</span>.',
          question_text_plain: 'The capital of France is [[gap]].',
          correct_answers: ['Paris'],
          options: ['London', 'Berlin', 'Madrid'],
          time_limit_in_seconds: 30,
          level: 2
        }
      },
      trueFalse: {
        summary: 'True/false question',
        value: {
          type: 'true_false',
          statement: 'Paris is the capital of France.',
          correct_answer: true,
          time_limit_in_seconds: 15,
          level: 1
        }
      },
      multipleChoice: {
        summary: 'Multiple choice question',
        value: {
          type: 'multiple_choice_single',
          question_text: 'What is the capital of France?',
          options: ['London', 'Paris', 'Berlin', 'Madrid'],
          correct_option_indices: [1],
          allow_multiple_selection: false,
          time_limit_in_seconds: 30,
          level: 2
        }
      }
    }
  })
  async addQuestion(@Req() req: any, @Param('setId') setId: string, @Body() dto: any): Promise<ApiResponse<any>> {
    const tutorId = req.user.sub;
    const result = await this.tutorWaterfallService.addQuestion(tutorId, setId, dto);
    return ApiResponse.success(result, 'Question added successfully');
  }

  @Put('questions/:questionId')
  @ApiOperation({ 
    summary: 'Update question (Not implemented)',
    description: 'Updates a specific question. This endpoint is currently not implemented.'
  })
  @ApiParam({ 
    name: 'questionId', 
    description: 'Question ID', 
    example: '123e4567-e89b-12d3-a456-426614174000' 
  })
  @ApiBody({
    description: 'Question update data (endpoint not implemented)',
    schema: {
      oneOf: [
        {
          type: 'object',
          description: 'Fill-in-blank question update',
          properties: {
            question_text: { type: 'string', example: 'Updated: The cat <span class="blank-highlight">___</span> on the mat.' },
            question_text_plain: { type: 'string', example: 'Updated: The cat [[gap]] on the mat.' },
            correct_answers: { type: 'array', items: { type: 'string' }, example: ['sat', 'sleeps'] },
            options: { type: 'array', items: { type: 'string' }, example: ['sits', 'standing', 'lying', 'sleeps'] },
            time_limit_in_seconds: { type: 'number', example: 45 },
            level: { type: 'number', example: 3 }
          }
        },
        {
          type: 'object',
          description: 'True/false question update',
          properties: {
            statement: { type: 'string', example: 'Updated: The Earth is flat.' },
            correct_answer: { type: 'boolean', example: false },
            time_limit_in_seconds: { type: 'number', example: 20 },
            level: { type: 'number', example: 2 }
          }
        },
        {
          type: 'object',
          description: 'Multiple choice question update',
          properties: {
            question_text: { type: 'string', example: 'Updated: Which are programming languages?' },
            options: { type: 'array', items: { type: 'string' }, example: ['JavaScript', 'HTML', 'Python', 'CSS', 'Java'] },
            correct_option_indices: { type: 'array', items: { type: 'number' }, example: [0, 2, 4] },
            allow_multiple_selection: { type: 'boolean', example: true },
            min_selections: { type: 'number', example: 2 },
            max_selections: { type: 'number', example: 3 },
            time_limit_in_seconds: { type: 'number', example: 60 },
            level: { type: 'number', example: 4 }
          }
        }
      ]
    },
    examples: {
      fillInBlankUpdate: {
        summary: 'Update fill-in-blank question',
        value: {
          question_text: 'The dog <span class="blank-highlight">___</span> in the park.',
          question_text_plain: 'The dog [[gap]] in the park.',
          correct_answers: ['runs', 'plays'],
          options: ['walks', 'sleeps', 'eats'],
          time_limit_in_seconds: 35,
          level: 2
        }
      },
      trueFalseUpdate: {
        summary: 'Update true/false question',
        value: {
          statement: 'Water boils at 100°C at sea level.',
          correct_answer: true,
          time_limit_in_seconds: 25,
          level: 2
        }
      },
      multipleChoiceUpdate: {
        summary: 'Update multiple choice question',
        value: {
          question_text: 'Which of these are web technologies?',
          options: ['React', 'Photoshop', 'Node.js', 'Excel', 'Vue.js'],
          correct_option_indices: [0, 2, 4],
          allow_multiple_selection: true,
          min_selections: 2,
          max_selections: 3,
          time_limit_in_seconds: 50,
          level: 3
        }
      }
    }
  })
  async updateQuestion(@Req() req: any, @Param('questionId') questionId: string, @Body() dto: any): Promise<ApiResponse<any>> {
    const tutorId = req.user.sub;
    const result = await this.tutorWaterfallService.updateQuestion(tutorId, questionId, dto);
    return ApiResponse.success(result, 'Question updated successfully');
  }

  @Delete('questions/:questionId')
  @ApiOperation({ 
    summary: 'Delete question (Not implemented)',
    description: 'Soft deletes a specific question. This endpoint is currently not implemented.'
  })
  @ApiParam({ 
    name: 'questionId', 
    description: 'Question ID', 
    example: '123e4567-e89b-12d3-a456-426614174000' 
  })
  async deleteQuestion(@Req() req: any, @Param('questionId') questionId: string): Promise<ApiResponse<any>> {
    const tutorId = req.user.sub;
    await this.tutorWaterfallService.deleteQuestion(tutorId, questionId);
    return ApiResponse.success(null, 'Question deleted successfully');
  }
}