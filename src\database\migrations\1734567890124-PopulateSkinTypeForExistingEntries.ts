import { MigrationInterface, QueryRunner } from 'typeorm';

export class PopulateSkinTypeForExistingEntries1734567890124 implements MigrationInterface {
  name = 'PopulateSkinTypeForExistingEntries1734567890124';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Update existing entries to have skin_type = 'global' where skin_id is not null
    await queryRunner.query(`
      UPDATE "diary_entry" 
      SET "skin_type" = 'global' 
      WHERE "skin_id" IS NOT NULL
    `);

    // For entries with no skin_id, set a default global skin if available
    const defaultSkinResult = await queryRunner.query(`
      SELECT id FROM "diary_skin" 
      WHERE "is_active" = true 
      ORDER BY "created_at" ASC 
      LIMIT 1
    `);

    if (defaultSkinResult && defaultSkinResult.length > 0) {
      const defaultSkinId = defaultSkinResult[0].id;
      
      await queryRunner.query(`
        UPDATE "diary_entry" 
        SET "skin_id" = $1, "skin_type" = 'global' 
        WHERE "skin_id" IS NULL
      `, [defaultSkinId]);
    }

    // Log the migration results
    const updatedCount = await queryRunner.query(`
      SELECT COUNT(*) as count FROM "diary_entry" WHERE "skin_type" = 'global'
    `);
    
    console.log(`Updated ${updatedCount[0].count} diary entries with skin_type = 'global'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Reset skin_type to default value
    await queryRunner.query(`
      UPDATE "diary_entry" 
      SET "skin_type" = 'global'
    `);
  }
}