import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { VirtualAdmin } from '../../database/entities/virtual-admin.entity';
import { User, UserType } from '../../database/entities/user.entity';

/**
 * Service for managing virtual admin entities
 * Ensures there's always a virtual admin available for admin conversations
 */
@Injectable()
export class VirtualAdminService implements OnModuleInit {
  private readonly logger = new Logger(VirtualAdminService.name);
  private virtualAdminUserId: string | null = null;
  private virtualAdminCache: VirtualAdmin | null = null;
  private cacheExpiry: number = 0;
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes cache

  constructor(
    @InjectRepository(VirtualAdmin)
    private readonly virtualAdminRepository: Repository<VirtualAdmin>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * Initialize the virtual admin on module startup
   */
  async onModuleInit() {
    await this.ensureVirtualAdminExists();
  }

  /**
   * Get the virtual admin user ID (cached)
   * @returns The user ID of the virtual admin
   */
  async getVirtualAdminUserId(): Promise<string> {
    if (this.virtualAdminUserId) {
      return this.virtualAdminUserId;
    }

    const virtualAdmin = await this.getVirtualAdmin();
    this.virtualAdminUserId = virtualAdmin.userId;
    return this.virtualAdminUserId;
  }

  /**
   * Get the virtual admin entity with user details (cached)
   * @returns The virtual admin entity with user relation
   */
  async getVirtualAdmin(): Promise<VirtualAdmin> {
    const now = Date.now();

    // Return cached version if still valid
    if (this.virtualAdminCache && now < this.cacheExpiry) {
      return this.virtualAdminCache;
    }

    // Fetch from database
    const virtualAdmin = await this.virtualAdminRepository.findOne({
      where: { isActive: true },
      relations: ['user'],
    });

    if (!virtualAdmin) {
      throw new Error('Virtual admin not found. Please run the migration to create it.');
    }

    // Update cache
    this.virtualAdminCache = virtualAdmin;
    this.cacheExpiry = now + this.CACHE_TTL;
    this.virtualAdminUserId = virtualAdmin.userId;

    return virtualAdmin;
  }

  /**
   * Get virtual admin details for message mapping (optimized)
   * @returns Object with userId, displayName, and profilePicture
   */
  async getVirtualAdminForMessage(): Promise<{ userId: string; isSenderVirtualAdmin: boolean; displayName: string; profilePicture: string | null }> {
    const virtualAdmin = await this.getVirtualAdmin();
    return {
      userId: virtualAdmin.userId,
      isSenderVirtualAdmin: true,
      displayName: virtualAdmin.displayName,
      profilePicture: virtualAdmin.user?.profilePicture || null,
    };
  }

  /**
   * Ensure a virtual admin exists in the database
   * This method is called during module initialization and migrations
   */
  async ensureVirtualAdminExists(): Promise<VirtualAdmin> {
    return await this.dataSource.transaction(async (manager) => {
      // Check if virtual admin already exists
      let virtualAdmin = await manager.findOne(VirtualAdmin, {
        where: { isActive: true },
        relations: ['user'],
      });

      if (virtualAdmin) {
        this.virtualAdminUserId = virtualAdmin.userId;
        return virtualAdmin;
      }

      this.logger.log('Creating virtual admin user and entity...');

      // Create the virtual admin user first
      const virtualAdminUser = manager.create(User, {
        userId: 'virtual-admin',
        name: 'HEC Admin',
        email: '<EMAIL>',
        password: require('crypto').randomBytes(32).toString('hex'), // Generate secure random password
        type: UserType.ADMIN,
        isActive: true,
        isConfirmed: true,
        phoneNumber: '******-HEC-ADMIN',
      });

      const savedUser = await manager.save(virtualAdminUser);

      // Create the virtual admin entity
      virtualAdmin = manager.create(VirtualAdmin, {
        userId: savedUser.id,
        displayName: 'HEC Admin',
        description: 'Virtual admin user for unified admin conversations',
        isActive: true,
        settings: {
          autoResponse: false,
          notificationEnabled: true,
          maxConcurrentChats: 1000,
        },
      });

      virtualAdmin = await manager.save(virtualAdmin);
      
      // Load the user relation
      virtualAdmin = await manager.findOne(VirtualAdmin, {
        where: { id: virtualAdmin.id },
        relations: ['user'],
      });

      this.virtualAdminUserId = virtualAdmin.userId;
      this.logger.log(`Virtual admin created with user ID: ${this.virtualAdminUserId}`);

      return virtualAdmin;
    });
  }

  /**
   * Invalidate cache (call after updates)
   */
  private invalidateCache(): void {
    this.virtualAdminCache = null;
    this.cacheExpiry = 0;
    this.virtualAdminUserId = null;
  }

  /**
   * Update virtual admin settings
   * @param settings New settings to apply
   */
  async updateSettings(settings: Record<string, any>): Promise<VirtualAdmin> {
    const virtualAdmin = await this.getVirtualAdmin();

    virtualAdmin.settings = {
      ...virtualAdmin.settings,
      ...settings,
    };

    const updated = await this.virtualAdminRepository.save(virtualAdmin);
    this.invalidateCache(); // Invalidate cache after update
    return updated;
  }

  /**
   * Deactivate the current virtual admin and optionally create a new one
   * @param createNew Whether to create a new virtual admin
   */
  async deactivateVirtualAdmin(createNew: boolean = true): Promise<VirtualAdmin | null> {
    const currentVirtualAdmin = await this.getVirtualAdmin();
    
    currentVirtualAdmin.isActive = false;
    await this.virtualAdminRepository.save(currentVirtualAdmin);
    
    this.virtualAdminUserId = null;

    if (createNew) {
      return await this.ensureVirtualAdminExists();
    }

    return null;
  }
}
