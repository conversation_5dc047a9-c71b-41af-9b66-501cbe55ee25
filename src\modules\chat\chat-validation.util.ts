import { BadRequestException } from '@nestjs/common';

/**
 * Utility functions for chat input validation and sanitization
 */
export class ChatValidationUtil {
  /**
   * Sanitize user input to prevent injection attacks
   */
  static sanitizeInput(input: string): string {
    if (!input || typeof input !== 'string') {
      return '';
    }
    
    // Remove potential injection characters
    return input
      .replace(/[\r\n\t]/g, ' ') // Replace newlines and tabs with spaces
      .replace(/[<>]/g, '') // Remove angle brackets
      .trim()
      .substring(0, 1000); // Limit length
  }

  /**
   * Validate conversation ID format
   */
  static validateConversationId(conversationId: string): string {
    if (!conversationId || typeof conversationId !== 'string') {
      throw new BadRequestException('Invalid conversation ID');
    }

    // Allow only alphanumeric characters, hyphens, and underscores
    const sanitized = conversationId.replace(/[^a-zA-Z0-9\-_]/g, '');
    
    if (sanitized.length === 0 || sanitized.length > 100) {
      throw new BadRequestException('Invalid conversation ID format');
    }

    return sanitized;
  }

  /**
   * Validate user ID format
   */
  static validateUserId(userId: string): string {
    if (!userId || typeof userId !== 'string') {
      throw new BadRequestException('Invalid user ID');
    }

    // Allow only alphanumeric characters and hyphens
    const sanitized = userId.replace(/[^a-zA-Z0-9\-]/g, '');
    
    if (sanitized.length === 0 || sanitized.length > 50) {
      throw new BadRequestException('Invalid user ID format');
    }

    return sanitized;
  }

  /**
   * Validate message content
   */
  static validateMessageContent(content: string): string {
    if (!content || typeof content !== 'string') {
      throw new BadRequestException('Message content is required');
    }

    const sanitized = this.sanitizeInput(content);
    
    if (sanitized.length === 0) {
      throw new BadRequestException('Message content cannot be empty');
    }

    if (sanitized.length > 5000) {
      throw new BadRequestException('Message content is too long');
    }

    return sanitized;
  }

  /**
   * Sanitize log message to prevent log injection
   */
  static sanitizeLogMessage(message: string): string {
    if (!message || typeof message !== 'string') {
      return '[Invalid log message]';
    }

    return message
      .replace(/[\r\n\t]/g, ' ') // Replace newlines and tabs
      .replace(/[^\x20-\x7E]/g, '') // Remove non-printable characters
      .substring(0, 500); // Limit length
  }

  /**
   * Validate MIME type to prevent HTTP response splitting
   */
  static validateMimeType(mimeType: string): string {
    if (!mimeType || typeof mimeType !== 'string') {
      return 'application/octet-stream';
    }

    // Remove any potential injection characters
    const sanitized = mimeType
      .replace(/[\r\n\t]/g, '')
      .replace(/[^\w\-\/\.]/g, '')
      .toLowerCase();

    // Validate against common MIME types
    const validMimeTypes = [
      'text/plain', 'text/html', 'text/css', 'text/javascript',
      'application/json', 'application/pdf', 'application/octet-stream',
      'image/jpeg', 'image/png', 'image/gif', 'image/webp',
      'audio/mpeg', 'audio/wav', 'video/mp4', 'video/webm'
    ];

    if (validMimeTypes.includes(sanitized) || /^(text|image|audio|video|application)\/[\w\-\.]+$/.test(sanitized)) {
      return sanitized;
    }

    return 'application/octet-stream';
  }
}