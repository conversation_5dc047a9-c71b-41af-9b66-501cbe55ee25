import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddMissionDiaryEntryNotificationFields1749017547029 implements MigrationInterface {
  name = 'AddMissionDiaryEntryNotificationFields1749017547029';

  public async up(queryRunner: QueryRunner): Promise<void> {
    console.log('Adding mission diary entry history table and notification fields...');

    // Check and create mission_diary_entry_history table if it doesn't exist
    const historyTableExists = await queryRunner.query(`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_name='mission_diary_entry_history'
    `);

    if (historyTableExists.length === 0) {
      await queryRunner.query(`
        CREATE TABLE "mission_diary_entry_history" (
          "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
          "created_at" TIMESTAMP NOT NULL DEFAULT now(),
          "updated_at" TIMESTAMP DEFAULT now(),
          "created_by" character varying(36),
          "updated_by" character varying(36),
          "mission_entry_id" uuid NOT NULL,
          "content" text NOT NULL,
          "version_number" integer NOT NULL,
          "is_latest" boolean NOT NULL DEFAULT false,
          "word_count" integer NOT NULL,
          "meta_data" json,
          CONSTRAINT "PK_mission_diary_entry_history" PRIMARY KEY ("id")
        )
      `);
      console.log('Created mission_diary_entry_history table');
    } else {
      console.log('mission_diary_entry_history table already exists, skipping');
    }

    // Create indexes for mission_diary_entry_history (with existence checks)
    const index1Exists = await queryRunner.query(`
      SELECT indexname
      FROM pg_indexes
      WHERE tablename='mission_diary_entry_history' AND indexname='IDX_mission_entry_history_entry_version'
    `);

    if (index1Exists.length === 0) {
      await queryRunner.query(`
        CREATE INDEX "IDX_mission_entry_history_entry_version"
        ON "mission_diary_entry_history" ("mission_entry_id", "version_number")
      `);
      console.log('Created index IDX_mission_entry_history_entry_version');
    } else {
      console.log('Index IDX_mission_entry_history_entry_version already exists, skipping');
    }

    const index2Exists = await queryRunner.query(`
      SELECT indexname
      FROM pg_indexes
      WHERE tablename='mission_diary_entry_history' AND indexname='IDX_mission_entry_history_entry_latest'
    `);

    if (index2Exists.length === 0) {
      await queryRunner.query(`
        CREATE INDEX "IDX_mission_entry_history_entry_latest"
        ON "mission_diary_entry_history" ("mission_entry_id", "is_latest")
      `);
      console.log('Created index IDX_mission_entry_history_entry_latest');
    } else {
      console.log('Index IDX_mission_entry_history_entry_latest already exists, skipping');
    }

    const index3Exists = await queryRunner.query(`
      SELECT indexname
      FROM pg_indexes
      WHERE tablename='mission_diary_entry_history' AND indexname='IDX_mission_entry_history_created_at'
    `);

    if (index3Exists.length === 0) {
      await queryRunner.query(`
        CREATE INDEX "IDX_mission_entry_history_created_at"
        ON "mission_diary_entry_history" ("created_at")
      `);
      console.log('Created index IDX_mission_entry_history_created_at');
    } else {
      console.log('Index IDX_mission_entry_history_created_at already exists, skipping');
    }

    // Add foreign key constraint for mission_entry_id (with existence check)
    const fkExists = await queryRunner.query(`
      SELECT constraint_name
      FROM information_schema.table_constraints
      WHERE table_name='mission_diary_entry_history' AND constraint_name='FK_mission_entry_history_entry'
    `);

    if (fkExists.length === 0) {
      await queryRunner.query(`
        ALTER TABLE "mission_diary_entry_history"
        ADD CONSTRAINT "FK_mission_entry_history_entry"
        FOREIGN KEY ("mission_entry_id")
        REFERENCES "mission_diary_entry"("id")
        ON DELETE CASCADE
      `);
      console.log('Added FK_mission_entry_history_entry constraint');
    } else {
      console.log('FK_mission_entry_history_entry constraint already exists, skipping');
    }

    // Add new fields to mission_diary_entry table (with existence checks)

    // Check and add first_submission_notified column if it doesn't exist
    const firstSubmissionNotifiedExists = await queryRunner.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name='mission_diary_entry' AND column_name='first_submission_notified'
    `);

    if (firstSubmissionNotifiedExists.length === 0) {
      await queryRunner.query(`
        ALTER TABLE "mission_diary_entry"
        ADD COLUMN "first_submission_notified" boolean NOT NULL DEFAULT false
      `);
      console.log('Added first_submission_notified column to mission_diary_entry');
    } else {
      console.log('first_submission_notified column already exists in mission_diary_entry, skipping');
    }

    // Check and add original_reviewed_version_id column if it doesn't exist
    const originalReviewedVersionExists = await queryRunner.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name='mission_diary_entry' AND column_name='original_reviewed_version_id'
    `);

    if (originalReviewedVersionExists.length === 0) {
      await queryRunner.query(`
        ALTER TABLE "mission_diary_entry"
        ADD COLUMN "original_reviewed_version_id" uuid
      `);
      console.log('Added original_reviewed_version_id column to mission_diary_entry');
    } else {
      console.log('original_reviewed_version_id column already exists in mission_diary_entry, skipping');
    }

    // Check and add total_versions column if it doesn't exist
    const totalVersionsExists = await queryRunner.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name='mission_diary_entry' AND column_name='total_versions'
    `);

    if (totalVersionsExists.length === 0) {
      await queryRunner.query(`
        ALTER TABLE "mission_diary_entry"
        ADD COLUMN "total_versions" integer NOT NULL DEFAULT 0
      `);
      console.log('Added total_versions column to mission_diary_entry');
    } else {
      console.log('total_versions column already exists in mission_diary_entry, skipping');
    }

    // Add foreign key constraint for original_reviewed_version_id (with existence check)
    const originalVersionFkExists = await queryRunner.query(`
      SELECT constraint_name
      FROM information_schema.table_constraints
      WHERE table_name='mission_diary_entry' AND constraint_name='FK_mission_entry_original_reviewed_version'
    `);

    if (originalVersionFkExists.length === 0) {
      await queryRunner.query(`
        ALTER TABLE "mission_diary_entry"
        ADD CONSTRAINT "FK_mission_entry_original_reviewed_version"
        FOREIGN KEY ("original_reviewed_version_id")
        REFERENCES "mission_diary_entry_history"("id")
        ON DELETE SET NULL
      `);
      console.log('Added FK_mission_entry_original_reviewed_version constraint');
    } else {
      console.log('FK_mission_entry_original_reviewed_version constraint already exists, skipping');
    }

    console.log('Successfully added mission diary entry notification and version tracking fields');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    console.log('Removing mission diary entry notification and version tracking fields...');

    // Remove foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "mission_diary_entry"
      DROP CONSTRAINT "FK_mission_entry_original_reviewed_version"
    `);

    await queryRunner.query(`
      ALTER TABLE "mission_diary_entry_history"
      DROP CONSTRAINT "FK_mission_entry_history_entry"
    `);

    // Remove new fields from mission_diary_entry
    await queryRunner.query(`
      ALTER TABLE "mission_diary_entry"
      DROP COLUMN "total_versions"
    `);

    await queryRunner.query(`
      ALTER TABLE "mission_diary_entry"
      DROP COLUMN "original_reviewed_version_id"
    `);

    await queryRunner.query(`
      ALTER TABLE "mission_diary_entry"
      DROP COLUMN "first_submission_notified"
    `);

    // Drop mission_diary_entry_history table
    await queryRunner.query(`DROP TABLE "mission_diary_entry_history"`);

    console.log('Successfully removed mission diary entry notification and version tracking fields');
  }
}
