import { <PERSON><PERSON><PERSON>, <PERSON>um<PERSON>, <PERSON>To<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { WaterfallSet } from './waterfall-set.entity';

@Entity()
export class WaterfallTrueFalseQuestion extends AuditableBaseEntity {
  @Column({ name: 'statement', type: 'text' })
  statement: string;

  @Column({ name: 'correct_answer' })
  correctAnswer: boolean;

  @Column({ name: 'set_id' })
  setId: string;

  @Column({ name: 'time_limit_in_seconds', nullable: true })
  timeLimitInSeconds: number;

  @Column({ name: 'level', nullable: true })
  level: number;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  // Relationship removed to allow both admin and tutor set IDs
  // @ManyToOne(() => WaterfallSet, (set) => set.questions, { onDelete: 'CASCADE' })
  // @JoinColumn({ name: 'set_id' })
  // set: WaterfallSet;
}