import { UserType } from '../../src/database/entities/user.entity';
import { TutorApprovalStatus } from '../../src/database/entities/tutor-approval.entity';
import { LoginUserDto, RegisterDto, ForgotPasswordDto, ResetPasswordDto, ChangePasswordDto } from '../../src/database/models/users.dto';

export class AuthTestDataFactory {
  static createMockUser(overrides: Partial<any> = {}) {
    return {
      id: '<test-user-id>',
      userId: '<test-user-001>',
      email: '<<EMAIL>>',
      name: '<test-user>',
      type: UserType.STUDENT,
      isActive: true,
      isConfirmed: true,
      userRoles: [{ role: { name: 'student' } }],
      verifyPassword: jest.fn().mockReturnValue(true),
      setPassword: jest.fn(),
      toDto: jest.fn().mockReturnValue({
        id: '<test-user-id>',
        userId: '<test-user-001>',
        email: '<<EMAIL>>',
        type: 'student',
      }),
      lastLoginAt: new Date(),
      refreshToken: '',
      refreshTokenExpiry: new Date(0),
      ...overrides,
    };
  }

  static createLoginDto(overrides: Partial<LoginUserDto> = {}): LoginUserDto {
    return {
      userId: '<test-user-001>',
      password: '<test-password>',
      ...overrides,
    };
  }

  static createRegisterDto(overrides: Partial<RegisterDto> = {}): RegisterDto {
    return {
      userId: '<test-student-001>',
      email: '<<EMAIL>>',
      password: '<test-password>',
      confirmPassword: '<test-password>',
      phoneNumber: '<test-phone>',
      gender: 'male',
      type: UserType.STUDENT,
      agreedToTerms: true,
      ...overrides,
    };
  }

  static createForgotPasswordDto(overrides: Partial<ForgotPasswordDto> = {}): ForgotPasswordDto {
    return {
      identifier: '<<EMAIL>>',
      ...overrides,
    };
  }

  static createResetPasswordDto(overrides: Partial<ResetPasswordDto> = {}): ResetPasswordDto {
    return {
      token: '<reset-token>',
      newPassword: '<new-password>',
      ...overrides,
    };
  }

  static createChangePasswordDto(overrides: Partial<ChangePasswordDto> = {}): ChangePasswordDto {
    return {
      currentPassword: '<current-password>',
      newPassword: '<new-password>',
      confirmNewPassword: '<new-password>',
      ...overrides,
    };
  }

  static createMockVerification(overrides: Partial<any> = {}) {
    return {
      token: '<verification-token>',
      userId: '<test-user-id>',
      expirationTime: new Date(Date.now() + 300000),
      isUsed: false,
      ...overrides,
    };
  }

  static createMockPasswordReset(overrides: Partial<any> = {}) {
    return {
      token: '<reset-token>',
      userId: '<test-user-id>',
      expirationTime: new Date(Date.now() + 300000),
      isUsed: false,
      ...overrides,
    };
  }

  static createMockTutorApproval(overrides: Partial<any> = {}) {
    return {
      userId: '<test-tutor-id>',
      status: TutorApprovalStatus.APPROVED,
      createdAt: new Date(),
      ...overrides,
    };
  }

  static createMockRequest(overrides: Partial<any> = {}) {
    return {
      user: { sub: '<test-user-id>' },
      ip: '<test-ip>',
      get: jest.fn().mockReturnValue('<test-user-agent>'),
      ...overrides,
    };
  }
}