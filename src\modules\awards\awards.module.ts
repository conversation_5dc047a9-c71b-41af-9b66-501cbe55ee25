import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AwardsService } from './awards.service';
import { AwardsController } from './awards.controller';
import { Award } from '../../database/entities/award.entity';
import { AwardWinner } from '../../database/entities/award-winner.entity';
import { RewardPoint } from '../../database/entities/reward-point.entity';
import { AwardJob } from '../../database/entities/award-job.entity';

import { User } from '../../database/entities/user.entity';
import { Notification } from '../../database/entities/notification.entity';
import { DiaryEntry } from '../../database/entities/diary-entry.entity';
import { NovelEntry } from '../../database/entities/novel-entry.entity';
import { MissionDiaryEntry } from '../../database/entities/mission-diary-entry.entity';
import { CommonModule } from '../../common/common.module';
import { JwtService } from '@nestjs/jwt';
import { AwardScheduler } from './award.scheduler';
import { AwardSchedulerController } from './award-scheduler.controller';
import { DiaryModule } from '../diary/diary.module';
import { EssayModule } from '../essay/essay.module';
import { NovelModule } from '../novel/novel.module';
import { EmailModule } from '../email/email.module';
import { AwardNotificationService } from './award-notification.service';
import { MissionDiaryAwardService } from '../diary/mission-diary-award.service';
import { AwardJobService } from './award-job.service';
import { AdminAwardJobController } from './admin-award-monitoring.controller';
import { AwardSummaryService } from './award-summary.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([Award, AwardWinner, RewardPoint, AwardJob, User, Notification, DiaryEntry, NovelEntry, MissionDiaryEntry]),
    CommonModule,
    EmailModule,
    forwardRef(() => DiaryModule),
    forwardRef(() => EssayModule),
    forwardRef(() => NovelModule),
  ],
  controllers: [AwardsController, AwardSchedulerController, AdminAwardJobController],
  providers: [
    {
      provide: AwardsService,
      useClass: AwardsService,
    },
    {
      provide: AwardScheduler,
      useClass: AwardScheduler,
    },
    {
      provide: AwardNotificationService,
      useClass: AwardNotificationService,
    },
    {
      provide: MissionDiaryAwardService,
      useClass: MissionDiaryAwardService,
    },
    {
      provide: AwardJobService,
      useClass: AwardJobService,
    },
    {
      provide: AwardSummaryService,
      useClass: AwardSummaryService,
    },
    JwtService,
  ],
  exports: [AwardsService, AwardScheduler, AwardNotificationService, MissionDiaryAwardService, AwardJobService, AwardSummaryService],
})
export class AwardsModule {}
