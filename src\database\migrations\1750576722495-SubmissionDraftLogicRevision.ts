import { MigrationInterface, QueryRunner } from 'typeorm';

export class SubmissionDraftLogicRevision1750576722495 implements MigrationInterface {
  name = 'SubmissionDraftLogicRevision1750576722495';

  public async up(queryRunner: QueryRunner): Promise<void> {
    console.log('Starting submission/draft logic revision migration...');

    // Add new fields to diary_entry table
    console.log('Adding new fields to diary_entry table...');
    await queryRunner.query(`
      ALTER TABLE "diary_entry"
      ADD COLUMN "is_draft" boolean DEFAULT true,
      ADD COLUMN "last_submitted_at" timestamp,
      ADD COLUMN "last_reviewed_at" timestamp,
      ADD COLUMN "can_submit_new_version" boolean DEFAULT true,
      ADD COLUMN "submitted_version_count" integer DEFAULT 0,
      ADD COLUMN "current_submitted_version_id" uuid,
      ADD COLUMN "is_resubmission" boolean DEFAULT false,
      ADD COLUMN "resubmission_type" varchar,
      ADD COLUMN "previous_review_count" integer DEFAULT 0,
      ADD COLUMN "previous_confirmation_count" integer DEFAULT 0
    `);

    // Add new fields to mission_diary_entry table
    console.log('Adding new fields to mission_diary_entry table...');
    await queryRunner.query(`
      ALTER TABLE "mission_diary_entry"
      ADD COLUMN "is_draft" boolean DEFAULT true,
      ADD COLUMN "last_submitted_at" timestamp,
      ADD COLUMN "last_reviewed_at" timestamp,
      ADD COLUMN "can_submit_new_version" boolean DEFAULT true,
      ADD COLUMN "submitted_version_count" integer DEFAULT 0,
      ADD COLUMN "current_submitted_version_id" uuid,
      ADD COLUMN "is_resubmission" boolean DEFAULT false,
      ADD COLUMN "resubmission_type" varchar,
      ADD COLUMN "previous_review_count" integer DEFAULT 0,
      ADD COLUMN "previous_confirmation_count" integer DEFAULT 0
    `);

    // Add new fields to novel_entry table
    console.log('Adding new fields to novel_entry table...');
    await queryRunner.query(`
      ALTER TABLE "novel_entry"
      ADD COLUMN "is_draft" boolean DEFAULT true,
      ADD COLUMN "last_submitted_at" timestamp,
      ADD COLUMN "last_reviewed_at" timestamp,
      ADD COLUMN "can_submit_new_version" boolean DEFAULT true,
      ADD COLUMN "submitted_version_count" integer DEFAULT 0,
      ADD COLUMN "current_submitted_version_id" uuid,
      ADD COLUMN "is_resubmission" boolean DEFAULT false,
      ADD COLUMN "resubmission_type" varchar,
      ADD COLUMN "previous_review_count" integer DEFAULT 0,
      ADD COLUMN "previous_confirmation_count" integer DEFAULT 0
    `);

    // Add new fields to diary_entry_history table
    console.log('Adding new fields to diary_entry_history table...');
    await queryRunner.query(`
      ALTER TABLE "diary_entry_history"
      ADD COLUMN "is_submitted_version" boolean DEFAULT false,
      ADD COLUMN "submission_number" integer,
      ADD COLUMN "submitted_at" timestamp,
      ADD COLUMN "is_resubmission" boolean DEFAULT false,
      ADD COLUMN "resubmission_type" varchar,
      ADD COLUMN "previous_status" varchar
    `);

    // Add new fields to mission_diary_entry_history table
    console.log('Adding new fields to mission_diary_entry_history table...');
    await queryRunner.query(`
      ALTER TABLE "mission_diary_entry_history"
      ADD COLUMN "is_submitted_version" boolean DEFAULT false,
      ADD COLUMN "submission_number" integer,
      ADD COLUMN "submitted_at" timestamp,
      ADD COLUMN "is_resubmission" boolean DEFAULT false,
      ADD COLUMN "resubmission_type" varchar,
      ADD COLUMN "previous_status" varchar
    `);

    // Add new fields to novel_entry_history table
    console.log('Adding new fields to novel_entry_history table...');
    await queryRunner.query(`
      ALTER TABLE "novel_entry_history"
      ADD COLUMN "is_submitted_version" boolean DEFAULT false,
      ADD COLUMN "submission_number" integer,
      ADD COLUMN "submitted_at" timestamp,
      ADD COLUMN "is_resubmission" boolean DEFAULT false,
      ADD COLUMN "resubmission_type" varchar,
      ADD COLUMN "previous_status" varchar
    `);

    // Add foreign key constraints
    console.log('Adding foreign key constraints...');
    await queryRunner.query(`
      ALTER TABLE "diary_entry"
      ADD CONSTRAINT "FK_diary_entry_current_submitted_version"
      FOREIGN KEY ("current_submitted_version_id")
      REFERENCES "diary_entry_history"("id") ON DELETE SET NULL
    `);

    await queryRunner.query(`
      ALTER TABLE "mission_diary_entry"
      ADD CONSTRAINT "FK_mission_diary_entry_current_submitted_version"
      FOREIGN KEY ("current_submitted_version_id")
      REFERENCES "mission_diary_entry_history"("id") ON DELETE SET NULL
    `);

    await queryRunner.query(`
      ALTER TABLE "novel_entry"
      ADD CONSTRAINT "FK_novel_entry_current_submitted_version"
      FOREIGN KEY ("current_submitted_version_id")
      REFERENCES "novel_entry_history"("id") ON DELETE SET NULL
    `);

    console.log('Migration completed successfully');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    console.log('Rolling back submission/draft logic revision migration...');

    // Remove foreign key constraints
    await queryRunner.query(`ALTER TABLE "novel_entry" DROP CONSTRAINT "FK_novel_entry_current_submitted_version"`);
    await queryRunner.query(`ALTER TABLE "mission_diary_entry" DROP CONSTRAINT "FK_mission_diary_entry_current_submitted_version"`);
    await queryRunner.query(`ALTER TABLE "diary_entry" DROP CONSTRAINT "FK_diary_entry_current_submitted_version"`);

    // Remove new fields from history tables
    await queryRunner.query(`
      ALTER TABLE "novel_entry_history"
      DROP COLUMN "previous_status",
      DROP COLUMN "resubmission_type",
      DROP COLUMN "is_resubmission",
      DROP COLUMN "submitted_at",
      DROP COLUMN "submission_number",
      DROP COLUMN "is_submitted_version"
    `);

    await queryRunner.query(`
      ALTER TABLE "mission_diary_entry_history"
      DROP COLUMN "previous_status",
      DROP COLUMN "resubmission_type",
      DROP COLUMN "is_resubmission",
      DROP COLUMN "submitted_at",
      DROP COLUMN "submission_number",
      DROP COLUMN "is_submitted_version"
    `);

    await queryRunner.query(`
      ALTER TABLE "diary_entry_history"
      DROP COLUMN "previous_status",
      DROP COLUMN "resubmission_type",
      DROP COLUMN "is_resubmission",
      DROP COLUMN "submitted_at",
      DROP COLUMN "submission_number",
      DROP COLUMN "is_submitted_version"
    `);

    // Remove new fields from main tables
    await queryRunner.query(`
      ALTER TABLE "novel_entry"
      DROP COLUMN "previous_confirmation_count",
      DROP COLUMN "previous_review_count",
      DROP COLUMN "resubmission_type",
      DROP COLUMN "is_resubmission",
      DROP COLUMN "current_submitted_version_id",
      DROP COLUMN "submitted_version_count",
      DROP COLUMN "can_submit_new_version",
      DROP COLUMN "last_reviewed_at",
      DROP COLUMN "last_submitted_at",
      DROP COLUMN "is_draft"
    `);

    await queryRunner.query(`
      ALTER TABLE "mission_diary_entry"
      DROP COLUMN "previous_confirmation_count",
      DROP COLUMN "previous_review_count",
      DROP COLUMN "resubmission_type",
      DROP COLUMN "is_resubmission",
      DROP COLUMN "current_submitted_version_id",
      DROP COLUMN "submitted_version_count",
      DROP COLUMN "can_submit_new_version",
      DROP COLUMN "last_reviewed_at",
      DROP COLUMN "last_submitted_at",
      DROP COLUMN "is_draft"
    `);

    await queryRunner.query(`
      ALTER TABLE "diary_entry"
      DROP COLUMN "previous_confirmation_count",
      DROP COLUMN "previous_review_count",
      DROP COLUMN "resubmission_type",
      DROP COLUMN "is_resubmission",
      DROP COLUMN "current_submitted_version_id",
      DROP COLUMN "submitted_version_count",
      DROP COLUMN "can_submit_new_version",
      DROP COLUMN "last_reviewed_at",
      DROP COLUMN "last_submitted_at",
      DROP COLUMN "is_draft"
    `);

    console.log('Rollback completed successfully');
  }
}
