import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddDiarySkinRegistryPerformanceIndexes1754544400000 implements MigrationInterface {
  name = 'AddDiarySkinRegistryPerformanceIndexes1754544400000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if indexes already exist before creating them
    
    // Add index for diary_skin_id lookups (used in DELETE operations)
    const diarySkinIdIndexExists = await queryRunner.query(`
      SELECT indexname
      FROM pg_indexes
      WHERE tablename='diary_skin_registry' AND indexname='IDX_diary_skin_registry_diary_skin_id'
    `);

    if (diarySkinIdIndexExists.length === 0) {
      await queryRunner.query(`
        CREATE INDEX "IDX_diary_skin_registry_diary_skin_id"
        ON "diary_skin_registry" ("diary_skin_id")
      `);
      console.log('Created index IDX_diary_skin_registry_diary_skin_id');
    } else {
      console.log('Index IDX_diary_skin_registry_diary_skin_id already exists, skipping');
    }

    // Add index for user_id lookups (used in user-specific queries)
    const userIdIndexExists = await queryRunner.query(`
      SELECT indexname
      FROM pg_indexes
      WHERE tablename='diary_skin_registry' AND indexname='IDX_diary_skin_registry_user_id'
    `);

    if (userIdIndexExists.length === 0) {
      await queryRunner.query(`
        CREATE INDEX "IDX_diary_skin_registry_user_id"
        ON "diary_skin_registry" ("user_id")
      `);
      console.log('Created index IDX_diary_skin_registry_user_id');
    } else {
      console.log('Index IDX_diary_skin_registry_user_id already exists, skipping');
    }

    // Add composite index for diary_skin_id and updated_at (used in cleanup operations)
    const compositeIndexExists = await queryRunner.query(`
      SELECT indexname
      FROM pg_indexes
      WHERE tablename='diary_skin_registry' AND indexname='IDX_diary_skin_registry_skin_updated'
    `);

    if (compositeIndexExists.length === 0) {
      await queryRunner.query(`
        CREATE INDEX "IDX_diary_skin_registry_skin_updated"
        ON "diary_skin_registry" ("diary_skin_id", "updated_at" DESC)
      `);
      console.log('Created index IDX_diary_skin_registry_skin_updated');
    } else {
      console.log('Index IDX_diary_skin_registry_skin_updated already exists, skipping');
    }

    console.log('✅ Diary skin registry performance indexes migration completed');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes in reverse order
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_diary_skin_registry_skin_updated"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_diary_skin_registry_user_id"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_diary_skin_registry_diary_skin_id"`);
    
    console.log('✅ Diary skin registry performance indexes rollback completed');
  }
}
