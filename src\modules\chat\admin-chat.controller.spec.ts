import { Test, TestingModule } from '@nestjs/testing';
import { AdminChatController } from './admin-chat.controller';
import { ChatService } from './chat.service';
import { AdminChatService } from './admin-chat.service';
import { User, UserType } from '../../database/entities/user.entity';
import { Conversation, ConversationType, ConversationStatus } from '../../database/entities/conversation.entity';
import { CreateMessageDto, ConversationFilterDto, MessageDto } from '../../database/models/chat.dto';
import { MessageType, MessageStatus } from '../../database/entities/message.entity';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { AdminGuard } from '../../common/guards/admin.guard';

describe('AdminChatController', () => {
  let controller: AdminChatController;
  let chatService: jest.Mocked<ChatService>;
  let adminChatService: jest.Mocked<AdminChatService>;

  const mockAdmin: User = {
    id: 'admin-id',
    userId: 'admin',
    name: 'Admin User',
    email: '<EMAIL>',
    type: UserType.ADMIN,
  } as User;

  const mockStudent: User = {
    id: 'student-id',
    userId: 'student',
    name: 'Student User',
    email: '<EMAIL>',
    type: UserType.STUDENT,
  } as User;

  const mockConversation: Conversation = {
    id: 'conversation-id',
    participant1Id: 'admin-id',
    participant2Id: 'student-id',
    type: ConversationType.DIRECT,
    status: ConversationStatus.ACTIVE,
    participant1: null,
    participant2: null,
    lastMessageAt: null,
    lastMessageText: null,
    lastMessageSenderId: null,
    participant1UnreadCount: 0,
    participant2UnreadCount: 0,
    createdAt: new Date(),
    updatedAt: new Date(),
  } as Conversation;

  const mockMessage: MessageDto = {
    id: 'message-id',
    conversationId: 'conversation-id',
    senderId: 'admin-id',
    recipientId: 'student-id',
    type: MessageType.TEXT,
    content: 'Test message',
    status: MessageStatus.SENT,
    createdAt: new Date(),
    senderName: 'Admin User',
    attachments: [],
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AdminChatController],
      providers: [
        {
          provide: ChatService,
          useValue: {
            getConversations: jest.fn(),
            getUserById: jest.fn(),
          },
        },
        {
          provide: AdminChatService,
          useValue: {
            getOrCreateAdminConversation: jest.fn(),
            sendAdminMessage: jest.fn(),
          },
        },
        {
          provide: 'AdminConversationManagerService',
          useValue: {
            getAdminConversationsForAdmin: jest.fn(),
          },
        },
        {
          provide: 'APP_GUARD',
          useValue: {
            canActivate: jest.fn().mockReturnValue(true),
          },
        },
      ],
    })
    .overrideGuard(JwtAuthGuard)
    .useValue({ canActivate: jest.fn().mockReturnValue(true) })
    .overrideGuard(AdminGuard)
    .useValue({ canActivate: jest.fn().mockReturnValue(true) })
    .compile();

    controller = module.get<AdminChatController>(AdminChatController);
    chatService = module.get(ChatService);
    adminChatService = module.get(AdminChatService);
  });

  describe('getAdminConversations', () => {
    it('should return admin conversations successfully', async () => {
      // Arrange
      const filter: ConversationFilterDto = { page: 1, limit: 10 };
      const mockResponse = {
        items: [{
          id: mockConversation.id,
          type: mockConversation.type,
          status: mockConversation.status,
          participant: {
            id: mockStudent.id,
            name: mockStudent.name,
            userId: mockStudent.userId,
            email: mockStudent.email,
            type: mockStudent.type,
            profilePicture: null,
            isOnline: false,
          },
          lastMessageAt: null,
          lastMessageText: null,
          unreadCount: 0,
          createdAt: new Date(),
          isAdminConversation: true,
        }],
        total: 1,
        page: 1,
        limit: 10,
      };
      const mockManagerService = {
        getAdminConversationsForAdmin: jest.fn().mockResolvedValue({
          conversations: [{ ...mockConversation, user: mockStudent, adminUnreadCount: 0 }],
          total: 1,
          page: 1,
          limit: 10
        })
      };
      (controller as any).adminConversationManagerService = mockManagerService;

      // Act
      const result = await controller.getAdminConversations(mockAdmin, filter);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data.items).toHaveLength(1);
      expect(result.message).toBe('Admin conversations retrieved successfully');
    });

    it('should handle empty conversations list', async () => {
      // Arrange
      const filter: ConversationFilterDto = { page: 1, limit: 10 };
      const mockResponse = {
        items: [],
        total: 0,
        page: 1,
        limit: 10,
      };
      const mockManagerService = {
        getAdminConversationsForAdmin: jest.fn().mockResolvedValue({
          conversations: [],
          total: 0,
          page: 1,
          limit: 10
        })
      };
      (controller as any).adminConversationManagerService = mockManagerService;

      // Act
      const result = await controller.getAdminConversations(mockAdmin, filter);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data.items).toHaveLength(0);
      expect(result.data.total).toBe(0);
    });
  });

  describe('createOrGetAdminConversation', () => {
    it('should create or get admin conversation successfully', async () => {
      // Arrange
      const targetUserId = 'student-id';
      const mockAdminConversation = { id: 'admin-conv-id', userId: targetUserId, lastMessageAt: null, lastMessageText: null, adminUnreadCount: 0, createdAt: new Date() };
      adminChatService.getOrCreateAdminConversation.mockResolvedValue(mockAdminConversation);
      chatService.getUserById.mockResolvedValue(mockStudent);

      // Act
      const result = await controller.createOrGetAdminConversation(mockAdmin, targetUserId);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data.id).toBe(`admin-${mockAdminConversation.id}`);
      expect(result.data.isAdminConversation).toBe(true);
      expect(result.message).toBe('Admin conversation created or retrieved successfully');
      expect(adminChatService.getOrCreateAdminConversation).toHaveBeenCalledWith(mockAdmin.id, targetUserId);
      expect(chatService.getUserById).toHaveBeenCalledWith(targetUserId);
    });

    it('should handle conversation creation for different user types', async () => {
      // Arrange
      const tutorId = 'tutor-id';
      const mockTutor = { ...mockStudent, id: tutorId, type: UserType.TUTOR } as User;
      const mockTutorConversation = { id: 'tutor-conv-id', userId: tutorId, lastMessageAt: null, lastMessageText: null, adminUnreadCount: 0, createdAt: new Date() };
      adminChatService.getOrCreateAdminConversation.mockResolvedValue(mockTutorConversation);
      chatService.getUserById.mockResolvedValue(mockTutor);

      // Act
      const result = await controller.createOrGetAdminConversation(mockAdmin, tutorId);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data.id).toBe(`admin-${mockTutorConversation.id}`);
      expect(result.data.participant.id).toBe(tutorId);
      expect(adminChatService.getOrCreateAdminConversation).toHaveBeenCalledWith(mockAdmin.id, tutorId);
      expect(chatService.getUserById).toHaveBeenCalledWith(tutorId);
    });
  });

  describe('sendAdminMessage', () => {
    it('should send admin message successfully', async () => {
      // Arrange
      const conversationId = 'conversation-id';
      const createMessageDto: CreateMessageDto = {
        recipientId: 'student-id',
        content: 'Test admin message',
        type: MessageType.TEXT,
      };
      adminChatService.sendAdminMessage.mockResolvedValue(mockMessage);

      // Act
      const result = await controller.sendAdminMessage(mockAdmin, conversationId, createMessageDto);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockMessage);
      expect(result.message).toBe('Admin message sent successfully');
      expect(adminChatService.sendAdminMessage).toHaveBeenCalledWith(mockAdmin.id, conversationId, createMessageDto);
    });

    it('should handle different message types', async () => {
      // Arrange
      const conversationId = 'conversation-id';
      const createMessageDto: CreateMessageDto = {
        recipientId: 'student-id',
        content: 'Test image message',
        type: MessageType.IMAGE,
        metadata: { imageUrl: 'https://example.com/image.jpg' },
      };
      const imageMessage = { ...mockMessage, type: MessageType.IMAGE, content: 'Test image message' };
      adminChatService.sendAdminMessage.mockResolvedValue(imageMessage);

      // Act
      const result = await controller.sendAdminMessage(mockAdmin, conversationId, createMessageDto);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data.type).toBe(MessageType.IMAGE);
      expect(adminChatService.sendAdminMessage).toHaveBeenCalledWith(mockAdmin.id, conversationId, createMessageDto);
    });

    it('should handle message with attachments', async () => {
      // Arrange
      const conversationId = 'conversation-id';
      const createMessageDto: CreateMessageDto = {
        recipientId: 'student-id',
        content: 'Message with attachment',
        type: MessageType.FILE,
        attachmentIds: ['attachment-1', 'attachment-2'],
      };
      const messageWithAttachments = {
        ...mockMessage,
        type: MessageType.FILE,
        attachments: [
          {
            id: 'attachment-1',
            fileName: 'file1.pdf',
            filePath: '/uploads/file1.pdf',
            mimeType: 'application/pdf',
            fileSize: 1024,
            fileUrl: 'http://localhost:3000/uploads/file1.pdf'
          },
          {
            id: 'attachment-2',
            fileName: 'file2.docx',
            filePath: '/uploads/file2.docx',
            mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            fileSize: 2048,
            fileUrl: 'http://localhost:3000/uploads/file2.docx'
          },
        ],
      };
      adminChatService.sendAdminMessage.mockResolvedValue(messageWithAttachments);

      // Act
      const result = await controller.sendAdminMessage(mockAdmin, conversationId, createMessageDto);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data.attachments).toHaveLength(2);
      expect(adminChatService.sendAdminMessage).toHaveBeenCalledWith(mockAdmin.id, conversationId, createMessageDto);
    });
  });

  describe('getUserConversations', () => {
    it('should get user conversations successfully', async () => {
      // Arrange
      const userId = 'student-id';
      const filter: ConversationFilterDto = { page: 1, limit: 10 };
      const mockResponse = {
        items: [{
          id: mockConversation.id,
          type: mockConversation.type,
          status: mockConversation.status,
          participant: {
            id: mockStudent.id,
            name: mockStudent.name,
            userId: mockStudent.userId,
            email: mockStudent.email,
            type: mockStudent.type,
            profilePicture: null,
            isOnline: false,
          },
          lastMessageAt: null,
          lastMessageText: null,
          unreadCount: 0,
          createdAt: new Date(),
        }],
        total: 1,
        page: 1,
        limit: 10,
      };
      chatService.getConversations.mockResolvedValue(mockResponse);

      // Act
      const result = await controller.getUserConversations(userId, filter);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockResponse);
      expect(result.message).toBe('Conversations retrieved successfully');
      expect(chatService.getConversations).toHaveBeenCalledWith(userId, filter);
    });

    it('should handle user with no conversations', async () => {
      // Arrange
      const userId = 'new-user-id';
      const filter: ConversationFilterDto = { page: 1, limit: 10 };
      const mockResponse = {
        items: [],
        total: 0,
        page: 1,
        limit: 10,
      };
      chatService.getConversations.mockResolvedValue(mockResponse);

      // Act
      const result = await controller.getUserConversations(userId, filter);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data.items).toHaveLength(0);
      expect(chatService.getConversations).toHaveBeenCalledWith(userId, filter);
    });
  });

  describe('error handling', () => {
    it('should handle service errors gracefully', async () => {
      // Arrange
      const filter: ConversationFilterDto = { page: 1, limit: 10 };
      const mockManagerService = {
        getAdminConversationsForAdmin: jest.fn().mockRejectedValue(new Error('Database error'))
      };
      (controller as any).adminConversationManagerService = mockManagerService;

      // Act & Assert
      await expect(controller.getAdminConversations(mockAdmin, filter)).rejects.toThrow('Database error');
    });

    it('should handle conversation creation errors', async () => {
      // Arrange
      const targetUserId = 'invalid-user-id';
      adminChatService.getOrCreateAdminConversation.mockRejectedValue(new Error('User not found'));

      // Act & Assert
      await expect(controller.createOrGetAdminConversation(mockAdmin, targetUserId)).rejects.toThrow('User not found');
    });

    it('should handle message sending errors', async () => {
      // Arrange
      const conversationId = 'invalid-conversation-id';
      const createMessageDto: CreateMessageDto = {
        recipientId: 'student-id',
        content: 'Test message',
        type: MessageType.TEXT,
      };
      adminChatService.sendAdminMessage.mockRejectedValue(new Error('Conversation not found'));

      // Act & Assert
      await expect(controller.sendAdminMessage(mockAdmin, conversationId, createMessageDto))
        .rejects.toThrow('Conversation not found');
    });
  });
});
