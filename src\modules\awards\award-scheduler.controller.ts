import { Controller, Post, Get, Query, UseGuards, ParseIntPipe, Body } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';
import { MoreThanOrEqual } from 'typeorm';
import { AwardScheduler } from './award.scheduler';
import { AwardJobService } from './award-job.service';
import { AwardSummaryService } from './award-summary.service';
import { AdminGuard } from '../../common/guards/admin.guard';
import { GetUser } from '../../common/decorators/get-user.decorator';
import { User } from '../../database/entities/user.entity';
import { AwardJobType, AwardJobStatus } from '../../database/entities/award-job.entity';
import { TriggerAwardJobDto } from '../../database/models/award-job.dto';

@ApiTags('Award Scheduler & Triggers')
@Controller('admin/awards/scheduler')
@UseGuards(AdminGuard)
@ApiBearerAuth('JWT-auth')
export class AwardSchedulerController {
  constructor(
    private readonly awardScheduler: AwardScheduler,
    private readonly awardJobService: AwardJobService,
    private readonly awardSummaryService: AwardSummaryService,
  ) {}

  /**
   * Get scheduler status and next run information
   */
  @Get('status')
  @ApiOperation({
    summary: 'Get award scheduler status (Admin only)',
    description: 'Returns the current status of the award scheduler including processing flags and schedule information. Requires admin authentication.',
  })
  @ApiResponse({
    status: 200,
    description: 'Scheduler status retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        weeklyProcessing: { type: 'boolean', example: false },
        monthlyProcessing: { type: 'boolean', example: false },
        quarterlyProcessing: { type: 'boolean', example: false },
        annualProcessing: { type: 'boolean', example: false },
        nextWeeklyRun: { type: 'string', example: 'Every Sunday at 00:30 UTC (Diary module only)' },
        nextMonthlyRun: { type: 'string', example: 'Every 1st of month at 02:00 UTC (All modules)' },
        nextQuarterlyRun: { type: 'string', example: 'Every 1st of quarter at 01:00 UTC (All modules)' },
        nextAnnualRun: { type: 'string', example: 'January 1st at 03:00 UTC (All modules)' },
        lastWeeklyRun: { type: 'string', format: 'date-time', nullable: true },
        lastMonthlyRun: { type: 'string', format: 'date-time', nullable: true },
        lastQuarterlyRun: { type: 'string', format: 'date-time', nullable: true },
        lastAnnualRun: { type: 'string', format: 'date-time', nullable: true },
        lastWeeklyPeriod: {
          type: 'object',
          properties: {
            startDate: { type: 'string', format: 'date-time' },
            endDate: { type: 'string', format: 'date-time' },
            weekStarting: { type: 'string', format: 'date' },
            weekEnding: { type: 'string', format: 'date' },
          },
        },
        lastMonthlyPeriod: {
          type: 'object',
          properties: {
            startDate: { type: 'string', format: 'date-time' },
            endDate: { type: 'string', format: 'date-time' },
            month: { type: 'number' },
            year: { type: 'number' },
          },
        },
        lastQuarterlyPeriod: {
          type: 'object',
          properties: {
            startDate: { type: 'string', format: 'date-time' },
            endDate: { type: 'string', format: 'date-time' },
            quarter: { type: 'number' },
            year: { type: 'number' },
          },
        },
        lastAnnualPeriod: {
          type: 'object',
          properties: {
            startDate: { type: 'string', format: 'date-time' },
            endDate: { type: 'string', format: 'date-time' },
            year: { type: 'number' },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized - Admin access required' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin role required' })
  getSchedulerStatus() {
    // Return the status directly without extra wrapping
    return this.awardScheduler.getStatus();
  }

  /**
   * Diagnostic endpoint to check award system health
   */
  @Get('diagnostic')
  @ApiOperation({
    summary: 'Get award system diagnostic information (Admin only)',
    description: 'Returns diagnostic information about the award system including database status, award counts, and recent activity.',
  })
  async getAwardDiagnostic() {
    return await this.awardScheduler.getDiagnosticInfo();
  }



  /**
   * Quick health check endpoint for monitoring
   */
  @Get('health')
  @ApiOperation({
    summary: 'Quick award system health check (Admin only)',
    description: 'Returns a quick health status of the award system for monitoring purposes.',
  })
  @ApiResponse({
    status: 200,
    description: 'Health check completed',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'healthy' },
        timestamp: { type: 'string', format: 'date-time' },
        summary: {
          type: 'object',
          properties: {
            totalAwards: { type: 'number' },
            activeAwards: { type: 'number' },
            recentWinners24h: { type: 'number' },
            recentActivity24h: { type: 'number' },
            dbResponseTime: { type: 'number' },
          },
        },
        issues: { type: 'array', items: { type: 'string' } },
        nextScheduledRun: { type: 'string' },
      },
    },
  })
  async getHealthCheck() {
    try {
      const startTime = Date.now();

      // Quick database check
      await this.awardScheduler['awardRepository'].query('SELECT 1');
      const dbResponseTime = Date.now() - startTime;

      // Get basic counts
      const totalAwards = await this.awardScheduler['awardRepository'].count();
      const activeAwards = await this.awardScheduler['awardRepository'].count({ where: { isActive: true } });

      const now = new Date();
      const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);

      const recentWinners24h = await this.awardScheduler['awardWinnerRepository'].count({
        where: { createdAt: MoreThanOrEqual(last24Hours) }
      });

      const recentActivity24h = await this.awardScheduler['diaryEntryRepository'].count({
        where: { createdAt: MoreThanOrEqual(last24Hours) }
      });

      // Calculate next scheduled run
      const nextSunday = new Date(now);
      nextSunday.setUTCDate(now.getUTCDate() + (7 - now.getUTCDay()) % 7);
      nextSunday.setUTCHours(0, 30, 0, 0);
      if (nextSunday <= now) {
        nextSunday.setUTCDate(nextSunday.getUTCDate() + 7);
      }

      // Identify issues
      const issues = [];
      if (activeAwards === 0) issues.push('No active awards configured');
      if (recentWinners24h === 0 && recentActivity24h > 0) issues.push('No recent winners despite user activity');
      if (recentActivity24h === 0) issues.push('No user activity in last 24 hours');
      if (dbResponseTime > 1000) issues.push(`Slow database response: ${dbResponseTime}ms`);

      const status = issues.length === 0 ? 'healthy' : issues.length <= 2 ? 'warning' : 'critical';

      return {
        status,
        timestamp: new Date().toISOString(),
        summary: {
          totalAwards,
          activeAwards,
          recentWinners24h,
          recentActivity24h,
          dbResponseTime,
        },
        issues,
        nextScheduledRun: nextSunday.toISOString(),
      };
    } catch (error) {
      return {
        status: 'error',
        timestamp: new Date().toISOString(),
        error: error.message,
        issues: ['Health check failed'],
      };
    }
  }

  /**
   * Simple trigger for any job type
   */
  @Post('trigger')
  @ApiOperation({
    summary: 'Trigger award calculation job (Admin only)',
    description: 'Triggers award generation for specified job type with tracking.',
  })
  async triggerJob(@Body() triggerDto: TriggerAwardJobDto, @GetUser() user: User) {
    const job = await this.awardJobService.createJob(triggerDto, user.id);
    
    this.executeJobAsync(job.id, triggerDto.jobType, async () => {
      switch (triggerDto.jobType) {
        case AwardJobType.WEEKLY:
          await this.awardScheduler.triggerWeeklyAwards();
          return this.getWeeklyPeriod();
        case AwardJobType.MONTHLY:
          await this.awardScheduler.triggerMonthlyAwards();
          return this.getMonthlyPeriod();
        case AwardJobType.MANUAL:
          await this.awardScheduler.triggerTestAwards();
          return this.getTestPeriod();
      }
    });
    
    return {
      success: true,
      message: `${triggerDto.jobType} award generation triggered successfully`,
      jobId: job.id,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Manually trigger award generation for testing
   */
  @Post('trigger/test')
  @ApiOperation({
    summary: 'Manually trigger award generation for testing (Admin only)',
    description: 'Triggers award generation for the last completed period to test the system immediately.',
  })
  async triggerTestAwards(@GetUser() user: User) {
    const job = await this.awardJobService.createJob({ jobType: AwardJobType.MANUAL }, user.id);
    
    this.executeJobAsync(job.id, AwardJobType.MANUAL, async () => {
      await this.awardScheduler.triggerTestAwards();
      return this.getTestPeriod();
    });
    
    return {
      success: true,
      message: 'Test award generation triggered successfully',
      jobId: job.id,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Manually trigger weekly award generation
   */
  @Post('trigger/weekly')
  @ApiOperation({
    summary: 'Manually trigger weekly award generation (Diary module only)',
    description:
      'Generates weekly awards for the diary module. If no parameters are provided, processes the previous week (Monday to Sunday). If parameters are provided, the day should be Sunday for week ending.',
  })
  @ApiQuery({ name: 'year', required: false, description: 'Year (defaults to current year or calculated based on other params)' })
  @ApiQuery({ name: 'month', required: false, description: 'Month 1-12 (defaults to current month or calculated based on other params)' })
  @ApiQuery({ name: 'day', required: false, description: 'Day (should be Sunday for week ending, defaults to previous week if no params provided)' })
  @ApiResponse({ status: 200, description: 'Weekly awards generated successfully' })
  @ApiResponse({ status: 400, description: 'Invalid parameters' })
  @ApiResponse({ status: 500, description: 'Error generating awards' })
  async triggerWeeklyAwards(
    @GetUser() user: User,
    @Query('year', new ParseIntPipe({ optional: true })) year?: number,
    @Query('month', new ParseIntPipe({ optional: true })) month?: number,
    @Query('day', new ParseIntPipe({ optional: true })) day?: number,
  ) {
    const job = await this.awardJobService.createJob({ jobType: AwardJobType.WEEKLY }, user.id);
    
    this.executeJobAsync(job.id, AwardJobType.WEEKLY, async () => {
      await this.awardScheduler.triggerWeeklyAwards(year, month, day);
      return this.getWeeklyPeriod(year, month, day);
    });
    
    const targetDate = new Date(Date.UTC(year || new Date().getFullYear(), (month || new Date().getMonth() + 1) - 1, day || new Date().getDate()));
    return {
      message: `Weekly awards generation triggered for week ending ${targetDate.toISOString().slice(0, 10)}`,
      jobId: job.id,
      period: {
        year: year || new Date().getFullYear(),
        month: month || new Date().getMonth() + 1,
        day: day || new Date().getDate(),
        weekEnding: targetDate.toISOString().slice(0, 10),
      },
    };
  }

  /**
   * Manually trigger quarterly award generation
   */
  @Post('trigger/quarterly')
  @ApiOperation({
    summary: 'Manually trigger quarterly award generation',
    description: 'Generates quarterly awards for all modules. If no parameters are provided, processes the previous quarter. Smart year adjustment handles cross-year quarters automatically.',
  })
  @ApiQuery({ name: 'year', required: false, description: 'Year (defaults intelligently based on quarter and current date)' })
  @ApiQuery({ name: 'quarter', required: false, description: 'Quarter 1-4 (defaults to previous quarter)' })
  @ApiResponse({ status: 200, description: 'Quarterly awards generated successfully' })
  @ApiResponse({ status: 400, description: 'Invalid parameters' })
  @ApiResponse({ status: 500, description: 'Error generating awards' })
  async triggerQuarterlyAwards(
    @GetUser() user: User,
    @Query('year', new ParseIntPipe({ optional: true })) year?: number, 
    @Query('quarter', new ParseIntPipe({ optional: true })) quarter?: number
  ) {
    const job = await this.awardJobService.createJob({ jobType: AwardJobType.MANUAL }, user.id);
    
    this.executeJobAsync(job.id, AwardJobType.MANUAL, async () => {
      await this.awardScheduler.triggerQuarterlyAwards(year, quarter);
      return this.getQuarterlyPeriod(year, quarter);
    });
    
    const currentQuarter = Math.floor(new Date().getMonth() / 3) + 1;
    const targetQuarter = quarter || (currentQuarter === 1 ? 4 : currentQuarter - 1);
    const targetYear = year || new Date().getFullYear();

    return {
      message: `Quarterly awards generation triggered for Q${targetQuarter}/${targetYear}`,
      jobId: job.id,
      period: {
        year: targetYear,
        quarter: targetQuarter,
      },
    };
  }

  /**
   * Manually trigger monthly award generation
   */
  @Post('trigger/monthly')
  @ApiOperation({
    summary: 'Manually trigger monthly award generation',
    description: 'Generates monthly awards for all modules. If no parameters are provided, processes the previous month. Smart year adjustment handles cross-year months automatically.',
  })
  @ApiQuery({ name: 'year', required: false, description: 'Year (defaults intelligently based on month and current date)' })
  @ApiQuery({ name: 'month', required: false, description: 'Month 1-12 (defaults to previous month)' })
  @ApiResponse({ status: 200, description: 'Monthly awards generated successfully' })
  @ApiResponse({ status: 400, description: 'Invalid parameters' })
  @ApiResponse({ status: 500, description: 'Error generating awards' })
  async triggerMonthlyAwards(
    @GetUser() user: User,
    @Query('year', new ParseIntPipe({ optional: true })) year?: number, 
    @Query('month', new ParseIntPipe({ optional: true })) month?: number
  ) {
    const job = await this.awardJobService.createJob({ jobType: AwardJobType.MONTHLY }, user.id);
    
    this.executeJobAsync(job.id, AwardJobType.MONTHLY, async () => {
      await this.awardScheduler.triggerMonthlyAwards(year, month);
      return this.getMonthlyPeriod(year, month);
    });
    
    return {
      message: `Monthly awards generation triggered for ${month || 'previous month'}/${year || 'current year'}`,
      jobId: job.id,
      period: {
        year: year || new Date().getFullYear(),
        month: month || (new Date().getMonth() === 0 ? 12 : new Date().getMonth()),
      },
    };
  }

  /**
   * Manually trigger annual award generation
   */
  @Post('trigger/annual')
  @ApiOperation({ summary: 'Manually trigger annual award generation' })
  @ApiQuery({ name: 'year', required: false, description: 'Year (defaults to previous year)' })
  @ApiResponse({ status: 200, description: 'Annual awards generated successfully' })
  @ApiResponse({ status: 400, description: 'Invalid parameters' })
  @ApiResponse({ status: 500, description: 'Error generating awards' })
  async triggerAnnualAwards(
    @GetUser() user: User,
    @Query('year', new ParseIntPipe({ optional: true })) year?: number
  ) {
    const job = await this.awardJobService.createJob({ jobType: AwardJobType.MANUAL }, user.id);
    
    this.executeJobAsync(job.id, AwardJobType.MANUAL, async () => {
      await this.awardScheduler.triggerAnnualAwards(year);
      return this.getAnnualPeriod(year);
    });
    
    return {
      message: `Annual awards generation triggered for ${year || 'previous year'}`,
      jobId: job.id,
      period: {
        year: year || new Date().getFullYear() - 1,
      },
    };
  }

  private async executeJobAsync(
    jobId: string, 
    jobType: AwardJobType, 
    jobFunction: () => Promise<{ startDate: Date; endDate: Date; processedCount?: number }>
  ): Promise<void> {
    setImmediate(async () => {
      try {
        await this.awardJobService.updateJobStatus(jobId, AwardJobStatus.RUNNING);
        
        const result = await jobFunction();
        const processedCount = result.processedCount || (jobType === AwardJobType.WEEKLY ? 1 : 3);
        
        const summary = await this.awardSummaryService.generateSummary(
          jobType, result.startDate, result.endDate, processedCount
        );
        
        await this.awardJobService.updateJobStatus(
          jobId, AwardJobStatus.COMPLETED, processedCount, undefined, summary
        );
      } catch (error) {
        await this.awardJobService.updateJobStatus(
          jobId, AwardJobStatus.FAILED, 0, error.message
        );
      }
    });
  }

  private getWeeklyPeriod(year?: number, month?: number, day?: number) {
    const today = new Date();
    const endDate = year && month && day 
      ? new Date(Date.UTC(year, month - 1, day))
      : new Date(today.getTime() - 24 * 60 * 60 * 1000);
    const startDate = new Date(endDate.getTime() - 6 * 24 * 60 * 60 * 1000);
    return { startDate, endDate, processedCount: 1 };
  }

  private getMonthlyPeriod(year?: number, month?: number) {
    const today = new Date();
    const targetYear = year || today.getFullYear();
    const targetMonth = month || (today.getMonth() === 0 ? 12 : today.getMonth());
    const startDate = new Date(Date.UTC(targetYear, targetMonth - 1, 1));
    const endDate = new Date(Date.UTC(targetYear, targetMonth, 0));
    return { startDate, endDate, processedCount: 3 };
  }

  private getQuarterlyPeriod(year?: number, quarter?: number) {
    const today = new Date();
    const currentQuarter = Math.floor(today.getMonth() / 3) + 1;
    const targetQuarter = quarter || (currentQuarter === 1 ? 4 : currentQuarter - 1);
    const targetYear = year || today.getFullYear();
    const startDate = new Date(Date.UTC(targetYear, (targetQuarter - 1) * 3, 1));
    const endDate = new Date(Date.UTC(targetYear, targetQuarter * 3, 0));
    return { startDate, endDate, processedCount: 3 };
  }

  private getAnnualPeriod(year?: number) {
    const targetYear = year || new Date().getFullYear() - 1;
    const startDate = new Date(Date.UTC(targetYear, 0, 1));
    const endDate = new Date(Date.UTC(targetYear, 11, 31));
    return { startDate, endDate, processedCount: 3 };
  }

  private getTestPeriod() {
    const today = new Date();
    const endDate = new Date(today.getTime() - 24 * 60 * 60 * 1000);
    const startDate = new Date(endDate.getTime() - 30 * 24 * 60 * 60 * 1000);
    return { startDate, endDate, processedCount: 3 };
  }

}
