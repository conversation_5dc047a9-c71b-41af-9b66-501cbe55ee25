import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { AwardWinner } from '../../database/entities/award-winner.entity';
import { DiaryEntry, DiaryEntryStatus } from '../../database/entities/diary-entry.entity';
import { NovelEntry, NovelEntryStatus } from '../../database/entities/novel-entry.entity';
import { User } from '../../database/entities/user.entity';
import { AwardJobType } from '../../database/entities/award-job.entity';

export interface AwardSummaryData {
  totalParticipants: number;
  totalAwarded: number;
  awardsByModule: { [module: string]: number };
  participantsByModule: { [module: string]: number };
  noAwardReasons: string[];
}

@Injectable()
export class AwardSummaryService {
  private readonly logger = new Logger(AwardSummaryService.name);

  constructor(
    @InjectRepository(AwardWinner)
    private readonly awardWinnerRepository: Repository<AwardWinner>,
    @InjectRepository(DiaryEntry)
    private readonly diaryEntryRepository: Repository<DiaryEntry>,
    @InjectRepository(NovelEntry)
    private readonly novelEntryRepository: Repository<NovelEntry>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  async generateSummary(
    jobType: AwardJobType,
    startDate: Date,
    endDate: Date,
    processedCount: number
  ): Promise<string> {
    try {
      const summaryData = await this.collectSummaryData(startDate, endDate);
      return this.formatSummary(jobType, startDate, endDate, summaryData, processedCount);
    } catch (error) {
      this.logger.error(`Error generating award summary: ${error.message}`, error.stack);
      return `Award calculation completed with ${processedCount} modules processed. Summary generation failed: ${error.message}`;
    }
  }

  private async collectSummaryData(startDate: Date, endDate: Date): Promise<AwardSummaryData> {
    // Get award winners for this period
    const awardWinners = await this.awardWinnerRepository
      .createQueryBuilder('winner')
      .leftJoinAndSelect('winner.award', 'award')
      .where('winner.createdAt BETWEEN :startDate AND :endDate', { startDate, endDate })
      .getMany();

    // Get diary participants (reviewed entries)
    const diaryParticipants = await this.diaryEntryRepository
      .createQueryBuilder('entry')
      .select('DISTINCT entry.userId', 'userId')
      .where('entry.status = :status', { status: DiaryEntryStatus.REVIEWED })
      .andWhere('entry.createdAt BETWEEN :startDate AND :endDate', { startDate, endDate })
      .getRawMany();

    // Get novel participants (reviewed entries)
    const novelParticipants = await this.novelEntryRepository
      .createQueryBuilder('entry')
      .select('DISTINCT entry.userId', 'userId')
      .where('entry.status = :status', { status: NovelEntryStatus.REVIEWED })
      .andWhere('entry.createdAt BETWEEN :startDate AND :endDate', { startDate, endDate })
      .getRawMany();

    // Count total diary entries
    const totalDiaryEntries = await this.diaryEntryRepository.count({
      where: {
        status: DiaryEntryStatus.REVIEWED,
        createdAt: Between(startDate, endDate),
      },
    });

    // Count total novel entries
    const totalNovelEntries = await this.novelEntryRepository.count({
      where: {
        status: NovelEntryStatus.REVIEWED,
        createdAt: Between(startDate, endDate),
      },
    });

    // Calculate statistics
    const uniqueParticipants = new Set([
      ...diaryParticipants.map(p => p.userId),
      ...novelParticipants.map(p => p.userId),
    ]);

    const awardsByModule = awardWinners.reduce((acc, winner) => {
      const module = winner.award.module;
      acc[module] = (acc[module] || 0) + 1;
      return acc;
    }, {} as { [module: string]: number });

    const participantsByModule = {
      diary: diaryParticipants.length,
      novel: novelParticipants.length,
    };

    // Determine reasons for no awards
    const noAwardReasons = [];
    if (uniqueParticipants.size === 0) {
      noAwardReasons.push('No eligible participants found (no reviewed entries)');
    }
    if (totalDiaryEntries === 0 && totalNovelEntries === 0) {
      noAwardReasons.push('No content submissions during this period');
    }
    if (uniqueParticipants.size > 0 && awardWinners.length === 0) {
      noAwardReasons.push('Participants found but no awards criteria met');
    }

    return {
      totalParticipants: uniqueParticipants.size,
      totalAwarded: awardWinners.length,
      awardsByModule,
      participantsByModule,
      noAwardReasons,
    };
  }

  private formatSummary(
    jobType: AwardJobType,
    startDate: Date,
    endDate: Date,
    data: AwardSummaryData,
    processedCount: number
  ): string {
    const period = this.formatPeriod(jobType, startDate, endDate);
    const lines = [];

    lines.push(`${jobType.toUpperCase()} AWARD CALCULATION SUMMARY`);
    lines.push(`Period: ${period}`);
    lines.push(`Modules Processed: ${processedCount}`);
    lines.push('');

    // Participation summary
    lines.push('PARTICIPATION:');
    lines.push(`• Total Participants: ${data.totalParticipants}`);
    
    if (Object.keys(data.participantsByModule).length > 0) {
      Object.entries(data.participantsByModule).forEach(([module, count]) => {
        if (count > 0) {
          lines.push(`• ${module.charAt(0).toUpperCase() + module.slice(1)} Participants: ${count}`);
        }
      });
    }
    lines.push('');

    // Awards summary
    lines.push('AWARDS DISTRIBUTED:');
    lines.push(`• Total Awards: ${data.totalAwarded}`);
    
    if (Object.keys(data.awardsByModule).length > 0) {
      Object.entries(data.awardsByModule).forEach(([module, count]) => {
        lines.push(`• ${module.charAt(0).toUpperCase() + module.slice(1)} Awards: ${count}`);
      });
    } else if (data.totalParticipants > 0) {
      lines.push('• No awards distributed this period');
    }
    lines.push('');

    // Reasons for no awards (if applicable)
    if (data.noAwardReasons.length > 0) {
      lines.push('NOTES:');
      data.noAwardReasons.forEach(reason => {
        lines.push(`• ${reason}`);
      });
      lines.push('');
    }

    // Success rate
    if (data.totalParticipants > 0) {
      const successRate = Math.round((data.totalAwarded / data.totalParticipants) * 100);
      lines.push(`Award Rate: ${successRate}% (${data.totalAwarded}/${data.totalParticipants} participants)`);
    }

    return lines.join('\n');
  }

  private formatPeriod(jobType: AwardJobType, startDate: Date, endDate: Date): string {
    const start = startDate.toISOString().split('T')[0];
    const end = endDate.toISOString().split('T')[0];
    
    switch (jobType) {
      case AwardJobType.WEEKLY:
        return `Week of ${start} to ${end}`;
      case AwardJobType.MONTHLY:
        const month = startDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
        return `${month} (${start} to ${end})`;
      case AwardJobType.MANUAL:
        return `Manual calculation: ${start} to ${end}`;
      default:
        return `${start} to ${end}`;
    }
  }
}