import { MigrationInterface, QueryRunner } from 'typeorm';

export class FixDiaryEntrySkinConstraints1692439760000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // First drop the existing constraint
    try {
      await queryRunner.query(`
        ALTER TABLE "diary_entry" DROP CONSTRAINT IF EXISTS "CHK_diary_entry_skin_exclusivity";
      `);
      console.log('✓ Dropped old skin exclusivity constraint');
    } catch (error) {
      console.log('No existing check constraint to drop');
    }

    // Ensure skin_type column exists and has correct check constraint
    const hasSkinType = await queryRunner.hasColumn('diary_entry', 'skin_type');
    if (!hasSkinType) {
      await queryRunner.query(`
        ALTER TABLE "diary_entry"
        ADD COLUMN "skin_type" character varying DEFAULT 'global';

        ALTER TABLE "diary_entry"
        ADD CONSTRAINT "CHK_diary_entry_skin_type"
        CHECK (skin_type IN ('global', 'student'));
      `);
      console.log('✓ Added skin_type column with check constraint');
    }

    // Update skin_type based on existing data
    await queryRunner.query(`
      UPDATE "diary_entry"
      SET skin_type = CASE
        WHEN skin_id IS NOT NULL THEN 'global'
        WHEN student_skin_id IS NOT NULL THEN 'student'
        ELSE 'global'
      END;
    `);
    console.log('✓ Updated skin_type values based on existing data');

    // Clean up inconsistent data
    await queryRunner.query(`
      -- Clear student_skin_id where global skin is set
      UPDATE "diary_entry"
      SET student_skin_id = NULL
      WHERE skin_id IS NOT NULL;

      -- Clear skin_id where student skin is set
      UPDATE "diary_entry"
      SET skin_id = NULL
      WHERE student_skin_id IS NOT NULL;
    `);
    console.log('✓ Cleaned up inconsistent skin data');

    // Add the new improved check constraint
    await queryRunner.query(`
      ALTER TABLE "diary_entry"
      ADD CONSTRAINT "CHK_diary_entry_skin_exclusivity"
      CHECK (
        (skin_type = 'global' AND skin_id IS NOT NULL AND student_skin_id IS NULL) OR
        (skin_type = 'student' AND skin_id IS NULL AND student_skin_id IS NOT NULL) OR
        (skin_id IS NULL AND student_skin_id IS NULL)
      );
    `);
    console.log('✓ Added new improved skin exclusivity constraint');

    // Add helpful indexes
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_diary_entry_skin_type" ON "diary_entry"("skin_type");
      CREATE INDEX IF NOT EXISTS "IDX_diary_entry_skin_id" ON "diary_entry"("skin_id");
      CREATE INDEX IF NOT EXISTS "IDX_diary_entry_student_skin_id" ON "diary_entry"("student_skin_id");
    `);
    console.log('✓ Added performance indexes for skin-related columns');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes first
    await queryRunner.query(`
      DROP INDEX IF EXISTS "IDX_diary_entry_skin_type";
      DROP INDEX IF EXISTS "IDX_diary_entry_skin_id";
      DROP INDEX IF EXISTS "IDX_diary_entry_student_skin_id";
    `);

    // Drop the new constraint
    await queryRunner.query(`
      ALTER TABLE "diary_entry" DROP CONSTRAINT IF EXISTS "CHK_diary_entry_skin_exclusivity";
    `);

    // Drop the skin type check constraint if it exists
    await queryRunner.query(`
      ALTER TABLE "diary_entry" DROP CONSTRAINT IF EXISTS "CHK_diary_entry_skin_type";
    `);

    // Note: We don't drop the skin_type column to preserve data,
    // but we remove its check constraint
  }
}
