# Transcription Module - Frontend Integration Guide

## Overview

This guide provides frontend developers with the necessary information to integrate the Transcription module, which enables English sentence copying practice using classic children's books.

## UI Flows for All Users

### Student UI Flow Components

#### Flow 1: Entry to Practice
```
Dashboard Card → Book Selection → Practice Interface → Results
```

#### Flow 2: Progress Monitoring
```
Progress Tab → Session History → Detailed Analytics
```

### Admin UI Flow Components

#### Flow 1: Content Management
```
Admin Dashboard → Add Book → Extract Sentences → Review & Approve
```

#### Flow 2: System Monitoring
```
Analytics Dashboard → Usage Reports → Error Pattern Analysis
```

### Tutor UI Flow Components

#### Flow 1: Student Monitoring
```
Student List → Individual Progress → Error Analysis → Feedback
```

#### Flow 2: Class Overview
```
Class Dashboard → Comparative Analysis → Recommendations
```

## User Interface Components

### 1. TranscriptionHome Component

**Purpose**: Main entry point for transcription practice

**API Calls**:
```typescript
// Get available books
GET /transcription/books

// Get student progress
GET /transcription/my-progress
```

**UI Elements**:
- Book selection grid
- Recent session summary
- Progress overview
- "Start Practice" button

### 2. BookSelection Component

**Purpose**: Display available classic books for practice

**API Integration**:
```typescript
const fetchBooks = async () => {
  const response = await fetch('/api/transcription/books', {
    headers: { 'Authorization': `Bearer ${token}` }
  });
  return response.json();
};
```

**UI Elements**:
- Book cards with title, author, publication year
- Sentence count display
- Book cover images (if available)
- Selection buttons

### 3. SentencePractice Component

**Purpose**: Core sentence copying interface

**API Workflow**:
```typescript
// 1. Start session
const startSession = async (bookId: string) => {
  const response = await fetch('/api/transcription/sessions', {
    method: 'POST',
    headers: { 
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ bookId })
  });
  return response.json();
};

// 2. Get sentences
const getSentences = async (bookId: string) => {
  const response = await fetch(`/api/transcription/books/${bookId}/sentences`, {
    headers: { 'Authorization': `Bearer ${token}` }
  });
  return response.json();
};

// 3. Submit attempt
const submitAttempt = async (sessionId: string, attempt: AttemptData) => {
  const response = await fetch(`/api/transcription/sessions/${sessionId}/attempts`, {
    method: 'POST',
    headers: { 
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(attempt)
  });
  return response.json();
};
```

**UI Elements**:
- Original sentence display
- Text input area for copying
- Real-time character comparison
- Error highlighting
- Progress indicator
- Timer (optional)

### 4. FeedbackDisplay Component

**Purpose**: Show immediate feedback on copying attempts

**Data Structure**:
```typescript
interface AttemptResponse {
  id: string;
  isCorrect: boolean;
  errors: Array<{
    type: 'spelling' | 'punctuation' | 'capitalization';
    position: number;
    expected: string;
    actual: string;
  }>;
  timeSpentSeconds: number;
  sentence: {
    content: string;
    difficultyLevel: string;
    grammarPattern: string;
  };
}
```

**UI Elements**:
- Side-by-side comparison
- Error highlighting with corrections
- Success/error indicators
- Grammar pattern information
- "Try Again" / "Next Sentence" buttons

### 5. ProgressView Component

**Purpose**: Display student progress and statistics

**API Integration**:
```typescript
const getProgress = async () => {
  const response = await fetch('/api/transcription/my-progress', {
    headers: { 'Authorization': `Bearer ${token}` }
  });
  return response.json();
};
```

**UI Elements**:
- Session history list
- Accuracy statistics
- Time spent tracking
- Grammar patterns practiced
- Achievement indicators

## State Management

### React State Structure

```typescript
interface TranscriptionState {
  // Book data
  books: Book[];
  selectedBook: Book | null;
  sentences: Sentence[];
  
  // Session data
  currentSession: Session | null;
  currentSentenceIndex: number;
  
  // Practice data
  userInput: string;
  isSubmitting: boolean;
  lastAttempt: AttemptResponse | null;
  
  // Progress data
  sessions: Session[];
  totalAccuracy: number;
  practiceTime: number;
}
```

### Redux Actions (if using Redux)

```typescript
// Book actions
const FETCH_BOOKS_REQUEST = 'FETCH_BOOKS_REQUEST';
const FETCH_BOOKS_SUCCESS = 'FETCH_BOOKS_SUCCESS';
const SELECT_BOOK = 'SELECT_BOOK';

// Session actions
const START_SESSION_REQUEST = 'START_SESSION_REQUEST';
const START_SESSION_SUCCESS = 'START_SESSION_SUCCESS';
const SUBMIT_ATTEMPT_REQUEST = 'SUBMIT_ATTEMPT_REQUEST';
const SUBMIT_ATTEMPT_SUCCESS = 'SUBMIT_ATTEMPT_SUCCESS';

// Progress actions
const FETCH_PROGRESS_SUCCESS = 'FETCH_PROGRESS_SUCCESS';
```

## Real-time Validation

### Character-by-Character Comparison

```typescript
const validateInput = (original: string, input: string) => {
  const errors = [];
  const originalChars = original.split('');
  const inputChars = input.split('');
  
  for (let i = 0; i < Math.max(originalChars.length, inputChars.length); i++) {
    const originalChar = originalChars[i] || '';
    const inputChar = inputChars[i] || '';
    
    if (originalChar !== inputChar) {
      errors.push({
        position: i,
        expected: originalChar,
        actual: inputChar,
        type: getErrorType(originalChar, inputChar)
      });
    }
  }
  
  return errors;
};
```

### Visual Feedback Implementation

```typescript
const renderTextWithHighlighting = (original: string, input: string) => {
  const errors = validateInput(original, input);
  
  return original.split('').map((char, index) => {
    const hasError = errors.some(error => error.position === index);
    const className = hasError ? 'error-highlight' : 'correct-highlight';
    
    return (
      <span key={index} className={className}>
        {char}
      </span>
    );
  });
};
```

## Error Handling

### API Error Responses

```typescript
const handleApiError = (error: any) => {
  switch (error.status) {
    case 401:
      // Redirect to login
      break;
    case 403:
      // Show permission denied message
      break;
    case 404:
      // Show not found message
      break;
    case 500:
      // Show server error message
      break;
    default:
      // Show generic error message
  }
};
```

### Network Error Handling

```typescript
const apiCall = async (url: string, options: RequestInit) => {
  try {
    const response = await fetch(url, options);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    if (error instanceof TypeError) {
      // Network error
      throw new Error('Network connection failed');
    }
    throw error;
  }
};
```

## Performance Optimization

### Debounced Input Validation

```typescript
import { debounce } from 'lodash';

const debouncedValidation = debounce((input: string, original: string) => {
  const errors = validateInput(original, input);
  setValidationErrors(errors);
}, 300);
```

### Lazy Loading

```typescript
const LazyProgressView = lazy(() => import('./ProgressView'));
const LazyBookSelection = lazy(() => import('./BookSelection'));
```

### Caching Strategy

```typescript
// Cache books data
const booksCache = new Map();

const fetchBooksWithCache = async () => {
  if (booksCache.has('books')) {
    return booksCache.get('books');
  }
  
  const books = await fetchBooks();
  booksCache.set('books', books);
  return books;
};
```

## Accessibility Features

### Keyboard Navigation

```typescript
const handleKeyDown = (event: KeyboardEvent) => {
  switch (event.key) {
    case 'Enter':
      if (event.ctrlKey) {
        submitAttempt();
      }
      break;
    case 'Escape':
      clearInput();
      break;
    case 'Tab':
      if (event.shiftKey) {
        focusPreviousElement();
      } else {
        focusNextElement();
      }
      break;
  }
};
```

### Screen Reader Support

```jsx
<div role="main" aria-label="Sentence copying practice">
  <div aria-live="polite" aria-atomic="true">
    {feedbackMessage}
  </div>
  
  <label htmlFor="sentence-input">
    Type the sentence shown above:
  </label>
  <textarea
    id="sentence-input"
    aria-describedby="sentence-original"
    value={userInput}
    onChange={handleInputChange}
  />
</div>
```

## Mobile Responsiveness

### Touch-Friendly Interface

```css
.practice-button {
  min-height: 44px;
  min-width: 44px;
  padding: 12px 16px;
}

.text-input {
  font-size: 16px; /* Prevents zoom on iOS */
  padding: 12px;
}
```

### Responsive Layout

```css
@media (max-width: 768px) {
  .sentence-display {
    font-size: 18px;
    line-height: 1.5;
    margin-bottom: 20px;
  }
  
  .practice-area {
    flex-direction: column;
  }
}
```

## Testing Integration

### Unit Tests

```typescript
describe('TranscriptionPractice', () => {
  test('validates input correctly', () => {
    const original = "Alice was beginning to get very tired.";
    const input = "Alice was begining to get very tired.";
    
    const errors = validateInput(original, input);
    
    expect(errors).toHaveLength(1);
    expect(errors[0].type).toBe('spelling');
    expect(errors[0].expected).toBe('beginning');
    expect(errors[0].actual).toBe('begining');
  });
});
```

### Integration Tests

```typescript
describe('Transcription API Integration', () => {
  test('completes full practice session', async () => {
    // Start session
    const session = await startSession(bookId);
    expect(session.id).toBeDefined();
    
    // Submit attempt
    const attempt = await submitAttempt(session.id, attemptData);
    expect(attempt.isCorrect).toBe(true);
    
    // Check progress
    const progress = await getProgress();
    expect(progress.sessions).toContain(session.id);
  });
});
```

This integration guide provides frontend developers with all necessary information to implement the transcription module UI components while maintaining consistency with the existing HEC application design patterns.