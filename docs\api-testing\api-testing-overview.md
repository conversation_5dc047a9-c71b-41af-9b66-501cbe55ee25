# API Testing Flow

This document outlines the testing flow for the HEC backend APIs.

## Testing Approach

Our API testing approach follows these principles:

1. **Comprehensive Coverage**: Test all API endpoints and their various response scenarios
2. **Isolation**: Test each endpoint in isolation to identify specific issues
3. **Integration**: Test related endpoints together to ensure they work as a cohesive system
4. **Authentication**: Test with different user roles to ensure proper authorization
5. **Edge Cases**: Test boundary conditions and error handling

## Testing Environment Setup

1. Ensure you have access to the testing environment
2. Set up <PERSON>man or your preferred API testing tool
3. Import the HEC API collection (available from the development team)
4. Configure environment variables for different testing scenarios

## General Testing Flow

For each API endpoint, follow this general testing flow:

1. **Authentication Testing**
   - Test with valid authentication token
   - Test with invalid/expired token
   - Test with insufficient permissions

2. **Input Validation Testing**
   - Test with valid input parameters
   - Test with missing required parameters
   - Test with invalid parameter formats
   - Test with boundary values (min/max)

3. **Response Validation Testing**
   - Verify correct HTTP status codes
   - Verify response structure matches API documentation
   - Verify data integrity and consistency

4. **Error Handling Testing**
   - Verify appropriate error messages for invalid inputs
   - Verify system handles exceptions gracefully

5. **Performance Testing**
   - Verify response times are within acceptable limits
   - Test with larger data sets where applicable

## Module-Specific Testing Flows

### Core API Endpoints Structure

All API endpoints in the HEC backend follow these base paths (no `/api` prefix):

- **Authentication**: `/auth/*`
- **Users**: `/users/*`
- **Plans**: `/plans/*`
- **Diary**: `/diary/*`
- **Play Modules**: `/play/*`
- **Shop**: `/shop/*`
- **Notifications**: `/notifications/*`

### Individual Module Testing
The following sections outline the specific testing flows for individual modules:

#### Core Modules
- [Authentication API Testing](authentication-api-testing.md)
- [Users API Testing](users-api-testing.md)
- [Plans API Testing](plans-api-testing.md)
- [**Diary API Testing**](diary-api-testing.md) - **UPDATED**: Complete diary module with skin management, version history, and cover photos
- [Shop API Testing](shop-api-testing.md)
- [Notification API Testing](notification-api-testing.md)

#### Interactive Modules
- [**Play Module API Testing**](play-module-api-testing.md) - **UPDATED**: Story Maker and Block Game testing with correct endpoints

#### Management Modules
- [Student Friendship API Testing](student-friendship-api-testing.md)
- [Tutor Assignment API Testing](tutor-assignment-api-testing.md)

### Key Updates Made

1. **Endpoint Corrections**: Removed incorrect `/api` prefix from all endpoints
2. **Missing Endpoints Added**: 
   - Diary skin management (`/diary/skins/*`)
   - Version history (`/diary/entries/{id}/history`, `/diary/entries/{id}/versions/*`)
   - Cover photo management (`/diary/cover-photo`)
   - Entry search and filtering
   - Friend sharing functionality
3. **Story Maker Endpoints**: All endpoints use `/play/story-maker/play/*` structure
4. **Block Game Endpoints**: All endpoints use `/play/block/*` structure
5. **Response Structure**: Updated to match actual ApiResponse wrapper format

## Test Reporting

After completing the tests for each module:

1. Document any issues found with clear steps to reproduce
2. Categorize issues by severity (Critical, High, Medium, Low)
3. Include environment details and test data used
4. Submit issues to the development team through the designated tracking system

## Regression Testing

When new features are added or bugs are fixed:

1. Rerun tests for the affected module
2. Verify that the fix resolves the reported issue
3. Verify that the fix doesn't introduce new issues
4. Update test documentation if the expected behavior has changed
