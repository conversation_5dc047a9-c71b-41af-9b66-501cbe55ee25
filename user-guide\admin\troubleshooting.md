# Administrator Troubleshooting Guide

## 🚨 Emergency Response Procedures

### Critical System Issues

#### Platform Down/Inaccessible
**Immediate Actions (First 5 minutes):**
1. **Check System Status**
   ```bash
   # Check server status
   curl -I https://hecplatform.com/health
   
   # Check database connectivity
   pg_isready -h localhost -p 5432
   
   # Check application logs
   tail -f /var/log/hec/application.log
   ```

2. **Verify Infrastructure**
   - AWS/Server status dashboard
   - CDN status (CloudFront/CloudFlare)
   - Database server health
   - Load balancer status

3. **Communication Protocol**
   - Post status update on status page
   - Send notification to all admins
   - Prepare user communication
   - Contact technical support team

#### Database Connection Issues
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Check connection pool
SELECT count(*) FROM pg_stat_activity;

# Check for blocking queries
SELECT * FROM pg_stat_activity WHERE state = 'active';

# Restart database if needed (last resort)
sudo systemctl restart postgresql
```

### Performance Degradation

#### High Response Times
**Diagnostic Steps:**
1. **Check System Resources**
   ```bash
   # CPU usage
   top -p $(pgrep -d',' node)
   
   # Memory usage
   free -h
   
   # Disk I/O
   iostat -x 1
   
   # Network connections
   netstat -an | grep :3000 | wc -l
   ```

2. **Application Performance**
   ```bash
   # Check slow queries
   SELECT query, mean_time, calls 
   FROM pg_stat_statements 
   ORDER BY mean_time DESC LIMIT 10;
   
   # Check API response times
   grep "Response time" /var/log/hec/api.log | tail -100
   ```

3. **Immediate Mitigation**
   - Enable maintenance mode if necessary
   - Scale up server resources
   - Clear application cache
   - Restart application services

## 👥 User Management Issues

### Login Problems

#### Users Cannot Login
**Common Causes & Solutions:**

1. **Password Issues**
   ```javascript
   // Reset user password (Admin API)
   POST /api/admin/users/{userId}/reset-password
   {
     "send_email": true,
     "temporary_password": true
   }
   ```

2. **Account Status Issues**
   ```sql
   -- Check user account status
   SELECT id, email, status, email_verified_at, created_at 
   FROM users 
   WHERE email = '<EMAIL>';
   
   -- Activate suspended account
   UPDATE users 
   SET status = 'active' 
   WHERE email = '<EMAIL>';
   ```

3. **Email Verification Problems**
   ```javascript
   // Resend verification email
   POST /api/admin/users/{userId}/resend-verification
   
   // Manually verify email
   PATCH /api/admin/users/{userId}
   {
     "email_verified_at": "2024-12-20T10:00:00Z"
   }
   ```

### Permission and Role Issues

#### Incorrect User Permissions
```sql
-- Check user roles and permissions
SELECT u.email, r.name as role, p.name as permission
FROM users u
JOIN user_roles ur ON u.id = ur.user_id
JOIN roles r ON ur.role_id = r.id
JOIN role_permissions rp ON r.id = rp.role_id
JOIN permissions p ON rp.permission_id = p.id
WHERE u.email = '<EMAIL>';

-- Update user role
UPDATE user_roles 
SET role_id = (SELECT id FROM roles WHERE name = 'tutor')
WHERE user_id = (SELECT id FROM users WHERE email = '<EMAIL>');
```

#### Tutor Assignment Issues
```javascript
// Check tutor-student assignments
GET /api/admin/assignments?tutor_id={tutorId}

// Reassign student to different tutor
POST /api/admin/assignments
{
  "student_id": "student123",
  "tutor_id": "tutor456",
  "effective_date": "2024-12-20"
}

// Remove assignment
DELETE /api/admin/assignments/{assignmentId}
```

## 📚 Content Management Issues

### Story Maker Problems

#### Stories Not Loading
**Diagnostic Steps:**
1. **Check File Storage**
   ```bash
   # Verify file exists
   ls -la /var/hec/uploads/stories/
   
   # Check file permissions
   chmod 644 /var/hec/uploads/stories/*.jpg
   
   # Test S3 connectivity (if using S3)
   aws s3 ls s3://hec-stories/
   ```

2. **Database Integrity**
   ```sql
   -- Check story records
   SELECT id, title, image_path, status, created_at 
   FROM story_makers 
   WHERE status = 'active';
   
   -- Verify image paths
   SELECT id, title, image_path 
   FROM story_makers 
   WHERE image_path IS NULL OR image_path = '';
   ```

3. **CDN Issues**
   ```bash
   # Test CDN delivery
   curl -I https://cdn.hecplatform.com/stories/image123.jpg
   
   # Clear CDN cache
   aws cloudfront create-invalidation \
     --distribution-id E123456789 \
     --paths "/stories/*"
   ```

#### Story Submission Failures
```javascript
// Check submission status
GET /api/admin/story-submissions?status=failed

// Retry failed submissions
POST /api/admin/story-submissions/{submissionId}/retry

// Manual scoring for failed AI evaluation
POST /api/admin/story-submissions/{submissionId}/manual-score
{
  "creativity_score": 4,
  "grammar_score": 3,
  "relevance_score": 5,
  "admin_notes": "Manual scoring due to AI service timeout"
}
```

### Diary System Issues

#### Entries Not Saving
**Common Causes:**
1. **Auto-save Failures**
   ```javascript
   // Check auto-save logs
   GET /api/admin/logs?type=auto_save&status=error
   
   // Force save diary entry
   POST /api/admin/diary-entries/{entryId}/force-save
   ```

2. **Version History Problems**
   ```sql
   -- Check version history
   SELECT entry_id, version, created_at, content_length 
   FROM diary_entry_versions 
   WHERE entry_id = 'entry123' 
   ORDER BY version DESC;
   
   -- Restore from version
   INSERT INTO diary_entries (id, content, user_id, created_at)
   SELECT entry_id, content, user_id, created_at 
   FROM diary_entry_versions 
   WHERE entry_id = 'entry123' AND version = 5;
   ```

#### Theme Loading Issues
```bash
# Check theme files
ls -la /var/hec/themes/

# Verify theme configuration
cat /var/hec/themes/config.json

# Test theme API
curl https://api.hecplatform.com/themes/available
```

## 🎮 Game System Troubleshooting

### Block Game Issues

#### Game Not Starting
```javascript
// Check game configuration
GET /api/admin/block-games/{gameId}/config

// Verify word bank
GET /api/admin/word-banks?game_type=block_game

// Reset game state
DELETE /api/admin/game-sessions/{sessionId}
```

#### Scoring Problems
```sql
-- Check scoring algorithm
SELECT game_id, user_id, score, accuracy, completion_time 
FROM game_results 
WHERE game_type = 'block_game' 
AND created_at > NOW() - INTERVAL '1 day';

-- Recalculate scores
UPDATE game_results 
SET score = (accuracy * 100) + (CASE WHEN completion_time < 60 THEN 20 ELSE 0 END)
WHERE game_type = 'block_game' 
AND score IS NULL;
```

### AI Service Issues

#### Story Evaluation Failures
```bash
# Check AI service status
curl -H "Authorization: Bearer $AI_API_KEY" \
  https://ai-service.hecplatform.com/health

# Retry failed evaluations
POST /api/admin/ai-evaluations/retry-failed
{
  "date_range": "last_24_hours",
  "max_retries": 3
}
```

#### Fallback Procedures
```javascript
// Enable manual evaluation mode
PATCH /api/admin/settings
{
  "ai_evaluation_enabled": false,
  "manual_evaluation_required": true
}

// Bulk assign for manual review
POST /api/admin/story-submissions/assign-for-review
{
  "submission_ids": ["sub1", "sub2", "sub3"],
  "reviewer_id": "admin123"
}
```

## 🔧 System Administration Issues

### File Upload Problems

#### Upload Failures
```bash
# Check upload directory permissions
ls -la /var/hec/uploads/
chmod 755 /var/hec/uploads/
chown hec:hec /var/hec/uploads/

# Check disk space
df -h /var/hec/uploads/

# Test upload endpoint
curl -X POST -F "file=@test.jpg" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  https://api.hecplatform.com/upload
```

#### S3 Upload Issues
```bash
# Test S3 credentials
aws s3 ls s3://hec-uploads/ --profile hec-production

# Check S3 bucket policy
aws s3api get-bucket-policy --bucket hec-uploads

# Test presigned URL generation
POST /api/admin/files/presigned-url
{
  "filename": "test.jpg",
  "content_type": "image/jpeg"
}
```

### Email Delivery Issues

#### Emails Not Sending
```bash
# Check email queue
SELECT * FROM email_queue WHERE status = 'pending';

# Test SMTP connection
telnet smtp.gmail.com 587

# Retry failed emails
UPDATE email_queue 
SET status = 'pending', attempts = 0 
WHERE status = 'failed' AND attempts < 3;
```

#### Email Configuration
```javascript
// Test email configuration
POST /api/admin/test-email
{
  "to": "<EMAIL>",
  "subject": "Test Email",
  "template": "system_test"
}

// Check email templates
GET /api/admin/email-templates
```

## 📊 Performance Optimization

### Database Performance

#### Slow Queries
```sql
-- Find slow queries
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements 
WHERE mean_time > 100 
ORDER BY mean_time DESC;

-- Check missing indexes
SELECT schemaname, tablename, attname, n_distinct, correlation 
FROM pg_stats 
WHERE schemaname = 'public' 
AND n_distinct > 100;

-- Analyze table statistics
ANALYZE diary_entries;
ANALYZE story_maker_submissions;
```

#### Connection Pool Issues
```bash
# Check active connections
SELECT count(*) FROM pg_stat_activity;

# Check connection pool configuration
grep -r "pool" /etc/hec/database.conf

# Restart connection pool
sudo systemctl restart pgbouncer
```

### Application Performance

#### Memory Issues
```bash
# Check Node.js memory usage
ps aux | grep node

# Generate heap dump
kill -USR2 $(pgrep node)

# Analyze memory leaks
node --inspect app.js
```

#### Cache Issues
```bash
# Clear Redis cache
redis-cli FLUSHALL

# Check cache hit rates
redis-cli INFO stats | grep hit_rate

# Restart cache service
sudo systemctl restart redis
```

## 🔒 Security Issues

### Suspicious Activity

#### Unusual Login Patterns
```sql
-- Check failed login attempts
SELECT ip_address, COUNT(*) as attempts, MAX(created_at) as last_attempt
FROM login_attempts 
WHERE success = false 
AND created_at > NOW() - INTERVAL '1 hour'
GROUP BY ip_address 
HAVING COUNT(*) > 10;

-- Block suspicious IPs
INSERT INTO blocked_ips (ip_address, reason, blocked_until)
VALUES ('*************', 'Multiple failed login attempts', NOW() + INTERVAL '24 hours');
```

#### Account Compromise
```javascript
// Immediately suspend account
PATCH /api/admin/users/{userId}
{
  "status": "suspended",
  "reason": "Security concern - unauthorized access"
}

// Force logout all sessions
DELETE /api/admin/users/{userId}/sessions

// Reset password and require verification
POST /api/admin/users/{userId}/security-reset
{
  "reset_password": true,
  "require_email_verification": true,
  "notify_user": true
}
```

### Data Integrity Issues

#### Corrupted Data Detection
```sql
-- Check for orphaned records
SELECT COUNT(*) FROM diary_entries de
LEFT JOIN users u ON de.user_id = u.id
WHERE u.id IS NULL;

-- Verify referential integrity
SELECT COUNT(*) FROM story_maker_submissions sms
LEFT JOIN story_makers sm ON sms.story_maker_id = sm.id
WHERE sm.id IS NULL;
```

#### Data Recovery Procedures
```bash
# Restore from backup
pg_restore -h localhost -U hec -d hec_production \
  /backups/hec_backup_2024-12-20.sql

# Verify data integrity after restore
SELECT COUNT(*) FROM users;
SELECT COUNT(*) FROM diary_entries;
SELECT COUNT(*) FROM story_makers;
```

## 📋 Troubleshooting Checklist

### Daily Health Checks
- [ ] System uptime and performance metrics
- [ ] Error logs review
- [ ] Database connection status
- [ ] File upload functionality
- [ ] Email delivery status
- [ ] User login success rates

### Weekly System Review
- [ ] Performance trend analysis
- [ ] Security audit logs
- [ ] Backup verification
- [ ] Disk space monitoring
- [ ] User feedback review
- [ ] Third-party service status

### Monthly Maintenance
- [ ] Database optimization
- [ ] Log file rotation
- [ ] Security updates
- [ ] Performance benchmarking
- [ ] Disaster recovery testing
- [ ] Documentation updates

## 🆘 Emergency Contacts

### Internal Team
- **Technical Lead**: <EMAIL>
- **Database Admin**: <EMAIL>
- **Security Team**: <EMAIL>
- **DevOps**: <EMAIL>

### External Services
- **AWS Support**: Enterprise Support Plan
- **CDN Provider**: CloudFlare Enterprise
- **Email Service**: SendGrid Premium Support
- **Monitoring**: DataDog 24/7 Support

### Escalation Procedures
1. **Level 1**: Technical team member
2. **Level 2**: Technical lead + DevOps
3. **Level 3**: CTO + External consultants
4. **Level 4**: CEO + Board notification

---

**Emergency Hotline**: +1-800-HEC-HELP (24/7 support)
**Status Page**: https://status.hecplatform.com
**Documentation**: https://docs.hecplatform.com/troubleshooting

*For immediate assistance with critical issues, use the emergency hotline. For non-critical issues, create a support ticket through the admin dashboard.*