import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AdminDashboardController } from './admin-dashboard.controller';
import { TutorDashboardController } from './tutor-dashboard.controller';
import { AdminDashboardService } from './admin-dashboard.service';
import { TutorDashboardService } from './tutor-dashboard.service';

// User and Plan entities
import { User } from '../../database/entities/user.entity';
import { UserPlan } from '../../database/entities/user-plan.entity';
import { Plan } from '../../database/entities/plan.entity';
import { PlanFeature } from '../../database/entities/plan-feature.entity';
import { StudentTutorMapping } from '../../database/entities/student-tutor-mapping.entity';

// Attendance entities
import { DiaryEntryAttendance } from '../../database/entities/diary-entry-attendance.entity';

// Submission entities
import { DiaryEntry } from '../../database/entities/diary-entry.entity';
import { MissionDiaryEntry } from '../../database/entities/mission-diary-entry.entity';
import { NovelEntry } from '../../database/entities/novel-entry.entity';
import { QATaskSubmissions } from '../../database/entities/qa-task-submissions.entity';
import { QAMissionTasks } from '../../database/entities/qa-mission-tasks.entity';
import { EssayTaskSubmissions } from '../../database/entities/essay-task-submissions.entity';
import { EssayMissionTasks } from '../../database/entities/essay-mission-tasks.entity';
import { BlockGameAttempt } from '../../database/entities/block-game-attempt.entity';
import { StoryMakerParticipation } from '../../database/entities/story-maker-participation.entity';

// Review and feedback entities
import { DiaryFeedback } from '../../database/entities/diary-feedback.entity';
import { DiaryCorrection } from '../../database/entities/diary-correction.entity';
import { MissionDiaryEntryFeedback } from '../../database/entities/mission-diary-entry-feedback.entity';
import { NovelFeedback } from '../../database/entities/novel-feedback.entity';
import { NovelCorrection } from '../../database/entities/novel-correction.entity';
import { QATaskSubmissionMarking } from '../../database/entities/qa-task-submission-marking.entity';
import { EssayTaskSubmissionMarking } from '../../database/entities/essay-task-submission-marking.entity';

// Common module for shared services
import { CommonModule } from '../../common/common.module';
import { QASubmission } from 'src/database/entities/qa-submission.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      // User and Plan entities
      User,
      UserPlan,
      Plan,
      PlanFeature,
      StudentTutorMapping,

      // Attendance entities
      DiaryEntryAttendance,

      // Submission entities
      DiaryEntry,
      MissionDiaryEntry,
      NovelEntry,
      QATaskSubmissions,
      QAMissionTasks,
      QASubmission,
      EssayTaskSubmissions,
      EssayMissionTasks,
      BlockGameAttempt,
      StoryMakerParticipation,

      // Review and feedback entities
      DiaryFeedback,
      DiaryCorrection,
      MissionDiaryEntryFeedback,
      NovelFeedback,
      NovelCorrection,
      QATaskSubmissionMarking,
      EssayTaskSubmissionMarking,
    ]),
    CommonModule,
  ],
  controllers: [AdminDashboardController, TutorDashboardController],
  providers: [AdminDashboardService, TutorDashboardService],
  exports: [AdminDashboardService, TutorDashboardService],
})
export class DashboardModule {}
