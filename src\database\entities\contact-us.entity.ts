import { Entity, Column } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';

export enum ContactStatus {
  PENDING = 'pending',
  RESPONDED = 'responded',
  CLOSED = 'closed',
}

@Entity()
export class ContactUs extends AuditableBaseEntity {
  @Column()
  name: string;

  @Column()
  email: string;

  @Column({ nullable: true })
  phone: string;

  @Column()
  subject: string;

  @Column({ type: 'text' })
  message: string;

  @Column({
    type: 'enum',
    enum: ContactStatus,
    default: ContactStatus.PENDING,
  })
  status: ContactStatus;

  @Column({ name: 'admin_response', type: 'text', nullable: true })
  adminResponse: string;

  @Column({ name: 'responded_by', nullable: true })
  respondedBy: string;

  @Column({ name: 'responded_at', type: 'timestamp', nullable: true })
  respondedAt: Date;
}