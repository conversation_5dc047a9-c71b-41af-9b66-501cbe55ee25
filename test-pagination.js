// Simple test to verify pagination fix
const { PagedListDto } = require('./dist/common/models/paged-list.dto');

console.log('Testing PagedListDto pagination fix...\n');

// Test case 1: With all parameters (should work correctly)
console.log('Test 1: With all parameters (page=3, limit=10)');
const test1 = new PagedListDto(['item1', 'item2'], 39, 3, 10);
console.log('currentPage:', test1.currentPage); // Should be 3
console.log('totalPages:', test1.totalPages); // Should be 4
console.log('itemsPerPage:', test1.itemsPerPage); // Should be 10
console.log('totalCount:', test1.totalCount); // Should be 39
console.log('');

// Test case 2: With only items and totalCount (old broken behavior)
console.log('Test 2: With only items and totalCount (should default to page=1)');
const test2 = new PagedListDto(['item1', 'item2'], 39);
console.log('currentPage:', test2.currentPage); // Should be 1 (default)
console.log('totalPages:', test2.totalPages); // Should be 20 (39/2 items.length)
console.log('itemsPerPage:', test2.itemsPerPage); // Should be 2 (items.length)
console.log('totalCount:', test2.totalCount); // Should be 39
console.log('');

// Test case 3: Edge case with 0 items
console.log('Test 3: Edge case with 0 items');
const test3 = new PagedListDto([], 0, 1, 10);
console.log('currentPage:', test3.currentPage); // Should be 1
console.log('totalPages:', test3.totalPages); // Should be 1 (fallback)
console.log('itemsPerPage:', test3.itemsPerPage); // Should be 10
console.log('totalCount:', test3.totalCount); // Should be 0
console.log('');

console.log('All tests completed!');
