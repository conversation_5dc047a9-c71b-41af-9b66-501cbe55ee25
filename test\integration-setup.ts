import 'reflect-metadata';
import { DataSource } from 'typeorm';
import { getTestDatabaseConfig } from './utils/test-database.config';

let testDataSource: DataSource;

// Integration test specific setup
beforeAll(async () => {
  // Set test environment variables
  process.env.NODE_ENV = 'test';
  process.env.JWT_SECRET = 'test-jwt-secret-integration';
  process.env.DATABASE_URL = 'sqlite::memory:';
  
  // Initialize test database
  testDataSource = new DataSource(getTestDatabaseConfig());
  await testDataSource.initialize();
  await testDataSource.runMigrations();
}, 30000);

// Clean database between tests
beforeEach(async () => {
  // Clear all tables but keep schema
  const entities = testDataSource.entityMetadatas;
  for (const entity of entities) {
    const repository = testDataSource.getRepository(entity.name);
    await repository.clear();
  }
});

// Global test teardown
afterAll(async () => {
  if (testDataSource && testDataSource.isInitialized) {
    await testDataSource.destroy();
  }
});

// Mock external services for integration tests
jest.mock('nodemailer', () => ({
  createTransport: jest.fn(() => ({
    sendMail: jest.fn().mockResolvedValue({ messageId: 'test-message-id' }),
  })),
}));

jest.mock('@aws-sdk/client-s3', () => ({
  S3Client: jest.fn(() => ({
    send: jest.fn(),
  })),
  PutObjectCommand: jest.fn(),
  GetObjectCommand: jest.fn(),
}));

// Export test data source for use in tests
export { testDataSource };