import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateAdminConversationParticipant1755673800000 implements MigrationInterface {
  name = 'CreateAdminConversationParticipant1755673800000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE "admin_conversation_participant" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "conversation_id" uuid NOT NULL,
        "admin_id" uuid NOT NULL,
        "is_active" boolean NOT NULL DEFAULT true,
        "last_accessed_at" TIMESTAMP,
        "unread_count" integer NOT NULL DEFAULT 0,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_admin_conversation_participant" PRIMARY KEY ("id")
      )
    `);

    await queryRunner.query(`
      ALTER TABLE "admin_conversation_participant" 
      ADD CONSTRAINT "FK_admin_conversation_participant_conversation" 
      FOREIGN KEY ("conversation_id") REFERENCES "conversation"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "admin_conversation_participant" 
      ADD CONSTRAINT "FK_admin_conversation_participant_admin" 
      FOREIGN KEY ("admin_id") REFERENCES "user"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      CREATE UNIQUE INDEX "IDX_admin_conversation_participant_conversation_admin" 
      ON "admin_conversation_participant" ("conversation_id", "admin_id")
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "admin_conversation_participant"`);
  }
}