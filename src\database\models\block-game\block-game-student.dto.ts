import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsUUID, IsArray, ValidateNested, ArrayMinSize, IsNumber, IsPositive } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO for block game sentence in student view with gaps and options
 */
export class BlockGameSentenceResponseDto {
  @ApiProperty({
    description: 'The starting part of the sentence with [[gap]] markers',
    example: 'The fan makes a [[gap]]',
  })
  starting_part: string;

  @ApiProperty({
    description: 'The expanding part of the sentence with [[gap]] markers',
    example: '[[gap]] sound',
  })
  expanding_part: string;

  @ApiProperty({
    description: 'Options for filling gaps in starting part',
    example: ['loud', 'strange', 'quiet'],
    type: [String],
  })
  starting_gap_options: string[];

  @ApiProperty({
    description: 'Options for filling gaps in expanding part',
    example: ['sound', 'noise', 'music'],
    type: [String],
  })
  expanding_gap_options: string[];

  @ApiProperty({
    description: 'Correct answers for gaps in starting part',
    example: ['strange'],
    type: [String],
  })
  starting_part_answers: string[];

  @ApiProperty({
    description: 'Correct answers for gaps in expanding part',
    example: ['loud'],
    type: [String],
  })
  expanding_part_answers: string[];

  @ApiProperty({
    description: 'The order of the sentence in the game',
    example: 1,
  })
  sentence_order: number;
}



/**
 * DTO for block game detail (student view)
 */
export class BlockGameDetailDto {
  @ApiProperty({
    description: 'The ID of the block game',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'The title of the block game',
    example: 'Basic Sentence Building',
  })
  title: string;

  @ApiProperty({
    description: 'The total score for the block game',
    example: 20,
  })
  score: number;

  @ApiProperty({
    description: 'The number of sentences to construct',
    example: 5,
  })
  sentence_count: number;

  @ApiProperty({
    description: 'The sentences with gaps and answer options',
    type: [BlockGameSentenceResponseDto],
    example: [
      {
        starting_part: 'The cat makes a [[gap]]',
        expanding_part: '[[gap]] sound',
        starting_gap_options: ['loud', 'strange', 'quiet', 'beautiful'],
        expanding_gap_options: ['loud', 'soft', 'harsh', 'pleasant'],
        starting_part_answers: ['strange'],
        expanding_part_answers: ['loud'],
        sentence_order: 1
      },
      {
        starting_part: 'I will [[gap]]',
        expanding_part: 'it [[gap]] morning',
        starting_gap_options: ['check', 'see', 'find', 'watch'],
        expanding_gap_options: ['tomorrow', 'today', 'yesterday', 'next'],
        starting_part_answers: ['check'],
        expanding_part_answers: ['tomorrow'],
        sentence_order: 2
      }
    ],
  })
  sentences: BlockGameSentenceResponseDto[];


}

/**
 * DTO for student sentence construction with gap answers
 */
export class StudentSentenceConstructionDto {
  @ApiProperty({
    description: 'The starting part constructed by student (optional)',
    example: 'The fan makes a',
    required: false,
  })
  @IsString()
  starting_sentence?: string;

  @ApiProperty({
    description: 'The expanding part constructed by student (optional)',
    example: 'strange sound',
    required: false,
  })
  @IsString()
  expanding_sentence?: string;

  @ApiProperty({
    description: 'Student answers for gaps in starting part',
    example: ['strange'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  starting_gap_answers: string[];

  @ApiProperty({
    description: 'Student answers for gaps in expanding part',
    example: ['loud'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  expanding_gap_answers: string[];

  @ApiProperty({
    description: 'The order of the sentence',
    example: 1,
  })
  @IsNumber()
  @IsPositive()
  sentence_order: number;
}

/**
 * DTO for submitting block game attempt
 */
export class SubmitBlockGameDto {
  @ApiProperty({
    description: 'The ID of the block game',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  block_game_id: string;

  @ApiProperty({
    description: 'Array of sentence constructions by the student',
    type: [StudentSentenceConstructionDto],
    example: [
      {
        starting_gap_answers: ['strange'],
        expanding_gap_answers: ['loud'],
        sentence_order: 1,
      },
      {
        starting_gap_answers: ['check'],
        expanding_gap_answers: ['tomorrow'],
        sentence_order: 2,
      },
    ],
  })
  @IsArray()
  @ArrayMinSize(1, { message: 'At least one sentence construction must be provided' })
  @ValidateNested({ each: true })
  @Type(() => StudentSentenceConstructionDto)
  sentence_constructions: StudentSentenceConstructionDto[];
}

/**
 * DTO for block game attempt result
 */
export class BlockGameAttemptResultDto {
  @ApiProperty({
    description: 'The score achieved',
    example: 16,
  })
  score: number;

  @ApiProperty({
    description: 'The total possible score',
    example: 20,
  })
  total_score: number;

  @ApiProperty({
    description: 'The percentage score',
    example: 80,
  })
  percentage: number;

  @ApiProperty({
    description: 'Detailed results for each sentence with gap validation',
    example: [
      {
        sentence_order: 1,
        student_starting_gaps: ['strange'],
        student_expanding_gaps: ['loud'],
        correct_starting: 'The cat makes a [[gap]]',
        correct_expanding: '[[gap]] sound',
        correct_starting_answers: ['strange'],
        correct_expanding_answers: ['loud'],
        is_correct: true,
        points_earned: 10,
      },
      {
        sentence_order: 2,
        student_starting_gaps: ['see'],
        student_expanding_gaps: ['today'],
        correct_starting: 'I will [[gap]]',
        correct_expanding: 'it [[gap]] morning',
        correct_starting_answers: ['check'],
        correct_expanding_answers: ['tomorrow'],
        is_correct: false,
        points_earned: 0,
      },
    ],
  })
  sentence_results: Array<{
    sentence_order: number;
    student_starting?: string;
    student_expanding?: string;
    student_starting_gaps: string[];
    student_expanding_gaps: string[];
    correct_starting: string;
    correct_expanding: string;
    correct_starting_answers: string[];
    correct_expanding_answers: string[];
    is_correct: boolean;
    points_earned: number;
  }>;

  @ApiProperty({
    description: 'When the attempt was submitted',
    example: '2023-01-15T10:30:00.000Z',
  })
  submitted_at: Date;
}

/**
 * DTO for block game list (student view)
 */
export class BlockGameListItemDto {
  @ApiProperty({
    description: 'The ID of the block game',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'The title of the block game',
    example: 'Basic Sentence Building',
  })
  title: string;

  @ApiProperty({
    description: 'The total score for the block game',
    example: 20,
  })
  score: number;

  @ApiProperty({
    description: 'The number of sentences in the game',
    example: 5,
  })
  sentence_count: number;

  @ApiProperty({
    description: 'Whether the student has played this game',
    example: true,
  })
  is_played: boolean;

  @ApiProperty({
    description: 'The best score achieved by the student (if played)',
    example: 18,
    required: false,
  })
  best_score?: number;

  @ApiProperty({
    description: 'The number of attempts made by the student',
    example: 3,
  })
  attempt_count: number;
}
