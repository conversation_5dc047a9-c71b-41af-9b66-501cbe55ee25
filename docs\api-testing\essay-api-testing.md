# Essay Module API Testing Flow

This document outlines the testing flow for the Essay Module API endpoints.

## Prerequisites

Before testing the Essay API:

1. Ensure the HEC backend is running
2. Have valid authentication tokens for students
3. Ensure student has subscription with ENGLISH_ESSAY feature
4. Set up your API testing tool (<PERSON><PERSON> recommended)

## Essay Mission Discovery Testing Flow

### Test Case 1: Get Available Missions

1. Authenticate with a student token
2. Send a GET request to `/api/student-essay/getMission` with pagination parameters:
   ```json
   {
     "page": 1,
     "limit": 10,
     "sortBy": "createdAt",
     "sortDirection": "DESC"
   }
   ```
3. Verify HTTP status code is 200 OK
4. Verify response contains paginated list of missions
5. Verify missions include title, description, difficulty level
6. Verify only active missions are returned

### Test Case 2: Mission Filtering and Sorting

1. Test different sorting options (title, difficulty, createdAt)
2. Test pagination with different page sizes
3. Verify sorting works correctly
4. Test edge cases (empty results, large page numbers)

## Essay Task Management Testing Flow

### Test Case 1: Start Essay Task

1. Authenticate with a student token
2. Send a POST request to `/api/student-essay/start/task`:
   ```json
   {
     "taskId": "123e4567-e89b-12d3-a456-426614174000"
   }
   ```
3. Verify HTTP status code is 200 OK
4. Verify response contains task submission details
5. Verify task status is set to "STARTED"
6. Verify task is associated with the authenticated student

### Test Case 2: Start Free Essay

1. Authenticate with a student token
2. Send a POST request to `/api/student-essay/start/essay`:
   ```json
   {
     "taskId": null
   }
   ```
3. Verify HTTP status code is 200 OK
4. Verify free essay session is created
5. Verify student can write without specific mission constraints

### Test Case 3: Task Start Validation

1. Test starting a task that doesn't exist
2. Test starting a task that's already started
3. Test starting a task without proper subscription
4. Verify appropriate error responses for each case

## Essay Writing Process Testing Flow

### Test Case 1: Auto-Save Content

1. Authenticate with a student token
2. Start an essay task
3. Send a POST request to `/api/student-essay/submit/essay/update`:
   ```json
   {
     "submissionId": "123e4567-e89b-12d3-a456-426614174000",
     "content": "This is my essay content in progress...",
     "title": "My Essay Title",
     "skinId": "123e4567-e89b-12d3-a456-426614174000",
     "metaData": {
       "timeSpent": 120,
       "lastDraftSavedAt": "2024-01-15T10:30:00Z"
     }
   }
   ```
4. Verify HTTP status code is 200 OK
5. Verify content is saved as draft
6. Verify metadata is updated correctly

### Test Case 2: Content Validation

1. Test with empty content
2. Test with extremely long content
3. Test with invalid skinId
4. Test with malformed metadata
5. Verify appropriate validation errors

### Test Case 3: Draft Persistence

1. Save draft content multiple times
2. Verify latest content is always preserved
3. Test concurrent save attempts
4. Verify draft timestamps are updated correctly

## Essay Submission Testing Flow

### Test Case 1: Submit Essay for Review

1. Authenticate with a student token
2. Complete essay writing process
3. Send a POST request to `/api/student-essay/submit/essay`:
   ```json
   {
     "taskId": "123e4567-e89b-12d3-a456-426614174000",
     "content": "This is my final essay submission",
     "title": "My Final Essay Title",
     "skinId": "123e4567-e89b-12d3-a456-426614174000",
     "metaData": {
       "timeSpent": 300,
       "wordCount": 250
     }
   }
   ```
4. Verify HTTP status code is 200 OK
5. Verify essay status changes to "SUBMITTED"
6. Verify submission timestamp is recorded
7. Verify tutor assignment notification is triggered

### Test Case 2: Submission Validation

1. Test submitting without required content
2. Test submitting with invalid task ID
3. Test submitting already submitted essay
4. Test submitting without active task
5. Verify appropriate error responses

## Essay Management Testing Flow

### Test Case 1: Get Active Essay

1. Authenticate with a student token
2. Start an essay task
3. Send a GET request to `/api/student-essay/activeEssay`
4. Verify HTTP status code is 200 OK
5. Verify response contains current active essay details
6. Verify draft content is included if available

### Test Case 2: Get Active Task by ID

1. Authenticate with a student token
2. Start an essay task
3. Send a GET request to `/api/student-essay/submissions/{taskId}`
4. Verify HTTP status code is 200 OK
5. Verify response contains specific task submission details
6. Test with non-existent task ID and verify 404 response

### Test Case 3: Get Essay History

1. Authenticate with a student token
2. Submit multiple essays
3. Send a GET request to `/api/student-essay/myEssays` with pagination:
   ```json
   {
     "page": 1,
     "limit": 10,
     "sortBy": "submittedAt",
     "sortDirection": "DESC"
   }
   ```
4. Verify HTTP status code is 200 OK
5. Verify response contains paginated list of submitted essays
6. Verify only student's own essays are returned

### Test Case 4: Get Specific Essay Details

1. Authenticate with a student token
2. Send a GET request to `/api/student-essay/myEssays/{id}`
3. Verify HTTP status code is 200 OK
4. Verify response contains complete essay details
5. Verify tutor feedback is included if available
6. Test with non-existent essay ID and verify 404 response

## Skin Management Testing Flow

### Test Case 1: Get Available Skins

1. Authenticate with a student token
2. Send a GET request to `/api/student-essay/skins` with pagination
3. Verify HTTP status code is 200 OK
4. Verify response contains available diary skins
5. Verify student's own skins are included
6. Verify skin accessibility based on purchases

### Test Case 2: Get Task Skin Information

1. Authenticate with a student token
2. Start an essay task
3. Send a GET request to `/api/student-essay/skins/{taskId}`
4. Verify HTTP status code is 200 OK
5. Verify response contains skin configuration for the task
6. Verify default vs task-specific skin information

### Test Case 3: Set Default Skin

1. Authenticate with a student token
2. Send a POST request to `/api/student-essay/skin/default`:
   ```json
   {
     "skinId": "123e4567-e89b-12d3-a456-426614174000"
   }
   ```
3. Verify HTTP status code is 200 OK
4. Verify default skin preference is saved
5. Verify new essays use the default skin
6. Test with invalid skin ID and verify error response

## Integration Testing Flow

### Test Case 1: Complete Essay Workflow

1. **Discovery Phase**
   - Get available missions
   - Select appropriate mission

2. **Writing Phase**
   - Start essay task
   - Write content with auto-save
   - Apply custom skin
   - Set essay title

3. **Submission Phase**
   - Submit final essay
   - Verify submission confirmation
   - Check essay appears in history

4. **Review Phase**
   - Wait for tutor review
   - Check for feedback notifications
   - View reviewed essay with comments

### Test Case 2: Multiple Essay Management

1. Start multiple essay tasks
2. Work on different essays concurrently
3. Verify each essay maintains separate state
4. Submit essays in different order
5. Verify all essays are tracked correctly

## Error Handling Testing Flow

### Test Case 1: Network Interruption

1. Start essay writing process
2. Simulate network interruption during auto-save
3. Verify graceful error handling
4. Verify content recovery when connection restored

### Test Case 2: Session Expiry

1. Start essay writing
2. Let authentication token expire
3. Attempt to save content
4. Verify appropriate authentication error
5. Verify content is preserved for recovery

### Test Case 3: Concurrent Access

1. Start essay on multiple devices/browsers
2. Make simultaneous content updates
3. Verify conflict resolution
4. Verify data consistency

## Performance Testing Flow

### Test Case 1: Auto-Save Performance

1. Measure auto-save response times
2. Test with large content (10,000+ words)
3. Verify performance remains acceptable
4. Test rapid successive auto-saves

### Test Case 2: Essay List Performance

1. Create large number of essays (100+)
2. Test pagination performance
3. Verify search and filter performance
4. Test sorting with large datasets

## Security Testing Flow

### Test Case 1: Access Control

1. Attempt to access other students' essays
2. Verify proper authorization checks
3. Test task access without subscription
4. Verify skin access permissions

### Test Case 2: Data Validation

1. Test XSS prevention in essay content
2. Test SQL injection in search parameters
3. Verify input sanitization
4. Test file upload security (if applicable)

## Edge Cases and Boundary Testing

### Test Case 1: Content Limits

1. Test with maximum allowed content length
2. Test with empty content submission
3. Test with special characters and Unicode
4. Test with HTML/script content

### Test Case 2: Timing Edge Cases

1. Submit essay exactly at deadline
2. Test auto-save during submission
3. Test rapid start/stop of tasks
4. Verify timestamp accuracy across time zones

This comprehensive testing flow ensures the Essay Module functions correctly across all user scenarios and edge cases.