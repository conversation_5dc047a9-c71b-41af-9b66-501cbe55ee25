import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddConversationUniqueConstraint1755673500000 implements MigrationInterface {
  name = 'AddConversationUniqueConstraint1755673500000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // First, identify and fix any existing duplicate regular conversations
    console.log('Checking for duplicate regular conversations...');
    
    // Find duplicate regular conversations (same participants with multiple conversations)
    const duplicates = await queryRunner.query(`
      SELECT 
        LEAST(participant1_id, participant2_id) as user1,
        GREATEST(participant1_id, participant2_id) as user2,
        COUNT(*) as count,
        array_agg(id ORDER BY created_at ASC) as conversation_ids
      FROM conversation 
      WHERE is_admin_conversation = false 
        AND participant1_id IS NOT NULL 
        AND participant2_id IS NOT NULL
      GROUP BY LEAST(participant1_id, participant2_id), GREATEST(participant1_id, participant2_id)
      HAVING COUNT(*) > 1
    `);

    if (duplicates.length > 0) {
      console.log(`Found ${duplicates.length} pairs with duplicate regular conversations. Fixing...`);
      
      for (const duplicate of duplicates) {
        const conversationIds = duplicate.conversation_ids;
        
        // Keep the first conversation, merge others into it
        const primaryConversationId = conversationIds[0];
        const conversationsToMerge = conversationIds.slice(1);

        console.log(`Merging ${conversationsToMerge.length} conversations into ${primaryConversationId} for users ${duplicate.user1} and ${duplicate.user2}`);

        for (const convId of conversationsToMerge) {
          // Move all messages from duplicate conversation to primary conversation
          await queryRunner.query(`
            UPDATE message 
            SET conversation_id = $1 
            WHERE conversation_id = $2
          `, [primaryConversationId, convId]);

          // Delete the duplicate conversation
          await queryRunner.query(`
            DELETE FROM conversation WHERE id = $1
          `, [convId]);
        }

        // Update the primary conversation to use consistent participant ordering
        await queryRunner.query(`
          UPDATE conversation
          SET participant1_id = $1, participant2_id = $2
          WHERE id = $3
        `, [duplicate.user1, duplicate.user2, primaryConversationId]);
      }
    }

    // Now add the unique constraint to prevent future duplicates
    // Create a partial unique index for regular conversations
    await queryRunner.query(`
      CREATE UNIQUE INDEX "IDX_conversation_participants_unique"
      ON "conversation" (LEAST(participant1_id, participant2_id), GREATEST(participant1_id, participant2_id))
      WHERE "is_admin_conversation" = false 
        AND "participant1_id" IS NOT NULL 
        AND "participant2_id" IS NOT NULL
    `);

    console.log('Regular conversation duplicates fixed and unique constraint added.');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the unique index
    await queryRunner.query(`
      DROP INDEX IF EXISTS "IDX_conversation_participants_unique"
    `);

    console.log('Conversation unique constraint removed.');
  }
}