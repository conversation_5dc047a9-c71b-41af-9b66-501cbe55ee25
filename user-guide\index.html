<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HEC User Guide - Comprehensive Documentation</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .header {
            text-align: center;
            margin-bottom: 50px;
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        h1 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 3em;
            font-weight: 700;
            margin-bottom: 10px;
        }

        h2 {
            color: #2c3e50;
            margin-top: 40px;
            font-size: 2.2em;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
            margin-bottom: 30px;
        }

        p {
            margin-bottom: 20px;
            font-size: 18px;
            color: #555;
        }

        .subtitle {
            font-size: 1.3em;
            color: #7f8c8d;
            margin-bottom: 0;
        }

        .button {
            display: inline-block;
            background-color: #2c3e50;
            color: white;
            padding: 15px 30px;
            border-radius: 8px;
            text-decoration: none;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s;
            box-shadow: 0 4px 12px rgba(44, 62, 80, 0.2);
            margin: 8px;
        }

        .button:hover {
            background-color: #34495e;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(44, 62, 80, 0.3);
        }

        .button.primary {
            background-color: #3498db;
            font-size: 18px;
            padding: 18px 35px;
        }

        .button.primary:hover {
            background-color: #2980b9;
        }

        .quick-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 30px 0;
        }

        .quick-link {
            background: white;
            padding: 25px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .quick-link:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: #3498db;
        }

        .quick-link strong {
            display: block;
            font-size: 1.1em;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .quick-link small {
            color: #7f8c8d;
            font-size: 0.9em;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin: 40px 0;
        }

        .feature-item {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }

        .feature-item:hover {
            transform: translateY(-3px);
        }

        .feature-item h4 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 1.3em;
            margin-bottom: 15px;
        }

        .feature-item p {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .role-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin: 50px 0;
        }

        .role-card {
            background: white;
            border-radius: 15px;
            padding: 35px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            transition: all 0.3s;
            border-top: 5px solid #3498db;
        }

        .role-card:nth-child(2) {
            border-top-color: #e67e22;
        }

        .role-card:nth-child(3) {
            border-top-color: #9b59b6;
        }

        .role-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .role-card h3 {
            color: #2c3e50;
            font-size: 1.5em;
            margin-top: 0;
            margin-bottom: 20px;
        }

        .role-icon {
            font-size: 3em;
            margin-bottom: 15px;
            display: block;
        }

        .role-card p {
            margin-bottom: 12px;
            font-size: 16px;
        }

        .role-card strong {
            color: #2c3e50;
        }

        .navigation-section {
            text-align: center;
            margin-top: 50px;
            padding: 40px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .navigation-section h3 {
            color: #2c3e50;
            font-size: 1.8em;
            margin-bottom: 15px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }

        .stat-item {
            background: white;
            padding: 25px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #3498db;
            display: block;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 0.9em;
            margin-top: 5px;
        }

        .tip-box {
            background: #e8f6f3;
            border-left: 5px solid #27ae60;
            padding: 20px;
            margin: 30px 0;
            border-radius: 0 8px 8px 0;
        }

        .tip-box strong {
            color: #27ae60;
        }

        @media (max-width: 768px) {
            .header {
                padding: 25px;
            }
            
            h1 {
                font-size: 2.2em;
            }
            
            .role-grid {
                grid-template-columns: 1fr;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <script>
        function openGuide(path) {
            window.location.href = `/user-guide/view-guide.html?path=${path}`;
        }
    </script>
</head>
<body>
    <div class="header">
        <h1>🎓 HEC User Guide</h1>
        <p class="subtitle">Complete Documentation for the Handwriting Education Center</p>
        <p>Comprehensive guides, tutorials, and resources for students, tutors, and administrators</p>
    </div>

    <h2>📚 Comprehensive Learning Resources</h2>
    <p>
        The HEC platform offers a complete educational ecosystem featuring advanced diary writing tools, 
        AI-powered interactive games, social learning capabilities, and detailed progress analytics. 
        Our documentation covers every aspect of the platform to ensure you can leverage all available features effectively.
    </p>

    <div class="stats-grid">
        <div class="stat-item">
            <span class="stat-number">11+</span>
            <div class="stat-label">Detailed Guides</div>
        </div>
        <div class="stat-item">
            <span class="stat-number">3</span>
            <div class="stat-label">User Roles</div>
        </div>
        <div class="stat-item">
            <span class="stat-number">50+</span>
            <div class="stat-label">Features Covered</div>
        </div>
        <div class="stat-item">
            <span class="stat-number">100%</span>
            <div class="stat-label">Platform Coverage</div>
        </div>
    </div>

    <div class="quick-links">
        <div class="quick-link" onclick="openGuide('README.md')">
            <strong>🚀 Quick Start</strong><br>
            <small>Get started immediately with platform overview</small>
        </div>
        <div class="quick-link" onclick="openGuide('student/getting-started.md')">
            <strong>👨🎓 Student Onboarding</strong><br>
            <small>Complete setup and first steps guide</small>
        </div>
        <div class="quick-link" onclick="openGuide('tutor/diary-review.md')">
            <strong>👩🏫 Review Process</strong><br>
            <small>Step-by-step feedback workflow</small>
        </div>
        <div class="quick-link" onclick="openGuide('admin/user-management.md')">
            <strong>👨💼 Admin Tools</strong><br>
            <small>User and system management</small>
        </div>
    </div>

    <h2>🎯 Platform Features Overview</h2>
    <div class="features-grid">
        <div class="feature-item">
            <h4>📖 Advanced Diary System</h4>
            <p>Digital diary writing with beautiful customizable themes, automatic saving, version history tracking, and integrated tutor feedback system for continuous improvement.</p>
        </div>
        <div class="feature-item">
            <h4>🎮 AI-Powered Learning Games</h4>
            <p>Story Maker for creative writing development and Block Games for grammar practice, both featuring intelligent scoring algorithms and personalized feedback.</p>
        </div>
        <div class="feature-item">
            <h4>👥 Social Learning Network</h4>
            <p>Student connections, story sharing capabilities, community galleries, collaborative learning features, and peer support systems.</p>
        </div>
        <div class="feature-item">
            <h4>📊 Comprehensive Analytics</h4>
            <p>Detailed progress tracking, writing improvement metrics, game performance analysis, achievement systems, and learning milestone recognition.</p>
        </div>
        <div class="feature-item">
            <h4>🎨 Extensive Customization</h4>
            <p>Personal profile customization, custom diary themes, achievement badge collections, and personalized learning experience configuration.</p>
        </div>
        <div class="feature-item">
            <h4>🔒 Safe Learning Environment</h4>
            <p>Age-appropriate content filtering, privacy controls, moderated community interactions, and comprehensive data protection measures.</p>
        </div>
    </div>

    <h2>👥 Role-Specific Documentation</h2>
    <div class="role-grid">
        <div class="role-card">
            <span class="role-icon">👨🎓</span>
            <h3>Student Experience Guide</h3>
            <p><strong>Creative Expression:</strong> Master diary writing techniques and storytelling skills</p>
            <p><strong>Interactive Learning:</strong> Excel at Story Maker and Block Games with proven strategies</p>
            <p><strong>Social Engagement:</strong> Build friendships and participate in the learning community</p>
            <p><strong>Progress Mastery:</strong> Track improvements and unlock achievements systematically</p>
            <p><strong>Customization:</strong> Personalize your learning environment and diary themes</p>
            <div class="button-container">
                <a href="javascript:void(0)" onclick="openGuide('student/getting-started.md')" class="button">Start Learning Journey</a>
            </div>
        </div>
        <div class="role-card">
            <span class="role-icon">👩🏫</span>
            <h3>Tutor Management System</h3>
            <p><strong>Student Oversight:</strong> Efficiently manage multiple students with comprehensive dashboards</p>
            <p><strong>Feedback Excellence:</strong> Provide detailed, constructive feedback using advanced review tools</p>
            <p><strong>Progress Monitoring:</strong> Track individual student development with detailed analytics</p>
            <p><strong>Communication Hub:</strong> Maintain effective student and parent communication channels</p>
            <p><strong>Teaching Analytics:</strong> Measure teaching effectiveness and student engagement</p>
            <div class="button-container">
                <a href="javascript:void(0)" onclick="openGuide('tutor/README.md')" class="button">Explore Tutor Tools</a>
            </div>
        </div>
        <div class="role-card">
            <span class="role-icon">👨💼</span>
            <h3>Administrator Control Center</h3>
            <p><strong>User Administration:</strong> Complete control over accounts, roles, and permissions management</p>
            <p><strong>Content Management:</strong> Oversee story prompts, themes, and educational content</p>
            <p><strong>System Analytics:</strong> Monitor platform-wide performance and user engagement metrics</p>
            <p><strong>Configuration Control:</strong> Customize features, subscription plans, and system settings</p>
            <p><strong>Security Management:</strong> Maintain platform security and data protection standards</p>
            <div class="button-container">
                <a href="javascript:void(0)" onclick="openGuide('admin/README.md')" class="button">Access Admin Guide</a>
            </div>
        </div>
    </div>

    <div class="tip-box">
        <strong>💡 Pro Tip:</strong> Use the search function in the documentation browser to quickly locate specific features, 
        troubleshooting guides, or step-by-step tutorials. All guides include detailed screenshots and practical examples.
    </div>

    <div class="navigation-section">
        <h3>🗺️ Navigate the Complete Documentation</h3>
        <p>Access our comprehensive guide browser with advanced search, categorized navigation, and cross-referenced content</p>
        <div class="button-container">
            <a href="/user-guide/view-guide.html?path=README.md" class="button primary">Browse All Documentation</a>
            <a href="/user-guide/view-guide.html?path=student/diary/diary-basics.md" class="button">Diary Writing Guide</a>
            <a href="/user-guide/view-guide.html?path=student/games/story-maker.md" class="button">Story Maker Tutorial</a>
            <a href="/user-guide/view-guide.html?path=tutor/diary-review.md" class="button">Review Process</a>
        </div>
        
        <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
            <h4 style="margin-top: 0; color: #2c3e50;">📋 Documentation Features</h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; text-align: left;">
                <div>✅ <strong>Searchable Content</strong><br><small>Find any topic instantly</small></div>
                <div>✅ <strong>Mobile Responsive</strong><br><small>Access on any device</small></div>
                <div>✅ <strong>Cross-Referenced</strong><br><small>Navigate between related topics</small></div>
                <div>✅ <strong>Regular Updates</strong><br><small>Always current information</small></div>
            </div>
        </div>
    </div>

    <footer style="text-align: center; margin-top: 50px; padding: 30px; color: #7f8c8d; border-top: 1px solid #ecf0f1;">
        <p><strong>HEC Platform Documentation</strong> | Last Updated: December 2024</p>
        <p>Comprehensive guides for students, tutors, and administrators</p>
    </footer>
</body>
</html>