import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddStudentSkinSupportToDiaryEntry1642000000000 implements MigrationInterface {
  name = 'AddStudentSkinSupportToDiaryEntry1642000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add student_skin_id column
    await queryRunner.query(`
      ALTER TABLE "diary_entry" 
      ADD COLUMN "student_skin_id" uuid NULL
    `);

    // Add skin_type column with default value
    await queryRunner.query(`
      ALTER TABLE "diary_entry" 
      ADD COLUMN "skin_type" varchar NOT NULL DEFAULT 'global'
    `);

    // Add foreign key constraint for student_skin_id
    await queryRunner.query(`
      ALTER TABLE "diary_entry" 
      ADD CONSTRAINT "FK_diary_entry_student_skin" 
      FOREIGN KEY ("student_skin_id") 
      REFERENCES "student_diary_skin"("id") 
      ON DELETE SET NULL ON UPDATE NO ACTION
    `);

    // Make skin_id nullable since we now support student skins
    await queryRunner.query(`
      ALTER TABLE "diary_entry" 
      ALTER COLUMN "skin_id" DROP NOT NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove foreign key constraint
    await queryRunner.query(`
      ALTER TABLE "diary_entry" 
      DROP CONSTRAINT "FK_diary_entry_student_skin"
    `);

    // Remove columns
    await queryRunner.query(`
      ALTER TABLE "diary_entry" 
      DROP COLUMN "student_skin_id"
    `);

    await queryRunner.query(`
      ALTER TABLE "diary_entry" 
      DROP COLUMN "skin_type"
    `);

    // Make skin_id not nullable again
    await queryRunner.query(`
      ALTER TABLE "diary_entry" 
      ALTER COLUMN "skin_id" SET NOT NULL
    `);
  }
}