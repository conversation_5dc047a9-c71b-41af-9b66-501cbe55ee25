import { WritingEntryStatus } from '../enums/writing-entry-status.enum';

/**
 * Base interface for all writing entries (diary, mission diary, novel)
 * Ensures consistent structure and behavior across all writing modules
 */
export interface IWritingEntry {
  id: string;
  content: string;
  wordCount: number;
  status: any; // Allow any status type for flexibility
  unifiedStatus: WritingEntryStatus;

  // Submission/Draft tracking
  isDraft: boolean;
  lastSubmittedAt: Date | null;
  lastReviewedAt: Date | null;
  canSubmitNewVersion: boolean;
  submittedVersionCount: number;
  currentSubmittedVersionId: string | null;

  // Resubmission tracking
  isResubmission: boolean;
  resubmissionType: 'after_review' | 'after_confirmation' | null;
  previousReviewCount: number;
  previousConfirmationCount: number;

  // Timestamps
  createdAt: Date;
  updatedAt: Date;
  submittedAt: Date | null;
  reviewedAt: Date | null;

  // Student/Tutor tracking
  studentId: string;
  reviewedBy: string | null;
  evaluatedBy: string | null;
  evaluatedAt: Date | null;

  // Score tracking
  score: number | null;
  gainedScore: number | null;
}

/**
 * Base interface for writing entry history/versions
 */
export interface IWritingEntryHistory {
  id: string;
  content: string;
  versionNumber: number;
  isLatest: boolean;
  isSubmittedVersion: boolean;
  submissionNumber: number | null;
  submittedAt: Date | null;
  wordCount: number;
  metaData?: any;

  // Resubmission tracking
  isResubmission: boolean;
  resubmissionType: 'after_review' | 'after_confirmation' | null;
  previousStatus: string | null;

  // Timestamps
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy: string;
}

/**
 * Unified submission DTO interface
 */
export interface ISubmitWritingEntryDto {
  content: string;
  skinId?: string;
  backgroundColor?: string;
}

/**
 * Unified update DTO interface
 */
export interface IUpdateWritingEntryDto {
  content?: string;
  skinId?: string;
  backgroundColor?: string;
}

/**
 * Unified response DTO interface
 */
export interface IWritingEntryResponseDto {
  id: string;
  content: string;
  wordCount: number;
  status: WritingEntryStatus;
  isDraft: boolean;
  submittedVersionCount: number;
  canSubmitNewVersion: boolean;
  lastSubmittedAt: Date | null;
  lastReviewedAt: Date | null;
  score: number | null;
  gainedScore: number | null;
  createdAt: Date;
  updatedAt: Date;
  submittedAt: Date | null;
  reviewedAt: Date | null;

  // Resubmission tracking
  isResubmission: boolean;
  resubmissionType: 'after_review' | 'after_confirmation' | null;
  previousReviewCount: number;
  previousConfirmationCount: number;
}

/**
 * Unified feedback/correction interface
 */
export interface IWritingEntryFeedback {
  id: string;
  entryId: string;
  tutorId: string;
  feedback: string;
  score: number | null;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Service interface for writing entry operations
 */
export interface IWritingEntryService {
  /**
   * Update entry (save as draft)
   */
  updateEntry(studentId: string, entryId: string, updateDto: IUpdateWritingEntryDto): Promise<IWritingEntryResponseDto>;

  /**
   * Submit entry for review
   */
  submitEntry(studentId: string, entryId: string, submitDto: ISubmitWritingEntryDto): Promise<IWritingEntryResponseDto>;

  /**
   * Get entry by ID
   */
  getEntryById(entryId: string, userId: string): Promise<IWritingEntryResponseDto>;

  /**
   * Get entry history/versions
   */
  getEntryHistory(entryId: string, userId: string): Promise<IWritingEntryHistory[]>;
}

/**
 * Review service interface for writing entries
 */
export interface IWritingEntryReviewService {
  /**
   * Start reviewing an entry
   */
  startReview(entryId: string, tutorId: string): Promise<IWritingEntryResponseDto>;

  /**
   * Submit review/correction
   */
  submitReview(entryId: string, tutorId: string, reviewDto: any): Promise<IWritingEntryFeedback>;

  /**
   * Confirm entry review
   */
  confirmReview(entryId: string, tutorId: string): Promise<IWritingEntryResponseDto>;
}
