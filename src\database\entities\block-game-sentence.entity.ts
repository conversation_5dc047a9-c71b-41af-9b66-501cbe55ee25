import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { BlockGame } from './block-game.entity';

@Entity()
export class BlockGameSentence extends AuditableBaseEntity {
  @Column({ name: 'block_game_id' })
  blockGameId: string;

  @Column({ name: 'starting_part', type: 'text' })
  startingPart: string;

  @Column({ name: 'expanding_part', type: 'text' })
  expandingPart: string;

  @Column({ name: 'starting_part_answers', type: 'text', array: true, default: '{}' })
  startingPartAnswers: string[];

  @Column({ name: 'starting_part_distractors', type: 'text', array: true, default: '{}' })
  startingPartDistractors: string[];

  @Column({ name: 'expanding_part_answers', type: 'text', array: true, default: '{}' })
  expandingPartAnswers: string[];

  @Column({ name: 'expanding_part_distractors', type: 'text', array: true, default: '{}' })
  expandingPartDistractors: string[];

  @Column({ name: 'sentence_order' })
  sentenceOrder: number;

  // Note: No foreign key constraint to allow referencing both BlockGame and TutorBlockGame
}
