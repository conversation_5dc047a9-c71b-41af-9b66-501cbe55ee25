import { <PERSON><PERSON><PERSON>, Column, <PERSON><PERSON><PERSON><PERSON><PERSON>, Join<PERSON>olumn } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { TranscriptionSession } from './transcription-session.entity';
import { Sentence } from './sentence.entity';

@Entity('transcription_attempts')
export class TranscriptionAttempt extends AuditableBaseEntity {
  @Column({ name: 'session_id', type: 'uuid' })
  sessionId: string;

  @Column({ name: 'sentence_id', type: 'uuid' })
  sentenceId: string;

  @Column({ name: 'user_input', type: 'text' })
  userInput: string;

  @Column({ name: 'is_correct', type: 'boolean' })
  isCorrect: boolean;

  @Column({ type: 'json', nullable: true })
  errors: any;

  @Column({ name: 'time_spent_seconds', type: 'int', nullable: true })
  timeSpentSeconds: number;

  @Column({ name: 'attempted_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  attemptedAt: Date;

  @ManyToOne(() => TranscriptionSession, session => session.attempts)
  @JoinColumn({ name: 'session_id' })
  session: TranscriptionSession;

  @ManyToOne(() => Sentence, sentence => sentence.attempts)
  @JoinColumn({ name: 'sentence_id' })
  sentence: Sentence;
}