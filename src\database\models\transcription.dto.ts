import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsBoolean, IsNumber, IsUUID } from 'class-validator';

export class CreateBookDto {
  @ApiProperty({ example: 'Alice\'s Adventures in Wonderland' })
  @IsString()
  title: string;

  @ApiProperty({ example: '<PERSON>' })
  @IsString()
  author: string;

  @ApiProperty({ example: 'A classic children\'s story', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ example: 1865 })
  @IsNumber()
  publicationYear: number;

  @ApiProperty({ example: 'Chapter 1. Down the Rabbit-Hole...' })
  @IsString()
  content: string;
}

export class BookResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  title: string;

  @ApiProperty()
  author: string;

  @ApiProperty()
  description: string;

  @ApiProperty()
  publicationYear: number;

  @ApiProperty()
  content: string;

  @ApiProperty()
  sentenceCount: number;

  @ApiProperty()
  isActive: boolean;

  @ApiProperty()
  createdAt: Date;
}

export class SentenceResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  content: string;

  @ApiProperty()
  orderIndex: number;

  @ApiProperty()
  difficultyLevel: string;

  @ApiProperty()
  grammarPattern: string;

  @ApiProperty()
  bookId: string;
}

export class CreateTranscriptionSessionDto {
  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************' })
  @IsUUID()
  bookId: string;
}

export class TranscriptionSessionResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  studentId: string;

  @ApiProperty()
  bookId: string;

  @ApiProperty()
  startedAt: Date;

  @ApiProperty()
  completedAt: Date;

  @ApiProperty()
  totalAttempts: number;

  @ApiProperty()
  correctAttempts: number;

  @ApiProperty()
  isCompleted: boolean;
}

export class CreateTranscriptionAttemptDto {
  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************' })
  @IsUUID()
  sentenceId: string;

  @ApiProperty({ example: 'Alice was beginning to get very tired.' })
  @IsString()
  userInput: string;

  @ApiProperty({ example: 45, required: false })
  @IsOptional()
  @IsNumber()
  timeSpentSeconds?: number;
}

export class TranscriptionAttemptResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  sentenceId: string;

  @ApiProperty()
  userInput: string;

  @ApiProperty()
  isCorrect: boolean;

  @ApiProperty()
  errors: any;

  @ApiProperty()
  timeSpentSeconds: number;

  @ApiProperty()
  attemptedAt: Date;

  @ApiProperty()
  sentence: SentenceResponseDto;
}