import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TranscriptionAttempt } from '../../../database/entities/transcription-attempt.entity';
import { CreateTranscriptionAttemptDto, TranscriptionAttemptResponseDto } from '../../../database/models/transcription.dto';
import { SentenceService } from './sentence.service';
import { TranscriptionSessionService } from './transcription-session.service';

@Injectable()
export class TranscriptionAttemptService {
  constructor(
    @InjectRepository(TranscriptionAttempt)
    private attemptRepository: Repository<TranscriptionAttempt>,
    private sentenceService: SentenceService,
    private sessionService: TranscriptionSessionService,
  ) {}

  async create(sessionId: string, createAttemptDto: CreateTranscriptionAttemptDto): Promise<TranscriptionAttemptResponseDto> {
    const sentence = await this.sentenceService.findById(createAttemptDto.sentenceId);
    const validation = this.validateTranscription(sentence.content, createAttemptDto.userInput);

    const attempt = this.attemptRepository.create({
      sessionId,
      sentenceId: createAttemptDto.sentenceId,
      userInput: createAttemptDto.userInput,
      isCorrect: validation.isCorrect,
      errors: validation.errors,
      timeSpentSeconds: createAttemptDto.timeSpentSeconds,
      attemptedAt: new Date(),
    });

    const savedAttempt = await this.attemptRepository.save(attempt);
    
    // Update session statistics asynchronously for better performance
    setImmediate(() => {
      this.sessionService.updateSessionStats(sessionId, validation.isCorrect).catch(err => {
        console.error('Failed to update session stats:', err);
      });
    });

    return this.mapToResponseDto(savedAttempt, sentence);
  }

  async findBySessionId(sessionId: string): Promise<TranscriptionAttemptResponseDto[]> {
    const attempts = await this.attemptRepository.find({
      where: { sessionId },
      relations: ['sentence'],
      order: { attemptedAt: 'ASC' }
    });
    
    return attempts.map(attempt => this.mapToResponseDto(attempt, attempt.sentence));
  }

  private validateTranscription(originalText: string, userInput: string): { isCorrect: boolean; errors: any } {
    const original = originalText.trim().toLowerCase();
    const input = userInput.trim().toLowerCase();
    
    if (original === input) {
      return { isCorrect: true, errors: null };
    }

    const errors = [];
    const originalWords = original.split(' ');
    const inputWords = input.split(' ');

    // Check for spelling errors
    for (let i = 0; i < Math.max(originalWords.length, inputWords.length); i++) {
      const originalWord = originalWords[i] || '';
      const inputWord = inputWords[i] || '';
      
      if (originalWord !== inputWord) {
        errors.push({
          type: 'spelling',
          position: i,
          expected: originalWord,
          actual: inputWord,
        });
      }
    }

    // Check punctuation
    if (originalText.slice(-1) !== userInput.slice(-1)) {
      errors.push({
        type: 'punctuation',
        expected: originalText.slice(-1),
        actual: userInput.slice(-1) || 'missing',
      });
    }

    return { isCorrect: false, errors };
  }

  private mapToResponseDto(attempt: TranscriptionAttempt, sentence?: any): TranscriptionAttemptResponseDto {
    return {
      id: attempt.id,
      sentenceId: attempt.sentenceId,
      userInput: attempt.userInput,
      isCorrect: attempt.isCorrect,
      errors: attempt.errors,
      timeSpentSeconds: attempt.timeSpentSeconds,
      attemptedAt: attempt.attemptedAt,
      sentence: sentence,
    };
  }
}