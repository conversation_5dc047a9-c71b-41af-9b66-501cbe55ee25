import { Controller, Get, Post, Body, UseGuards, Param, ParseUUIDPipe } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiParam, ApiBody } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../../common/guards/jwt.guard';
import { StudentGuard } from '../../../common/guards/student.guard';
import { GetUser } from '../../../common/decorators/get-user.decorator';
import { BlockGameService } from './block-game.service';
import { BlockGameDetailDto, SubmitBlockGameDto, BlockGameAttemptResultDto } from '../../../database/models/block-game/block-game-student.dto';
import { ApiResponse } from '../../../common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiErrorResponse } from '../../../common/decorators/api-response.decorator';

@ApiTags('Play-Block')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard, StudentGuard)
@Controller('play/block')
export class BlockGameController {
  constructor(private readonly blockGameService: BlockGameService) {}

  @Get('play')
  @ApiOperation({
    summary: 'Get a random block game to play',
    description: 'Retrieves a random active block game with gap-based sentences and answer options for student to play. Prioritizes tutor-created games over admin games.',
  })
  @ApiOkResponseWithType(BlockGameDetailDto, 'Random block game retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'No games available at the moment')
  async getRandomBlockGame(@GetUser() user: any): Promise<ApiResponse<BlockGameDetailDto>> {
    const studentId = user.sub || user.id;
    const result = await this.blockGameService.getRandomBlockGame(studentId);
    return ApiResponse.success(result, 'Random block game retrieved successfully');
  }

  @Get('play-again/:gameId')
  @ApiOperation({
    summary: 'Play a specific block game again',
    description: 'Retrieves a specific block game with gap-based sentences and answer options for student to play again.',
  })
  @ApiParam({
    name: 'gameId',
    type: 'string',
    description: 'Block game ID to play again',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiOkResponseWithType(BlockGameDetailDto, 'Block game retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Game not found or not available')
  async playBlockGameAgain(@Param('gameId', ParseUUIDPipe) gameId: string, @GetUser() user: any): Promise<ApiResponse<BlockGameDetailDto>> {
    const studentId = user.sub || user.id;
    const result = await this.blockGameService.getBlockGameForPlay(gameId, studentId);
    return ApiResponse.success(result, 'Block game retrieved successfully');
  }

  @Get('games/:id')
  @ApiOperation({
    summary: "Get a block game for playing. (It's kept for future if replay option is needed)",
    description: 'Retrieves a specific block game with gap-based sentences and answer options for student to play',
  })
  @ApiParam({
    name: 'id',
    type: 'string',
    description: 'Block game ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiOkResponseWithType(BlockGameDetailDto, 'Block game retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Game not found or not available')
  async getBlockGameForPlay(@Param('id', ParseUUIDPipe) id: string, @GetUser() user: any): Promise<ApiResponse<BlockGameDetailDto>> {
    const studentId = user.sub || user.id;
    const result = await this.blockGameService.getBlockGameForPlay(id, studentId);
    return ApiResponse.success(result, 'Block game retrieved successfully');
  }

  @Post('submit')
  @UseGuards(JwtAuthGuard, StudentGuard)
  @ApiOperation({
    summary: 'Submit block game attempt',
    description: "Submit student's gap answers for each sentence and get detailed results with scoring based on correct gap filling",
  })
  @ApiBody({
    description: 'Block game submission with gap answers',
    type: SubmitBlockGameDto,
    examples: {
      gapAnswersSubmission: {
        summary: 'Submit gap answers for block game',
        description: 'Example submission focusing on gap answers for each sentence',
        value: {
          block_game_id: '123e4567-e89b-12d3-a456-************',
          sentence_constructions: [
            {
              starting_gap_answers: ['strange'],
              expanding_gap_answers: ['loud'],
              sentence_order: 1
            },
            {
              starting_gap_answers: ['check'],
              expanding_gap_answers: ['tomorrow'],
              sentence_order: 2
            }
          ]
        }
      },
      withOptionalSentences: {
        summary: 'Submit with optional sentence text',
        description: 'Example submission including optional sentence constructions',
        value: {
          block_game_id: '123e4567-e89b-12d3-a456-************',
          sentence_constructions: [
            {
              starting_sentence: 'The cat makes a strange',
              expanding_sentence: 'loud sound',
              starting_gap_answers: ['strange'],
              expanding_gap_answers: ['loud'],
              sentence_order: 1
            },
            {
              starting_sentence: 'I will check',
              expanding_sentence: 'it tomorrow morning',
              starting_gap_answers: ['check'],
              expanding_gap_answers: ['tomorrow'],
              sentence_order: 2
            }
          ]
        }
      }
    }
  })
  @ApiOkResponseWithType(BlockGameAttemptResultDto, 'Block game submitted successfully')
  @ApiErrorResponse(400, 'Invalid submission data')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Game not found or not available')
  async submitBlockGame(@Body() submitDto: SubmitBlockGameDto, @GetUser() user: any): Promise<ApiResponse<BlockGameAttemptResultDto>> {
    const studentId = user.sub || user.id;
    const result = await this.blockGameService.submitAttempt(studentId, submitDto);
    return ApiResponse.success(result, 'You completed successfully');
  }
}
