import { Test, TestingModule } from '@nestjs/testing';
import { AuditLogService } from './audit-log.service';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import { AuditLog } from '../../database/entities/audit-log.entity';
import { LoggerService } from './logger.service';
import { CurrentUserService } from './current-user.service';

describe('AuditLogService', () => {
  let service: AuditLogService;
  let mockRepository: jest.Mocked<Repository<AuditLog>>;
  let mockLoggerService: jest.Mocked<LoggerService>;
  let mockCurrentUserService: jest.Mocked<CurrentUserService>;

  const createMockRepository = () => ({
    create: jest.fn(),
    save: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
    createQueryBuilder: jest.fn(() => ({
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      getMany: jest.fn(),
      getOne: jest.fn(),
    })),
  });

  beforeEach(async () => {
    const mockLogger = {
      log: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
    };

    const mockCurrentUser = {
      getCurrentUser: jest.fn(),
      getUserId: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuditLogService,
        {
          provide: getRepositoryToken(AuditLog),
          useValue: createMockRepository(),
        },
        {
          provide: LoggerService,
          useValue: mockLogger,
        },
        {
          provide: CurrentUserService,
          useValue: mockCurrentUser,
        },
      ],
    }).compile();

    service = module.get<AuditLogService>(AuditLogService);
    mockRepository = module.get(getRepositoryToken(AuditLog));
    mockLoggerService = module.get(LoggerService);
    mockCurrentUserService = module.get(CurrentUserService);
  });

  describe('Core Functionality', () => {
    it('should be defined', () => {
      expect(service).toBeDefined();
    });

    it('should create audit log entry successfully', async () => {
      const auditData = {
        action: 'CREATE',
        entityType: 'User',
        entityId: 'user-123',
        changes: { name: 'New User' },
        userId: 'current-user-123',
      };

      const mockAuditLog = {
        id: 'audit-123',
        ...auditData,
        timestamp: new Date(),
      };

      mockRepository.create.mockReturnValue(mockAuditLog as any);
      mockRepository.save.mockResolvedValue(mockAuditLog as any);
      mockCurrentUserService.getUserId.mockReturnValue('current-user-123');

      const result = await service.createAuditLog(auditData);

      expect(result).toBeDefined();
      expect(mockRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          action: 'CREATE',
          entityType: 'User',
          entityId: 'user-123',
        })
      );
      expect(mockRepository.save).toHaveBeenCalled();
    });

    it('should retrieve audit logs with filters', async () => {
      const filters = {
        entityType: 'User',
        action: 'UPDATE',
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-12-31'),
      };

      const mockLogs = [
        { id: 'log-1', action: 'UPDATE', entityType: 'User' },
        { id: 'log-2', action: 'UPDATE', entityType: 'User' },
      ];

      const mockQueryBuilder = mockRepository.createQueryBuilder();
      mockQueryBuilder.getMany.mockResolvedValue(mockLogs as any);

      const result = await service.getAuditLogs(filters);

      expect(result).toEqual(mockLogs);
      expect(mockQueryBuilder.where).toHaveBeenCalled();
      expect(mockQueryBuilder.andWhere).toHaveBeenCalled();
    });

    it('should get audit trail for specific entity', async () => {
      const entityType = 'DiaryEntry';
      const entityId = 'diary-123';

      const mockTrail = [
        { id: 'log-1', action: 'CREATE', timestamp: new Date() },
        { id: 'log-2', action: 'UPDATE', timestamp: new Date() },
      ];

      const mockQueryBuilder = mockRepository.createQueryBuilder();
      mockQueryBuilder.getMany.mockResolvedValue(mockTrail as any);

      const result = await service.getEntityAuditTrail(entityType, entityId);

      expect(result).toEqual(mockTrail);
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        'entityType = :entityType AND entityId = :entityId',
        { entityType, entityId }
      );
      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith('timestamp', 'DESC');
    });
  });

  describe('Security & Validation', () => {
    it('should sanitize sensitive data in changes', async () => {
      const auditData = {
        action: 'UPDATE',
        entityType: 'User',
        entityId: 'user-123',
        changes: {
          name: 'John Doe',
          password: 'secret123',
          email: '<EMAIL>',
        },
        userId: 'current-user-123',
      };

      mockRepository.create.mockReturnValue({} as any);
      mockRepository.save.mockResolvedValue({} as any);
      mockCurrentUserService.getUserId.mockReturnValue('current-user-123');

      await service.createAuditLog(auditData);

      expect(mockRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          changes: expect.not.objectContaining({
            password: 'secret123',
          }),
        })
      );
    });

    it('should validate required fields', async () => {
      const invalidData = {
        action: '',
        entityType: '',
        entityId: '',
      };

      await expect(service.createAuditLog(invalidData as any)).rejects.toThrow(
        'Required audit log fields missing'
      );
    });

    it('should prevent audit log tampering', async () => {
      const auditData = {
        action: 'DELETE',
        entityType: 'AuditLog',
        entityId: 'audit-123',
        userId: 'malicious-user',
      };

      await expect(service.createAuditLog(auditData)).rejects.toThrow(
        'Cannot audit audit log operations'
      );
    });

    it('should validate user permissions for audit access', async () => {
      mockCurrentUserService.getCurrentUser.mockReturnValue({
        id: 'user-123',
        type: 'STUDENT',
      } as any);

      await expect(service.getAuditLogs({})).rejects.toThrow(
        'Insufficient permissions to access audit logs'
      );
    });
  });

  describe('Data Integrity', () => {
    it('should handle database save failures', async () => {
      const auditData = {
        action: 'CREATE',
        entityType: 'User',
        entityId: 'user-123',
        userId: 'current-user-123',
      };

      mockRepository.create.mockReturnValue({} as any);
      mockRepository.save.mockRejectedValue(new Error('Database error'));
      mockCurrentUserService.getUserId.mockReturnValue('current-user-123');

      await expect(service.createAuditLog(auditData)).rejects.toThrow('Database error');
      expect(mockLoggerService.error).toHaveBeenCalledWith(
        expect.stringContaining('Failed to create audit log')
      );
    });

    it('should handle concurrent audit log creation', async () => {
      const auditData = {
        action: 'UPDATE',
        entityType: 'User',
        entityId: 'user-123',
        userId: 'current-user-123',
      };

      mockRepository.create.mockReturnValue({} as any);
      mockRepository.save.mockResolvedValue({} as any);
      mockCurrentUserService.getUserId.mockReturnValue('current-user-123');

      const promises = Array(10).fill(0).map(() => 
        service.createAuditLog({ ...auditData })
      );

      await expect(Promise.all(promises)).resolves.toHaveLength(10);
      expect(mockRepository.save).toHaveBeenCalledTimes(10);
    });

    it('should maintain audit log immutability', async () => {
      const existingLog = {
        id: 'audit-123',
        action: 'CREATE',
        entityType: 'User',
        timestamp: new Date(),
      };

      mockRepository.findOne.mockResolvedValue(existingLog as any);

      await expect(
        service.updateAuditLog('audit-123', { action: 'UPDATE' })
      ).rejects.toThrow('Audit logs are immutable');
    });
  });

  describe('Edge Cases', () => {
    it('should handle null/undefined inputs', async () => {
      await expect(service.createAuditLog(null)).rejects.toThrow();
      await expect(service.createAuditLog(undefined)).rejects.toThrow();
    });

    it('should handle large change objects', async () => {
      const largeChanges = {};
      for (let i = 0; i < 1000; i++) {
        largeChanges[`field${i}`] = `value${i}`;
      }

      const auditData = {
        action: 'UPDATE',
        entityType: 'User',
        entityId: 'user-123',
        changes: largeChanges,
        userId: 'current-user-123',
      };

      mockRepository.create.mockReturnValue({} as any);
      mockRepository.save.mockResolvedValue({} as any);
      mockCurrentUserService.getUserId.mockReturnValue('current-user-123');

      await expect(service.createAuditLog(auditData)).resolves.toBeDefined();
      expect(mockLoggerService.warn).toHaveBeenCalledWith(
        expect.stringContaining('Large audit log entry')
      );
    });

    it('should handle special characters in entity data', async () => {
      const auditData = {
        action: 'CREATE',
        entityType: 'User',
        entityId: 'user-123',
        changes: {
          name: 'José María Ñoño',
          description: 'Special chars: <>&"\'',
        },
        userId: 'current-user-123',
      };

      mockRepository.create.mockReturnValue({} as any);
      mockRepository.save.mockResolvedValue({} as any);
      mockCurrentUserService.getUserId.mockReturnValue('current-user-123');

      await expect(service.createAuditLog(auditData)).resolves.toBeDefined();
    });
  });
});