import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, LessThanOrEqual, MoreThan, In } from 'typeorm';
import { <PERSON><PERSON>, CronExpression } from '@nestjs/schedule';
import { NotificationOutbox, OutboxStatus, OutboxNotificationType } from '../../database/entities/notification-outbox.entity';
import { NotificationService } from './notification.service';
import { NotificationType } from '../../database/entities/notification.entity';
import { getCurrentUTCDate } from '../../common/utils/date-utils';

@Injectable()
export class NotificationOutboxService {
  private readonly logger = new Logger(NotificationOutboxService.name);
  private readonly batchSize = 50; // Process 50 notifications at a time
  private isProcessing = false;

  constructor(
    @InjectRepository(NotificationOutbox)
    private readonly outboxRepository: Repository<NotificationOutbox>,
    private readonly notificationService: NotificationService,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * Map OutboxNotificationType back to NotificationType for the notification helper
   */
  private mapFromOutboxType(type: OutboxNotificationType): NotificationType {
    const mapping: Record<OutboxNotificationType, NotificationType> = {
      [OutboxNotificationType.DIARY_SUBMISSION]: NotificationType.DIARY_SUBMISSION,
      [OutboxNotificationType.DIARY_REVIEWED]: NotificationType.DIARY_REVIEW,
      [OutboxNotificationType.DIARY_CONFIRMED]: NotificationType.DIARY_REVIEW,
      [OutboxNotificationType.MISSION_SUBMISSION]: NotificationType.MISSION_SUBMISSION,
      [OutboxNotificationType.MISSION_REVIEWED]: NotificationType.MISSION_FEEDBACK,
      [OutboxNotificationType.MISSION_CONFIRMED]: NotificationType.MISSION_CONFIRMED,
      [OutboxNotificationType.NOVEL_SUBMISSION]: NotificationType.NOVEL_SUBMISSION,
      [OutboxNotificationType.NOVEL_REVIEWED]: NotificationType.NOVEL_REVIEW,
      [OutboxNotificationType.QA_SUBMISSION]: NotificationType.QA_SUBMISSION,
      [OutboxNotificationType.QA_REVIEWED]: NotificationType.QA_REVIEW,
      [OutboxNotificationType.GENERAL]: NotificationType.SYSTEM,
      [OutboxNotificationType.SYSTEM]: NotificationType.SYSTEM,
      [OutboxNotificationType.REMINDER]: NotificationType.SYSTEM,
      [OutboxNotificationType.ACHIEVEMENT]: NotificationType.AWARD_WINNER,
      [OutboxNotificationType.FRIEND_REQUEST]: NotificationType.DIARY_FOLLOW_REQUEST,
      [OutboxNotificationType.FRIEND_ACCEPTED]: NotificationType.SYSTEM,
      [OutboxNotificationType.CHAT_MESSAGE]: NotificationType.CHAT_MESSAGE,
      [OutboxNotificationType.SUBSCRIPTION_EXPIRY]: NotificationType.SYSTEM,
      [OutboxNotificationType.SUBSCRIPTION_RENEWED]: NotificationType.SYSTEM,
      [OutboxNotificationType.PAYMENT_SUCCESS]: NotificationType.SYSTEM,
      [OutboxNotificationType.PAYMENT_FAILED]: NotificationType.SYSTEM,
      [OutboxNotificationType.TUTOR_ASSIGNMENT]: NotificationType.TUTOR_ASSIGNMENT,
      [OutboxNotificationType.TUTOR_FEEDBACK]: NotificationType.TUTOR_ASSIGNMENT,
      [OutboxNotificationType.ADMIN_ANNOUNCEMENT]: NotificationType.SYSTEM,
    };

    return mapping[type] || NotificationType.SYSTEM;
  }

  /**
   * Add a notification to the outbox for reliable delivery
   * Includes deduplication logic to prevent duplicate notifications
   */
  async addToOutbox(
    userId: string,
    type: OutboxNotificationType,
    title: string,
    message: string,
    options: {
      relatedEntityId?: string;
      relatedEntityType?: string;
      htmlContent?: string;
      webLink?: string;
      deepLink?: string;
      sendEmail?: boolean;
      sendPush?: boolean;
      sendInApp?: boolean;
      sendRealtime?: boolean;
    } = {},
    metadata: {
      submissionId?: string;
      entryType?: string;
      priority?: number;
      batchId?: string;
    } = {},
  ): Promise<NotificationOutbox> {
    try {
      // Check for duplicate notifications to prevent spam
      const duplicateCheck = await this.checkForDuplicateNotification(userId, type, options, metadata);
      if (duplicateCheck) {
        this.logger.log(`Duplicate notification detected for user ${userId}, type ${type}. Using existing: ${duplicateCheck.id}`);
        return duplicateCheck;
      }

      const outboxEntry = this.outboxRepository.create({
        userId,
        type,
        title,
        message,
        options,
        metadata,
        status: OutboxStatus.PENDING,
      });

      const saved = await this.outboxRepository.save(outboxEntry);
      this.logger.log(`Added notification to outbox: ${saved.id} for user ${userId}`);

      return saved;
    } catch (error) {
      this.logger.error(`Failed to add notification to outbox: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Check for duplicate notifications to prevent spam
   * Looks for similar notifications within the last 5 minutes
   */
  private async checkForDuplicateNotification(
    userId: string,
    type: OutboxNotificationType,
    options: any,
    metadata: any
  ): Promise<NotificationOutbox | null> {
    try {
      // Only check for duplicates for certain notification types that are prone to duplication
      const duplicateCheckTypes = [
        OutboxNotificationType.TUTOR_ASSIGNMENT,
        OutboxNotificationType.DIARY_SUBMISSION,
        OutboxNotificationType.NOVEL_SUBMISSION,
        OutboxNotificationType.QA_SUBMISSION,
        OutboxNotificationType.MISSION_SUBMISSION
      ];

      if (!duplicateCheckTypes.includes(type)) {
        return null; // No duplicate check for other types
      }

      // Check for notifications in the last 5 minutes
      const fiveMinutesAgo = new Date(getCurrentUTCDate().getTime() - 5 * 60 * 1000);

      const existingNotification = await this.outboxRepository.findOne({
        where: {
          userId,
          type,
          createdAt: MoreThan(fiveMinutesAgo),
          status: In([OutboxStatus.PENDING, OutboxStatus.PROCESSING, OutboxStatus.COMPLETED])
        },
        order: { createdAt: 'DESC' }
      });

      if (existingNotification) {
        // Additional checks for tutor assignments to prevent duplicates during subscription
        if (type === OutboxNotificationType.TUTOR_ASSIGNMENT) {
          // Check if it's the same related entity (student-tutor mapping)
          if (options?.relatedEntityId &&
              existingNotification.options?.relatedEntityId === options.relatedEntityId) {
            return existingNotification;
          }

          // Check if it's the same batch (same subscription event)
          if (metadata?.batchId &&
              existingNotification.metadata?.batchId === metadata.batchId) {
            return existingNotification;
          }
        }

        // For other types, check related entity
        if (options?.relatedEntityId &&
            existingNotification.options?.relatedEntityId === options.relatedEntityId) {
          return existingNotification;
        }
      }

      return null;
    } catch (error) {
      this.logger.error(`Error checking for duplicate notifications: ${error.message}`, error.stack);
      return null; // Don't block notification creation if duplicate check fails
    }
  }

  /**
   * Process pending notifications from the outbox
   * Runs every 30 seconds
   */
  @Cron(CronExpression.EVERY_30_SECONDS)
  async processPendingNotifications(): Promise<void> {
    if (this.isProcessing) {
      this.logger.debug('Outbox processing already in progress, skipping...');
      return;
    }

    this.isProcessing = true;
    
    try {
      const pendingNotifications = await this.getPendingNotifications();
      
      if (pendingNotifications.length === 0) {
        this.logger.debug('No pending notifications to process');
        return;
      }

      this.logger.log(`Processing ${pendingNotifications.length} pending notifications`);
      
      await this.processNotificationBatch(pendingNotifications);
      
    } catch (error) {
      this.logger.error(`Error processing outbox notifications: ${error.message}`, error.stack);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Process retry notifications
   * Runs every 5 minutes
   */
  @Cron(CronExpression.EVERY_5_MINUTES)
  async processRetryNotifications(): Promise<void> {
    try {
      const retryNotifications = await this.getRetryNotifications();
      
      if (retryNotifications.length === 0) {
        this.logger.debug('No retry notifications to process');
        return;
      }

      this.logger.log(`Processing ${retryNotifications.length} retry notifications`);
      
      await this.processNotificationBatch(retryNotifications);
      
    } catch (error) {
      this.logger.error(`Error processing retry notifications: ${error.message}`, error.stack);
    }
  }

  /**
   * Get pending notifications to process
   */
  private async getPendingNotifications(): Promise<NotificationOutbox[]> {
    return this.outboxRepository.find({
      where: {
        status: OutboxStatus.PENDING,
      },
      order: {
        createdAt: 'ASC',
      },
      take: this.batchSize,
    });
  }

  /**
   * Get notifications ready for retry
   */
  private async getRetryNotifications(): Promise<NotificationOutbox[]> {
    return this.outboxRepository.find({
      where: {
        status: OutboxStatus.RETRY,
        nextRetryAt: LessThanOrEqual(getCurrentUTCDate()),
      },
      order: {
        nextRetryAt: 'ASC',
      },
      take: this.batchSize,
    });
  }

  /**
   * Process a batch of notifications
   */
  private async processNotificationBatch(notifications: NotificationOutbox[]): Promise<void> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();

    for (const notification of notifications) {
      await queryRunner.startTransaction();
      
      try {
        // Mark as processing
        notification.markProcessing();
        await queryRunner.manager.save(notification);
        
        await queryRunner.commitTransaction();
        
        // Process the notification (outside transaction)
        await this.processNotification(notification);
        
      } catch (error) {
        await queryRunner.rollbackTransaction();
        this.logger.error(`Failed to process notification ${notification.id}: ${error.message}`, error.stack);
        
        // Handle failure
        await this.handleNotificationFailure(notification, error.message);
      }
    }
    
    await queryRunner.release();
  }

  /**
   * Process a single notification
   */
  private async processNotification(notification: NotificationOutbox): Promise<void> {
    try {
      this.logger.debug(`Processing notification ${notification.id} for user ${notification.userId}`);
      
      await this.notificationService.notify({
        userId: notification.userId,
        type: this.mapFromOutboxType(notification.type),
        title: notification.title,
        message: notification.message,
        relatedEntityId: notification.options?.relatedEntityId,
        relatedEntityType: notification.options?.relatedEntityType,
        htmlContent: notification.options?.htmlContent,
      });

      // Mark as completed
      notification.markCompleted();
      await this.outboxRepository.save(notification);
      
      this.logger.log(`Successfully processed notification ${notification.id}`);
      
    } catch (error) {
      this.logger.error(`Failed to send notification ${notification.id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Handle notification processing failure
   */
  private async handleNotificationFailure(notification: NotificationOutbox, errorMessage: string): Promise<void> {
    try {
      if (notification.canRetry()) {
        notification.markForRetry(errorMessage);
        this.logger.warn(`Notification ${notification.id} marked for retry (attempt ${notification.retryCount}/${notification.maxRetries})`);
      } else {
        notification.markFailed(errorMessage);
        this.logger.error(`Notification ${notification.id} failed permanently after ${notification.retryCount} attempts`);
      }
      
      await this.outboxRepository.save(notification);
      
    } catch (error) {
      this.logger.error(`Failed to update notification status ${notification.id}: ${error.message}`, error.stack);
    }
  }

  /**
   * Clean up old completed notifications
   * Runs daily at 2 AM
   */
  @Cron('0 2 * * *')
  async cleanupOldNotifications(): Promise<void> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - 7); // Keep for 7 days
      
      const result = await this.outboxRepository.delete({
        status: OutboxStatus.COMPLETED,
        processedAt: LessThanOrEqual(cutoffDate),
      });
      
      this.logger.log(`Cleaned up ${result.affected} old completed notifications`);
      
    } catch (error) {
      this.logger.error(`Failed to cleanup old notifications: ${error.message}`, error.stack);
    }
  }

  /**
   * Get outbox statistics for monitoring
   */
  async getOutboxStats(): Promise<{
    pending: number;
    processing: number;
    completed: number;
    failed: number;
    retry: number;
  }> {
    const stats = await this.outboxRepository
      .createQueryBuilder('outbox')
      .select('status')
      .addSelect('COUNT(*)', 'count')
      .groupBy('status')
      .getRawMany();

    const result = {
      pending: 0,
      processing: 0,
      completed: 0,
      failed: 0,
      retry: 0,
    };

    stats.forEach(stat => {
      result[stat.status] = parseInt(stat.count);
    });

    return result;
  }

  /**
   * Manually retry failed notifications
   */
  async retryFailedNotifications(limit: number = 100): Promise<number> {
    const failedNotifications = await this.outboxRepository.find({
      where: {
        status: OutboxStatus.FAILED,
      },
      take: limit,
    });

    for (const notification of failedNotifications) {
      if (notification.retryCount < notification.maxRetries) {
        notification.status = OutboxStatus.RETRY;
        notification.nextRetryAt = getCurrentUTCDate();
        await this.outboxRepository.save(notification);
      }
    }

    this.logger.log(`Marked ${failedNotifications.length} failed notifications for retry`);
    return failedNotifications.length;
  }
}
