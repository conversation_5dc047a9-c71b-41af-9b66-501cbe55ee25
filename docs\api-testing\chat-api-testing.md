# Chat Module API Testing Flow

This document outlines the testing flow for the Chat Module API endpoints.

## Prerequisites

Before testing the Chat API:

1. Ensure the HEC backend is running
2. Have valid authentication tokens for different user roles (students, tutors, admins)
3. Set up WebSocket testing capability for real-time features
4. Set up your API testing tool (<PERSON><PERSON> recommended)
5. Ensure file upload testing capability

## Chat Contacts Testing Flow

### Test Case 1: Get Available Chat Contacts

1. Authenticate with a student token
2. Send a GET request to `/api/chat/contacts`
3. Verify HTTP status code is 200 OK
4. Verify response contains available contacts (tutors, admins)
5. Verify students cannot see other students as contacts
6. Verify contact information includes name, email, role

### Test Case 2: Filter Contacts

1. Send a GET request to `/api/chat/contacts?name=John`
2. Verify filtered results contain only matching names
3. Test filtering by email: `/api/chat/contacts?email=<EMAIL>`
4. Test filtering by phone: `/api/chat/contacts?phone=123456789`
5. Verify case-insensitive filtering works correctly

### Test Case 3: Contact Access Control

1. Authenticate with different user roles
2. Verify students can only contact tutors/admins
3. Verify tutors can contact students and admins
4. Verify admins can contact all users
5. Test unauthorized contact access attempts

## Conversation Management Testing Flow

### Test Case 1: Get User Conversations

1. Authenticate with a user token
2. Send a GET request to `/api/chat/conversations` with pagination:
   ```json
   {
     "page": 1,
     "limit": 10,
     "search": "",
     "status": "active",
     "type": "direct"
   }
   ```
3. Verify HTTP status code is 200 OK
4. Verify response contains paginated conversations
5. Verify only user's conversations are returned
6. Verify conversation metadata includes participant info and last message

### Test Case 2: Filter Conversations

1. Test search functionality: `/api/chat/conversations?search=homework`
2. Test status filtering: `/api/chat/conversations?status=archived`
3. Test type filtering: `/api/chat/conversations?type=group`
4. Verify filtering works correctly
5. Test combined filters

### Test Case 3: Get Specific Conversation

1. Send a GET request to `/api/chat/conversations/{id}`
2. Verify HTTP status code is 200 OK
3. Verify response contains complete conversation details
4. Verify participant information is included
5. Test with non-existent conversation ID and verify 404 response
6. Test accessing conversation user is not part of and verify 403 response

## Message Management Testing Flow

### Test Case 1: Send Text Message

1. Authenticate with a user token
2. Send a POST request to `/api/chat/messages`:
   ```json
   {
     "recipientId": "123e4567-e89b-12d3-a456-************",
     "content": "Hello, I need help with my essay.",
     "type": "TEXT"
   }
   ```
3. Verify HTTP status code is 200 OK
4. Verify message is created and conversation is established
5. Verify message appears in both sender's and recipient's conversations

### Test Case 2: Send Message with Attachment

1. Upload a file first using `/api/chat/upload`
2. Send a POST request to `/api/chat/messages`:
   ```json
   {
     "recipientId": "123e4567-e89b-12d3-a456-************",
     "content": "Please review this document",
     "type": "FILE",
     "fileId": "file-uuid",
     "fileName": "essay.pdf"
   }
   ```
3. Verify HTTP status code is 200 OK
4. Verify file attachment is properly linked
5. Verify file can be accessed by recipient

### Test Case 3: Message Validation

1. Test sending message to non-existent user
2. Test sending empty message content
3. Test sending message with invalid file attachment
4. Test sending message to unauthorized recipient (student to student)
5. Verify appropriate validation errors for each case

### Test Case 4: Get Conversation Messages

1. Send a GET request to `/api/chat/conversations/{id}/messages` with filters:
   ```json
   {
     "page": 1,
     "limit": 20,
     "type": "TEXT",
     "search": "homework",
     "status": "delivered"
   }
   ```
2. Verify HTTP status code is 200 OK
3. Verify response contains paginated messages
4. Verify messages are ordered by timestamp
5. Verify user can only access messages from conversations they're part of

### Test Case 5: Message Search

1. Test searching messages by content
2. Test filtering by message type (TEXT, FILE, SYSTEM)
3. Test filtering by message status
4. Verify search works across conversation history
5. Test search with special characters and Unicode

## File Upload and Management Testing Flow

### Test Case 1: Upload Chat File

1. Authenticate with a user token
2. Send a POST request to `/api/chat/upload` with multipart/form-data:
   ```
   Content-Type: multipart/form-data
   file: [binary file data]
   ```
3. Verify HTTP status code is 200 OK
4. Verify response contains file ID and metadata
5. Verify file is stored securely
6. Verify file size and type restrictions are enforced

### Test Case 2: File Type Validation

1. Test uploading allowed file types (images, documents, etc.)
2. Test uploading restricted file types (executables, scripts)
3. Test uploading files exceeding size limits
4. Verify appropriate validation errors
5. Test malicious file upload attempts

### Test Case 3: Download Chat File

1. Upload a file and get file ID
2. Send a GET request to `/api/chat/files/{id}`
3. Verify HTTP status code is 200 OK
4. Verify file is downloaded with correct headers
5. Verify file content matches uploaded content
6. Test accessing file without proper permissions

### Test Case 4: File Access Control

1. Upload file as one user
2. Attempt to access file as different user
3. Verify proper authorization checks
4. Verify only conversation participants can access files
5. Test file access after conversation archival

## Message Status Management Testing Flow

### Test Case 1: Mark Messages as Read

1. Send messages to a user
2. Authenticate as recipient
3. Send a POST request to `/api/chat/messages/{conversationId}/read`
4. Verify HTTP status code is 200 OK
5. Verify all unread messages in conversation are marked as read
6. Verify read status is reflected in conversation list

### Test Case 2: Message Delivery Status

1. Send a message
2. Verify message status progresses: sent → delivered → read
3. Test offline recipient scenarios
4. Verify status updates are real-time via WebSocket

### Test Case 3: Unread Message Count

1. Send multiple messages to a user
2. Verify unread count increases
3. Mark some messages as read
4. Verify unread count decreases accordingly
5. Test unread count across multiple conversations

## Real-time Communication Testing Flow

### Test Case 1: WebSocket Connection

1. Establish WebSocket connection with valid JWT token
2. Verify connection is accepted
3. Test connection with invalid token
4. Verify connection is rejected with proper error

### Test Case 2: Real-time Message Delivery

1. Establish WebSocket connections for two users
2. Send message from user A to user B
3. Verify user B receives message instantly via WebSocket
4. Verify message appears in both users' conversation lists
5. Test message delivery when recipient is offline

### Test Case 3: Typing Indicators

1. Establish WebSocket connections
2. Simulate typing activity
3. Verify typing indicators are sent to conversation participants
4. Verify typing indicators stop after inactivity
5. Test typing indicators in group conversations

### Test Case 4: Connection Management

1. Test WebSocket reconnection after network interruption
2. Verify message queue during disconnection
3. Test multiple WebSocket connections from same user
4. Verify proper cleanup on connection close

## Admin Chat Features Testing Flow

### Test Case 1: Admin Conversation Management

1. Authenticate with admin token
2. Test accessing any user's conversations
3. Test moderating conversations
4. Test archiving/deleting conversations
5. Verify admin permissions are properly enforced

### Test Case 2: Virtual Admin Features

1. Test automated admin responses
2. Test admin conversation routing
3. Test escalation to human admin
4. Verify virtual admin behavior is appropriate

## Integration Testing Flow

### Test Case 1: Complete Chat Workflow

1. **Contact Discovery**
   - Student finds available tutors
   - Tutor sees assigned students

2. **Conversation Initiation**
   - Student sends first message to tutor
   - Conversation is automatically created
   - Both parties receive notifications

3. **Message Exchange**
   - Text messages back and forth
   - File sharing (documents, images)
   - Real-time delivery via WebSocket

4. **Conversation Management**
   - Mark messages as read
   - Search conversation history
   - Archive completed conversations

### Test Case 2: Multi-User Chat Scenarios

1. Student chats with multiple tutors simultaneously
2. Tutor manages conversations with multiple students
3. Admin monitors and moderates conversations
4. Verify proper isolation between conversations

### Test Case 3: Cross-Module Integration

1. Chat notifications integrate with notification system
2. File uploads integrate with file management system
3. User permissions integrate with role-based access
4. Chat history integrates with user profiles

## Performance Testing Flow

### Test Case 1: Message Volume Testing

1. Send large number of messages rapidly
2. Test conversation with thousands of messages
3. Verify pagination performance
4. Test search performance with large message history

### Test Case 2: File Upload Performance

1. Upload large files (within limits)
2. Test concurrent file uploads
3. Verify upload progress tracking
4. Test file download performance

### Test Case 3: WebSocket Performance

1. Test with many concurrent WebSocket connections
2. Verify message broadcasting performance
3. Test connection stability under load
4. Verify memory usage remains reasonable

## Security Testing Flow

### Test Case 1: Message Security

1. Test XSS prevention in message content
2. Test script injection attempts
3. Verify message content sanitization
4. Test malicious file upload prevention

### Test Case 2: Access Control Security

1. Test unauthorized conversation access
2. Test message interception attempts
3. Verify JWT token validation
4. Test session hijacking prevention

### Test Case 3: File Security

1. Test malicious file upload attempts
2. Verify file type validation
3. Test file access without proper permissions
4. Verify secure file storage and retrieval

## Error Handling Testing Flow

### Test Case 1: Network Interruption

1. Start chat session
2. Simulate network interruption
3. Verify graceful error handling
4. Verify message recovery when connection restored

### Test Case 2: Server Errors

1. Simulate server errors during message sending
2. Test file upload failures
3. Verify appropriate error messages
4. Test retry mechanisms

### Test Case 3: WebSocket Errors

1. Test WebSocket connection failures
2. Simulate server restart during chat
3. Verify automatic reconnection
4. Test message queue during disconnection

## Edge Cases Testing Flow

### Test Case 1: Boundary Conditions

1. Test with maximum message length
2. Test with maximum file size
3. Test rapid message sending
4. Verify system limits are enforced

### Test Case 2: Concurrent Operations

1. Send messages while marking others as read
2. Upload files while sending messages
3. Test simultaneous WebSocket operations
4. Verify data consistency

### Test Case 3: Time-based Scenarios

1. Test message timestamps across time zones
2. Test conversation archival after inactivity
3. Verify message ordering with rapid sends
4. Test file expiration policies

This comprehensive testing flow ensures the Chat Module provides secure, reliable, and performant communication capabilities for all user types in the HEC system.