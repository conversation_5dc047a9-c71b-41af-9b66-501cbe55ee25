import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { AwardJob, AwardJobStatus, AwardJobType } from '../../database/entities/award-job.entity';
import { 
  TriggerAwardJobDto, 
  AwardJobResponseDto, 
  AwardJobStatsDto, 
  AwardJobFilterDto 
} from '../../database/models/award-job.dto';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';
import { getCurrentUTCDate } from '../../common/utils/date-utils';

@Injectable()
export class AwardJobService {
  private readonly logger = new Logger(AwardJobService.name);

  constructor(
    @InjectRepository(AwardJob)
    private readonly awardJobRepository: Repository<AwardJob>,
  ) {}

  async getAllJobs(
    filterDto?: AwardJobFilterDto,
    paginationDto?: PaginationDto
  ): Promise<PagedListDto<AwardJobResponseDto>> {
    try {
      let query = this.awardJobRepository.createQueryBuilder('job');

      // Apply filters
      if (filterDto) {
        if (filterDto.jobType) {
          query = query.andWhere('job.jobType = :jobType', { jobType: filterDto.jobType });
        }
        if (filterDto.status) {
          query = query.andWhere('job.status = :status', { status: filterDto.status });
        }
        if (filterDto.startDate && filterDto.endDate) {
          query = query.andWhere('job.createdAt BETWEEN :startDate AND :endDate', {
            startDate: filterDto.startDate,
            endDate: filterDto.endDate,
          });
        }
      }

      const totalCount = await query.getCount();

      // Apply pagination
      if (paginationDto) {
        const { page = 1, limit = 10, sortBy = 'createdAt', sortDirection = 'DESC' } = paginationDto;
        const skip = (page - 1) * limit;
        query = query.skip(skip).take(limit).orderBy(`job.${sortBy}`, sortDirection);
      } else {
        query = query.orderBy('job.createdAt', 'DESC');
      }

      const jobs = await query.getMany();
      const jobDtos = jobs.map(job => this.mapToResponseDto(job));

      return new PagedListDto(jobDtos, totalCount);
    } catch (error) {
      this.logger.error(`Error getting award jobs: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getJobById(id: string): Promise<AwardJobResponseDto> {
    try {
      const job = await this.awardJobRepository.findOne({ where: { id } });
      if (!job) {
        throw new NotFoundException(`Award job with ID ${id} not found`);
      }
      return this.mapToResponseDto(job);
    } catch (error) {
      this.logger.error(`Error getting award job: ${error.message}`, error.stack);
      throw error;
    }
  }

  async createJob(triggerDto: TriggerAwardJobDto | { jobType: AwardJobType }, adminId: string): Promise<AwardJobResponseDto> {
    try {
      // Check if there's already a running job of the same type
      const runningJob = await this.awardJobRepository.findOne({
        where: { jobType: triggerDto.jobType, status: AwardJobStatus.RUNNING },
      });

      if (runningJob) {
        throw new BadRequestException(`A ${triggerDto.jobType} award job is already running`);
      }

      // Create new job
      const job = this.awardJobRepository.create({
        jobType: triggerDto.jobType,
        status: AwardJobStatus.PENDING,
        triggeredBy: adminId === 'system' ? null : adminId,
        nextRunAt: getCurrentUTCDate(),
      });

      const savedJob = await this.awardJobRepository.save(job);
      this.logger.log(`Award job ${savedJob.id} created by admin ${adminId}`);

      return this.mapToResponseDto(savedJob);
    } catch (error) {
      this.logger.error(`Error creating award job: ${error.message}`, error.stack);
      throw error;
    }
  }



  async getJobStats(): Promise<AwardJobStatsDto> {
    try {
      const totalJobs = await this.awardJobRepository.count();
      const completedJobs = await this.awardJobRepository.count({ 
        where: { status: AwardJobStatus.COMPLETED } 
      });
      const failedJobs = await this.awardJobRepository.count({ 
        where: { status: AwardJobStatus.FAILED } 
      });
      const runningJobs = await this.awardJobRepository.count({ 
        where: { status: AwardJobStatus.RUNNING } 
      });

      // Get average execution time for completed jobs
      const avgResult = await this.awardJobRepository
        .createQueryBuilder('job')
        .select('AVG(job.executionTimeMs)', 'avg')
        .where('job.status = :status', { status: AwardJobStatus.COMPLETED })
        .andWhere('job.executionTimeMs IS NOT NULL')
        .getRawOne();

      const averageExecutionTime = avgResult?.avg ? Math.round(avgResult.avg) : 0;

      // Get next scheduled run
      const nextScheduledJob = await this.awardJobRepository.findOne({
        where: { status: AwardJobStatus.PENDING },
        order: { nextRunAt: 'ASC' },
      });

      // Get last successful run
      const lastSuccessfulJob = await this.awardJobRepository.findOne({
        where: { status: AwardJobStatus.COMPLETED },
        order: { completedAt: 'DESC' },
      });

      // Get recent jobs (last 5)
      const recentJobs = await this.awardJobRepository.find({
        order: { createdAt: 'DESC' },
        take: 5,
      });

      // Get currently running jobs
      const currentlyRunning = await this.awardJobRepository.find({
        where: { status: AwardJobStatus.RUNNING },
        order: { startedAt: 'DESC' },
      });

      const successRate = totalJobs > 0 ? Math.round((completedJobs / totalJobs) * 100) : 0;

      return {
        totalJobs,
        completedJobs,
        failedJobs,
        runningJobs,
        averageExecutionTime,
        nextScheduledRun: nextScheduledJob?.nextRunAt,
        lastSuccessfulRun: lastSuccessfulJob?.completedAt,
        successRate,
        recentJobs: recentJobs.map(job => this.mapToResponseDto(job)),
        currentlyRunning: currentlyRunning.map(job => this.mapToResponseDto(job)),
      };
    } catch (error) {
      this.logger.error(`Error getting award job stats: ${error.message}`, error.stack);
      throw error;
    }
  }

  async updateJobStatus(
    jobId: string, 
    status: AwardJobStatus, 
    processedCount?: number, 
    errorMessage?: string,
    summary?: string
  ): Promise<void> {
    try {
      const job = await this.awardJobRepository.findOne({ where: { id: jobId } });
      if (!job) {
        throw new NotFoundException(`Award job with ID ${jobId} not found`);
      }

      const now = getCurrentUTCDate();
      
      if (status === AwardJobStatus.RUNNING && !job.startedAt) {
        job.startedAt = now;
      }
      
      if (status === AwardJobStatus.COMPLETED || status === AwardJobStatus.FAILED) {
        job.completedAt = now;
        if (job.startedAt) {
          job.executionTimeMs = now.getTime() - job.startedAt.getTime();
        }
      }

      job.status = status;
      job.lastRunAt = now;
      
      if (processedCount !== undefined) {
        job.processedCount = processedCount;
      }
      
      if (errorMessage) {
        job.errorMessage = errorMessage;
      }
      
      if (summary) {
        job.summary = summary;
      }

      await this.awardJobRepository.save(job);
      this.logger.log(`Award job ${jobId} status updated to ${status}`);
    } catch (error) {
      this.logger.error(`Error updating job status: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getLatestJobByType(jobType: AwardJobType): Promise<AwardJobResponseDto> {
    try {
      const job = await this.awardJobRepository.findOne({
        where: { jobType },
        order: { createdAt: 'DESC' },
      });
      
      if (!job) {
        throw new NotFoundException(`No jobs found for type ${jobType}`);
      }
      
      return this.mapToResponseDto(job);
    } catch (error) {
      this.logger.error(`Error getting latest job by type: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getRunningJobs(): Promise<AwardJobResponseDto[]> {
    try {
      const jobs = await this.awardJobRepository.find({
        where: { status: AwardJobStatus.RUNNING },
        order: { startedAt: 'DESC' },
      });
      
      return jobs.map(job => this.mapToResponseDto(job));
    } catch (error) {
      this.logger.error(`Error getting running jobs: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getJobsByStatus(status: AwardJobStatus, limit: number = 10): Promise<AwardJobResponseDto[]> {
    try {
      const jobs = await this.awardJobRepository.find({
        where: { status },
        order: { createdAt: 'DESC' },
        take: limit,
      });
      
      return jobs.map(job => this.mapToResponseDto(job));
    } catch (error) {
      this.logger.error(`Error getting jobs by status: ${error.message}`, error.stack);
      throw error;
    }
  }

  private mapToResponseDto(job: AwardJob): AwardJobResponseDto {
    return {
      id: job.id,
      jobType: job.jobType,
      status: job.status,
      nextRunAt: job.nextRunAt,
      lastRunAt: job.lastRunAt,
      startedAt: job.startedAt,
      completedAt: job.completedAt,
      processedCount: job.processedCount,
      errorMessage: job.errorMessage,
      executionTimeMs: job.executionTimeMs,
      triggeredBy: job.triggeredBy,
      summary: job.summary,
      createdAt: job.createdAt,
      updatedAt: job.updatedAt,
    };
  }
}