import { MigrationInterface, QueryRunner } from "typeorm";

export class EssayStudentDiarySkinPreferenceNullCheck1755600446960 implements MigrationInterface {
    name = 'EssayStudentDiarySkinPreferenceNullCheck1755600446960'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "essay_module_skin_preference" DROP CONSTRAINT "FK_3a54381f8049ae135e0bf604a92"`);
        await queryRunner.query(`ALTER TABLE "essay_module_skin_preference" ALTER COLUMN "skin_id" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "essay_module_skin_preference" ADD CONSTRAINT "FK_3a54381f8049ae135e0bf604a92" FOREIGN KEY ("skin_id") REFERENCES "diary_skin"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: Query<PERSON>unner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "essay_module_skin_preference" DROP CONSTRAINT "FK_3a54381f8049ae135e0bf604a92"`);
        await queryRunner.query(`ALTER TABLE "essay_module_skin_preference" ALTER COLUMN "skin_id" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "essay_module_skin_preference" ADD CONSTRAINT "FK_3a54381f8049ae135e0bf604a92" FOREIGN KEY ("skin_id") REFERENCES "diary_skin"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
