import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTranscriptionTables1748000000000 implements MigrationInterface {
  name = 'CreateTranscriptionTables1748000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE "books" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "title" character varying(255) NOT NULL,
        "author" character varying(255) NOT NULL,
        "description" text,
        "publication_year" integer NOT NULL DEFAULT 0,
        "content" text,
        "is_active" boolean NOT NULL DEFAULT true,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_books" PRIMARY KEY ("id")
      )
    `);

    await queryRunner.query(`
      CREATE TABLE "sentences" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "content" text NOT NULL,
        "order_index" integer NOT NULL,
        "difficulty_level" character varying(50),
        "grammar_pattern" character varying(100),
        "book_id" uuid NOT NULL,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_sentences" PRIMARY KEY ("id")
      )
    `);

    await queryRunner.query(`
      CREATE TABLE "transcription_sessions" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "student_id" uuid NOT NULL,
        "started_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "completed_at" TIMESTAMP,
        "total_attempts" integer NOT NULL DEFAULT 0,
        "correct_attempts" integer NOT NULL DEFAULT 0,
        "is_completed" boolean NOT NULL DEFAULT false,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_transcription_sessions" PRIMARY KEY ("id")
      )
    `);

    await queryRunner.query(`
      CREATE TABLE "transcription_attempts" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "session_id" uuid NOT NULL,
        "sentence_id" uuid NOT NULL,
        "user_input" text NOT NULL,
        "is_correct" boolean NOT NULL,
        "errors" json,
        "time_spent_seconds" integer,
        "attempted_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_transcription_attempts" PRIMARY KEY ("id")
      )
    `);

    await queryRunner.query(`
      ALTER TABLE "sentences" 
      ADD CONSTRAINT "FK_sentences_bookId" 
      FOREIGN KEY ("book_id") REFERENCES "books"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "transcription_sessions" 
      ADD CONSTRAINT "FK_transcription_sessions_studentId" 
      FOREIGN KEY ("student_id") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "transcription_attempts" 
      ADD CONSTRAINT "FK_transcription_attempts_sessionId" 
      FOREIGN KEY ("session_id") REFERENCES "transcription_sessions"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "transcription_attempts" 
      ADD CONSTRAINT "FK_transcription_attempts_sentenceId" 
      FOREIGN KEY ("sentence_id") REFERENCES "sentences"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "transcription_attempts" DROP CONSTRAINT "FK_transcription_attempts_sentenceId"`);
    await queryRunner.query(`ALTER TABLE "transcription_attempts" DROP CONSTRAINT "FK_transcription_attempts_sessionId"`);
    await queryRunner.query(`ALTER TABLE "transcription_sessions" DROP CONSTRAINT "FK_transcription_sessions_studentId"`);
    await queryRunner.query(`ALTER TABLE "sentences" DROP CONSTRAINT "FK_sentences_bookId"`);
    await queryRunner.query(`DROP TABLE "transcription_attempts"`);
    await queryRunner.query(`DROP TABLE "transcription_sessions"`);
    await queryRunner.query(`DROP TABLE "sentences"`);
    await queryRunner.query(`DROP TABLE "books"`);
  }
}