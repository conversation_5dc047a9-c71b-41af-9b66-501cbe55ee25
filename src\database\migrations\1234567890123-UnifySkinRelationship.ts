import { MigrationInterface, QueryRunner } from 'typeorm';

export class UnifySkinRelationship1234567890123 implements MigrationInterface {
  name = 'UnifySkinRelationship1234567890123';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key constraint on skin_id
    await queryRunner.query(`ALTER TABLE "diary_entry" DROP CONSTRAINT IF EXISTS "FK_ad12d4b9edf19455766d9457d6b"`);
    
    // Migrate existing student skin references to unified format
    await queryRunner.query(`
      UPDATE diary_entry 
      SET skin_id = student_skin_id, skin_type = 'student' 
      WHERE student_skin_id IS NOT NULL
    `);

    // Set skin_type to 'global' for entries with only skin_id
    await queryRunner.query(`
      UPDATE diary_entry 
      SET skin_type = 'global' 
      WHERE skin_id IS NOT NULL AND skin_type = 'global'
    `);

    // Drop legacy column
    await queryRunner.query(`ALTER TABLE "diary_entry" DROP COLUMN "student_skin_id"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Restore legacy column
    await queryRunner.query(`ALTER TABLE "diary_entry" ADD "student_skin_id" uuid`);
    
    // Restore student_skin_id for student skins
    await queryRunner.query(`
      UPDATE diary_entry 
      SET student_skin_id = skin_id 
      WHERE skin_type = 'student'
    `);
  }
}