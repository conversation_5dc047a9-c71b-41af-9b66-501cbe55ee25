import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateDiaryEntrySkinConstraint1754700000000 implements MigrationInterface {
  name = 'UpdateDiaryEntrySkinConstraint1754700000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop the existing constraint if it exists
    try {
      await queryRunner.query(`
        ALTER TABLE "diary_entry" 
        DROP CONSTRAINT IF EXISTS "CHK_diary_entry_skin_exclusivity"
      `);
      console.log('Dropped existing CHK_diary_entry_skin_exclusivity constraint');
    } catch (error) {
      console.log('Constraint CHK_diary_entry_skin_exclusivity might not exist, continuing...');
    }

    // Update existing entries that might have NULL skin_type but have skin_id
    try {
      const updateResult = await queryRunner.query(`
        UPDATE diary_entry 
        SET skin_type = 'global' 
        WHERE skin_type IS NULL AND skin_id IS NOT NULL AND student_skin_id IS NULL
      `);
      console.log('Updated existing entries with NULL skin_type to global');
    } catch (error) {
      console.log('Error updating existing entries:', error.message);
    }

    // Set default skin_type for entries that have skin_id but no skin_type
    try {
      await queryRunner.query(`
        UPDATE diary_entry 
        SET skin_type = 'global' 
        WHERE skin_type IS NULL AND skin_id IS NOT NULL
      `);
    } catch (error) {
      console.log('Error setting default skin_type:', error.message);
    }

    // Add the updated constraint that is more flexible
    try {
      await queryRunner.query(`
        ALTER TABLE "diary_entry" 
        ADD CONSTRAINT "CHK_diary_entry_skin_exclusivity" 
        CHECK (
          (skin_type = 'global' AND skin_id IS NOT NULL AND student_skin_id IS NULL) OR
          (skin_type = 'student' AND skin_id IS NULL AND student_skin_id IS NOT NULL) OR
          (skin_type IS NULL AND skin_id IS NOT NULL AND student_skin_id IS NULL) OR
          (skin_id IS NOT NULL AND student_skin_id IS NULL)
        )
      `);
      console.log('Added updated CHK_diary_entry_skin_exclusivity constraint');
    } catch (error) {
      console.log('Error adding updated constraint:', error.message);
      throw error;
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the updated constraint
    await queryRunner.query(`
      ALTER TABLE "diary_entry" 
      DROP CONSTRAINT IF EXISTS "CHK_diary_entry_skin_exclusivity"
    `);

    // Restore a simpler constraint
    await queryRunner.query(`
      ALTER TABLE "diary_entry" 
      ADD CONSTRAINT "CHK_diary_entry_skin_exclusivity" 
      CHECK (
        (skin_type = 'global' AND skin_id IS NOT NULL AND student_skin_id IS NULL) OR
        (skin_type = 'student' AND skin_id IS NULL AND student_skin_id IS NOT NULL)
      )
    `);
  }
}