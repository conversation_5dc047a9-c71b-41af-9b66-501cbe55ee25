import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';
import { getCurrentUTCDate } from '../../common/utils/date-utils';

export enum OutboxNotificationType {
  DIARY_SUBMISSION = 'DIARY_SUBMISSION',
  DIARY_REVIEWED = 'DIARY_REVIEWED',
  DIARY_CONFIRMED = 'DIARY_CONFIRMED',
  MISSION_SUBMISSION = 'MISSION_SUBMISSION',
  MISSION_REVIEWED = 'MISSION_REVIEWED',
  MISSION_CONFIRMED = 'MISSION_CONFIRMED',
  NOVEL_SUBMISSION = 'NOVEL_SUBMISSION',
  NOVEL_REVIEWED = 'NOVEL_REVIEWED',
  QA_SUBMISSION = 'QA_SUBMISSION',
  QA_REVIEWED = 'QA_REVIEWED',
  GENERAL = 'GENERAL',
  SYSTEM = 'SYSTEM',
  REMINDER = 'REMINDER',
  ACHIEVEMENT = 'ACHIEVEMENT',
  FRIEND_REQUEST = 'FRIEND_REQUEST',
  FRIEND_ACCEPTED = 'FRIEND_ACCEPTED',
  CHAT_MESSAGE = 'CHAT_MESSAGE',
  SUBSCRIPTION_EXPIRY = 'SUBSCRIPTION_EXPIRY',
  SUBSCRIPTION_RENEWED = 'SUBSCRIPTION_RENEWED',
  PAYMENT_SUCCESS = 'PAYMENT_SUCCESS',
  PAYMENT_FAILED = 'PAYMENT_FAILED',
  TUTOR_ASSIGNMENT = 'TUTOR_ASSIGNMENT',
  TUTOR_FEEDBACK = 'TUTOR_FEEDBACK',
  ADMIN_ANNOUNCEMENT = 'ADMIN_ANNOUNCEMENT',
}

export enum OutboxStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  RETRY = 'retry'
}

@Entity('notification_outbox')
@Index(['status', 'createdAt'])
@Index(['status', 'nextRetryAt'])
export class NotificationOutbox {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'userId', type: 'varchar', length: 255 })
  userId: string;

  @Column({ type: 'enum', enum: OutboxNotificationType })
  type: OutboxNotificationType;

  @Column({ type: 'varchar', length: 500 })
  title: string;

  @Column({ type: 'text' })
  message: string;

  @Column({ type: 'jsonb', nullable: true })
  options: {
    relatedEntityId?: string;
    relatedEntityType?: string;
    htmlContent?: string;
    webLink?: string;
    deepLink?: string;
    sendEmail?: boolean;
    sendPush?: boolean;
    sendInApp?: boolean;
    sendMobile?: boolean;
    sendSms?: boolean;
    sendRealtime?: boolean;
  };

  @Column({ type: 'enum', enum: OutboxStatus, default: OutboxStatus.PENDING })
  status: OutboxStatus;

  @Column({ name: 'retryCount', type: 'int', default: 0 })
  retryCount: number;

  @Column({ name: 'maxRetries', type: 'int', default: 3 })
  maxRetries: number;

  @Column({ name: 'nextRetryAt', type: 'timestamp', nullable: true })
  nextRetryAt: Date;

  @Column({ name: 'processedAt', type: 'timestamp', nullable: true })
  processedAt: Date;

  @Column({ name: 'lastError', type: 'text', nullable: true })
  lastError: string;

  @Column({ type: 'jsonb', nullable: true })
  metadata: {
    submissionId?: string;
    entryType?: string;
    priority?: number;
    batchId?: string;
  };

  @CreateDateColumn({ name: 'createdAt' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updatedAt' })
  updatedAt: Date;

  // Helper methods
  canRetry(): boolean {
    return this.retryCount < this.maxRetries && 
           this.status !== OutboxStatus.COMPLETED &&
           (this.nextRetryAt === null || this.nextRetryAt <= getCurrentUTCDate());
  }

  markForRetry(error: string): void {
    this.status = OutboxStatus.RETRY;
    this.retryCount += 1;
    this.lastError = error;
    
    // Exponential backoff: 2^retryCount minutes, max 60 minutes
    const delayMinutes = Math.min(Math.pow(2, this.retryCount), 60);
    this.nextRetryAt = new Date(getCurrentUTCDate().getTime() + delayMinutes * 60 * 1000);
  }

  markCompleted(): void {
    this.status = OutboxStatus.COMPLETED;
    this.processedAt = getCurrentUTCDate();
    this.lastError = null;
  }

  markFailed(error: string): void {
    this.status = OutboxStatus.FAILED;
    this.lastError = error;
    this.processedAt = getCurrentUTCDate();
  }

  markProcessing(): void {
    this.status = OutboxStatus.PROCESSING;
  }
}
