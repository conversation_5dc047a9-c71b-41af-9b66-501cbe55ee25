import { Entity, Column, OneToMany } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { WaterfallQuestion } from './waterfall-question.entity';
import { WaterfallTrueFalseQuestion } from './waterfall-true-false-question.entity';
import { WaterfallMultipleChoiceQuestion } from './waterfall-multiple-choice-question.entity';
import { WaterfallQuestionType } from './tutor-waterfall-set.entity';

@Entity()
export class WaterfallSet extends AuditableBaseEntity {
  @Column()
  title: string;

  @Column({ name: 'total_score' })
  totalScore: number;

  @Column({ name: 'total_questions' })
  totalQuestions: number;

  @Column({ name: 'question_type', type: 'enum', enum: WaterfallQuestionType, nullable: true })
  questionType: WaterfallQuestionType;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  // Relationships removed to allow questions to reference both admin and tutor sets
  // @OneToMany(() => WaterfallQuestion, (question) => question.set, { cascade: true })
  // questions: WaterfallQuestion[];

  // @OneToMany(() => WaterfallTrueFalseQuestion, (question) => question.set, { cascade: true })
  // trueFalseQuestions: WaterfallTrueFalseQuestion[];

  // @OneToMany(() => WaterfallMultipleChoiceQuestion, (question) => question.set, { cascade: true })
  // multipleChoiceQuestions: WaterfallMultipleChoiceQuestion[];
}
