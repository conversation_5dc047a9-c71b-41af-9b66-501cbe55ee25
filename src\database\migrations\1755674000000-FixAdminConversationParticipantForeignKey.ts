import { MigrationInterface, QueryRunner } from 'typeorm';

export class FixAdminConversationParticipantForeignKey1755674000000 implements MigrationInterface {
  name = 'FixAdminConversationParticipantForeignKey1755674000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop the incorrect foreign key constraint
    await queryRunner.query(`
      ALTER TABLE "admin_conversation_participant" 
      DROP CONSTRAINT IF EXISTS "FK_admin_conversation_participant_conversation"
    `);

    // Add the correct foreign key constraint to admin_conversation table
    await queryRunner.query(`
      ALTER TABLE "admin_conversation_participant" 
      ADD CONSTRAINT "FK_admin_conversation_participant_admin_conversation" 
      FOREIGN KEY ("conversation_id") REFERENCES "admin_conversation"("id") ON DELETE CASCADE
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the correct foreign key constraint
    await queryRunner.query(`
      ALTER TABLE "admin_conversation_participant" 
      DROP CONSTRAINT IF EXISTS "FK_admin_conversation_participant_admin_conversation"
    `);

    // Restore the incorrect foreign key constraint (for rollback purposes)
    await queryRunner.query(`
      ALTER TABLE "admin_conversation_participant" 
      ADD CONSTRAINT "FK_admin_conversation_participant_conversation" 
      FOREIGN KEY ("conversation_id") REFERENCES "conversation"("id") ON DELETE CASCADE
    `);
  }
}