import { Test, TestingModule } from '@nestjs/testing';
import { TutorMatchingService } from './tutor-matching.service';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import { StudentTutorMapping, MappingStatus } from '../../database/entities/student-tutor-mapping.entity';
import { User, UserType } from '../../database/entities/user.entity';
import { PlanFeature } from '../../database/entities/plan-feature.entity';
import { DiaryEntry } from '../../database/entities/diary-entry.entity';
import { AsyncNotificationHelperService } from '../notification/async-notification-helper.service';
import { ChatService } from '../chat/chat.service';
import { Logger } from '@nestjs/common';

describe('TutorMatchingService - Simple Test', () => {
  let service: TutorMatchingService;
  let studentTutorMappingRepository: Repository<StudentTutorMapping>;

  const mockStudent = {
    id: 'student-1',
    name: '<PERSON>',
    email: '<EMAIL>',
    type: UserType.STUDENT,
    isActive: true,
    isConfirmed: true,
  } as User;

  const mockTutor1 = {
    id: 'tutor-1',
    name: 'Jane Smith',
    email: '<EMAIL>',
    type: UserType.TUTOR,
    isActive: true,
    isConfirmed: true,
  } as User;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TutorMatchingService,
        {
          provide: getRepositoryToken(StudentTutorMapping),
          useValue: {
            find: jest.fn(),
            findOne: jest.fn(),
            create: jest.fn(),
            save: jest.fn(),
            count: jest.fn(),
            createQueryBuilder: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(User),
          useValue: {
            findOne: jest.fn(),
            createQueryBuilder: jest.fn().mockReturnValue({
              leftJoinAndSelect: jest.fn().mockReturnThis(),
              where: jest.fn().mockReturnThis(),
              andWhere: jest.fn().mockReturnThis(),
              getMany: jest.fn(),
            }),
          },
        },
        {
          provide: getRepositoryToken(PlanFeature),
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(DiaryEntry),
          useValue: {
            findOne: jest.fn(),
            find: jest.fn(),
            createQueryBuilder: jest.fn(),
          },
        },
        {
          provide: ChatService,
          useValue: {
            getOrCreateConversation: jest.fn(),
          },
        },
        {
          provide: AsyncNotificationHelperService,
          useValue: {
            notify: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<TutorMatchingService>(TutorMatchingService);
    studentTutorMappingRepository = module.get<Repository<StudentTutorMapping>>(getRepositoryToken(StudentTutorMapping));

    // Mock logger to avoid console output during tests
    jest.spyOn(Logger.prototype, 'log').mockImplementation();
    jest.spyOn(Logger.prototype, 'warn').mockImplementation();
    jest.spyOn(Logger.prototype, 'error').mockImplementation();
  });

  describe('getStudentTutorAssignments', () => {
    it('should return all active assignments for a student', async () => {
      const mockAssignments = [
        {
          id: 'assignment-1',
          studentId: mockStudent.id,
          tutorId: mockTutor1.id,
          planFeatureId: 'feature-1',
          status: MappingStatus.ACTIVE,
          assignedDate: new Date('2024-01-01'),
          tutor: mockTutor1,
          planFeature: { id: 'feature-1', name: 'Writing' },
        },
      ] as StudentTutorMapping[];

      jest.spyOn(studentTutorMappingRepository, 'find').mockResolvedValue(mockAssignments);

      const result = await service.getStudentTutorAssignments(mockStudent.id);

      expect(result).toEqual(mockAssignments);
      expect(studentTutorMappingRepository.find).toHaveBeenCalledWith({
        where: {
          studentId: mockStudent.id,
          status: MappingStatus.ACTIVE,
        },
        relations: ['tutor', 'planFeature'],
        order: { assignedDate: 'ASC' },
      });
    });

    it('should return empty array on error', async () => {
      jest.spyOn(studentTutorMappingRepository, 'find').mockRejectedValue(new Error('Database error'));

      const result = await service.getStudentTutorAssignments(mockStudent.id);

      expect(result).toEqual([]);
    });
  });

  describe('getOrSelectPreferredTutor', () => {
    it('should return existing preferred tutor for student with assignments', async () => {
      const mockAssignments = [
        {
          id: 'assignment-1',
          studentId: mockStudent.id,
          tutorId: mockTutor1.id,
          planFeatureId: 'feature-1',
          status: MappingStatus.ACTIVE,
          assignedDate: new Date('2024-01-01'),
          tutor: mockTutor1,
          planFeature: { id: 'feature-1', name: 'Writing' },
        },
      ] as StudentTutorMapping[];

      jest.spyOn(service, 'getStudentTutorAssignments').mockResolvedValue(mockAssignments);

      const result = await service.getOrSelectPreferredTutor(mockStudent.id);

      expect(result).toEqual(mockTutor1);
    });

    it('should select new tutor for student with no assignments', async () => {
      jest.spyOn(service, 'getStudentTutorAssignments').mockResolvedValue([]);
      jest.spyOn(service, 'selectTutorByOverallWorkload').mockResolvedValue(mockTutor1);

      const result = await service.getOrSelectPreferredTutor(mockStudent.id);

      expect(result).toEqual(mockTutor1);
      expect(service.selectTutorByOverallWorkload).toHaveBeenCalled();
    });
  });
});
