import { Controller, Get, Post, Body, Param, UseGuards, Req } from '@nestjs/common';
import { ApiBearerAuth, ApiTags, ApiOperation } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { StudentGuard } from '../../common/guards/student.guard';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiOkResponseWithArrayType } from '../../common/decorators/api-response.decorator';
import { BookService } from './services/book.service';
import { SentenceService } from './services/sentence.service';
import { TranscriptionSessionService } from './services/transcription-session.service';
import { TranscriptionAttemptService } from './services/transcription-attempt.service';
import {
  BookResponseDto,
  SentenceResponseDto,
  CreateTranscriptionSessionDto,
  TranscriptionSessionResponseDto,
  CreateTranscriptionAttemptDto,
  TranscriptionAttemptResponseDto,
} from '../../database/models/transcription.dto';

@ApiTags('transcription-student')
@Controller('transcription')
@UseGuards(JwtAuthGuard, StudentGuard)
@ApiBearerAuth('JWT-auth')
export class StudentTranscriptionController {
  constructor(
    private readonly bookService: BookService,
    private readonly sentenceService: SentenceService,
    private readonly sessionService: TranscriptionSessionService,
    private readonly attemptService: TranscriptionAttemptService,
  ) {}

  @Get('books')
  @ApiOperation({ summary: 'Get available books for practice (Student only)' })
  @ApiOkResponseWithArrayType(BookResponseDto, 'Books retrieved successfully')
  async getBooks(): Promise<ApiResponse<BookResponseDto[]>> {
    const result = await this.bookService.findAll();
    return ApiResponse.success(result, 'Books retrieved successfully');
  }

  @Get('books/:id/sentences')
  @ApiOperation({ summary: 'Get sentences from a book (Student only)' })
  @ApiOkResponseWithArrayType(SentenceResponseDto, 'Sentences retrieved successfully')
  async getBookSentences(@Param('id') bookId: string): Promise<ApiResponse<SentenceResponseDto[]>> {
    const result = await this.sentenceService.findByBookId(bookId);
    return ApiResponse.success(result, 'Sentences retrieved successfully');
  }

  @Post('sessions')
  @ApiOperation({ summary: 'Start a new transcription session (Student only)' })
  @ApiOkResponseWithType(TranscriptionSessionResponseDto, 'Session started successfully')
  async startSession(
    @Req() req: any,
    @Body() createSessionDto: CreateTranscriptionSessionDto,
  ): Promise<ApiResponse<TranscriptionSessionResponseDto>> {
    const result = await this.sessionService.create(req.user.id, createSessionDto);
    return ApiResponse.success(result, 'Session started successfully', 201);
  }

  @Get('sessions/:id')
  @ApiOperation({ summary: 'Get session details (Student only)' })
  @ApiOkResponseWithType(TranscriptionSessionResponseDto, 'Session retrieved successfully')
  async getSession(@Param('id') id: string): Promise<ApiResponse<TranscriptionSessionResponseDto>> {
    const result = await this.sessionService.findById(id);
    return ApiResponse.success(result, 'Session retrieved successfully');
  }

  @Post('sessions/:id/attempts')
  @ApiOperation({ summary: 'Submit a sentence copying attempt (Student only)' })
  @ApiOkResponseWithType(TranscriptionAttemptResponseDto, 'Attempt submitted successfully')
  async submitAttempt(
    @Param('id') sessionId: string,
    @Body() createAttemptDto: CreateTranscriptionAttemptDto,
  ): Promise<ApiResponse<TranscriptionAttemptResponseDto>> {
    const result = await this.attemptService.create(sessionId, createAttemptDto);
    return ApiResponse.success(result, 'Attempt submitted successfully', 201);
  }

  @Get('sessions/:id/attempts')
  @ApiOperation({ summary: 'Get session attempts (Student only)' })
  @ApiOkResponseWithArrayType(TranscriptionAttemptResponseDto, 'Attempts retrieved successfully')
  async getSessionAttempts(@Param('id') sessionId: string): Promise<ApiResponse<TranscriptionAttemptResponseDto[]>> {
    const result = await this.attemptService.findBySessionId(sessionId);
    return ApiResponse.success(result, 'Attempts retrieved successfully');
  }

  @Get('my-progress')
  @ApiOperation({ summary: 'Get student progress (Student only)' })
  @ApiOkResponseWithArrayType(TranscriptionSessionResponseDto, 'Progress retrieved successfully')
  async getMyProgress(@Req() req: any): Promise<ApiResponse<TranscriptionSessionResponseDto[]>> {
    const result = await this.sessionService.findByStudentId(req.user.id);
    return ApiResponse.success(result, 'Progress retrieved successfully');
  }
}