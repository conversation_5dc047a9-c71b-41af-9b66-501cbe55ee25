import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddAuditColumnsToAwardJob1734168556000 implements MigrationInterface {
  name = 'AddAuditColumnsToAwardJob1734168556000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add missing audit columns to award_job table
    await queryRunner.addColumns('award_job', [
      new TableColumn({
        name: 'created_by',
        type: 'uuid',
        isNullable: true,
      }),
      new TableColumn({
        name: 'updated_by',
        type: 'uuid',
        isNullable: true,
      }),
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumns('award_job', ['created_by', 'updated_by']);
  }
}