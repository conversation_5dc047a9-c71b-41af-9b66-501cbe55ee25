import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveAllWaterfallForeignKeyConstraints1756500000000 implements MigrationInterface {
  name = 'RemoveAllWaterfallForeignKeyConstraints1756500000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Remove foreign key constraints using simple DROP IF EXISTS
    await queryRunner.query(`ALTER TABLE "waterfall_participation" DROP CONSTRAINT IF EXISTS "FK_b54f4c72fadee4feb2ca98da56d"`);
    await queryRunner.query(`ALTER TABLE "waterfall_answer" DROP CONSTRAINT IF EXISTS "FK_d9104da9519ec2a769ba473f498"`);
    await queryRunner.query(`ALTER TABLE "waterfall_question" DROP CONSTRAINT IF EXISTS "FK_c19ed78ab6eb5bbd64702f2a456"`);
    await queryRunner.query(`ALTER TABLE "waterfall_true_false_question" DROP CONSTRAINT IF EXISTS "FK_48780dba8d9da4250e52f5d2821"`);
    await queryRunner.query(`ALTER TABLE "waterfall_multiple_choice_question" DROP CONSTRAINT IF EXISTS "FK_waterfall_multiple_choice_question_set_id"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Re-add constraints (may fail if tutor data exists)
    await queryRunner.query(`
      ALTER TABLE "waterfall_participation" 
      ADD CONSTRAINT "FK_b54f4c72fadee4feb2ca98da56d" 
      FOREIGN KEY ("setId") REFERENCES "waterfall_set"("id") ON DELETE CASCADE
    `);
    
    await queryRunner.query(`
      ALTER TABLE "waterfall_answer" 
      ADD CONSTRAINT "FK_d9104da9519ec2a769ba473f498" 
      FOREIGN KEY ("questionId") REFERENCES "waterfall_question"("id") ON DELETE CASCADE
    `);
  }
}