# Award System Monitoring Guide

## Overview

This guide provides comprehensive instructions for monitoring the HEC award calculation system, identifying issues, and resolving common problems.

## 🔍 Monitoring Dashboard

### Quick Health Check
```bash
GET /admin/awards/scheduler/health
```
**Response Indicators:**
- `status: "healthy"` - System operating normally
- `status: "warning"` - Minor issues detected
- `status: "critical"` - Immediate attention required

### System Statistics
```bash
GET /admin/awards/monitoring/stats
```
**Key Metrics:**
- `totalJobs` - All jobs executed
- `completedJobs` - Successfully completed
- `failedJobs` - Failed executions
- `runningJobs` - Currently executing
- `successRate` - Overall success percentage
- `recentJobs` - Last 5 job executions
- `currentlyRunning` - Active jobs

## 📊 Job Monitoring

### Check All Jobs
```bash
GET /admin/awards/monitoring?page=1&limit=10&sortBy=createdAt&sortDirection=DESC
```

### Monitor Specific Job
```bash
GET /admin/awards/monitoring/jobs/{jobId}
```

### Check Job by Type
```bash
GET /admin/awards/monitoring/status/weekly
GET /admin/awards/monitoring/status/monthly
GET /admin/awards/monitoring/status/manual
```

### View Running Jobs
```bash
GET /admin/awards/monitoring/running
```

## 🚨 Common Issues & Solutions

### 1. **Jobs Stuck in "Running" Status**

**Symptoms:**
- Job shows `status: "running"` for >30 minutes
- No recent completion timestamps

**Diagnosis:**
```bash
GET /admin/awards/monitoring/running
```

**Solutions:**
1. **Check Application Logs:**
   ```bash
   # Look for error messages in application logs
   tail -f logs/application.log | grep "award"
   ```

2. **Manual Intervention:**
   ```bash
   # Trigger new job (system will prevent duplicates)
   POST /admin/awards/scheduler/trigger
   {
     "jobType": "manual"
   }
   ```

3. **Database Cleanup (Last Resort):**
   ```sql
   -- Update stuck jobs to failed status
   UPDATE award_job 
   SET status = 'failed', 
       error_message = 'Job timeout - manually resolved',
       completed_at = NOW()
   WHERE status = 'running' 
   AND started_at < NOW() - INTERVAL '1 hour';
   ```

### 2. **No Recent Award Winners**

**Symptoms:**
- `recentWinners24h: 0` despite user activity
- Awards configured but not distributed

**Diagnosis:**
```bash
GET /admin/awards/scheduler/diagnostic
```

**Check:**
- Active awards: `activeAwards > 0`
- Recent activity: `recentActivity24h > 0`
- Confirmed entries: `lastMonthConfirmedDiaryEntries > 0`

**Solutions:**
1. **Verify Award Configuration:**
   ```sql
   SELECT * FROM award WHERE is_active = true;
   ```

2. **Check Entry Status:**
   ```sql
   SELECT status, COUNT(*) 
   FROM diary_entry 
   WHERE created_at > NOW() - INTERVAL '7 days'
   GROUP BY status;
   ```

3. **Manual Award Trigger:**
   ```bash
   POST /admin/awards/scheduler/trigger/test
   ```

### 3. **Automatic Jobs Not Running**

**Symptoms:**
- No jobs created automatically
- `lastWeeklyRun` or `lastMonthlyRun` outdated

**Diagnosis:**
```bash
GET /admin/awards/scheduler/status
```

**Solutions:**
1. **Check Cron Service:**
   ```bash
   # Verify application is running
   ps aux | grep node
   
   # Check if scheduler is active
   GET /admin/awards/scheduler/status
   ```

2. **Manual Schedule Trigger:**
   ```bash
   # Trigger weekly awards
   POST /admin/awards/scheduler/trigger/weekly
   
   # Trigger monthly awards  
   POST /admin/awards/scheduler/trigger/monthly
   ```

3. **Restart Application:**
   ```bash
   # Restart the application to reinitialize cron jobs
   npm run start:prod
   ```

### 4. **High Failure Rate**

**Symptoms:**
- `successRate < 80%`
- Multiple failed jobs in recent history

**Diagnosis:**
```bash
GET /admin/awards/monitoring?status=failed&limit=5
```

**Common Causes & Solutions:**

1. **Database Connection Issues:**
   - **Check:** Database connectivity
   - **Fix:** Restart database service, check connection pool

2. **Missing Dependencies:**
   - **Check:** Required services (diary, essay, novel modules)
   - **Fix:** Ensure all modules are running

3. **Data Validation Errors:**
   - **Check:** Job error messages for validation failures
   - **Fix:** Verify data integrity in source tables

## 🔧 Maintenance Tasks

### Daily Monitoring Checklist

1. **Check System Health:**
   ```bash
   GET /admin/awards/scheduler/health
   ```

2. **Verify Recent Jobs:**
   ```bash
   GET /admin/awards/monitoring?limit=5
   ```

3. **Monitor Success Rate:**
   ```bash
   GET /admin/awards/monitoring/stats
   ```

### Weekly Maintenance

1. **Review Failed Jobs:**
   ```bash
   GET /admin/awards/monitoring?status=failed&startDate=2024-12-01&endDate=2024-12-07
   ```

2. **Check Award Distribution:**
   ```bash
   GET /admin/awards/scheduler/diagnostic
   ```

3. **Cleanup Old Jobs (Optional):**
   ```sql
   -- Archive jobs older than 3 months
   DELETE FROM award_job 
   WHERE created_at < NOW() - INTERVAL '3 months'
   AND status IN ('completed', 'failed');
   ```

## 📈 Performance Optimization

### Monitor Execution Times
- **Target:** `executionTimeMs < 30000` (30 seconds)
- **Warning:** `executionTimeMs > 60000` (1 minute)
- **Critical:** `executionTimeMs > 300000` (5 minutes)

### Optimize Slow Jobs
1. **Check Database Indexes:**
   ```sql
   -- Ensure proper indexes exist
   CREATE INDEX IF NOT EXISTS idx_diary_entry_status_created 
   ON diary_entry(status, created_at);
   
   CREATE INDEX IF NOT EXISTS idx_award_winner_created 
   ON award_winner(created_at);
   ```

2. **Monitor Database Performance:**
   ```sql
   -- Check slow queries
   SELECT query, mean_time, calls 
   FROM pg_stat_statements 
   WHERE query LIKE '%award%' 
   ORDER BY mean_time DESC;
   ```

## 🚀 Emergency Procedures

### System Down Recovery

1. **Immediate Assessment:**
   ```bash
   GET /admin/awards/scheduler/health
   GET /admin/awards/monitoring/running
   ```

2. **Stop All Running Jobs:**
   ```sql
   UPDATE award_job 
   SET status = 'failed', 
       error_message = 'System recovery - job terminated',
       completed_at = NOW()
   WHERE status = 'running';
   ```

3. **Restart Services:**
   ```bash
   # Restart application
   npm run start:prod
   
   # Verify health
   GET /admin/awards/scheduler/health
   ```

4. **Resume Operations:**
   ```bash
   # Test with manual trigger
   POST /admin/awards/scheduler/trigger/test
   
   # Monitor execution
   GET /admin/awards/monitoring/running
   ```

## 📞 Escalation Procedures

### When to Escalate

- **Critical:** System health status "critical" for >1 hour
- **High:** Success rate <50% for >24 hours  
- **Medium:** Jobs stuck in running status for >2 hours
- **Low:** Minor performance degradation

### Escalation Contacts

1. **Technical Lead** - System architecture issues
2. **Database Admin** - Database performance problems
3. **DevOps Team** - Infrastructure and deployment issues

## 📋 Monitoring Checklist

### ✅ Daily (5 minutes)
- [ ] Check system health status
- [ ] Verify no jobs stuck in running status
- [ ] Review success rate (should be >90%)

### ✅ Weekly (15 minutes)  
- [ ] Review failed jobs and error patterns
- [ ] Check award distribution trends
- [ ] Verify automatic job scheduling

### ✅ Monthly (30 minutes)
- [ ] Performance analysis and optimization
- [ ] Database maintenance and cleanup
- [ ] System capacity planning review

---

**Remember:** The award system is designed to be self-healing and resilient. Most issues resolve automatically, but proactive monitoring ensures optimal performance and user experience.