import { MigrationInterface, QueryRunner } from 'typeorm';

export class FixNovelSkinConstraints1755670400000 implements MigrationInterface {
  name = 'FixNovelSkinConstraints1755670400000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop the foreign key constraint for novel_entry.skin_id if it exists
    try {
      await queryRunner.query(`
        ALTER TABLE "novel_entry" 
        DROP CONSTRAINT IF EXISTS "FK_novel_entry_skin"
      `);
      console.log('Dropped FK_novel_entry_skin constraint');
    } catch (error) {
      console.log('FK_novel_entry_skin constraint does not exist or already dropped');
    }

    // Clean up any invalid skin_id references in novel_entry table
    await queryRunner.query(`
      UPDATE "novel_entry" 
      SET "skin_id" = NULL 
      WHERE "skin_id" IS NOT NULL 
      AND "skin_id" NOT IN (
        SELECT "id" FROM "diary_skin" WHERE "is_active" = true
        UNION
        SELECT "id" FROM "student_diary_skin" WHERE "is_active" = true
      )
    `);

    console.log('Fixed novel skin constraint issues');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Note: We don't recreate the foreign key constraint in down migration
    // as it was causing issues. The skin_id field remains as a simple string reference.
    console.log('Novel skin constraint fix rollback - no action needed');
  }
}