import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddMessageAndExpiryToDiaryFriendShares1706675678903 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop existing foreign key constraints if they exist
    try {
      await queryRunner.query(`
        ALTER TABLE "diary_entry" DROP CONSTRAINT IF EXISTS "FK_diary_entry_skin_id";
        ALTER TABLE "diary_entry" DROP CONSTRAINT IF EXISTS "FK_diary_entry_student_skin_id";
      `);
    } catch (error) {
      console.log('No existing foreign key constraints to drop');
    }

    // Drop existing check constraint if it exists
    try {
      await queryRunner.query(`
        ALTER TABLE "diary_entry" DROP CONSTRAINT IF EXISTS "CHK_diary_entry_skin_exclusivity";
      `);
    } catch (error) {
      console.log('No existing check constraint to drop');
    }

    // Add new foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "diary_entry"
      ADD CONSTRAINT "FK_diary_entry_skin_id"
      FOREIGN KEY ("skin_id")
      REFERENCES "diary_skin"("id")
      ON DELETE SET NULL
      ON UPDATE CASCADE;

      ALTER TABLE "diary_entry"
      ADD CONSTRAINT "FK_diary_entry_student_skin_id"
      FOREIGN KEY ("student_skin_id")
      REFERENCES "student_diary_skin"("id")
      ON DELETE SET NULL
      ON UPDATE CASCADE;
    `);

    // Add check constraint to ensure only one skin type is used
    await queryRunner.query(`
      ALTER TABLE "diary_entry"
      ADD CONSTRAINT "CHK_diary_entry_skin_exclusivity"
      CHECK (
        (skin_id IS NOT NULL AND student_skin_id IS NULL) OR
        (skin_id IS NULL AND student_skin_id IS NOT NULL) OR
        (skin_id IS NULL AND student_skin_id IS NULL)
      );
    `);

    // Add skin_type column if it doesn't exist
    const hasSkinType = await queryRunner.hasColumn('diary_entry', 'skin_type');
    if (!hasSkinType) {
      await queryRunner.query(`
        ALTER TABLE "diary_entry"
        ADD COLUMN "skin_type" character varying CHECK (skin_type IN ('global', 'student')) DEFAULT 'global';
      `);
    }

    // Update skin_type based on which skin_id is set
    await queryRunner.query(`
      UPDATE "diary_entry"
      SET skin_type = CASE
        WHEN skin_id IS NOT NULL THEN 'global'
        WHEN student_skin_id IS NOT NULL THEN 'student'
        ELSE 'global'
      END;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove the check constraint
    await queryRunner.query(`
      ALTER TABLE "diary_entry" DROP CONSTRAINT IF EXISTS "CHK_diary_entry_skin_exclusivity";
    `);

    // Remove the foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "diary_entry" DROP CONSTRAINT IF EXISTS "FK_diary_entry_skin_id";
      ALTER TABLE "diary_entry" DROP CONSTRAINT IF EXISTS "FK_diary_entry_student_skin_id";
    `);

    // Remove the skin_type column
    await queryRunner.query(`
      ALTER TABLE "diary_entry" DROP COLUMN IF EXISTS "skin_type";
    `);
  }
}
