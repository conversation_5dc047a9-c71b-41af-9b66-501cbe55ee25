import { MigrationInterface, QueryRunner } from 'typeorm';

export class FixDiaryEntrySkinConstraint1754600000000 implements MigrationInterface {
  name = 'FixDiaryEntrySkinConstraint1754600000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop the existing constraint if it exists
    try {
      await queryRunner.query(`
        ALTER TABLE "diary_entry" 
        DROP CONSTRAINT IF EXISTS "CHK_diary_entry_skin_exclusivity"
      `);
    } catch (error) {
      console.log('Constraint CHK_diary_entry_skin_exclusivity might not exist, continuing...');
    }

    // Add the updated constraint that allows for backward compatibility
    try {
      await queryRunner.query(`
        ALTER TABLE "diary_entry" 
        ADD CONSTRAINT "CHK_diary_entry_skin_exclusivity" 
        CHECK (
          (skin_type = 'global' AND skin_id IS NOT NULL AND student_skin_id IS NULL) OR
          (skin_type = 'student' AND skin_id IS NULL AND student_skin_id IS NOT NULL) OR
          (skin_type IS NULL AND skin_id IS NOT NULL AND student_skin_id IS NULL)
        )
      `);
    } catch (error) {
      console.log('Error adding updated constraint:', error.message);
      throw error;
    }

    // Update existing entries that might have NULL skin_type but have skin_id
    try {
      await queryRunner.query(`
        UPDATE diary_entry 
        SET skin_type = 'global' 
        WHERE skin_type IS NULL AND skin_id IS NOT NULL AND student_skin_id IS NULL
      `);
    } catch (error) {
      console.log('Error updating existing entries:', error.message);
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the updated constraint
    await queryRunner.query(`
      ALTER TABLE "diary_entry" 
      DROP CONSTRAINT IF EXISTS "CHK_diary_entry_skin_exclusivity"
    `);

    // Restore the original constraint
    await queryRunner.query(`
      ALTER TABLE "diary_entry" 
      ADD CONSTRAINT "CHK_diary_entry_skin_exclusivity" 
      CHECK (
        (skin_type = 'global' AND skin_id IS NOT NULL AND student_skin_id IS NULL) OR
        (skin_type = 'student' AND skin_id IS NULL AND student_skin_id IS NOT NULL)
      )
    `);
  }
}