import { Injectable, Logger } from '@nestjs/common';
import { TimezoneService } from '../services/timezone.service';

@Injectable()
export class SocketTimezoneTransformer {
  private readonly logger = new Logger(SocketTimezoneTransformer.name);

  constructor(private readonly timezoneService: TimezoneService) {}

  /**
   * Transform socket event data with timezone conversion
   */
  async transform(data: any, userId: string): Promise<any> {
    if (!data || !userId) {
      return data;
    }

    try {
      const userTimezone = await this.timezoneService.getUserTimezone(userId);
      return this.transformTimestamps(data, userTimezone);
    } catch (error) {
      this.logger.error(`Error transforming socket data for user ${userId}:`, error);
      return data;
    }
  }

  private transformTimestamps(obj: any, timezone: string): any {
    if (!obj || typeof obj !== 'object') {
      return obj;
    }

    if (obj instanceof Date) {
      return this.timezoneService.convertUTCToUserTimezone(obj, timezone);
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.transformTimestamps(item, timezone));
    }

    const transformed = { ...obj };
    
    // Transform common timestamp fields
    const timestampFields = [
      'createdAt', 'updatedAt', 'timestamp', 'readAt', 'deliveredAt',
      'lastLoginAt', 'scheduledAt', 'completedAt'
    ];

    for (const field of timestampFields) {
      if (transformed[field]) {
        if (transformed[field] instanceof Date) {
          transformed[field] = this.timezoneService.convertUTCToUserTimezone(
            transformed[field],
            timezone
          );
        } else if (typeof transformed[field] === 'string' && this.isISODateString(transformed[field])) {
          const date = new Date(transformed[field]);
          if (!isNaN(date.getTime())) {
            transformed[field] = this.timezoneService.convertUTCToUserTimezone(date, timezone);
          }
        }
      }
    }

    // Recursively transform nested objects
    for (const key in transformed) {
      if (transformed[key] && typeof transformed[key] === 'object') {
        transformed[key] = this.transformTimestamps(transformed[key], timezone);
      }
    }

    return transformed;
  }

  private isISODateString(value: string): boolean {
    return /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/.test(value);
  }
}