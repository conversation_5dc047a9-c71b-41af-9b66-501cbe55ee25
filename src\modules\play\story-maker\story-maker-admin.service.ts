import { Injectable, Logger, BadRequestException, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, FindOptionsWhere, DataSource, ILike, In } from 'typeorm';
import { StoryMaker } from '../../../database/entities/story-maker.entity';
import { StoryMakerRegistry } from '../../../database/entities/story-maker-registry.entity';
import { StoryMakerParticipation } from '../../../database/entities/story-maker-participation.entity';
import { StoryMakerSubmission } from '../../../database/entities/story-maker-submission.entity';
import { StoryMakerEvaluation } from '../../../database/entities/story-maker-evaluation.entity';
import { User } from '../../../database/entities/user.entity';

import { CreateStoryMakerDto, UpdateStoryMakerDto, StoryMakerResponseDto, GetStoriesQueryDto, ToggleStoryStatusDto } from '../../../database/models/story-maker/story-maker.dto';
import { FileRegistryService } from '../../../common/services/file-registry.service';
import { GeminiAiService } from '../../../common/services/gemini-ai.service';
import { FileEntityType } from '../../../common/enums/file-entity-type.enum';
import { PagedListDto } from '../../../common/models/paged-list.dto';
import { GetParticipantsQueryDto, ParticipantDetailResponseDto, GroupedParticipantListResponseDto, GroupedParticipantItemDto } from '../../../database/models/story-maker/story-maker-admin.dto';
import { StoryMakerSubmissionResponseDto, StoryMakerEvaluationResponseDto } from '../../../database/models/story-maker/story-maker-submission.dto';

@Injectable()
export class StoryMakerAdminService {
  private readonly logger = new Logger(StoryMakerAdminService.name);

  constructor(
    @InjectRepository(StoryMaker)
    private readonly storyMakerRepository: Repository<StoryMaker>,
    @InjectRepository(StoryMakerRegistry)
    private readonly storyMakerRegistryRepository: Repository<StoryMakerRegistry>,
    @InjectRepository(StoryMakerParticipation)
    private readonly participationRepository: Repository<StoryMakerParticipation>,
    @InjectRepository(StoryMakerSubmission)
    private readonly submissionRepository: Repository<StoryMakerSubmission>,
    @InjectRepository(StoryMakerEvaluation)
    private readonly evaluationRepository: Repository<StoryMakerEvaluation>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly fileRegistryService: FileRegistryService,
    private readonly geminiAiService: GeminiAiService,
    private readonly dataSource: DataSource,

  ) {}

  /**
   * Create a new story
   */
  async createStory(createStoryMakerDto: CreateStoryMakerDto, picture: any): Promise<StoryMakerResponseDto> {
    if (!picture) {
      throw new BadRequestException('Picture is required');
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Create the story entity first to get the UUID
      const storyMaker = queryRunner.manager.create(StoryMaker, {
        title: createStoryMakerDto.title,
        instruction: createStoryMakerDto.instruction,
        picture: '', // Temporary placeholder, will be updated after file upload
        deadline: createStoryMakerDto.deadline,
        isActive: true,
      });

      const savedStory = await queryRunner.manager.save(storyMaker);
      this.logger.log(`Created story maker: ${savedStory.id}`);

      // Validate that we have a valid UUID before uploading
      if (!savedStory.id) {
        throw new Error('Story maker ID is missing after creation');
      }

      this.logger.log(`Uploading file for story maker with UUID: ${savedStory.id}`);

      // Commit the transaction first so the story exists for the foreign key constraint
      await queryRunner.commitTransaction();
      this.logger.log('Transaction committed - story maker created successfully');

      // Now upload the file with the actual story ID (outside transaction)
      let uploadResult: { filePath: string; registry?: any };
      try {
        uploadResult = await this.fileRegistryService.uploadFile(FileEntityType.STORY_MAKER, picture, createStoryMakerDto.title, { entityId: savedStory.id });

        this.logger.log(`File upload successful - filePath: ${uploadResult.filePath}`);
      } catch (uploadError) {
        this.logger.error(`Error uploading file: ${uploadError.message}`);
        // If file upload fails, we need to clean up the created story
        try {
          await this.storyMakerRepository.remove(savedStory);
          this.logger.log(`Cleaned up story maker after upload failure: ${savedStory.id}`);
        } catch (cleanupError) {
          this.logger.error(`Error cleaning up story maker after upload failure: ${cleanupError.message}`);
        }
        throw uploadError;
      }

      // Update the story with the actual picture path (outside transaction)
      savedStory.picture = uploadResult.filePath;

      // Perform image analysis using Gemini AI
      let imageAnalysis = null;
      try {
        this.logger.log('Starting image analysis with Gemini AI');
        imageAnalysis = await this.geminiAiService.analyzeImage(picture.buffer, picture.mimetype);
        this.logger.log('Image analysis completed successfully');
        this.logger.debug('Image analysis result:', imageAnalysis);

        // Save the analysis to the story
        savedStory.imageAnalysis = {
          objects: imageAnalysis.objects,
          scene: imageAnalysis.scene,
          mood: imageAnalysis.mood,
          themes: imageAnalysis.themes,
          colors: imageAnalysis.colors,
          setting: imageAnalysis.setting,
          characters: imageAnalysis.characters,
          emotions: imageAnalysis.emotions,
          description: imageAnalysis.description,
          relevanceKeywords: imageAnalysis.relevanceKeywords,
        };
      } catch (analysisError) {
        this.logger.error(`Image analysis failed: ${analysisError.message}`, analysisError.stack);
        // Continue without analysis - it's not critical for story creation
        this.logger.warn('Continuing story creation without image analysis');
      }

      await this.storyMakerRepository.save(savedStory);
      this.logger.log(`Updated story maker with picture path and image analysis: ${savedStory.picture}`);

      const pictureUrl = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.STORY_MAKER, savedStory.id);

      return {
        id: savedStory.id,
        title: savedStory.title,
        instruction: savedStory.instruction,
        picture: pictureUrl,
        is_active: savedStory.isActive,
        deadline: savedStory.deadline,
        image_analysis: savedStory.imageAnalysis,
        created_at: savedStory.createdAt,
        updated_at: savedStory.updatedAt,
      };
    } catch (error) {
      // Rollback the transaction in case of error (only if it's still active)
      if (!queryRunner.isReleased && queryRunner.isTransactionActive) {
        await queryRunner.rollbackTransaction();
      }
      this.logger.error(`Error in transaction: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to create story: ${error.message}`);
    } finally {
      // Release the query runner
      if (!queryRunner.isReleased) {
        await queryRunner.release();
      }
    }
  }

  /**
   * Map entity to DTO
   */
  async mapToResponseDto(storyMaker: StoryMaker): Promise<StoryMakerResponseDto> {
    const pictureUrl = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.STORY_MAKER, storyMaker.id);

    return {
      id: storyMaker.id,
      title: storyMaker.title,
      instruction: storyMaker.instruction,
      picture: pictureUrl || '',
      is_active: storyMaker.isActive,
      deadline: storyMaker.deadline,
      image_analysis: storyMaker.imageAnalysis,
      created_at: storyMaker.createdAt,
      updated_at: storyMaker.updatedAt,
    };
  }

  /**
   * Get paginated stories with filtering
   */
  async getStories(query: GetStoriesQueryDto): Promise<PagedListDto<StoryMakerResponseDto>> {
    const { page = 1, limit = 10, sortBy = 'createdAt', sortDirection = 'DESC', search, isActive } = query;
    const skip = (page - 1) * limit;
    const queryBuilder = this.storyMakerRepository.createQueryBuilder('story');

    if (search) {
      queryBuilder.andWhere('story.title ILIKE :search', { search: `%${search}%` });
    }

    if (isActive !== undefined) {
      queryBuilder.andWhere('story.isActive = :isActive', { isActive });
    }

    const validSortFields = ['title', 'createdAt', 'score'];
    const validSortBy = validSortFields.includes(sortBy) ? sortBy : 'createdAt';
    queryBuilder.orderBy(`story.${validSortBy}`, sortDirection);

    queryBuilder.skip(skip).take(limit);
    const [stories, totalCount] = await queryBuilder.getManyAndCount();

    const responseDtos: StoryMakerResponseDto[] = [];
    for (const story of stories) {
      responseDtos.push(await this.mapToResponseDto(story));
    }

    return new PagedListDto(responseDtos, totalCount, page, limit);
  }

  /**
   * Get a single story by ID
   */
  async getStoryById(id: string): Promise<StoryMakerResponseDto> {
    const story = await this.storyMakerRepository.findOne({
      where: { id },
    });

    if (!story) {
      throw new NotFoundException(`Story not found. Please check if the story exists.`);
    }

    return this.mapToResponseDto(story);
  }

  /**
   * Update a story
   */
  async updateStory(id: string, updateStoryMakerDto: UpdateStoryMakerDto, picture?: any): Promise<StoryMakerResponseDto> {
    const story = await this.storyMakerRepository.findOne({
      where: { id },
    });

    if (!story) {
      throw new NotFoundException(`Story not found. Please check if the story exists.`);
    }

    // Check if the story has any participation records
    const hasParticipation = await this.hasParticipation(id);
    if (hasParticipation) {
      this.logger.warn(`Attempted to update story maker with ID ${id} (${story.title}) that has participation records`);
      throw new ForbiddenException(`Cannot update story "${story.title}" because it has student participation records. Create a new story instead.`);
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Update story fields - ignore null values and empty strings to keep previous values
      if (updateStoryMakerDto.title !== undefined && updateStoryMakerDto.title !== null && updateStoryMakerDto.title.trim() !== '') {
        story.title = updateStoryMakerDto.title;
      }

      if (updateStoryMakerDto.instruction !== undefined && updateStoryMakerDto.instruction !== null && updateStoryMakerDto.instruction.trim() !== '') {
        story.instruction = updateStoryMakerDto.instruction;
      }

      if (updateStoryMakerDto.isActive !== undefined) {
        // For boolean fields, we can allow explicit false values but not null
        story.isActive = updateStoryMakerDto.isActive === null ? story.isActive : updateStoryMakerDto.isActive;
      }

      if (updateStoryMakerDto.deadline !== undefined) {
        story.deadline = updateStoryMakerDto.deadline;
      }

      // Handle picture update if provided
      if (picture) {
        // Find existing registry entry to get the old file path
        const existingRegistry = await this.storyMakerRegistryRepository.findOne({
          where: { storyMakerId: id },
        });

        // Store the old file path for deletion after successful upload
        const oldFilePath = existingRegistry?.filePath;

        // FileRegistryService handles file validation internally
        const uploadResult = await this.fileRegistryService.uploadFile(FileEntityType.STORY_MAKER, picture, story.title, { entityId: id });

        // Update the picture path
        story.picture = uploadResult.filePath;

        if (existingRegistry) {
          // Update existing registry
          existingRegistry.filePath = uploadResult.filePath;
          existingRegistry.fileName = picture.originalname;
          existingRegistry.mimeType = picture.mimetype;
          existingRegistry.fileSize = picture.size;
          await queryRunner.manager.save(existingRegistry);

          // Delete the old file if it exists and is different from the new one
          if (oldFilePath && oldFilePath !== uploadResult.filePath) {
            try {
              this.logger.log(`Deleting old file: ${oldFilePath}`);
              this.fileRegistryService.deleteFile(oldFilePath);
            } catch (error) {
              // Log the error but don't fail the update
              this.logger.warn(`Failed to delete old file ${oldFilePath} for story maker with ID ${id} (${story.title}): ${error.message}`);
            }
          }
        } else {
          // Create new registry entry
          const registryEntry = queryRunner.manager.create(StoryMakerRegistry, {
            storyMakerId: id,
            filePath: uploadResult.filePath,
            fileName: picture.originalname,
            mimeType: picture.mimetype,
            fileSize: picture.size,
          });
          await queryRunner.manager.save(registryEntry);
        }
      }

      // Save the updated story
      const updatedStory = await queryRunner.manager.save(story);
      await queryRunner.commitTransaction();

      return this.mapToResponseDto(updatedStory);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Failed to update story maker with ID ${id} (${story.title}): ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to update story "${story.title}". Please try again later.`);
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Check if a story has any participation records
   * @param storyMakerId The ID of the story maker to check
   * @returns True if the story has participation records, false otherwise
   */
  private async hasParticipation(storyMakerId: string): Promise<boolean> {
    const count = await this.participationRepository.count({
      where: { storyMakerId },
    });
    return count > 0;
  }

  /**
   * Get all submissions for a specific student and story
   * @param studentId The ID of the student
   * @param storyId The ID of the story maker
   * @returns Detailed participation information with all submissions
   */
  async getStudentStoryParticipation(studentId: string, storyId: string): Promise<ParticipantDetailResponseDto> {
    try {
      // Create query builder for participation
      const queryBuilder = this.participationRepository
        .createQueryBuilder('participation')
        .leftJoinAndSelect('participation.storyMaker', 'storyMaker')
        .innerJoin('user', 'student', 'participation.studentId = student.id')
        .leftJoin('user', 'evaluator', 'participation.evaluatedBy = evaluator.id')
        .where('participation.studentId = :studentId', { studentId })
        .andWhere('participation.storyMakerId = :storyId', { storyId })
        .addSelect('student.name', 'student_name')
        .addSelect('student.email', 'student_email')
        .addSelect('student.profilePicture', 'student_profile_picture')
        .addSelect('evaluator.name', 'evaluator_name')
        .addSelect('evaluator.profilePicture', 'evaluator_profile_picture');

      // Execute query
      const participation = await queryBuilder.getOne();

      if (!participation) {
        // Get student name for a more user-friendly error message
        const student = await this.userRepository.findOne({
          where: { id: studentId },
        });

        // Get story title for a more user-friendly error message
        const story = await this.storyMakerRepository.findOne({
          where: { id: storyId },
        });

        const studentName = student ? student.name : 'Unknown';
        const storyTitle = story ? story.title : 'Unknown';

        throw new NotFoundException(`No participation found for student "${studentName}" in story "${storyTitle}"`);
      }

      const rawData = participation as any;

      // Get all submissions for this participation
      const submissions = await this.submissionRepository.find({
        where: { participationId: participation.id },
        order: { submittedAt: 'DESC' },
      });

      // Get all evaluations for these submissions
      const submissionIds = submissions.map((sub) => sub.id);
      const evaluations = await this.evaluationRepository.find({
        where: { submissionId: In(submissionIds) },
      });

      // Log the number of submissions found
      this.logger.log(`Found ${submissions.length} submissions for student ${studentId} (${rawData.student_name}) in story maker ${storyId} (${participation.storyMaker.title})`);

      // Map submissions to DTOs with their evaluations
      const submissionDtos = submissions.map((submission) => {
        const evaluation = evaluations.find((evaluation) => evaluation.submissionId === submission.id);

        const submissionDto: any = {
          id: submission.id,
          content: submission.content,
          submitted_at: submission.submittedAt,
          is_evaluated: submission.isEvaluated,
          created_at: submission.createdAt,
        };

        if (evaluation) {
          submissionDto.evaluation = {
            id: evaluation.id,
            // AI evaluation fields (no tutor needed)
            ai_feedback: evaluation.aiFeedback,
            content_task_fulfillment: evaluation.contentTaskFulfillment,
            organization_coherence: evaluation.organizationCoherence,
            grammar_accuracy: evaluation.grammarAccuracy,
            vocabulary_lexical: evaluation.vocabularyLexical,
            sentence_fluency_style: evaluation.sentenceFluencyStyle,
            clarity_cohesion: evaluation.clarityCohesion,
            creativity: evaluation.creativity,
            critical_thinking: evaluation.criticalThinking,
            expressiveness: evaluation.expressiveness,
            total_score: evaluation.totalScore,
            word_count: evaluation.wordCount,
            grammar_error_count: evaluation.grammarErrorCount,
            evaluated_at: evaluation.evaluatedAt,
            created_at: evaluation.createdAt,
          };
        }

        return submissionDto;
      });

      // Map to DTO
      return {
        id: participation.id,
        student_id: participation.studentId,
        student_name: rawData.student_name,
        student_email: rawData.student_email,
        student_profile_picture: rawData.student_profile_picture,
        story_maker_id: participation.storyMakerId,
        story_maker_title: participation.storyMaker.title,
        is_evaluated: participation.isEvaluated,
        score: participation.score,
        first_submitted_at: participation.firstSubmittedAt,
        evaluated_at: participation.evaluatedAt,
        evaluated_by: participation.evaluatedBy,
        evaluator_name: rawData.evaluator_name,
        evaluator_profile_picture: rawData.evaluator_profile_picture,
        submissions: submissionDtos,
        created_at: participation.createdAt,
        updated_at: participation.updatedAt,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to get participation: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to get participation');
    }
  }

  /**
   * Get a list of story maker participations grouped by student
   * @param queryDto Query parameters for filtering and pagination
   * @returns Paginated list of participations grouped by student
   */
  async getGroupedParticipations(queryDto: GetParticipantsQueryDto): Promise<GroupedParticipantListResponseDto> {
    try {
      const { page = 1, limit = 10, search, storyMakerId, isEvaluated, sortBy = 'firstSubmittedAt', sortDirection = 'DESC' } = queryDto;
      const skip = (page - 1) * limit;

      // First, get all unique students who have participated
      const studentQueryBuilder = this.participationRepository
        .createQueryBuilder('participation')
        .select('participation.studentId', 'student_id')
        .addSelect('student.name', 'student_name')
        .addSelect('student.email', 'student_email')
        .addSelect('student.profilePicture', 'student_profile_picture')
        .innerJoin('user', 'student', 'participation.studentId = student.id')
        .groupBy('participation.studentId')
        .addGroupBy('student.name')
        .addGroupBy('student.email')
        .addGroupBy('student.profilePicture');

      // Apply student filters
      if (search) {
        studentQueryBuilder.andWhere('(student.name ILIKE :search OR student.email ILIKE :search)', { search: `%${search}%` });
      }

      if (storyMakerId) {
        studentQueryBuilder.andWhere('participation.storyMakerId = :storyMakerId', { storyMakerId });
      }

      // Apply sorting
      switch (sortBy) {
        case 'studentName':
          studentQueryBuilder.orderBy('student.name', sortDirection);
          break;
        default:
          studentQueryBuilder.orderBy('student.name', 'ASC');
      }

      // Apply pagination to students
      studentQueryBuilder.skip(skip).take(limit);

      // Get total count of unique students
      const totalStudentCount = await studentQueryBuilder.getCount();

      // Get the paginated list of students
      const students = await studentQueryBuilder.getRawMany();

      // For each student, get their participations
      const groupedParticipants: GroupedParticipantItemDto[] = [];

      for (const student of students) {
        // Get all participations for this student
        const participationsQueryBuilder = this.participationRepository
          .createQueryBuilder('participation')
          .leftJoinAndSelect('participation.storyMaker', 'storyMaker')
          .leftJoin('user', 'evaluator', 'participation.evaluatedBy = evaluator.id')
          .where('participation.studentId = :studentId', { studentId: student.student_id });

        // Apply participation filters
        if (isEvaluated !== undefined) {
          participationsQueryBuilder.andWhere('participation.isEvaluated = :isEvaluated', { isEvaluated });
        }

        if (storyMakerId) {
          participationsQueryBuilder.andWhere('participation.storyMakerId = :storyMakerId', { storyMakerId });
        }

        // Add select fields for evaluator information
        participationsQueryBuilder.addSelect('evaluator.name', 'evaluator_name').addSelect('evaluator.profilePicture', 'evaluator_profile_picture');

        // Get participations
        const participations = await participationsQueryBuilder.getMany();
        const rawData = await participationsQueryBuilder.getRawMany();

        // For each participation, get the submission count
        const stories = [];
        for (let i = 0; i < participations.length; i++) {
          const participation = participations[i];
          const raw = rawData[i];

          // Count submissions for this participation
          const submissionCount = await this.submissionRepository.count({
            where: { participationId: participation.id },
          });

          // Get latest submission date
          const latestSubmission = await this.submissionRepository.findOne({
            where: { participationId: participation.id },
            order: { submittedAt: 'DESC' },
          });

          stories.push({
            story_maker_id: participation.storyMakerId,
            story_maker_title: participation.storyMaker.title,
            participation_id: participation.id,
            is_evaluated: participation.isEvaluated,
            score: participation.score,
            first_submitted_at: participation.firstSubmittedAt,
            latest_submitted_at: latestSubmission?.submittedAt || participation.firstSubmittedAt,
            evaluated_at: participation.evaluatedAt,
            attempt_count: submissionCount,
            evaluator_name: raw.evaluator_name,
          });
        }

        // Sort stories by latest submission date (newest first)
        stories.sort((a, b) => {
          if (!a.latest_submitted_at) return 1;
          if (!b.latest_submitted_at) return -1;
          return b.latest_submitted_at.getTime() - a.latest_submitted_at.getTime();
        });

        // Add to grouped participants
        groupedParticipants.push({
          student_id: student.student_id,
          student_name: student.student_name,
          student_email: student.student_email,
          student_profile_picture: student.student_profile_picture,
          stories,
        });
      }

      return {
        students: groupedParticipants,
        total_count: totalStudentCount,
      };
    } catch (error) {
      this.logger.error(`Failed to get grouped participations: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to get grouped participations');
    }
  }

  /**
   * Delete a story
   */
  async deleteStory(id: string): Promise<void> {
    const story = await this.storyMakerRepository.findOne({
      where: { id },
    });

    if (!story) {
      throw new NotFoundException(`Story not found. Please check if the story exists.`);
    }

    // Check if the story has any participation records
    const hasParticipation = await this.hasParticipation(id);
    if (hasParticipation) {
      this.logger.warn(`Attempted to delete story maker with ID ${id} (${story.title}) that has participation records`);
      throw new ForbiddenException(`Cannot delete story "${story.title}" because it has student participation records. You can deactivate it instead.`);
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Find registry entries for this story
      const registryEntries = await this.storyMakerRegistryRepository.find({
        where: { storyMakerId: id },
      });

      // Delete registry entries first
      if (registryEntries.length > 0) {
        for (const entry of registryEntries) {
          // Delete the file from storage
          if (entry.filePath) {
            try {
              this.fileRegistryService.deleteFile(entry.filePath);
            } catch (error) {
              this.logger.warn(`Failed to delete file ${entry.filePath} for story maker with ID ${id} (${story.title}): ${error.message}`);
              // Continue with deletion even if file removal fails
            }
          }

          // Delete the registry entry
          await queryRunner.manager.remove(entry);
        }
      }

      // Delete the story
      await queryRunner.manager.remove(story);
      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Failed to delete story maker with ID ${id} (${story.title}): ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to delete story "${story.title}". Please try again later.`);
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Toggle story active status
   */
  async toggleStoryStatus(id: string, dto: ToggleStoryStatusDto): Promise<StoryMakerResponseDto> {
    const story = await this.storyMakerRepository.findOne({
      where: { id },
    });

    if (!story) {
      throw new NotFoundException('Story not found');
    }

    try {
      // Update only the active status
      story.isActive = dto.is_active;
      await this.storyMakerRepository.save(story);

      return this.mapToResponseDto(story);
    } catch (error) {
      this.logger.error(`Failed to toggle story status with ID ${id}: ${error.message}`, error.stack);
      throw new BadRequestException('Could not update story status. Please try again later.');
    }
  }
}
