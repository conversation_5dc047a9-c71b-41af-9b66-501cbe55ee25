import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddBookIdToTranscriptionSessions1734567890123 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Clear existing sessions first to avoid constraint issues
    await queryRunner.query(`TRUNCATE TABLE "transcription_sessions" CASCADE`);
    
    // Add book_id column as nullable first
    await queryRunner.addColumn('transcription_sessions', new TableColumn({
      name: 'book_id',
      type: 'uuid',
      isNullable: true,
    }));
    
    // Add foreign key constraint
    await queryRunner.query(`
      ALTER TABLE "transcription_sessions" 
      ADD CONSTRAINT "FK_transcription_sessions_book_id" 
      FOREIGN KEY ("book_id") REFERENCES "books"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);
    
    // Make column not nullable after constraint is added
    await queryRunner.query(`ALTER TABLE "transcription_sessions" ALTER COLUMN "book_id" SET NOT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "transcription_sessions" DROP CONSTRAINT "FK_transcription_sessions_book_id"`);
    await queryRunner.dropColumn('transcription_sessions', 'book_id');
  }
}