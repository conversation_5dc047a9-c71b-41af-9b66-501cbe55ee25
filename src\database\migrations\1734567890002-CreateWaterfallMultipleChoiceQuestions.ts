import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateWaterfallMultipleChoiceQuestions1734567890002 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'waterfall_multiple_choice_question',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'gen_random_uuid()',
          },
          {
            name: 'question_text',
            type: 'text',
          },
          {
            name: 'options',
            type: 'text[]',
          },
          {
            name: 'correct_option_indices',
            type: 'integer[]',
          },
          {
            name: 'allow_multiple_selection',
            type: 'boolean',
            default: false,
          },
          {
            name: 'min_selections',
            type: 'integer',
            isNullable: true,
          },
          {
            name: 'max_selections',
            type: 'integer',
            isNullable: true,
          },
          {
            name: 'set_id',
            type: 'uuid',
          },
          {
            name: 'time_limit_in_seconds',
            type: 'integer',
            isNullable: true,
          },
          {
            name: 'level',
            type: 'integer',
            isNullable: true,
          },
          {
            name: 'is_active',
            type: 'boolean',
            default: true,
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'created_by',
            type: 'uuid',
            isNullable: true,
          },
          {
            name: 'updated_by',
            type: 'uuid',
            isNullable: true,
          },
        ],
        foreignKeys: [
          {
            columnNames: ['set_id'],
            referencedTableName: 'waterfall_set',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
          },
        ],
      }),
      true,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('waterfall_multiple_choice_question');
  }
}