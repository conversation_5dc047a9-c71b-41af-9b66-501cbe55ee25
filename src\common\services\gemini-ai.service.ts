import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { GoogleGenerativeAI, HarmCategory, HarmBlockThreshold } from '@google/generative-ai';

export interface GeminiEvaluationResult {
  // New 9 criteria (0-100 scale)
  contentTaskFulfillment: number; // 0-100
  organizationCoherence: number; // 0-100
  grammarAccuracy: number; // 0-100
  vocabularyLexical: number; // 0-100
  sentenceFluencyStyle: number; // 0-100
  clarityCohesion: number; // 0-100
  creativity: number; // 0-100
  criticalThinking: number; // 0-100
  expressiveness: number; // 0-100
  
  // Supporting data
  grammarErrorCount: number;
  grammarErrors: string[];
  overallFeedback: string;
  processingTime: number; // milliseconds

  // Legacy compatibility (calculated from new criteria)
  creativityScore?: number; // Calculated from creativity/20
  sentencePowerScore?: number; // Calculated from sentenceFluencyStyle/33
  accuracyScore?: number; // Calculated from grammarAccuracy/33
  relevanceScore?: number; // Calculated from contentTaskFulfillment/20
  
  // Explanations
  creativityExplanation?: string;
  sentencePowerExplanation?: string;
  relevanceExplanation?: string;
  adminInstructions?: string;
  instructionAdherence?: number;
}

export interface SentenceCountResult {
  sentenceCount: number;
  sentences: string[];
}

export interface ImageAnalysisResult {
  objects: string[];
  scene: string;
  mood: string;
  themes: string[];
  colors: string[];
  setting: string;
  characters: string[];
  emotions: string[];
  description: string;
  relevanceKeywords: string[];
  processingTime: number;
}

@Injectable()
export class GeminiAiService {
  private readonly logger = new Logger(GeminiAiService.name);
  private readonly genAI: GoogleGenerativeAI;
  private readonly model: any;

  constructor(private readonly configService: ConfigService) {
    const apiKey = this.configService.get<string>('GOOGLE_GEMINI_API_KEY');
    
    if (!apiKey) {
      this.logger.error('GOOGLE_GEMINI_API_KEY is not configured');
      throw new Error('Google Gemini API key is required');
    }

    this.genAI = new GoogleGenerativeAI(apiKey);
    
    // Initialize the model with safety settings
    this.model = this.genAI.getGenerativeModel({
      model: 'gemini-1.5-flash',
      safetySettings: [
        {
          category: HarmCategory.HARM_CATEGORY_HARASSMENT,
          threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
        },
        {
          category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
          threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
        },
        {
          category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
          threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
        },
        {
          category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
          threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
        },
      ],
    });

    this.logger.log('Google Gemini AI service initialized successfully');
  }

  /**
   * Count sentences in the given text
   * This is used for Step 1 scoring (sentence count = points)
   */
  countSentences(text: string): SentenceCountResult {
    if (!text || text.trim().length === 0) {
      return { sentenceCount: 0, sentences: [] };
    }

    // Clean the text and split by sentence-ending punctuation
    const cleanText = text.trim();
    
    // Split by sentence endings: . ! ? followed by space or end of string
    const sentences = cleanText
      .split(/[.!?]+(?:\s+|$)/)
      .map(s => s.trim())
      .filter(s => s.length > 0);

    return {
      sentenceCount: sentences.length,
      sentences: sentences
    };
  }

  /**
   * Evaluate a story using Google Gemini AI
   * This performs the comprehensive evaluation for all AI-based criteria
   * @param content The story content to evaluate
   * @param imageAnalysis Optional image analysis to provide context for relevance scoring
   * @param adminInstructions Optional admin instructions to guide the evaluation
   */
  async evaluateStory(content: string, imageAnalysis?: any, adminInstructions?: string): Promise<GeminiEvaluationResult> {
    const startTime = Date.now();

    try {
      this.logger.log('Starting Gemini AI evaluation for story content');
      if (imageAnalysis) {
        this.logger.log('Image analysis provided - relevance scoring will be included');
      }

      // Prepare the evaluation prompt
      const prompt = this.buildEvaluationPrompt(content, imageAnalysis, adminInstructions);

      // Call Gemini API
      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      this.logger.debug('Raw Gemini response:', text);

      // Parse the structured response
      const evaluation = this.parseEvaluationResponse(text, imageAnalysis, content);

      const processingTime = Date.now() - startTime;

      this.logger.log(`Gemini AI evaluation completed in ${processingTime}ms`);
      this.logger.debug('Evaluation result:', evaluation);

      // Validate that relevance score is present when image analysis was provided
      if (imageAnalysis && evaluation.relevanceScore === undefined) {
        this.logger.warn('Image analysis was provided but relevance score is missing from evaluation');
      }

      return {
        ...evaluation,
        processingTime,
      };

    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.logger.error(`Gemini AI evaluation failed after ${processingTime}ms: ${error.message}`, error.stack);
      throw error; // Don't provide fallback, let the error propagate
    }
  }

  /**
   * Build the comprehensive evaluation prompt for Gemini
   */
  private buildEvaluationPrompt(content: string, imageAnalysis?: any, adminInstructions?: string): string {
    let contextSection = 'This is a creative story written by a student.';
    let relevanceSection = '';
    let instructionSection = '';

    // Add admin instructions if available
    if (adminInstructions) {
      instructionSection = `
ADMIN INSTRUCTIONS:
The student was given the following instructions for this story:
"${adminInstructions}"

Please consider these instructions when evaluating the story, especially for relevance and creativity.`;
    }

    if (imageAnalysis) {
      contextSection = `This story was written based on an image prompt. Here is the detailed analysis of the image:

IMAGE CONTEXT:
- Scene: ${imageAnalysis.scene || 'Creative scene'}
- Setting: ${imageAnalysis.setting || 'Creative setting'}
- Mood: ${imageAnalysis.mood || 'Inspiring'}
- Objects: ${imageAnalysis.objects?.join(', ') || 'Creative elements'}
- Themes: ${imageAnalysis.themes?.join(', ') || 'Creative themes'}
- Characters: ${imageAnalysis.characters?.join(', ') || 'Creative characters'}
- Emotions: ${imageAnalysis.emotions?.join(', ') || 'Inspiring emotions'}
- Relevance Keywords: ${imageAnalysis.relevanceKeywords?.join(', ') || 'Creative keywords'}
- Description: ${imageAnalysis.description || 'An inspiring creative image'}`;

      relevanceSection = `
5. RELEVANCE TO IMAGE AND INSTRUCTIONS (Score 1-5) - MANDATORY:
   This story was written based on an image prompt. You MUST evaluate how well the story incorporates the image elements.

   Check these specific elements from the image analysis:
   - SCENE/SETTING: "${imageAnalysis.scene || 'N/A'}"
   - OBJECTS: [${imageAnalysis.objects?.join(', ') || 'N/A'}]
   - MOOD: "${imageAnalysis.mood || 'N/A'}"
   - THEMES: [${imageAnalysis.themes?.join(', ') || 'N/A'}]
   - CHARACTERS: [${imageAnalysis.characters?.join(', ') || 'N/A'}]
   - RELEVANCE KEYWORDS: [${imageAnalysis.relevanceKeywords?.join(', ') || 'N/A'}]

   Scoring criteria (YOU MUST PROVIDE A SCORE):
   - 1 = Story has no connection to the image context or admin instructions
   - 2 = Minimal connection, mentions 1-2 relevant elements from image or basic instruction following
   - 3 = Moderate connection, incorporates 3-4 image elements and follows most instructions
   - 4 = Strong connection, well-integrated with 5-6 image elements and follows instructions well
   - 5 = Excellent connection, creatively uses most/all image elements and fully addresses instructions

   IMPORTANT: You MUST include "relevanceScore" and "relevanceExplanation" in your JSON response.`;
    }

    let jsonFormat = `{
  "contentTaskFulfillment": [0-100],
  "organizationCoherence": [0-100],
  "grammarAccuracy": [0-100],
  "vocabularyLexical": [0-100],
  "sentenceFluencyStyle": [0-100],
  "clarityCohesion": [0-100],
  "creativity": [0-100],
  "criticalThinking": [0-100],
  "expressiveness": [0-100],
  "grammarErrorCount": [number],
  "grammarErrors": ["error1", "error2", ...]`;

    if (adminInstructions) {
      jsonFormat += `,
  "instructionAdherence": [1-5],
  "instructionExplanation": "Brief explanation of how well instructions were followed"`;
    }

    if (imageAnalysis) {
      jsonFormat += `,
  "relevanceScore": [1-5],
  "relevanceExplanation": "REQUIRED: Detailed explanation of how well the story relates to the specific image elements listed above"`;
    }

    jsonFormat += `,
  "overallFeedback": "Encouraging feedback for the student"
}`;

    return `
You are an expert English writing evaluator for students. Please evaluate the following story on multiple criteria and provide your response in the exact JSON format specified below.

${contextSection}
${instructionSection}

STORY TO EVALUATE:
"${content}"

Please evaluate this story on the following 9 key writing criteria (0-100 scale each):

1. CONTENT & TASK FULFILLMENT (0-100):
   - Does the writing address the given task or question?
   - Are ideas relevant, sufficient, and well-supported?
   - Is the response complete without leaving out important points?

2. ORGANIZATION & COHERENCE (0-100):
   - Is there a clear structure (introduction, body, conclusion)?
   - Are ideas logically sequenced and easy to follow?
   - Are transitions and linking devices used appropriately?

3. GRAMMAR & ACCURACY (0-100):
   - Is grammar correct and consistent?
   - Are verb tenses, subject–verb agreement, and word forms used properly?
   - Are punctuation and spelling accurate?

4. VOCABULARY (LEXICAL RESOURCE) (0-100):
   - Does the writing use a wide range of vocabulary?
   - Are words chosen precisely to match meaning?
   - Is there variation instead of unnecessary repetition?

5. SENTENCE FLUENCY & STYLE (0-100):
   - Are sentences varied in structure (simple, compound, complex)?
   - Does the style fit the purpose (academic, formal, or creative)?
   - Is the writing smooth and natural, avoiding awkward phrasing?

6. CLARITY & COHESION (0-100):
   - Are ideas expressed clearly without ambiguity?
   - Is cohesion achieved through logical connectors, pronoun reference, and paragraphing?
   - Can the reader understand the message easily?

7. CREATIVITY (0-100):
   - Does the writing show original thinking and imagination?
   - Are creative elements well-integrated into the story?
   - Is the approach fresh and engaging?

8. CRITICAL THINKING (0-100):
   - Does the writing demonstrate analytical thinking?
   - Are ideas well-reasoned and supported?
   - Is there evidence of deeper understanding?

9. EXPRESSIVENESS (0-100):
   - Does the writing convey emotions and ideas effectively?
   - Is the voice engaging and authentic?
   - Does it connect with the reader?

ALSO PROVIDE:
- Count and list all grammatical errors (spelling, punctuation, grammar, syntax)
- Encouraging and constructive feedback for the student

${adminInstructions ? `5. INSTRUCTION ADHERENCE (Score 1-5):
   Based on the admin instructions provided, evaluate how well the student followed the specific requirements:
   - 1 = Did not follow instructions at all
   - 2 = Followed some basic instructions but missed key requirements
   - 3 = Followed most instructions adequately
   - 4 = Followed instructions well with good understanding
   - 5 = Excellently followed all instructions with creative interpretation

` : ''}${relevanceSection}

IMPORTANT: Respond ONLY with valid JSON in this exact format:
${jsonFormat}

Do not include any text outside the JSON response.
`;
  }

  /**
   * Parse the Gemini AI response into structured evaluation result
   */
  private parseEvaluationResponse(responseText: string, imageAnalysis?: any, originalContent?: string): Omit<GeminiEvaluationResult, 'processingTime'> {
    try {
      // Clean the response text to extract JSON
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }

      const jsonText = jsonMatch[0];
      const parsed = JSON.parse(jsonText);

      // Validate and sanitize the 9 criteria (0-100 scale) - no fallbacks
      const contentTaskFulfillment = Math.max(0, Math.min(100, parseInt(parsed.contentTaskFulfillment)));
      const organizationCoherence = Math.max(0, Math.min(100, parseInt(parsed.organizationCoherence)));
      const grammarAccuracy = Math.max(0, Math.min(100, parseInt(parsed.grammarAccuracy)));
      const vocabularyLexical = Math.max(0, Math.min(100, parseInt(parsed.vocabularyLexical)));
      const sentenceFluencyStyle = Math.max(0, Math.min(100, parseInt(parsed.sentenceFluencyStyle)));
      const clarityCohesion = Math.max(0, Math.min(100, parseInt(parsed.clarityCohesion)));
      const creativity = Math.max(0, Math.min(100, parseInt(parsed.creativity)));
      const criticalThinking = Math.max(0, Math.min(100, parseInt(parsed.criticalThinking)));
      const expressiveness = Math.max(0, Math.min(100, parseInt(parsed.expressiveness)));
      const grammarErrorCount = Math.max(0, parseInt(parsed.grammarErrorCount) || 0);

      const result: GeminiEvaluationResult = {
        contentTaskFulfillment,
        organizationCoherence,
        grammarAccuracy,
        vocabularyLexical,
        sentenceFluencyStyle,
        clarityCohesion,
        creativity,
        criticalThinking,
        expressiveness,
        grammarErrorCount,
        grammarErrors: Array.isArray(parsed.grammarErrors) ? parsed.grammarErrors : [],
        overallFeedback: parsed.overallFeedback,
        processingTime: 0, // Will be set by caller
        // Legacy compatibility
        creativityScore: Math.round(creativity / 20),
        sentencePowerScore: Math.round(sentenceFluencyStyle / 33),
        accuracyScore: Math.round(grammarAccuracy / 33),
        creativityExplanation: 'Evaluated using new 9-criteria system',
        sentencePowerExplanation: 'Evaluated using new 9-criteria system',
      };

      // Add relevance scoring if present in the response
      if (parsed.relevanceScore !== undefined) {
        result.relevanceScore = Math.max(1, Math.min(5, parseInt(parsed.relevanceScore) || 3));
        result.relevanceExplanation = parsed.relevanceExplanation || 'Story relevance to image evaluated';
      } else if (imageAnalysis) {
        // Log warning if image analysis was provided but relevance score is missing
        this.logger.warn('Relevance score missing from Gemini response despite image analysis being provided.');
      }

      // Add instruction adherence scoring if present
      if (parsed.instructionAdherence !== undefined) {
        result.instructionAdherence = Math.max(1, Math.min(5, parseInt(parsed.instructionAdherence) || 3));
      }

      return result;

    } catch (error) {
      this.logger.error(`Failed to parse Gemini response: ${error.message}`);
      this.logger.debug('Raw response:', responseText);
      throw error; // Don't provide fallback, let the error propagate
    }
  }

  /**
   * Calculate accuracy score based on grammar error count
   * Following the requirements:
   * - 3 points: 0-1 grammar errors
   * - 2 points: 2 grammar errors  
   * - 1 point: 3+ grammar errors
   */
  private calculateAccuracyScore(grammarErrorCount: number): number {
    if (grammarErrorCount <= 1) return 3;
    if (grammarErrorCount === 2) return 2;
    return 1; // 3+ errors
  }



  /**
   * Analyze an uploaded image to extract story-relevant information
   * @param imageBuffer The image file buffer
   * @param mimeType The MIME type of the image (e.g., 'image/jpeg', 'image/png')
   * @returns Detailed analysis of the image for story creation context
   */
  async analyzeImage(imageBuffer: Buffer, mimeType: string): Promise<ImageAnalysisResult> {
    const startTime = Date.now();

    try {
      this.logger.log('Starting Gemini AI image analysis');

      // Convert image buffer to base64
      const base64Image = imageBuffer.toString('base64');

      // Prepare the image analysis prompt
      const prompt = this.buildImageAnalysisPrompt();

      // Create the image part for Gemini
      const imagePart = {
        inlineData: {
          data: base64Image,
          mimeType: mimeType
        }
      };

      // Call Gemini API with image and prompt
      const result = await this.model.generateContent([prompt, imagePart]);
      const response = await result.response;
      const text = response.text();

      // Parse the structured response
      const analysis = this.parseImageAnalysisResponse(text);

      const processingTime = Date.now() - startTime;

      this.logger.log(`Gemini AI image analysis completed in ${processingTime}ms`);
      this.logger.debug('Image analysis result:', analysis);

      return {
        ...analysis,
        processingTime,
      };

    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.logger.error(`Gemini AI image analysis failed after ${processingTime}ms: ${error.message}`, error.stack);

      // Return fallback analysis to prevent system failure
      return this.getFallbackImageAnalysis(processingTime);
    }
  }

  /**
   * Build the image analysis prompt for Gemini
   */
  private buildImageAnalysisPrompt(): string {
    return `
You are an expert image analyst for creative writing prompts. Please analyze this image and extract information that would be useful for students writing stories based on this image.

Please analyze the image and provide your response in the exact JSON format specified below:

1. OBJECTS: List all significant objects, items, or elements visible in the image
2. SCENE: Describe the overall scene or setting in one clear sentence
3. MOOD: Describe the mood, atmosphere, or feeling the image conveys
4. THEMES: List potential story themes this image could inspire
5. COLORS: List the dominant colors in the image
6. SETTING: Describe the location/environment (e.g., "medieval castle", "modern city", "enchanted forest")
7. CHARACTERS: List any people, animals, or beings visible or suggested by the image
8. EMOTIONS: List emotions this image might evoke in viewers
9. DESCRIPTION: Provide a detailed, vivid description of the image (2-3 sentences)
10. RELEVANCE KEYWORDS: List 8-12 specific words/phrases that should appear in a relevant story. Include:
    - Specific nouns from the image (objects, places, characters)
    - Action words that relate to the scene
    - Descriptive adjectives that capture the mood
    - Thematic concepts the image suggests

IMPORTANT: Respond ONLY with valid JSON in this exact format:
{
  "objects": ["object1", "object2", ...],
  "scene": "One sentence description of the overall scene",
  "mood": "The mood or atmosphere",
  "themes": ["theme1", "theme2", ...],
  "colors": ["color1", "color2", ...],
  "setting": "The location or environment type",
  "characters": ["character1", "character2", ...],
  "emotions": ["emotion1", "emotion2", ...],
  "description": "Detailed 2-3 sentence description of the image",
  "relevanceKeywords": ["specific_noun1", "action_word1", "descriptive_adjective1", "theme_concept1", ...]
}

Do not include any text outside the JSON response.
`;
  }

  /**
   * Parse the image analysis response from Gemini
   */
  private parseImageAnalysisResponse(responseText: string): Omit<ImageAnalysisResult, 'processingTime'> {
    try {
      this.logger.debug('Parsing Gemini image analysis response');

      // Extract JSON from the response
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }

      const jsonText = jsonMatch[0];
      const parsed = JSON.parse(jsonText);

      // Validate and sanitize the response
      return {
        objects: Array.isArray(parsed.objects) ? parsed.objects : [],
        scene: parsed.scene || 'A scene suitable for creative storytelling',
        mood: parsed.mood || 'inspiring and creative',
        themes: Array.isArray(parsed.themes) ? parsed.themes : ['adventure', 'creativity'],
        colors: Array.isArray(parsed.colors) ? parsed.colors : [],
        setting: parsed.setting || 'creative setting',
        characters: Array.isArray(parsed.characters) ? parsed.characters : [],
        emotions: Array.isArray(parsed.emotions) ? parsed.emotions : ['wonder', 'curiosity'],
        description: parsed.description || 'An inspiring image perfect for creative storytelling',
        relevanceKeywords: Array.isArray(parsed.relevanceKeywords) ? parsed.relevanceKeywords : [],
      };

    } catch (error) {
      this.logger.error(`Failed to parse Gemini image analysis response: ${error.message}`);
      this.logger.debug('Raw response:', responseText);

      // Return safe fallback values
      return {
        objects: ['creative elements'],
        scene: 'A scene that inspires creative storytelling',
        mood: 'inspiring and imaginative',
        themes: ['creativity', 'imagination', 'storytelling'],
        colors: ['vibrant', 'inspiring'],
        setting: 'creative environment',
        characters: ['storyteller'],
        emotions: ['inspiration', 'creativity'],
        description: 'An inspiring image that sparks creativity and imagination for storytelling',
        relevanceKeywords: ['creative', 'story', 'imagination', 'narrative'],
      };
    }
  }

  /**
   * Provide fallback image analysis when Gemini API fails
   */
  private getFallbackImageAnalysis(processingTime: number): ImageAnalysisResult {
    this.logger.warn('Using fallback image analysis due to Gemini API failure');

    return {
      objects: ['creative elements', 'story inspiration'],
      scene: 'A creative scene perfect for storytelling',
      mood: 'inspiring and imaginative',
      themes: ['creativity', 'imagination', 'adventure', 'discovery'],
      colors: ['inspiring', 'vibrant'],
      setting: 'creative storytelling environment',
      characters: ['creative storyteller'],
      emotions: ['inspiration', 'wonder', 'creativity'],
      description: 'An inspiring image that encourages creative storytelling and imagination. Perfect for sparking student creativity.',
      relevanceKeywords: ['creative', 'story', 'imagination', 'narrative', 'adventure', 'discovery'],
      processingTime,
    };
  }



  /**
   * Test the Gemini AI connection
   */
  async testConnection(): Promise<boolean> {
    try {
      const testPrompt = 'Respond with exactly: "Connection successful"';
      const result = await this.model.generateContent(testPrompt);
      const response = await result.response;
      const text = response.text();

      this.logger.log('Gemini AI connection test result:', text);
      return text.includes('Connection successful');
    } catch (error) {
      this.logger.error('Gemini AI connection test failed:', error.message);
      return false;
    }
  }
}
