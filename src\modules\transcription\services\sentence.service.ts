import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Sentence } from '../../../database/entities/sentence.entity';
import { SentenceResponseDto } from '../../../database/models/transcription.dto';

@Injectable()
export class SentenceService {
  constructor(
    @InjectRepository(Sentence)
    private sentenceRepository: Repository<Sentence>,
  ) {}

  async extractSentencesFromBook(bookId: string, content: string): Promise<SentenceResponseDto[]> {
    // Simple sentence extraction - split by periods and clean up
    const sentences = content
      .split(/[.!?]+/)
      .map(s => s.trim())
      .filter(s => s.length > 10)
      .slice(0, 50); // Limit to first 50 sentences

    const sentenceEntities = sentences.map((content, index) => {
      return this.sentenceRepository.create({
        content: content + '.',
        orderIndex: index + 1,
        bookId,
        difficultyLevel: this.calculateDifficulty(content),
        grammarPattern: this.identifyGrammarPattern(content),
      });
    });

    const savedSentences = await this.sentenceRepository.save(sentenceEntities);
    return savedSentences.map(sentence => this.mapToResponseDto(sentence));
  }

  async findByBookId(bookId: string): Promise<SentenceResponseDto[]> {
    const sentences = await this.sentenceRepository.find({
      where: { bookId },
      order: { orderIndex: 'ASC' }
    });
    return sentences.map(sentence => this.mapToResponseDto(sentence));
  }

  async findById(id: string): Promise<SentenceResponseDto> {
    const sentence = await this.sentenceRepository.findOne({ where: { id } });
    if (!sentence) {
      throw new NotFoundException('Sentence not found');
    }
    return this.mapToResponseDto(sentence);
  }

  private calculateDifficulty(content: string): string {
    const wordCount = content.split(' ').length;
    if (wordCount <= 8) return 'easy';
    if (wordCount <= 15) return 'medium';
    return 'hard';
  }

  private identifyGrammarPattern(content: string): string {
    if (content.includes(' was ') || content.includes(' were ')) return 'past_tense';
    if (content.includes(' and ') || content.includes(' but ')) return 'compound';
    if (content.includes(' very ') || content.includes(' quite ')) return 'descriptive';
    return 'simple';
  }

  private mapToResponseDto(sentence: Sentence): SentenceResponseDto {
    return {
      id: sentence.id,
      content: sentence.content,
      orderIndex: sentence.orderIndex,
      difficultyLevel: sentence.difficultyLevel,
      grammarPattern: sentence.grammarPattern,
      bookId: sentence.bookId,
    };
  }
}