import { Injectable, BadRequestException, NotFoundException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { BlockGame } from '../../../database/entities/block-game.entity';
import { BlockGameSentence } from '../../../database/entities/block-game-sentence.entity';
import { BlockGameAttempt } from '../../../database/entities/block-game-attempt.entity';
import { TutorBlockGame } from '../../../database/entities/tutor-block-game.entity';
import { TutorWaterfallSet } from '../../../database/entities/tutor-waterfall-set.entity';
import { WaterfallSet } from '../../../database/entities/waterfall-set.entity';
import { User, UserType } from '../../../database/entities/user.entity';
import { BlockGameDetailDto, SubmitBlockGameDto, BlockGameAttemptResultDto } from '../../../database/models/block-game/block-game-student.dto';
import { BlockGameValidationService } from './block-game-validation.service';

@Injectable()
export class BlockGameService {
  private readonly logger = new Logger(BlockGameService.name);

  constructor(
    @InjectRepository(BlockGame)
    private readonly blockGameRepository: Repository<BlockGame>,
    @InjectRepository(BlockGameSentence)
    private readonly blockGameSentenceRepository: Repository<BlockGameSentence>,
    @InjectRepository(BlockGameAttempt)
    private readonly blockGameAttemptRepository: Repository<BlockGameAttempt>,
    @InjectRepository(TutorBlockGame)
    private readonly tutorBlockGameRepository: Repository<TutorBlockGame>,
    @InjectRepository(TutorWaterfallSet)
    private readonly tutorWaterfallSetRepository: Repository<TutorWaterfallSet>,
    @InjectRepository(WaterfallSet)
    private readonly adminWaterfallSetRepository: Repository<WaterfallSet>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly dataSource: DataSource,
    private readonly validationService: BlockGameValidationService,
  ) {}

  /**
   * Get a random active block game for student to play
   * Enhanced to prioritize tutor games over admin games
   */
  async getRandomBlockGame(studentId: string): Promise<BlockGameDetailDto & { source?: string }> {
    try {
      // Verify student exists
      const student = await this.userRepository.findOne({
        where: { id: studentId, type: UserType.STUDENT },
      });

      if (!student) {
        throw new NotFoundException('Student not found');
      }

      // Priority 1: Tutor games
      try {
        const tutorGame = await this.getTutorBlockGame(studentId);
        if (tutorGame) {
          return { ...tutorGame, source: 'tutor' };
        }
      } catch (error) {
        this.logger.warn(`No tutor games available for student ${studentId}: ${error.message}`);
      }

      // Priority 2: Admin games
      try {
        return this.getAdminBlockGame(studentId);
      } catch (error) {
        this.logger.warn(`No admin games available for student ${studentId}: ${error.message}`);
        throw new NotFoundException('No games available at the moment. Please try again later.');
      }
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      this.logger.error(`Failed to get random block game for student ${studentId}: ${error.message}`, error.stack);
      throw new BadRequestException('Could not retrieve a game at this time. Please try again later.');
    }
  }

  private async getTutorBlockGame(studentId: string): Promise<BlockGameDetailDto | null> {
    // Get all tutor games for this student
    const allTutorGames = await this.tutorBlockGameRepository.find({
      where: { studentId, isActive: true },
    });

    if (!allTutorGames || allTutorGames.length === 0) {
      return null;
    }

    // Filter games that have sentences
    const gamesWithSentences = [];
    for (const game of allTutorGames) {
      const sentenceCount = await this.blockGameSentenceRepository.count({
        where: { blockGameId: game.id },
      });
      if (sentenceCount > 0) {
        // Get play count for this student
        const playCount = await this.blockGameAttemptRepository.count({
          where: { blockGameId: game.id, studentId },
        });
        gamesWithSentences.push({ ...game, playCount });
      }
    }

    if (gamesWithSentences.length === 0) {
      return null;
    }

    // Find minimum play count
    const minPlayCount = Math.min(...gamesWithSentences.map(g => g.playCount));
    
    // Get games with minimum play count
    const leastPlayedGames = gamesWithSentences.filter(g => g.playCount === minPlayCount);
    
    // Randomly select from least played games
    const randomIndex = Math.floor(Math.random() * leastPlayedGames.length);
    const tutorGame = leastPlayedGames[randomIndex];

    // Get sentences for the tutor game
    const sentences = await this.blockGameSentenceRepository.find({
      where: { blockGameId: tutorGame.id },
      order: { sentenceOrder: 'ASC' },
    });

    // Map sentences to response format
    const sentenceResponses = sentences.map((sentence) => ({
      starting_part: sentence.startingPart,
      expanding_part: sentence.expandingPart,
      starting_gap_options: this.generateGapOptionsForSentence(sentence.startingPartAnswers || [], sentence.startingPartDistractors || [], 'starting'),
      expanding_gap_options: this.generateGapOptionsForSentence(sentence.expandingPartAnswers || [], sentence.expandingPartDistractors || [], 'expanding'),
      starting_part_answers: sentence.startingPartAnswers || [],
      expanding_part_answers: sentence.expandingPartAnswers || [],
      sentence_order: sentence.sentenceOrder,
    }));

    return {
      id: tutorGame.id,
      title: tutorGame.title,
      score: tutorGame.score,
      sentence_count: sentences.length,
      sentences: sentenceResponses,
    };
  }

  private async getAdminBlockGame(studentId: string): Promise<BlockGameDetailDto & { source: string }> {
    // First, get all active admin games
    const allGames = await this.blockGameRepository.find({
      where: { isActive: true },
    });

    if (!allGames || allGames.length === 0) {
      throw new NotFoundException('No admin games available');
    }

    // Filter games that have sentences
    const gamesWithSentences = [];
    for (const game of allGames) {
      const sentenceCount = await this.blockGameSentenceRepository.count({
        where: { blockGameId: game.id },
      });
      if (sentenceCount > 0) {
        // Get play count for this student
        const playCount = await this.blockGameAttemptRepository.count({
          where: { blockGameId: game.id, studentId },
        });
        gamesWithSentences.push({ ...game, playCount });
      }
    }

    if (gamesWithSentences.length === 0) {
      throw new NotFoundException('No admin games with sentences available');
    }

    // Find minimum play count
    const minPlayCount = Math.min(...gamesWithSentences.map(g => g.playCount));
    
    // Get games with minimum play count
    const leastPlayedGames = gamesWithSentences.filter(g => g.playCount === minPlayCount);
    
    // Randomly select from least played games
    const randomIndex = Math.floor(Math.random() * leastPlayedGames.length);
    const blockGame = leastPlayedGames[randomIndex];



    // Get sentences
    const sentences = await this.blockGameSentenceRepository.find({
      where: { blockGameId: blockGame.id },
      order: { sentenceOrder: 'ASC' },
    });

    // Map sentences to response format with gap options per sentence
    const sentenceResponses = sentences.map((sentence) => ({
      starting_part: sentence.startingPart,
      expanding_part: sentence.expandingPart,
      starting_gap_options: this.generateGapOptionsForSentence(sentence.startingPartAnswers || [], sentence.startingPartDistractors || [], 'starting'),
      expanding_gap_options: this.generateGapOptionsForSentence(sentence.expandingPartAnswers || [], sentence.expandingPartDistractors || [], 'expanding'),
      starting_part_answers: sentence.startingPartAnswers || [],
      expanding_part_answers: sentence.expandingPartAnswers || [],
      sentence_order: sentence.sentenceOrder,
    }));

    return {
      id: blockGame.id,
      title: blockGame.title,
      score: blockGame.score,
      sentence_count: sentences.length,
      sentences: sentenceResponses,
      source: 'admin',
    };
  }

  /**
   * Get a block game for playing with randomized word blocks
   */
  async getBlockGameForPlay(gameId: string, studentId: string): Promise<BlockGameDetailDto> {
    try {
      // Verify student exists
      const student = await this.userRepository.findOne({
        where: { id: studentId, type: UserType.STUDENT },
      });

      if (!student) {
        throw new NotFoundException('Student not found');
      }

      // Check if it's a tutor game first
      const tutorGame = await this.tutorBlockGameRepository.findOne({
        where: { id: gameId, isActive: true },
      });

      if (tutorGame) {
        // Get sentences for tutor game
        const sentences = await this.blockGameSentenceRepository.find({
          where: { blockGameId: gameId },
          order: { sentenceOrder: 'ASC' },
        });

        if (!sentences || sentences.length === 0) {
          throw new NotFoundException('Game not found or not available');
        }

        const sentenceResponses = sentences.map((sentence) => ({
          starting_part: sentence.startingPart,
          expanding_part: sentence.expandingPart,
          starting_gap_options: this.generateGapOptionsForSentence(sentence.startingPartAnswers || [], sentence.startingPartDistractors || [], 'starting'),
          expanding_gap_options: this.generateGapOptionsForSentence(sentence.expandingPartAnswers || [], sentence.expandingPartDistractors || [], 'expanding'),
          starting_part_answers: sentence.startingPartAnswers || [],
          expanding_part_answers: sentence.expandingPartAnswers || [],
          sentence_order: sentence.sentenceOrder,
        }));

        return {
          id: tutorGame.id,
          title: tutorGame.title,
          score: tutorGame.score,
          sentence_count: sentences.length,
          sentences: sentenceResponses,
        };
      }

      // Check admin game
      const blockGame = await this.blockGameRepository.findOne({
        where: { id: gameId, isActive: true },
      });

      if (!blockGame) {
        throw new NotFoundException('Game not found or not available');
      }

      // Get sentences for admin game
      const sentences = await this.blockGameSentenceRepository.find({
        where: { blockGameId: gameId },
        order: { sentenceOrder: 'ASC' },
      });

      if (!sentences || sentences.length === 0) {
        throw new NotFoundException('Game not found or not available');
      }

      // Map sentences to response format with gap options per sentence
      const sentenceResponses = sentences.map((sentence) => ({
        starting_part: sentence.startingPart,
        expanding_part: sentence.expandingPart,
        starting_gap_options: this.generateGapOptionsForSentence(sentence.startingPartAnswers || [], sentence.startingPartDistractors || [], 'starting'),
        expanding_gap_options: this.generateGapOptionsForSentence(sentence.expandingPartAnswers || [], sentence.expandingPartDistractors || [], 'expanding'),
        starting_part_answers: sentence.startingPartAnswers || [],
        expanding_part_answers: sentence.expandingPartAnswers || [],
        sentence_order: sentence.sentenceOrder,
      }));

      return {
        id: blockGame.id,
        title: blockGame.title,
        score: blockGame.score,
        sentence_count: sentences.length,
        sentences: sentenceResponses,
      };
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      this.logger.error(`Failed to get block game ${gameId} for student ${studentId}: ${error.message}`, error.stack);
      throw new BadRequestException('Could not retrieve game at this time. Please try again later.');
    }
  }

  /**
   * Submit a block game attempt and calculate score
   */
  async submitAttempt(studentId: string, dto: SubmitBlockGameDto): Promise<BlockGameAttemptResultDto> {
    try {
      // Verify student exists
      const student = await this.userRepository.findOne({
        where: { id: studentId, type: UserType.STUDENT },
      });

      if (!student) {
        throw new NotFoundException('Student not found');
      }

      // Validate input
      if (!dto.sentence_constructions || dto.sentence_constructions.length === 0) {
        throw new BadRequestException('At least one sentence construction must be provided');
      }

      // Validate each sentence construction (focus on gap answers)
      for (let i = 0; i < dto.sentence_constructions.length; i++) {
        const construction = dto.sentence_constructions[i];
        
        this.logger.debug(`Validating construction ${i + 1}:`, {
          starting_gap_answers: construction.starting_gap_answers,
          expanding_gap_answers: construction.expanding_gap_answers,
          sentence_order: construction.sentence_order
        });
        
        // Validate gap answers are provided
        if (!construction.starting_gap_answers || !Array.isArray(construction.starting_gap_answers)) {
          throw new BadRequestException(`Starting gap answers are required for construction ${i + 1}`);
        }
        if (!construction.expanding_gap_answers || !Array.isArray(construction.expanding_gap_answers)) {
          throw new BadRequestException(`Expanding gap answers are required for construction ${i + 1}`);
        }
        
        // Validate sentence order
        if (!construction.sentence_order || construction.sentence_order < 1) {
          throw new BadRequestException(`Valid sentence order is required for construction ${i + 1}`);
        }
      }

      // Check if it's a tutor game first (must also belong to this student)
      const tutorGame = await this.tutorBlockGameRepository.findOne({
        where: { id: dto.block_game_id, studentId, isActive: true },
      });

      let blockGame = null;
      if (tutorGame) {
        blockGame = tutorGame;
      } else {
        // Check admin game
        const adminGame = await this.blockGameRepository.findOne({
          where: { id: dto.block_game_id, isActive: true },
        });
        if (adminGame) {
          blockGame = adminGame;
        }
      }

      if (!blockGame) {
        throw new NotFoundException('Game not found or not available');
      }

      // Get sentences separately
      const sentences = await this.blockGameSentenceRepository.find({
        where: { blockGameId: dto.block_game_id },
        order: { sentenceOrder: 'ASC' },
      });

      this.logger.debug(`Found sentences for game ${dto.block_game_id}:`, {
        count: sentences.length,
        sentences: sentences.map(s => ({
          id: s.id,
          order: s.sentenceOrder,
          startingAnswers: s.startingPartAnswers,
          expandingAnswers: s.expandingPartAnswers
        }))
      });

      if (!sentences || sentences.length === 0) {
        throw new BadRequestException('This game has no sentences available.');
      }

      this.logger.debug(`Found ${sentences.length} sentences for game ${dto.block_game_id}`);
      this.logger.debug(`Received ${dto.sentence_constructions.length} constructions from student`);
      
      // Validate that the number of constructions matches the number of sentences
      if (dto.sentence_constructions.length !== sentences.length) {
        throw new BadRequestException(
          `Expected ${sentences.length} sentence constructions, but received ${dto.sentence_constructions.length}`
        );
      }

      const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Sort sentences by order for consistent comparison
      const correctSentences = sentences.sort((a, b) => a.sentenceOrder - b.sentenceOrder);

      // Calculate score and detailed results
      this.logger.debug('Starting score calculation...');
      const { score, sentenceResults } = this.calculateScore(dto.sentence_constructions, correctSentences, blockGame.score);
      this.logger.debug(`Score calculated: ${score}/${blockGame.score}`);

      // Store sentence constructions for the attempt
      const sentenceConstructions = {
        startingSentences: dto.sentence_constructions.map((sc) => sc.starting_sentence),
        expandingSentences: dto.sentence_constructions.map((sc) => sc.expanding_sentence),
        completedSentences: sentenceResults.map((result) => ({
          starting: result.student_starting,
          expanding: result.student_expanding,
          isCorrect: result.is_correct,
          sentenceOrder: result.sentence_order,
        })),
      };

      // Create attempt record
      const attempt = queryRunner.manager.create(BlockGameAttempt, {
        studentId,
        blockGameId: dto.block_game_id,
        score,
        totalScore: blockGame.score,
        sentenceConstructions,
        submittedAt: new Date(),
      });

      await queryRunner.manager.save(attempt);

      await queryRunner.commitTransaction();

      // Return detailed results
      const percentage = Math.round((score / blockGame.score) * 100);

      return {
        score,
        total_score: blockGame.score,
        percentage,
        sentence_results: sentenceResults,
        submitted_at: attempt.submittedAt,
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Transaction failed for student ${studentId}: ${error.message}`, error.stack);

      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }

      throw new BadRequestException(`Submission failed: ${error.message}`);
    } finally {
      await queryRunner.release();
    }
    } catch (error) {
      this.logger.error(`Submit attempt failed for student ${studentId}: ${error.message}`, error.stack);

      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }

      throw new BadRequestException(`Could not process submission: ${error.message}`);
    }
  }

  /**
   * Generate consistent gap options for a specific sentence part
   */
  private generateGapOptionsForSentence(correctAnswers: string[], distractors: string[], partType: 'starting' | 'expanding'): string[] {
    const defaultDistractors = partType === 'starting' 
      ? ['the', 'and', 'is', 'are', 'was', 'were']
      : ['have', 'has', 'will', 'can', 'could', 'should'];
    
    // Use provided distractors or fall back to default ones
    const finalDistractors = distractors && distractors.length > 0 
      ? distractors 
      : defaultDistractors.slice(0, 4);
    
    // Combine correct answers with distractors in consistent order
    const options = [...correctAnswers, ...finalDistractors];
    return options.sort(); // Sort alphabetically for consistent order
  }

  /**
   * Calculate score based on gap answers only (simplified scoring)
   */
  private calculateScore(studentConstructions: any[], correctSentences: BlockGameSentence[], totalScore: number) {
    this.logger.debug(`Calculating score for ${studentConstructions.length} constructions against ${correctSentences.length} sentences`);
    
    if (correctSentences.length === 0) {
      throw new BadRequestException('No sentences available for scoring');
    }
    
    const pointsPerSentence = totalScore / correctSentences.length;
    let correctCount = 0;
    const sentenceResults: any[] = [];

    // Create a map of correct sentences by order
    const correctSentenceMap = correctSentences.reduce((map, sentence) => {
      map[sentence.sentenceOrder] = sentence;
      return map;
    }, {});

    // Evaluate each student construction
    studentConstructions.forEach((construction, index) => {
      const sentenceOrder = construction.sentence_order || (index + 1);
      const correctSentence = correctSentenceMap[sentenceOrder];

      this.logger.debug(`Evaluating construction ${index + 1} (order ${sentenceOrder}):`, {
        studentStartingGaps: construction.starting_gap_answers,
        studentExpandingGaps: construction.expanding_gap_answers,
        correctStartingAnswers: correctSentence?.startingPartAnswers,
        correctExpandingAnswers: correctSentence?.expandingPartAnswers
      });

      if (correctSentence) {
        // Check gap answers only (no sentence structure validation)
        const startingGapsCorrect = this.validationService.checkGapAnswers(
          construction.starting_gap_answers || [],
          correctSentence.startingPartAnswers || []
        );
        const expandingGapsCorrect = this.validationService.checkGapAnswers(
          construction.expanding_gap_answers || [],
          correctSentence.expandingPartAnswers || []
        );

        // Sentence is correct if all gaps are filled correctly
        const isCorrect = startingGapsCorrect && expandingGapsCorrect;

        this.logger.debug(`Sentence ${sentenceOrder} evaluation:`, {
          startingGapsCorrect,
          expandingGapsCorrect,
          isCorrect
        });

        if (isCorrect) {
          correctCount++;
        }

        sentenceResults.push({
          sentence_order: sentenceOrder,
          student_starting: construction.starting_sentence?.trim() || '',
          student_expanding: construction.expanding_sentence?.trim() || '',
          student_starting_gaps: construction.starting_gap_answers || [],
          student_expanding_gaps: construction.expanding_gap_answers || [],
          correct_starting: correctSentence.startingPart,
          correct_expanding: correctSentence.expandingPart,
          correct_starting_answers: correctSentence.startingPartAnswers || [],
          correct_expanding_answers: correctSentence.expandingPartAnswers || [],
          is_correct: isCorrect,
          points_earned: isCorrect ? pointsPerSentence : 0,
        });
      }
    });

    const finalScore = Math.round(correctCount * pointsPerSentence);

    return {
      score: finalScore,
      sentenceResults: sentenceResults.sort((a, b) => a.sentence_order - b.sentence_order),
    };
  }


}
