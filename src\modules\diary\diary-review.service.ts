import { Injectable, NotFoundException, ConflictException, BadRequestException, ForbiddenException, Logger, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, In, Not } from 'typeorm';
import { DiaryEntry, DiaryEntryStatus } from '../../database/entities/diary-entry.entity';
import { DiaryFeedback } from '../../database/entities/diary-feedback.entity';
import { DiaryCorrection } from '../../database/entities/diary-correction.entity';
import { DiaryEntryHistory } from '../../database/entities/diary-entry-history.entity';
import { User } from '../../database/entities/user.entity';
import { PlanFeature, FeatureType } from '../../database/entities/plan-feature.entity';
import { CreateDiaryFeedbackDto, DiaryFeedbackResponseDto } from '../../database/models/diary.dto';
import { CreateDiaryCorrectionDto, DiaryCorrectionResponseDto } from '../../database/models/diary-correction.dto';
import { FeedbackOnlyDto } from '../../database/models/feedback-only.dto';
import { TutorMatchingService } from '../tutor-matching/tutor-matching.service';
import { AsyncNotificationHelperService } from '../notification/async-notification-helper.service';
import { NotificationType } from '../../database/entities/notification.entity';
import { RelatedEntityType } from '../../common/enums/related-entity-type.enum';
import { DeeplinkService, DeeplinkType } from '../../common/utils/deeplink.service';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';
import { getCurrentUTCDate } from '../../common/utils/date-utils';
import { PendingReviewEntryDto } from '../../database/models/diary.dto';
import { StudentTutorMapping, MappingStatus } from '../../database/entities/student-tutor-mapping.entity';

@Injectable()
export class DiaryReviewService {
  private readonly logger = new Logger(DiaryReviewService.name);
  constructor(
    @InjectRepository(DiaryEntry)
    private readonly diaryEntryRepository: Repository<DiaryEntry>,
    @InjectRepository(DiaryFeedback)
    private readonly diaryFeedbackRepository: Repository<DiaryFeedback>,
    @InjectRepository(DiaryCorrection)
    private readonly diaryCorrectionRepository: Repository<DiaryCorrection>,
    @InjectRepository(DiaryEntryHistory)
    private readonly diaryEntryHistoryRepository: Repository<DiaryEntryHistory>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(PlanFeature)
    private readonly planFeatureRepository: Repository<PlanFeature>,
    @InjectRepository(StudentTutorMapping)
    private readonly studentTutorMappingRepository: Repository<StudentTutorMapping>,
    private readonly dataSource: DataSource,
    @Inject(forwardRef(() => TutorMatchingService))
    private readonly tutorMatchingService: TutorMatchingService,
    private readonly asyncNotificationHelper: AsyncNotificationHelperService,
    private readonly deeplinkService: DeeplinkService,
  ) {}

  /**
   * Get the plan feature ID for the diary module
   * @returns The plan feature ID for the diary module
   */
  private async getDiaryModuleFeatureId(): Promise<string> {
    try {
      // Find the plan feature for HEC_USER_DIARY
      const diaryFeature = await this.planFeatureRepository.findOne({
        where: { type: FeatureType.HEC_USER_DIARY },
      });

      if (!diaryFeature) {
        this.logger.error('Diary module feature (HEC_USER_DIARY) not found in the database');
        throw new NotFoundException('Diary module feature not found');
      }

      this.logger.log(`Found diary module feature with ID: ${diaryFeature.id}`);
      return diaryFeature.id;
    } catch (error) {
      this.logger.error(`Error getting diary module feature ID: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Submit feedback only for a diary entry
   * @param entryId The ID of the diary entry
   * @param tutorId The ID of the tutor submitting the feedback
   * @param feedbackOnlyDto The feedback text only
   * @returns The created feedback
   */
  async submitFeedbackOnly(entryId: string, tutorId: string, feedbackOnlyDto: FeedbackOnlyDto): Promise<DiaryFeedbackResponseDto> {
    try {
      // Check if the user is a tutor
      const user = await this.userRepository.findOne({
        where: { id: tutorId },
        relations: ['userRoles', 'userRoles.role'],
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${tutorId} not found`);
      }

      // Check if the user has the tutor role
      const isTutor = user.userRoles.some((userRole) => userRole.role.name === 'tutor');
      if (!isTutor) {
        throw new ForbiddenException('Only tutors can provide feedback on diary entries');
      }

      // Get the diary entry
      const entry = await this.diaryEntryRepository.findOne({
        where: { id: entryId },
        relations: ['diary', 'diary.user'],
      });

      if (!entry) {
        throw new NotFoundException(`Diary entry with ID ${entryId} not found`);
      }

      // Check if the entry is in the correct state
      if (entry.status === DiaryEntryStatus.NEW) {
        throw new BadRequestException('Cannot provide feedback on a diary entry that has not been submitted');
      }

      // Create feedback with default values for other fields
      const feedback = this.diaryFeedbackRepository.create({
        diaryEntryId: entryId,
        tutorId: tutorId,
        feedback: feedbackOnlyDto.feedback,
        rating: 3, // Default middle rating
        award: null, // No award by default
      });

      const savedFeedback = await this.diaryFeedbackRepository.save(feedback);

      // Get tutor details
      const tutor = await this.userRepository.findOne({
        where: { id: tutorId },
      });

      // Update last activity date in tutor matching
      if (entry.diary?.user) {
        try {
          // Get the diary module feature ID
          const diaryModuleId = await this.getDiaryModuleFeatureId();

          // Update last activity date using the correct module ID
          await this.tutorMatchingService.updateLastActivityDate(entry.diary.userId, diaryModuleId);

          this.logger.log(`Updated last activity date for student ${entry.diary.userId} and diary module ${diaryModuleId}`);
        } catch (error) {
          this.logger.warn(`Failed to update last activity date: ${error.message}`);
        }
      }

      // Send notification to student
      try {
        // Create HTML content for email notification
        const htmlContent = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
            <div style="text-align: center; margin-bottom: 20px;">
              <h2 style="color: #333;">New Feedback on Your Diary Entry</h2>
            </div>
            <div style="margin-bottom: 20px;">
              <p>Hello ${entry.diary.user?.name || 'there'},</p>
              <p>${tutor.name} has provided feedback on your diary entry.</p>
              <p>Feedback: "${feedbackOnlyDto.feedback}"</p>
              <p>Please log in to the system to view the complete feedback.</p>
            </div>
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
              <p>This is an automated message from the HEC system.</p>
              <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
            </div>
          </div>
        `;

        await this.asyncNotificationHelper.notifyAsync(entry.diary.userId, NotificationType.DIARY_FEEDBACK, 'New Feedback on Your Diary Entry', `${tutor.name} has provided feedback on your diary entry.`, {
          relatedEntityId: entryId,
          relatedEntityType: RelatedEntityType.DIARY_ENTRY,
          htmlContent: htmlContent,
          // Use channel control parameters instead of channels array
          sendEmail: true,
          sendPush: true,
          sendInApp: true,
          sendRealtime: false,
        });
      } catch (error) {
        this.logger.warn(`Failed to send notification: ${error.message}`);
      }

      return {
        id: savedFeedback.id,
        tutorId: savedFeedback.tutorId,
        tutorName: tutor.name,
        feedback: savedFeedback.feedback,
        award: savedFeedback.award,
        createdAt: savedFeedback.createdAt,
      };
    } catch (error) {
      this.logger.error(`Error submitting feedback: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Submit feedback for a diary entry
   * @param entryId The ID of the diary entry
   * @param tutorId The ID of the tutor submitting the feedback
   * @param createDiaryFeedbackDto The feedback data
   * @returns The created feedback
   */
  async submitFeedback(entryId: string, tutorId: string, createDiaryFeedbackDto: CreateDiaryFeedbackDto): Promise<DiaryFeedbackResponseDto> {
    try {
      // Optimized combined tutor validation and entry loading
      const entry = await this.diaryEntryRepository
        .createQueryBuilder('entry')
        .leftJoinAndSelect('entry.diary', 'diary')
        .leftJoinAndSelect('diary.user', 'user')
        .innerJoin('student_tutor_mapping', 'stm', 'stm.studentId = diary.userId AND stm.tutorId = :tutorId AND stm.status = :status')
        .innerJoin('user_role', 'ur', 'ur.userId = :tutorId')
        .innerJoin('role', 'r', 'r.id = ur.roleId AND r.name = :roleName')
        .where('entry.id = :entryId', { entryId })
        .setParameters({ tutorId, status: 'active', roleName: 'tutor' })
        .getOne();

      if (!entry) {
        throw new ForbiddenException('Diary entry not found or you do not have access to provide feedback on this entry');
      }

      // Check if the entry is in the correct state
      if (entry.status === DiaryEntryStatus.NEW) {
        throw new BadRequestException('Cannot provide feedback on a diary entry that has not been submitted');
      }

      // Create feedback
      const feedback = this.diaryFeedbackRepository.create({
        diaryEntryId: entryId,
        tutorId: tutorId,
        feedback: createDiaryFeedbackDto.feedback,
        rating: createDiaryFeedbackDto.rating || 0,
        award: createDiaryFeedbackDto.award,
      });

      const savedFeedback = await this.diaryFeedbackRepository.save(feedback);

      // Get tutor details
      const tutor = await this.userRepository.findOne({
        where: { id: tutorId },
      });

      // Update last activity date in tutor matching
      if (entry.diary?.user) {
        try {
          // Get the diary module feature ID
          const diaryModuleId = await this.getDiaryModuleFeatureId();

          // Update last activity date using the correct module ID
          await this.tutorMatchingService.updateLastActivityDate(entry.diary.userId, diaryModuleId);

          this.logger.log(`Updated last activity date for student ${entry.diary.userId} and diary module ${diaryModuleId}`);
        } catch (error) {
          this.logger.warn(`Failed to update last activity date: ${error.message}`);
        }
      }

      // Send notification to student
      try {
        // Generate a deep link for the student to view the diary entry
        const viewLink = this.deeplinkService.getWebLink(DeeplinkType.DIARY_ENTRY, {
          id: entryId,
        });

        // Create HTML content with the view link
        const htmlContent = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
            <div style="text-align: center; margin-bottom: 20px;">
              <h2 style="color: #333;">New Feedback on Your Diary Entry</h2>
            </div>
            <div style="margin-bottom: 20px;">
              <p>Hello ${entry.diary.user?.name || 'there'},</p>
              <p>${tutor.name} has provided feedback on your diary entry.</p>
              <p>Feedback: "${createDiaryFeedbackDto.feedback}"</p>
              <p>Click the button below to view the complete feedback.</p>
              <div style="text-align: center; margin: 30px 0;">
                <a href="${viewLink}" style="background-color: #4CAF50; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">View Feedback</a>
              </div>
            </div>
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
              <p>This is an automated message from the HEC system.</p>
              <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
            </div>
          </div>
        `;

        // Send notification asynchronously to avoid blocking the response
        this.asyncNotificationHelper.notifyAsync(
          entry.diary.userId,
          NotificationType.DIARY_FEEDBACK,
          'New Feedback on Your Diary Entry',
          `${tutor.name} has provided feedback on your diary entry.`,
          {
            relatedEntityId: entryId,
            relatedEntityType: RelatedEntityType.DIARY_ENTRY,
            htmlContent: htmlContent,
            sendEmail: true,
            sendPush: true,
            sendInApp: true,
            sendRealtime: false,
          },
          {
            submissionId: entryId,
            entryType: 'diary_entry',
            priority: 2, // Medium priority for feedback
          }
        ).catch(error => {
          this.logger.error(`Failed to send async notification: ${error?.message || 'Unknown error'}`, error?.stack);
        });
      } catch (error) {
        this.logger.warn(`Failed to prepare notification: ${error.message}`);
      }

      return {
        id: savedFeedback.id,
        tutorId: savedFeedback.tutorId,
        tutorName: tutor.name,
        feedback: savedFeedback.feedback,
        award: savedFeedback.award,
        createdAt: savedFeedback.createdAt,
      };
    } catch (error) {
      this.logger.error(`Error submitting feedback: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Submit a correction for a diary entry
   * @param entryId The ID of the diary entry
   * @param tutorId The ID of the tutor submitting the correction
   * @param createDiaryCorrectionDto The correction data
   * @returns The created correction
   */
  async submitCorrection(entryId: string, tutorId: string, createDiaryCorrectionDto: CreateDiaryCorrectionDto): Promise<DiaryCorrectionResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();

    try {
      await queryRunner.startTransaction();
      // Optimized tutor role check using query builder for better performance
      const tutorCheck = await this.userRepository
        .createQueryBuilder('user')
        .innerJoin('user.userRoles', 'userRole')
        .innerJoin('userRole.role', 'role')
        .where('user.id = :tutorId', { tutorId })
        .andWhere('role.name = :roleName', { roleName: 'tutor' })
        .select(['user.id', 'user.name'])
        .getOne();

      if (!tutorCheck) {
        throw new ForbiddenException('Only tutors can provide corrections on diary entries');
      }

      // Optimized entry loading with tutor access validation
      const entry = await this.diaryEntryRepository
        .createQueryBuilder('entry')
        .leftJoinAndSelect('entry.diary', 'diary')
        .leftJoinAndSelect('diary.user', 'user')
        .leftJoinAndSelect('entry.correction', 'correction')
        .innerJoin('student_tutor_mapping', 'stm', 'stm.studentId = diary.userId AND stm.tutorId = :tutorId AND stm.status = :status')
        .where('entry.id = :entryId', { entryId })
        .setParameters({ tutorId, status: 'active' })
        .getOne();

      if (!entry) {
        throw new NotFoundException(`Diary entry with ID ${entryId} not found`);
      }

      // Check if the entry is in the correct state
      if (entry.status === DiaryEntryStatus.NEW) {
        throw new BadRequestException('Cannot provide correction on a diary entry that has not been submitted');
      }

      // Allow multiple corrections - tutors can update corrections unlimited times

      // Transaction already started above

      try {
        // REMOVED: Automatic history creation during review
        // History should only be created when student submits new versions, not during tutor review

        // Store resubmission tracking fields before update to preserve them
        const resubmissionType = entry.resubmissionType;
        const isResubmission = entry.isResubmission;
        const previousReviewCount = entry.previousReviewCount;
        const previousConfirmationCount = entry.previousConfirmationCount;
        const submittedVersionCount = entry.submittedVersionCount;

        // First update the entry status to REVIEWED
        entry.status = DiaryEntryStatus.REVIEWED;
        entry.evaluatedAt = new Date();
        entry.evaluatedBy = tutorId;
        entry.score = createDiaryCorrectionDto.score;

        // NEW REQUIREMENT: Enable subsequent submissions after review
        entry.lastReviewedAt = new Date();
        entry.canSubmitNewVersion = true;

        // PRESERVE resubmission tracking fields
        entry.resubmissionType = resubmissionType;
        entry.isResubmission = isResubmission;
        entry.previousReviewCount = previousReviewCount;
        entry.previousConfirmationCount = previousConfirmationCount;
        entry.submittedVersionCount = submittedVersionCount;

        // Save the entry first to ensure it exists
        const savedEntry = await queryRunner.manager.save(entry);

        // NEW UNIFIED LOGIC: Check if already reviewed (can only review once)
        if (entry.reviewedBy && entry.score !== null && entry.score !== undefined) {
          throw new BadRequestException('Entry has already been reviewed and scored. Only feedback can be added after review.');
        }

        // Check if correction already exists
        let correction = entry.correction;
        if (correction) {
          // Update existing correction
          correction.tutorId = tutorId;
          correction.correctionText = createDiaryCorrectionDto.correctionText || '';
          correction.score = createDiaryCorrectionDto.score;
          correction.comments = createDiaryCorrectionDto.comments;
        } else {
          // Create new correction
          correction = new DiaryCorrection();
          correction.diaryEntryId = savedEntry.id;
          correction.tutorId = tutorId;
          correction.correctionText = createDiaryCorrectionDto.correctionText || '';
          correction.score = createDiaryCorrectionDto.score;
          correction.comments = createDiaryCorrectionDto.comments;
        }

        // Save the correction
        const savedCorrection = await queryRunner.manager.save(correction);

        // Set original reviewed version if not already set
        await this.setOriginalReviewedVersion(savedEntry, queryRunner);

        // Commit the transaction
        await queryRunner.commitTransaction();

        // Get tutor details
        const tutor = await this.userRepository.findOne({
          where: { id: tutorId },
        });

        // Update last activity date in tutor matching
        if (entry.diary?.user) {
          try {
            // Get the diary module feature ID
            const diaryModuleId = await this.getDiaryModuleFeatureId();

            // Update last activity date using the correct module ID
            await this.tutorMatchingService.updateLastActivityDate(entry.diary.userId, diaryModuleId);
          } catch (error) {
            this.logger.warn(`Failed to update last activity date: ${error.message}`);
          }
        }

        // Send notification to student
        try {
          // Generate a deep link for the student to view the diary entry
          const viewLink = this.deeplinkService.getWebLink(DeeplinkType.DIARY_ENTRY, {
            id: entryId,
          });

          // Create HTML content with the view link
          const htmlContent = `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
              <div style="text-align: center; margin-bottom: 20px;">
                <h2 style="color: #333;">Your Diary Entry Has Been Reviewed</h2>
              </div>
              <div style="margin-bottom: 20px;">
                <p>Hello ${entry.diary.user?.name || 'there'},</p>
                <p>${tutor.name} has reviewed your diary entry and provided corrections.</p>
                <p>Score: ${createDiaryCorrectionDto.score}/10</p>
                <p>Click the button below to view the complete review and corrections.</p>
                <div style="text-align: center; margin: 30px 0;">
                  <a href="${viewLink}" style="background-color: #4CAF50; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">View Review</a>
                </div>
              </div>
              <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
                <p>This is an automated message from the HEC system.</p>
                <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
              </div>
            </div>
          `;

          // Send notification asynchronously to avoid blocking the response
          this.asyncNotificationHelper.notifyAsync(
            entry.diary.userId,
            NotificationType.DIARY_REVIEW,
            'Your Diary Entry Has Been Reviewed',
            `${tutor.name} has reviewed your diary entry and provided corrections.`,
            {
              relatedEntityId: entryId,
              relatedEntityType: RelatedEntityType.DIARY_ENTRY,
              htmlContent: htmlContent,
              sendEmail: true,
              sendPush: true,
              sendInApp: true,
              sendRealtime: false,
            },
            {
              submissionId: entryId,
              entryType: 'diary_entry',
              priority: 1, // High priority for review completion
            }
          ).catch(error => {
            this.logger.error(`Failed to send async notification: ${error?.message || 'Unknown error'}`, error?.stack);
          });
        } catch (error) {
          this.logger.warn(`Failed to prepare notification: ${error.message}`);
        }

        return {
          id: savedCorrection.id,
          diaryEntryId: savedCorrection.diaryEntryId,
          tutorId: savedCorrection.tutorId,
          tutorName: tutor.name,
          correctionText: savedCorrection.correctionText,
          score: savedCorrection.score,
          comments: savedCorrection.comments,
          createdAt: savedCorrection.createdAt,
          updatedAt: savedCorrection.updatedAt,
        };
      } catch (error) {
        // Rollback the transaction in case of error
        await queryRunner.rollbackTransaction();
        this.logger.error(`Error submitting correction: ${error.message}`, error.stack);
        throw error;
      } finally {
        // Release the query runner
        await queryRunner.release();
      }
    } catch (error) {
      this.logger.error(`Error submitting correction: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Add unlimited feedback to diary entry - NEW UNIFIED LOGIC
   * @param entryId The ID of the diary entry
   * @param tutorId The ID of the tutor adding feedback
   * @param feedbackText The feedback text
   * @returns The created feedback
   */
  async addDiaryFeedback(entryId: string, tutorId: string, feedbackText: string): Promise<DiaryFeedback> {
    try {
      // Check if the user is a tutor
      const user = await this.userRepository.findOne({
        where: { id: tutorId },
        relations: ['userRoles', 'userRoles.role'],
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${tutorId} not found`);
      }

      const isTutor = user.userRoles.some((userRole) => userRole.role.name === 'tutor');
      if (!isTutor) {
        throw new ForbiddenException('Only tutors can add feedback');
      }

      // Get the diary entry
      const entry = await this.diaryEntryRepository.findOne({
        where: { id: entryId },
        relations: ['diary', 'diary.user'],
      });

      if (!entry) {
        throw new NotFoundException(`Diary entry with ID ${entryId} not found`);
      }

      if (entry.status === DiaryEntryStatus.NEW) {
        throw new BadRequestException('Entry must be submitted to receive feedback');
      }

      // Create feedback
      const feedback = this.diaryFeedbackRepository.create({
        diaryEntryId: entryId,
        tutorId: tutorId,
        feedback: feedbackText,
        createdBy: tutorId,
        updatedBy: tutorId,
      });

      const savedFeedback = await this.diaryFeedbackRepository.save(feedback);

      // Send feedback notification
      try {
        await this.sendFeedbackNotification(entry, savedFeedback);
      } catch (notificationError) {
        this.logger.error(`Failed to send feedback notification: ${notificationError.message}`, notificationError.stack);
      }

      return savedFeedback;
    } catch (error) {
      this.logger.error(`Error adding diary feedback: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Send feedback notification to student
   */
  private async sendFeedbackNotification(entry: DiaryEntry, feedback: DiaryFeedback): Promise<void> {
    try {
      // Get tutor details
      const tutor = await this.userRepository.findOne({
        where: { id: feedback.tutorId },
      });

      if (!tutor || !entry.diary?.user) {
        this.logger.warn('Missing tutor or student information for feedback notification');
        return;
      }

      // Generate a deep link for the student to view the diary entry
      const viewLink = this.deeplinkService.getWebLink(DeeplinkType.DIARY_ENTRY, {
        id: entry.id,
      });

      // Create HTML content with the view link
      const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
          <div style="text-align: center; margin-bottom: 20px;">
            <h2 style="color: #333;">New Feedback on Your Diary Entry</h2>
          </div>
          <div style="margin-bottom: 20px;">
            <p>Hello ${entry.diary.user.name || 'there'},</p>
            <p>${tutor.name} has provided feedback on your diary entry.</p>
            <p>Feedback: "${feedback.feedback}"</p>
            <p>Click the button below to view the complete feedback.</p>
            <div style="text-align: center; margin: 30px 0;">
              <a href="${viewLink}" style="background-color: #4CAF50; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">View Feedback</a>
            </div>
          </div>
          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
            <p>This is an automated message from the HEC system.</p>
            <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
          </div>
        </div>
      `;

      // Send notification asynchronously to avoid blocking the response
      this.asyncNotificationHelper.notifyAsync(
        entry.diary.userId,
        NotificationType.DIARY_FEEDBACK,
        'New Feedback on Your Diary Entry',
        `${tutor.name} has provided feedback on your diary entry.`,
        {
          relatedEntityId: entry.id,
          relatedEntityType: RelatedEntityType.DIARY_ENTRY,
          sendEmail: true,
          sendPush: true,
          sendInApp: true,
          sendRealtime: false,
          htmlContent: htmlContent,
        },
        {
          submissionId: entry.id,
          entryType: 'diary_entry',
          priority: 2, // Medium priority for feedback
        }
      ).catch(error => {
        this.logger.error(`Failed to send async notification: ${error?.message || 'Unknown error'}`, error?.stack);
      });

      this.logger.log(`Queued feedback notification to student ${entry.diary.userId} for diary entry ${entry.id}`);
    } catch (error) {
      this.logger.error(`Failed to prepare feedback notification: ${error.message}`, error.stack);
      // Don't throw error to avoid breaking the feedback submission
    }
  }

  // REMOVED: confirmDiaryEntryReview method - confirm stage removed from lifecycle
  // Review submission is now the final state, no additional confirmation needed

  /**
   * Add a thanks message to a diary entry
   * @param entryId The ID of the diary entry
   * @param userId The ID of the user adding the thanks message
   * @param thanksMessage The thanks message
   * @returns The updated diary entry
   */
  async addThanksMessage(entryId: string, userId: string, thanksMessage: string): Promise<DiaryEntry> {
    try {
      // Get the diary entry
      const entry = await this.diaryEntryRepository.findOne({
        where: { id: entryId },
        relations: ['diary', 'diary.user'],
      });

      if (!entry) {
        throw new NotFoundException(`Diary entry with ID ${entryId} not found`);
      }

      // Check if the user owns the diary entry
      if (entry.diary.userId !== userId) {
        throw new ForbiddenException('You do not have permission to add a thanks message to this diary entry');
      }

      // Check if the entry is in the correct state
      if (entry.status !== DiaryEntryStatus.REVIEWED) {
        throw new BadRequestException(`Cannot add thanks message to a diary entry with status ${entry.status}`);
      }

      // Update the entry
      entry.thanksMessage = thanksMessage;

      // Save the entry
      return await this.diaryEntryRepository.save(entry);
    } catch (error) {
      this.logger.error(`Error adding thanks message: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get pending review entries for a tutor
   * @param tutorId ID of the tutor
   * @param paginationDto Pagination options
   * @returns Paginated list of pending review entries
   */
  async getPendingReviewEntries(tutorId?: string, paginationDto?: PaginationDto): Promise<PagedListDto<PendingReviewEntryDto>> {
    try {
      // Get the current date and time in UTC
      const now = getCurrentUTCDate();
      this.logger.log(`[getPendingReviewEntries] Current UTC time: ${now.toISOString()}`); // Get all active student assignments for this tutor using the repository
      const studentTutorMappings = await this.studentTutorMappingRepository
        .createQueryBuilder('mapping')
        .select('mapping.studentId')
        .where('mapping.tutorId = :tutorId', { tutorId })
        .andWhere('mapping.status = :mappingStatus', { mappingStatus: MappingStatus.ACTIVE })
        .getMany();

      const assignedStudentIds = studentTutorMappings.map((mapping) => mapping.studentId);

      // Get total count for pagination (only latest submissions per student per entry)
      const totalCountQuery = this.diaryEntryRepository
        .createQueryBuilder('entry')
        .innerJoin('entry.diary', 'diary')
        .where('diary.userId IN (:...assignedStudentIds)', { assignedStudentIds })
        .andWhere('entry.status = :status', { status: DiaryEntryStatus.SUBMIT })
        .andWhere('entry.isResubmission != :isResubmission', { isResubmission: true })
        .andWhere('entry.updatedAt = (' +
          'SELECT MAX(e2.updated_at) FROM diary_entry e2 ' +
          'INNER JOIN diary d2 ON e2.diary_id = d2.id ' +
          'WHERE d2.user_id = diary.user_id ' +
          'AND e2.entry_date = entry.entry_date ' +
          'AND e2.status = :status ' +
          'AND e2.is_resubmission != :isResubmission' +
        ')');
      
      const totalCount = await totalCountQuery.getCount();

      // Apply pagination if provided
      const options: any = {
        where: {
          diary: {
            userId: In(assignedStudentIds),
          },
          status: DiaryEntryStatus.SUBMIT,
          isResubmission: Not(true), // Exclude resubmissions
        },
        relations: ['diary', 'diary.user', 'feedbacks', 'feedbacks.tutor', 'settings', 'settings.settingsTemplate'],
        order: { updatedAt: 'ASC' }, // Oldest submissions first
      };

      // Extract filter parameters from paginationDto
      let moduleFilter: string = null;
      let studentNameFilter: string = null;
      let dateFromFilter: Date = null;
      let dateToFilter: Date = null;

      if (paginationDto) {
        const { page = 1, limit = 10, sortBy, sortDirection } = paginationDto;
        const skip = (page - 1) * limit;

        options.skip = skip;
        options.take = limit;

        if (sortBy && sortDirection) {
          options.order = { [sortBy]: sortDirection };
        }

        // Extract custom filter parameters if they exist
        if (paginationDto['moduleTitle']) {
          moduleFilter = paginationDto['moduleTitle'];
        }

        if (paginationDto['studentName']) {
          studentNameFilter = paginationDto['studentName'];
        }

        if (paginationDto['dateFrom']) {
          try {
            dateFromFilter = new Date(paginationDto['dateFrom']);
          } catch (e) {
            this.logger.warn(`Invalid dateFrom format: ${paginationDto['dateFrom']}`);
          }
        }

        if (paginationDto['dateTo']) {
          try {
            dateToFilter = new Date(paginationDto['dateTo']);
          } catch (e) {
            this.logger.warn(`Invalid dateTo format: ${paginationDto['dateTo']}`);
          }
        }
      }

      // Use query builder to get only latest submissions per student per entry
      const queryBuilder = this.diaryEntryRepository
        .createQueryBuilder('entry')
        .leftJoinAndSelect('entry.diary', 'diary')
        .leftJoinAndSelect('diary.user', 'user')
        .leftJoinAndSelect('entry.feedbacks', 'feedbacks')
        .leftJoinAndSelect('feedbacks.tutor', 'tutor')
        .leftJoinAndSelect('entry.settings', 'settings')
        .leftJoinAndSelect('settings.settingsTemplate', 'settingsTemplate')
        .where('diary.userId IN (:...assignedStudentIds)', { assignedStudentIds })
        .andWhere('entry.status = :status', { status: DiaryEntryStatus.SUBMIT })
        .andWhere('entry.isResubmission != :isResubmission', { isResubmission: true })
        .andWhere('entry.updatedAt = (' +
          'SELECT MAX(e2.updated_at) FROM diary_entry e2 ' +
          'INNER JOIN diary d2 ON e2.diary_id = d2.id ' +
          'WHERE d2.user_id = diary.user_id ' +
          'AND e2.entry_date = entry.entry_date ' +
          'AND e2.status = :status ' +
          'AND e2.is_resubmission != :isResubmission' +
        ')')
        .orderBy('entry.updatedAt', 'ASC');

      // Apply pagination
      if (options.skip) queryBuilder.skip(options.skip);
      if (options.take) queryBuilder.take(options.take);

      const allEntries = await queryBuilder.getMany();

      this.logger.log(`[getPendingReviewEntries] Found ${allEntries.length} total entries`);

      // Log the status distribution of entries
      const statusCounts = {
        [DiaryEntryStatus.NEW]: 0,
        [DiaryEntryStatus.SUBMIT]: 0,
        [DiaryEntryStatus.REVIEWED]: 0,

      };

      for (const entry of allEntries) {
        statusCounts[entry.status]++;
      }

      this.logger.log('[getPendingReviewEntries] Status distribution:', statusCounts);

      // Reset entries that have expired locks
      let resetCount = 0;
      for (const entry of allEntries) {
        if (entry.status === DiaryEntryStatus.SUBMIT && entry.reviewExpiryTime && entry.reviewExpiryTime < now) {
          this.logger.log(`[getPendingReviewEntries] Resetting expired lock for entry ${entry.id}. Expiry: ${entry.reviewExpiryTime?.toISOString()}, Now: ${now.toISOString()}`);
          entry.status = DiaryEntryStatus.SUBMIT;
          entry.reviewStartTime = null;
          entry.reviewExpiryTime = null;
          entry.reviewingTutorId = null;
          await this.diaryEntryRepository.save(entry);
          resetCount++;
        }
      }

      if (resetCount > 0) {
        this.logger.log(`[getPendingReviewEntries] Reset ${resetCount} entries with expired locks`);
      }

      // Filter to exclude NEW and REVIEWED entries
      const availableForReviewEntries = allEntries.filter(
        (entry) => entry.status !== DiaryEntryStatus.NEW && entry.status !== DiaryEntryStatus.REVIEWED,
      );

      this.logger.log(`[getPendingReviewEntries] Filtered to ${availableForReviewEntries.length} entries available for review (excluding NEW and CONFIRM entries)`);

      // Map the available entries to response DTOs
      const availableEntries = availableForReviewEntries.map((entry) => ({
        id: entry.id,
        title: entry.title,
        entryDate: entry.entryDate,
        studentName: entry.diary?.user?.name,
        studentId: entry.diary?.userId,
        submittedAt: entry.updatedAt,
        status: entry.status,
        reviewedByCurrentTutor: tutorId && entry.feedbacks?.some((feedback) => feedback.tutorId === tutorId),
        underReviewByOtherTutor: entry.status === DiaryEntryStatus.SUBMIT && entry.reviewingTutorId && (!tutorId || entry.reviewingTutorId !== tutorId),
        reviewedByTutorName: entry.feedbacks?.[0]?.tutor?.name,
        score: entry.score,
        evaluatedAt: entry.evaluatedAt,
        moduleTitle: entry.settings?.settingsTemplate?.title || entry.settings?.title,
        moduleLevel: entry.settings?.settingsTemplate?.level || entry.settings?.level,
        wordLimit: entry.settings?.settingsTemplate?.wordLimit || entry.settings?.wordLimit,
        settingsTemplateId: entry.settings?.settingsTemplate?.id || entry.settings?.settingsTemplateId,
        // Resubmission tracking fields
        isResubmission: entry.isResubmission || false,
        resubmissionType: entry.resubmissionType,
      }));

      // Apply filters
      const filteredEntries = availableEntries.filter((entry) => {
        // Filter out entries that are being reviewed by other tutors
        if (entry.underReviewByOtherTutor) {
          return false;
        }

        // Apply module filter if provided
        if (moduleFilter && (!entry.moduleTitle || !entry.moduleTitle.toLowerCase().includes(moduleFilter.toLowerCase()))) {
          return false;
        }

        // Apply student name filter if provided
        if (studentNameFilter && (!entry.studentName || !entry.studentName.toLowerCase().includes(studentNameFilter.toLowerCase()))) {
          return false;
        }

        // Apply date filters if provided
        if (dateFromFilter && (!entry.entryDate || new Date(entry.entryDate) < dateFromFilter)) {
          return false;
        }

        if (dateToFilter && (!entry.entryDate || new Date(entry.entryDate) > dateToFilter)) {
          return false;
        }

        return true;
      });

      // Sort entries if requested
      if (paginationDto && paginationDto.sortBy === 'moduleTitle') {
        filteredEntries.sort((a, b) => {
          const titleA = a.moduleTitle || '';
          const titleB = b.moduleTitle || '';
          return paginationDto.sortDirection === 'ASC' ? titleA.localeCompare(titleB) : titleB.localeCompare(titleA);
        });
      }

      // Sort entries by module level if requested
      if (paginationDto && paginationDto.sortBy === 'moduleLevel') {
        filteredEntries.sort((a, b) => {
          const levelA = a.moduleLevel || 0;
          const levelB = b.moduleLevel || 0;
          return paginationDto.sortDirection === 'ASC' ? levelA - levelB : levelB - levelA;
        });
      }

      this.logger.log(`[getPendingReviewEntries] After filtering, returning ${filteredEntries.length} entries`);
      return new PagedListDto(filteredEntries, totalCount);
    } catch (error) {
      this.logger.error('[getPendingReviewEntries] Error:', error.stack);
      throw error;
    }
  }

  /**
   * Pick a diary entry for review
   * @param entryId Entry ID
   * @param tutorId Tutor ID
   * @returns Updated diary entry
   */
  async pickEntryForReview(entryId: string, tutorId: string): Promise<DiaryEntry> {
    // Check if the user is a tutor
    const user = await this.userRepository.findOne({
      where: { id: tutorId },
      relations: ['userRoles', 'userRoles.role'],
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${tutorId} not found`);
    }

    // Check if the user has the tutor role
    const isTutor = user.userRoles.some((userRole) => userRole.role.name === 'tutor');
    if (!isTutor) {
      throw new ForbiddenException('Only tutors can pick diary entries for review');
    }

    const entry = await this.diaryEntryRepository.findOne({
      where: { id: entryId },
      relations: ['diary', 'diary.user', 'correction', 'feedbacks', 'feedbacks.tutor', 'settings', 'settings.settingsTemplate', 'likes'],
    });

    if (!entry) {
      throw new NotFoundException(`Diary entry with ID ${entryId} not found`);
    }

    // Check if the entry is available for review
    if (entry.status === DiaryEntryStatus.SUBMIT) {
      // Entry is in SUBMIT status, check if it's already being reviewed
      if (entry.reviewingTutorId) {
        // Check if the lock has expired
        // const now = getCurrentUTCDate();
        // if (!entry.reviewExpiryTime || entry.reviewExpiryTime > now) {
        //   throw new BadRequestException('This entry is currently being reviewed by another tutor');
        // }
      }
    } else if (entry.status === DiaryEntryStatus.REVIEWED) {
      throw new BadRequestException('This entry has already been reviewed');
    } else {
      throw new BadRequestException('This entry is not available for review');
    }

    // Mark the entry as being reviewed (still in SUBMIT status)
    entry.reviewStartTime = getCurrentUTCDate();

    // Set review expiry time to 2 hours from now in UTC
    const expiryTime = getCurrentUTCDate();
    expiryTime.setUTCHours(expiryTime.getUTCHours() + 2);
    entry.reviewExpiryTime = expiryTime;

    entry.reviewingTutorId = tutorId;

    return await this.diaryEntryRepository.save(entry);
  }

  /**
   * Calculate word count for content
   */
  private calculateWordCount(content: string): number {
    if (!content || content.trim().length === 0) {
      return 0;
    }
    return content.trim().split(/\s+/).length;
  }

  /**
   * Verify that a tutor has access to view a diary entry (for history/version viewing)
   * This doesn't check review status, just verifies the tutor can access the entry
   * @param entryId The ID of the diary entry
   * @param tutorId The ID of the tutor
   * @returns The diary entry if access is allowed
   */
  async verifyTutorAccess(entryId: string, tutorId: string): Promise<DiaryEntry> {
    // Verify tutor role
    const user = await this.userRepository.findOne({
      where: { id: tutorId },
      relations: ['userRoles', 'userRoles.role'],
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${tutorId} not found`);
    }

    // Check if the user has the tutor role
    const isTutor = user.userRoles.some((userRole) => userRole.role.name === 'tutor');
    if (!isTutor) {
      throw new ForbiddenException('Only tutors can access diary entries');
    }

    // Get the entry
    const entry = await this.diaryEntryRepository.findOne({
      where: { id: entryId },
      relations: ['diary', 'diary.user'],
    });

    if (!entry) {
      throw new NotFoundException(`Diary entry with ID ${entryId} not found`);
    }

    // For now, allow all tutors to access any entry for history viewing
    // In the future, you might want to add more specific access control here
    // (e.g., only tutors assigned to the student, or tutors who have reviewed the entry)

    return entry;
  }

  /**
   * Get all feedbacks for a diary entry
   * @param entryId The ID of the diary entry
   * @param tutorId The ID of the tutor requesting the feedbacks
   * @returns Array of feedback response DTOs
   */
  async getFeedbacks(entryId: string, tutorId: string): Promise<DiaryFeedbackResponseDto[]> {
    try {
      // Check if the user is a tutor
      const user = await this.userRepository.findOne({
        where: { id: tutorId },
        relations: ['userRoles', 'userRoles.role'],
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${tutorId} not found`);
      }

      // Check if the user has the tutor role
      const isTutor = user.userRoles.some((userRole) => userRole.role.name === 'tutor');
      if (!isTutor) {
        throw new ForbiddenException('Only tutors can view feedbacks on diary entries');
      }

      // Find the diary entry
      const entry = await this.diaryEntryRepository.findOne({
        where: { id: entryId },
        relations: ['diary', 'diary.user', 'feedbacks', 'feedbacks.tutor'],
      });

      if (!entry) {
        throw new NotFoundException(`Diary entry with ID ${entryId} not found`);
      }

      // Check if the tutor has access to this student
      const studentId = entry.diary.userId;
      const hasAccess = await this.tutorMatchingService.hasTutorStudentAccess(tutorId, studentId);
      if (!hasAccess) {
        throw new ForbiddenException("You do not have access to this student's diary entries");
      }

      // Map feedbacks to response DTOs
      const feedbacks =
        entry.feedbacks?.map((feedback) => ({
          id: feedback.id,
          tutorId: feedback.tutorId,
          tutorName: feedback.tutor?.name || 'Unknown Tutor',
          feedback: feedback.feedback,
          award: feedback.award,
          createdAt: feedback.createdAt,
        })) || [];

      this.logger.log(`Retrieved ${feedbacks.length} feedbacks for diary entry ${entryId}`);
      return feedbacks;
    } catch (error) {
      this.logger.error(`Error getting feedbacks for diary entry ${entryId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Set original reviewed version if not already set
   */
  private async setOriginalReviewedVersion(entry: DiaryEntry, queryRunner: any): Promise<void> {
    try {
      if (!entry.originalReviewedVersionId) {
        // Create a version history entry for the current state before review
        const originalVersion = new DiaryEntryHistory();
        originalVersion.diaryEntryId = entry.id;
        originalVersion.title = entry.title || '';
        originalVersion.content = entry.content || '';
        // Calculate next version number based on existing history records
        const existingVersionsCount = await this.diaryEntryHistoryRepository.count({
          where: { diaryEntryId: entry.id },
        });
        originalVersion.versionNumber = existingVersionsCount + 1;
        originalVersion.isLatest = false; // This is the original reviewed version, not the latest
        originalVersion.wordCount = this.calculateWordCount(entry.content || '');
        originalVersion.metaData = {
          updateTrigger: 'submit' as const,
          significantChange: true,
        };
        originalVersion.createdBy = entry.diary?.userId || entry.studentId;
        originalVersion.updatedBy = entry.diary?.userId || entry.studentId;

        const savedOriginalVersion = await queryRunner.manager.save(DiaryEntryHistory, originalVersion);

        // Update entry to reference this original version
        await queryRunner.manager.update(DiaryEntry, entry.id, {
          originalReviewedVersionId: savedOriginalVersion.id,
        });

        this.logger.log(`Set original reviewed version ${savedOriginalVersion.id} for diary entry ${entry.id}`);
      }
    } catch (error) {
      this.logger.error(`Error setting original reviewed version for entry ${entry.id}: ${error.message}`, error.stack);
      // Don't throw error to avoid breaking the review process
    }
  }

}
