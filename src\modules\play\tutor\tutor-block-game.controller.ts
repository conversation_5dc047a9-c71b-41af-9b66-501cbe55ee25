import { Controller, Get, Post, Put, Delete, Body, Param, UseGuards, Req } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiParam, ApiBody, ApiBearerAuth } from '@nestjs/swagger';
import { TutorGuard } from '../../../common/guards/tutor.guard';
import { ApiResponse } from '../../../common/dto/api-response.dto';
import { TutorBlockGameService } from './tutor-block-game.service';

@ApiTags('Tutor Block Games')
@ApiBearerAuth('JWT-auth')
@Controller('play/tutor/block')
@UseGuards(TutorGuard)
export class TutorBlockGameController {
  constructor(private readonly tutorBlockGameService: TutorBlockGameService) {}

  @Get('games')
  @ApiOperation({ 
    summary: 'Get all tutor block games',
    description: 'Retrieves all block games created by the authenticated tutor with basic information.'
  })
  async getTutorGames(@Req() req: any): Promise<ApiResponse<any>> {
    const tutorId = req.user.sub;
    const result = await this.tutorBlockGameService.getTutorGames(tutorId);
    return ApiResponse.success(result, 'Tutor block games retrieved successfully');
  }

  @Get('games/:gameId')
  @ApiOperation({ 
    summary: 'Get tutor block game with sentences',
    description: 'Retrieves a specific block game with all sentences and gap information.'
  })
  @ApiParam({ 
    name: 'gameId', 
    description: 'Block game ID', 
    example: '123e4567-e89b-12d3-a456-426614174000' 
  })
  async getTutorGameWithSentences(@Req() req: any, @Param('gameId') gameId: string): Promise<ApiResponse<any>> {
    const tutorId = req.user.sub;
    const result = await this.tutorBlockGameService.getTutorGameWithSentences(tutorId, gameId);
    return ApiResponse.success(result, 'Tutor block game with sentences retrieved successfully');
  }

  @Post('bulk-create')
  @ApiOperation({ 
    summary: 'Create multiple block games with gap sentences',
    description: 'Creates multiple block games for students. Each game contains sentences with gap markers and answer options.'
  })
  @ApiBody({
    description: 'Bulk create block games with gap sentences',
    schema: {
      type: 'object',
      properties: {
        games: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              title: { type: 'string', example: 'Sentence Building Practice' },
              student_id: { type: 'string', example: '123e4567-e89b-12d3-a456-426614174000' },
              module_type: { type: 'string', enum: ['diary', 'novel', 'essay', 'qa'], example: 'essay' },
              entry_id: { type: 'string', example: '456e7890-e89b-12d3-a456-426614174001' },
              score: { type: 'number', example: 100 },
              sentences: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    starting_part: { type: 'string', example: 'The cat makes a [[gap]]' },
                    expanding_part: { type: 'string', example: '[[gap]] sound' },
                    starting_part_answers: { type: 'array', items: { type: 'string' }, example: ['loud'] },
                    starting_part_distractors: { type: 'array', items: { type: 'string' }, example: ['quiet', 'soft', 'beautiful'] },
                    expanding_part_answers: { type: 'array', items: { type: 'string' }, example: ['strange'] },
                    expanding_part_distractors: { type: 'array', items: { type: 'string' }, example: ['normal', 'usual', 'typical'] },
                    sentence_order: { type: 'number', example: 1 }
                  },
                  required: ['starting_part', 'expanding_part', 'starting_part_answers', 'expanding_part_answers', 'sentence_order']
                }
              }
            },
            required: ['title', 'student_id', 'module_type', 'entry_id', 'sentences']
          }
        }
      },
      required: ['games']
    },
    examples: {
      blockGameExample: {
        summary: 'Block game with gap sentences',
        value: {
          games: [
            {
              title: 'Sentence Building Practice',
              student_id: '123e4567-e89b-12d3-a456-426614174000',
              module_type: 'essay',
              entry_id: '456e7890-e89b-12d3-a456-426614174001',
              score: 100,
              sentences: [
                {
                  starting_part: 'The cat makes a [[gap]]',
                  expanding_part: '[[gap]] sound',
                  starting_part_answers: ['loud'],
                  starting_part_distractors: ['quiet', 'soft', 'beautiful'],
                  expanding_part_answers: ['strange'],
                  expanding_part_distractors: ['normal', 'usual', 'typical'],
                  sentence_order: 1
                },
                {
                  starting_part: 'I will [[gap]]',
                  expanding_part: 'it [[gap]] morning',
                  starting_part_answers: ['check'],
                  starting_part_distractors: ['see', 'find', 'watch'],
                  expanding_part_answers: ['tomorrow'],
                  expanding_part_distractors: ['today', 'yesterday', 'now'],
                  sentence_order: 2
                }
              ]
            }
          ]
        }
      }
    }
  })
  async bulkCreateGames(@Req() req: any, @Body() dto: any): Promise<ApiResponse<any>> {
    const tutorId = req.user.sub;
    const result = await this.tutorBlockGameService.bulkCreateGames(tutorId, dto.games);
    return ApiResponse.success(result, 'Block games created successfully');
  }

  @Put('bulk-update')
  @ApiOperation({ 
    summary: 'Update multiple block games with sentences in bulk',
    description: 'Updates block games including their metadata (title, score) and sentences. Can update, add, or modify sentences within each game.'
  })
  @ApiBody({
    description: 'Bulk update block games with sentences',
    schema: {
      type: 'object',
      properties: {
        games: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string', example: '123e4567-e89b-12d3-a456-426614174000' },
              title: { type: 'string', example: 'Updated Sentence Building Practice' },
              score: { type: 'number', example: 150 },
              sentences: {
                type: 'array',
                description: 'Updated sentences for the game',
                items: {
                  type: 'object',
                  properties: {
                    id: { type: 'string', description: 'Sentence ID (optional for new sentences)' },
                    starting_part: { type: 'string' },
                    expanding_part: { type: 'string' },
                    starting_part_answers: { type: 'array', items: { type: 'string' } },
                    starting_part_distractors: { type: 'array', items: { type: 'string' } },
                    expanding_part_answers: { type: 'array', items: { type: 'string' } },
                    expanding_part_distractors: { type: 'array', items: { type: 'string' } },
                    sentence_order: { type: 'number' }
                  }
                }
              }
            },
            required: ['id']
          }
        }
      },
      required: ['games']
    }
  })
  async bulkUpdateGames(@Req() req: any, @Body() dto: any): Promise<ApiResponse<any>> {
    const tutorId = req.user.sub;
    const result = await this.tutorBlockGameService.bulkUpdateGames(tutorId, dto.games);
    return ApiResponse.success(result, 'Block games updated successfully');
  }

  @Delete('bulk-delete')
  @ApiOperation({ 
    summary: 'Soft delete multiple block games in bulk',
    description: 'Soft deletes (deactivates) multiple block games owned by the tutor. Games are marked as inactive rather than permanently deleted.'
  })
  @ApiBody({
    description: 'Array of game IDs to delete',
    schema: {
      type: 'object',
      properties: {
        game_ids: { 
          type: 'array', 
          items: { type: 'string' },
          example: ['123e4567-e89b-12d3-a456-426614174000', '456e7890-e89b-12d3-a456-426614174001']
        }
      },
      required: ['game_ids']
    }
  })
  async bulkDeleteGames(@Req() req: any, @Body() dto: { game_ids: string[] }): Promise<ApiResponse<any>> {
    const tutorId = req.user.sub;
    await this.tutorBlockGameService.bulkDeleteGames(tutorId, dto.game_ids);
    return ApiResponse.success(null, 'Block games deleted successfully');
  }

  @Post(':gameId/sentences')
  @ApiOperation({ 
    summary: 'Add sentence to block game',
    description: 'Adds a new sentence with gaps to an existing block game.'
  })
  @ApiParam({ name: 'gameId', description: 'Block game ID', example: '123e4567-e89b-12d3-a456-426614174000' })
  @ApiBody({
    description: 'Sentence data with gaps and answers',
    schema: {
      type: 'object',
      properties: {
        starting_part: { type: 'string', example: 'The dog [[gap]] in the park' },
        expanding_part: { type: 'string', example: '[[gap]] happily' },
        starting_part_answers: { type: 'array', items: { type: 'string' }, example: ['runs'] },
        starting_part_distractors: { type: 'array', items: { type: 'string' }, example: ['walks', 'sits', 'sleeps'] },
        expanding_part_answers: { type: 'array', items: { type: 'string' }, example: ['very'] },
        expanding_part_distractors: { type: 'array', items: { type: 'string' }, example: ['quite', 'really', 'extremely'] },
        sentence_order: { type: 'number', example: 3 }
      },
      required: ['starting_part', 'expanding_part', 'starting_part_answers', 'expanding_part_answers', 'sentence_order']
    }
  })
  async addSentence(@Req() req: any, @Param('gameId') gameId: string, @Body() dto: any): Promise<ApiResponse<any>> {
    const tutorId = req.user.sub;
    const result = await this.tutorBlockGameService.addSentence(tutorId, gameId, dto);
    return ApiResponse.success(result, 'Sentence added successfully');
  }
}