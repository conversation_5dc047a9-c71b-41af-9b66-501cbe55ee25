import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsEmail, IsNotEmpty, IsOptional, IsEnum, MaxLength } from 'class-validator';
import { ContactStatus } from '../entities/contact-us.entity';

export class CreateContactUsDto {
  @ApiProperty({ description: 'Full name', example: '<PERSON>' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  name: string;

  @ApiProperty({ description: 'Email address', example: '<EMAIL>' })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({ description: 'Phone number', example: '+1234567890', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(20)
  phone?: string;

  @ApiProperty({ description: 'Subject', example: 'Question about subscription' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(200)
  subject: string;

  @ApiProperty({ description: 'Message content', example: 'I have a question about...' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(2000)
  message: string;
}

export class AdminResponseDto {
  @ApiProperty({ description: 'Admin response message' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(2000)
  response: string;

  @ApiProperty({ description: 'Status', enum: ContactStatus })
  @IsEnum(ContactStatus)
  status: ContactStatus;
}

export class ContactUsResponseDto {
  @ApiProperty({ description: 'Contact ID' })
  id: string;

  @ApiProperty({ description: 'Name' })
  name: string;

  @ApiProperty({ description: 'Email' })
  email: string;

  @ApiProperty({ description: 'Phone', required: false })
  phone?: string;

  @ApiProperty({ description: 'Subject' })
  subject: string;

  @ApiProperty({ description: 'Message' })
  message: string;

  @ApiProperty({ description: 'Status', enum: ContactStatus })
  status: ContactStatus;

  @ApiProperty({ description: 'Admin response', required: false })
  adminResponse?: string;

  @ApiProperty({ description: 'Responded by admin ID', required: false })
  respondedBy?: string;

  @ApiProperty({ description: 'Response date', required: false })
  respondedAt?: Date;

  @ApiProperty({ description: 'Created date' })
  createdAt: Date;
}