import { MigrationInterface, QueryRunner } from 'typeorm';

export class FixSkinConstraints1734649000000 implements MigrationInterface {
  name = 'FixSkinConstraints1734649000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop all existing foreign key constraints on skin_id
    await queryRunner.query(`ALTER TABLE "diary_entry" DROP CONSTRAINT IF EXISTS "FK_ad12d4b9edf19455766d9457d6b"`);
    await queryRunner.query(`ALTER TABLE "diary_entry" DROP CONSTRAINT IF EXISTS "FK_diary_entry_skin_id"`);
    
    // Clean up invalid global skin references
    await queryRunner.query(`
      UPDATE diary_entry 
      SET skin_id = NULL, skin_type = 'global'
      WHERE skin_id IS NOT NULL 
      AND (skin_type = 'global' OR skin_type IS NULL)
      AND NOT EXISTS (SELECT 1 FROM diary_skin WHERE id = diary_entry.skin_id)
    `);

    // Clean up invalid student skin references
    await queryRunner.query(`
      UPDATE diary_entry 
      SET skin_id = NULL, skin_type = 'global'
      WHERE skin_id IS NOT NULL 
      AND skin_type = 'student' 
      AND NOT EXISTS (SELECT 1 FROM student_diary_skin WHERE id = diary_entry.skin_id)
    `);

    // Set default skin_type for entries without it
    await queryRunner.query(`
      UPDATE diary_entry 
      SET skin_type = 'global' 
      WHERE skin_type IS NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // This migration only cleans up data, no structural changes to revert
  }
}