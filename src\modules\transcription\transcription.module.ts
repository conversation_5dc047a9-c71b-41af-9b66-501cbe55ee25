import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Book } from '../../database/entities/book.entity';
import { Sentence } from '../../database/entities/sentence.entity';
import { TranscriptionSession } from '../../database/entities/transcription-session.entity';
import { TranscriptionAttempt } from '../../database/entities/transcription-attempt.entity';
import { AdminTranscriptionController } from './admin-transcription.controller';
import { StudentTranscriptionController } from './student-transcription.controller';
import { TutorTranscriptionController } from './tutor-transcription.controller';
import { BookService } from './services/book.service';
import { SentenceService } from './services/sentence.service';
import { TranscriptionSessionService } from './services/transcription-session.service';
import { TranscriptionAttemptService } from './services/transcription-attempt.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Book,
      Sentence,
      TranscriptionSession,
      TranscriptionAttempt,
    ]),
  ],
  controllers: [
    AdminTranscriptionController,
    StudentTranscriptionController,
    TutorTranscriptionController,
  ],
  providers: [
    BookService,
    SentenceService,
    TranscriptionSessionService,
    TranscriptionAttemptService,
  ],
  exports: [
    BookService,
    SentenceService,
    TranscriptionSessionService,
    TranscriptionAttemptService,
  ],
})
export class TranscriptionModule {}