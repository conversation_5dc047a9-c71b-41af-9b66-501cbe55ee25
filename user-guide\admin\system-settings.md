# System Settings & Configuration

## 🔧 Platform Configuration Overview

As an administrator, you have complete control over the HEC platform's core settings, features, and operational parameters. This guide covers all system configuration options available through the admin dashboard.

## 🏠 Accessing System Settings

### Navigation Path
1. **Login to Admin Dashboard** → `/admin/login`
2. **Navigate to Settings** → Admin Menu → "System Settings"
3. **Select Configuration Area** → Choose from available setting categories

### Settings Categories
- **General Platform Settings**
- **User Management Configuration**
- **Content & Media Settings**
- **Security & Privacy Controls**
- **Notification Settings**
- **Feature Flags & Toggles**
- **Storage & File Management**
- **API & Integration Settings**

## ⚙️ General Platform Settings

### Basic Configuration
```
Platform Name: "HEC - Handwriting Education Center"
Default Language: English (en)
Timezone: UTC (configurable per region)
Maintenance Mode: Disabled
Debug Mode: Disabled (Production)
```

### Platform Limits
- **Maximum Users**: 10,000 (configurable)
- **File Upload Limit**: 50MB per file
- **Daily API Requests**: 100,000 per user
- **Session Timeout**: 24 hours
- **Password Expiry**: 90 days (optional)

### Regional Settings
- **Date Format**: MM/DD/YYYY or DD/MM/YYYY
- **Time Format**: 12-hour or 24-hour
- **Currency**: USD, EUR, GBP (for subscription plans)
- **Number Format**: US (1,234.56) or EU (1.234,56)

## 👥 User Management Configuration

### Registration Settings
- **Open Registration**: Enabled/Disabled
- **Email Verification**: Required/Optional
- **Admin Approval**: Required for tutors
- **Default User Role**: Student
- **Username Requirements**: Minimum 3 characters, alphanumeric

### Account Policies
- **Password Requirements**:
  - Minimum 8 characters
  - Must include uppercase, lowercase, number
  - Special characters optional
  - Cannot reuse last 5 passwords
- **Account Lockout**: 5 failed attempts = 30-minute lockout
- **Inactive Account**: Disable after 180 days of inactivity

### Role Permissions Matrix
```
STUDENTS:
✅ Create diary entries
✅ Play games (Story Maker, Block Games)
✅ View own progress
✅ Connect with friends
✅ Customize profile
❌ Access admin functions
❌ Modify other users

TUTORS:
✅ All student permissions
✅ Review assigned student entries
✅ Provide feedback and corrections
✅ View student progress analytics
✅ Message students and parents
❌ Create/delete user accounts
❌ Modify system settings

ADMINS:
✅ All platform permissions
✅ User account management
✅ System configuration
✅ Content management
✅ Analytics and reporting
✅ Security settings
```

## 📚 Content & Media Settings

### Story Maker Configuration
- **Maximum Stories**: 500 active stories
- **Image Requirements**: 
  - Format: JPG, PNG, WebP
  - Size: 800x600 minimum, 1920x1080 maximum
  - File size: 5MB maximum
- **Content Moderation**: AI + Manual review
- **Approval Workflow**: Auto-approve/Manual review

### Diary System Settings
- **Entry Length**: 50-5000 characters
- **Auto-save Interval**: 30 seconds
- **Version History**: Keep 10 versions per entry
- **Theme Library**: 50+ pre-built themes
- **Custom Themes**: Enabled for premium users

### File Storage Configuration
```bash
# Local Storage Settings
STORAGE_PROVIDER=local
LOCAL_STORAGE_PATH=/var/hec/uploads
MAX_STORAGE_SIZE=100GB

# AWS S3 Settings (Alternative)
STORAGE_PROVIDER=s3
AWS_REGION=us-east-1
AWS_S3_BUCKET_NAME=hec-production-files
AWS_CLOUDFRONT_DOMAIN=d123456789.cloudfront.net
```

## 🔒 Security & Privacy Controls

### Data Protection Settings
- **GDPR Compliance**: Enabled
- **Data Retention**: 7 years (configurable)
- **Right to Deletion**: Automated process
- **Data Export**: JSON format available
- **Encryption**: AES-256 for sensitive data

### Privacy Controls
- **Profile Visibility**: 
  - Public: Visible to all users
  - Friends Only: Visible to connected friends
  - Private: Visible to user and assigned tutor only
- **Content Sharing**: User-controlled per entry
- **Location Data**: Disabled by default
- **Analytics Tracking**: Anonymized user behavior only

### Security Measures
- **Two-Factor Authentication**: Optional for all users, required for admins
- **IP Whitelisting**: Available for admin accounts
- **Session Management**: Concurrent session limits
- **Audit Logging**: All admin actions logged
- **Backup Schedule**: Daily automated backups

## 📧 Notification Settings

### Email Configuration
```bash
# SMTP Settings
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=[encrypted]
MAIL_ENCRYPTION=tls
```

### Notification Types
- **Welcome Emails**: New user registration
- **Feedback Notifications**: Tutor reviews completed
- **Achievement Alerts**: Badges and milestones
- **System Updates**: Maintenance and feature announcements
- **Security Alerts**: Login attempts, password changes

### Delivery Preferences
- **Email Frequency**: Immediate, Daily digest, Weekly summary
- **Push Notifications**: Enabled for mobile apps
- **In-App Notifications**: Real-time updates
- **SMS Notifications**: Available for critical alerts (premium)

## 🚀 Feature Flags & Toggles

### Core Features
```javascript
{
  "diary_system": true,
  "story_maker": true,
  "block_games": true,
  "social_features": true,
  "achievement_system": true,
  "tutor_feedback": true,
  "parent_dashboard": false, // Coming soon
  "mobile_app": true,
  "api_access": true
}
```

### Experimental Features
- **AI Writing Assistant**: Beta testing
- **Voice Recording**: Development phase
- **Video Diary Entries**: Planned feature
- **Collaborative Writing**: Under review
- **Advanced Analytics**: Premium feature

### Feature Rollout Controls
- **Gradual Rollout**: Enable for percentage of users
- **A/B Testing**: Compare feature variations
- **User Group Targeting**: Enable for specific user segments
- **Geographic Restrictions**: Limit features by region

## 📊 Analytics & Monitoring

### System Monitoring
- **Server Performance**: CPU, Memory, Disk usage
- **Database Performance**: Query times, connection pools
- **API Response Times**: Average, 95th percentile
- **Error Rates**: 4xx, 5xx HTTP responses
- **Uptime Monitoring**: 99.9% target availability

### User Analytics
- **Active Users**: Daily, Weekly, Monthly counts
- **Feature Usage**: Most/least used features
- **Content Creation**: Diary entries, stories per day
- **Engagement Metrics**: Session duration, return visits
- **Performance Metrics**: Game scores, improvement trends

### Reporting Configuration
- **Automated Reports**: Daily, Weekly, Monthly
- **Custom Dashboards**: Role-specific views
- **Data Export**: CSV, PDF, Excel formats
- **Real-time Alerts**: Performance thresholds
- **Historical Data**: 2-year retention policy

## 🔄 Backup & Recovery

### Backup Strategy
- **Database Backups**: 
  - Full backup: Daily at 2 AM UTC
  - Incremental: Every 6 hours
  - Retention: 30 days local, 1 year cloud
- **File Backups**:
  - User uploads: Daily sync to cloud storage
  - System files: Weekly full backup
  - Configuration: Version controlled

### Recovery Procedures
- **Point-in-time Recovery**: Up to 30 days
- **Disaster Recovery**: 4-hour RTO, 1-hour RPO
- **Testing Schedule**: Monthly recovery drills
- **Documentation**: Step-by-step recovery guides

## 🌐 API & Integration Settings

### API Configuration
```bash
# API Settings
API_VERSION=v1
API_RATE_LIMIT=1000/hour
API_AUTHENTICATION=JWT
API_DOCUMENTATION_URL=/api-docs
```

### Third-party Integrations
- **Authentication**: Google SSO, Microsoft SSO
- **Payment Processing**: Stripe, PayPal
- **Email Service**: SendGrid, Amazon SES
- **Analytics**: Google Analytics, Mixpanel
- **Support**: Zendesk, Intercom

### Webhook Configuration
- **User Events**: Registration, login, profile updates
- **Content Events**: Diary submissions, story completions
- **System Events**: Errors, performance alerts
- **Custom Webhooks**: Configurable endpoints

## 🛠️ Maintenance & Updates

### Scheduled Maintenance
- **Weekly Maintenance**: Sundays 2-4 AM UTC
- **Monthly Updates**: First Sunday of each month
- **Emergency Maintenance**: As needed with 2-hour notice
- **Maintenance Notifications**: Email + in-app alerts

### Update Procedures
1. **Staging Deployment**: Test all changes
2. **User Notification**: 48-hour advance notice
3. **Maintenance Mode**: Enable during updates
4. **Rollback Plan**: Immediate revert if issues
5. **Post-Update Testing**: Verify all systems

### Version Control
- **Release Versioning**: Semantic versioning (1.2.3)
- **Change Logs**: Detailed update documentation
- **Feature Flags**: Gradual feature rollouts
- **Rollback Capability**: Previous version restoration

## 📋 Configuration Checklist

### Initial Setup
- [ ] Configure basic platform settings
- [ ] Set up user registration policies
- [ ] Configure email/notification settings
- [ ] Enable required security measures
- [ ] Set up backup procedures
- [ ] Configure monitoring and alerts
- [ ] Test all integrations
- [ ] Document custom configurations

### Regular Maintenance
- [ ] Review user activity reports (weekly)
- [ ] Check system performance metrics (daily)
- [ ] Update security settings (monthly)
- [ ] Review and update feature flags (monthly)
- [ ] Test backup and recovery procedures (monthly)
- [ ] Update documentation (as needed)

### Security Audits
- [ ] Review user permissions (quarterly)
- [ ] Audit admin access logs (monthly)
- [ ] Update security policies (annually)
- [ ] Penetration testing (annually)
- [ ] Compliance review (annually)

---

**Next Steps**: After configuring system settings, proceed to [User Management](user-management.md) to set up user accounts and permissions, or explore [Content Management](content-management.md) to configure educational content.

*For technical support with system configuration, contact the development team or refer to the API documentation at `/api-docs`.*