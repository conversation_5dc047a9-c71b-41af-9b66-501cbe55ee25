import { Entity, Column, OneToMany } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { Sentence } from './sentence.entity';
import { TranscriptionSession } from './transcription-session.entity';

@Entity('books')
export class Book extends AuditableBaseEntity {
  @Column({ type: 'varchar', length: 255 })
  title: string;

  @Column({ type: 'varchar', length: 255 })
  author: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ name: 'publication_year', type: 'int', default: 0 })
  publicationYear: number;

  @Column({ type: 'text', nullable: true })
  content: string;

  @Column({ name: 'is_active', type: 'boolean', default: true })
  isActive: boolean;

  @OneToMany(() => Sentence, sentence => sentence.book)
  sentences: Sentence[];

  @OneToMany(() => TranscriptionSession, session => session.book)
  sessions: TranscriptionSession[];
}