import { forwardRef, Inject, Injectable, Logger, BadRequestException, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, Between, In } from 'typeorm';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';
import {
  CreateDiaryEntryDto,
  UpdateDiaryEntryDto,
  DiaryEntryResponseDto,
  DiaryEntryFilterDto,
  SubmitDiaryEntryDto,
  DiaryEntryHistoryResponseDto,
  DiaryEntryVersionDto,
  UpdateDiaryEntrySettingsDto,
} from '../../database/models/diary.dto';
import { AdminDiaryEntryFilterDto, AdminUpdateDiaryEntryDto } from '../../database/models/admin-diary.dto';
import { TutorMatchingService } from '../../modules/tutor-matching/tutor-matching.service';
import { AsyncNotificationHelperService } from '../../modules/notification/async-notification-helper.service';
import { NotificationType } from '../../database/entities/notification.entity';
import { DeeplinkService, DeeplinkType } from '../../common/utils/deeplink.service';
import { v4 as uuidv4 } from 'uuid';
import { DiaryEntryAttendance, AttendanceStatus } from '../../database/entities/diary-entry-attendance.entity';
import { DiaryMapperService } from './diary-mapper.service';
import { DiaryEntry, DiaryEntryStatus } from '../../database/entities/diary-entry.entity';
import { DiaryEntryHistory } from '../../database/entities/diary-entry-history.entity';
import { Diary } from '../../database/entities/diary.entity';
import { User } from '../../database/entities/user.entity';
import { DiarySkin } from '../../database/entities/diary-skin.entity';
import { DiaryEntrySettings } from '../../database/entities/diary-entry-settings.entity';
import { FriendshipStatus, StudentFriendship } from '../../database/entities/student-friendship.entity';
import { DiaryEntryFriendShare } from '../../database/entities/diary-entry-friend-share.entity';
import { FeatureType, PlanFeature } from '../../database/entities/plan-feature.entity';
import { DiarySettingsService } from './diary-settings.service';
import { DiaryEntryHistoryService } from './diary-entry-history.service';
import { DiaryVisibility } from '../../common/enums/diary-visibility.enum';
import { getCurrentUTCDate, getEndOfDayUTC, getStartOfDayUTC, parseYYYYMMDDToUTC } from '../../common/utils/date-utils';
import { TransactionHelper } from '../../common/utils/transaction-helper';

@Injectable()
export class DiaryEntryService {
  private readonly logger = new Logger(DiaryEntryService.name);

  constructor(
    @InjectRepository(DiaryEntry)
    private readonly diaryEntryRepository: Repository<DiaryEntry>,
    @InjectRepository(DiaryEntryHistory)
    private readonly diaryEntryHistoryRepository: Repository<DiaryEntryHistory>,
    @InjectRepository(Diary)
    private readonly diaryRepository: Repository<Diary>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(DiarySkin)
    private readonly diarySkinRepository: Repository<DiarySkin>,
    @InjectRepository(DiaryEntrySettings)
    private readonly diaryEntrySettingsRepository: Repository<DiaryEntrySettings>,
    @InjectRepository(StudentFriendship)
    private readonly studentFriendshipRepository: Repository<StudentFriendship>,
    @InjectRepository(DiaryEntryFriendShare)
    private readonly diaryFriendShareRepository: Repository<DiaryEntryFriendShare>,
    @InjectRepository(PlanFeature)
    private readonly planFeatureRepository: Repository<PlanFeature>,
    private readonly dataSource: DataSource,
    @Inject(forwardRef(() => DiarySettingsService))
    private readonly diarySettingsService: DiarySettingsService,
    @Inject(forwardRef(() => DiaryEntryHistoryService))
    private readonly diaryEntryHistoryService: DiaryEntryHistoryService,
    @Inject(forwardRef(() => TutorMatchingService))
    private readonly tutorMatchingService: TutorMatchingService,
    @Inject(forwardRef(() => DiaryMapperService))
    private readonly diaryMapperService: DiaryMapperService,
    private readonly asyncNotificationHelper: AsyncNotificationHelperService,
    private readonly deeplinkService: DeeplinkService,
    @InjectRepository(DiaryEntryAttendance)
    private readonly diaryEntryAttendanceRepository: Repository<DiaryEntryAttendance>,
  ) {}

  /**
   * Create a new diary entry
   * @param userId The ID of the user creating the entry
   * @param createDiaryEntryDto Data for creating a new diary entry
   * @returns The created diary entry
   */
  async createDiaryEntry(userId: string, createDiaryEntryDto: CreateDiaryEntryDto): Promise<DiaryEntryResponseDto> {
    try {
      // Get the diary for the user
      const diary = await this.getOrCreateDiary(userId);

      // Check if an entry already exists for the specified date
      const entryDate = createDiaryEntryDto.entryDate ? parseYYYYMMDDToUTC(createDiaryEntryDto.entryDate) : getCurrentUTCDate();

      // Prevent creating entries for today's date
      const today = getCurrentUTCDate();
      const todayStart = getStartOfDayUTC(today);
      const todayEnd = getEndOfDayUTC(today);

      if (entryDate >= todayStart && entryDate <= todayEnd) {
        throw new BadRequestException("Cannot create diary entries for today's date. Please use the daily entry feature instead.");
      }

      // Get start and end of the entry date in UTC
      const startOfDay = getStartOfDayUTC(entryDate);
      const endOfDay = getEndOfDayUTC(entryDate);

      // Check if an entry already exists for this date
      const existingEntry = await this.diaryEntryRepository.findOne({
        where: {
          diaryId: diary.id,
          entryDate: Between(startOfDay, endOfDay),
        },
        relations: ['skin', 'feedbacks', 'feedbacks.tutor', 'diary', 'diary.user', 'settings', 'settings.settingsTemplate', 'correction', 'correction.tutor', 'likes', 'originalReviewedVersion'],
      });

      if (existingEntry) {
        this.logger.log(`Entry already exists for date ${entryDate.toISOString()} for user ${userId}. Returning existing entry.`);
        return this.mapEntryToResponseDto(existingEntry);
      }

      // Use the user's default diary skin if available, otherwise fall back to diary's default skin
      let skinId = createDiaryEntryDto.skinId; // First priority: explicitly provided skin

      if (!skinId) {
        // Second priority: user's default diary skin
        const user = await this.userRepository.findOne({
          where: { id: userId },
          select: ['defaultDiarySkinId'],
        });
        skinId = user?.defaultDiarySkinId;
      }

      if (!skinId) {
        // Third priority: diary's default skin
        skinId = diary.defaultSkinId;
      }
      let skin = await this.diarySkinRepository.findOne({
        where: { id: skinId },
      });

      // If not found in global skins, check user-specific skins
      if (!skin) {
        this.logger.log(`Default skin with ID ${skinId} not found in global skins, checking student skins`);

        // Import the StudentDiarySkin entity dynamically to avoid circular dependencies
        const { StudentDiarySkin } = require('../../database/entities/student-diary-skin.entity');

        // Get the repository for StudentDiarySkin
        const studentDiarySkinRepository = this.dataSource.getRepository(StudentDiarySkin);

        // Check if it's a student-specific skin
        const studentSkin = await studentDiarySkinRepository.findOne({
          where: { id: skinId, studentId: userId },
        });

        if (studentSkin) {
          this.logger.log(`Found student-specific skin with ID ${skinId} for user ${userId}`);
          // Create a skin object with the necessary properties
          // Create a skin object with the necessary properties
          // We're using unknown as an intermediate type to avoid TypeScript errors
          // since we're not setting all properties of DiarySkin
          const skinData = {
            id: studentSkin.id,
            name: studentSkin.name,
            description: studentSkin.description,
            templateContent: studentSkin.templateContent,
            previewImagePath: studentSkin.previewImagePath,
            isActive: studentSkin.isActive,
            isGlobal: false,
            createdById: studentSkin.studentId,
            createdAt: new Date(),
            updatedAt: new Date(),
          };

          // Cast to DiarySkin
          skin = skinData as unknown as DiarySkin;

          // Add the isUsedIn property for our DTO mapping later
          (skin as any).isUsedIn = true;
        } else {
          this.logger.warn(`Default skin with ID ${skinId} not found in either global or student skins, falling back to a global skin`);
          // Fall back to a global skin if the default skin is not found
          skin = await this.diarySkinRepository.findOne({
            where: { isActive: true, isGlobal: true },
          });

          if (!skin) {
            throw new NotFoundException('No active diary skins found');
          }
        }
      }

      // Get the settings template
      let settingsTemplate: any;

      // If settingsTemplateId is provided, use it
      if (createDiaryEntryDto.settingsTemplateId) {
        try {
          settingsTemplate = await this.diarySettingsService.getDiarySettingsTemplateById(createDiaryEntryDto.settingsTemplateId);
        } catch (error) {
          this.logger.warn(`Provided settings template ID ${createDiaryEntryDto.settingsTemplateId} not found, falling back to default`);
        }
      }

      // If no template was found or provided, get the default template (first active template)
      if (!settingsTemplate) {
        const templates = await this.diarySettingsService.getActiveDiarySettingsTemplates();
        if (templates.items.length > 0) {
          settingsTemplate = templates.items[0];
        }
      }

      // Start a transaction
      const queryRunner = this.dataSource.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      try {
        // Handle skin ID
        let finalSkinId = skinId; // Default to the diary's default skin
        let skinObject = null;

        this.logger.log(`Default skin ID from diary: ${skinId}`);
        this.logger.log(`Provided skin ID in DTO: ${createDiaryEntryDto.skinId}`);

        // If a skinId is provided in the DTO, use it
        if (createDiaryEntryDto.skinId) {
          this.logger.log(`Using provided skin ID: ${createDiaryEntryDto.skinId}`);
          finalSkinId = createDiaryEntryDto.skinId;

          // Load the skin object for later use
          skinObject = await this.loadSkinData(finalSkinId, userId);

          if (skinObject) {
            this.logger.log(`Successfully loaded skin: ${skinObject.name} with ID ${skinObject.id}`);
          } else {
            this.logger.warn(`Could not load skin with ID ${finalSkinId}, but will still use this ID`);
          }
        } else {
          // No skin ID provided, use the default skin
          this.logger.log(`No skin ID provided, using default skin ID ${skinId}`);

          // Load the default skin object for later use
          skinObject = await this.loadSkinData(skinId, userId);

          if (skinObject) {
            this.logger.log(`Successfully loaded default skin: ${skinObject.name} with ID ${skinObject.id}`);
          } else {
            this.logger.warn(`Could not load default skin with ID ${skinId}`);
          }
        }

        // Instead of using the ORM, let's use raw SQL to ensure the skin ID is correctly set
        this.logger.log(`Using raw SQL to insert diary entry with skin ID ${finalSkinId}`);

        // Generate a UUID for the new entry
        const entryId = uuidv4();
        const now = new Date().toISOString();
        const backgroundColor = createDiaryEntryDto.backgroundColor || null;
        const isPrivate = createDiaryEntryDto.isPrivate !== undefined ? createDiaryEntryDto.isPrivate : false;
        const title = createDiaryEntryDto.title || '';
        const content = createDiaryEntryDto.content || '';

        // Insert the diary entry using raw SQL
        await queryRunner.query(
          `
          INSERT INTO diary_entry (
            id,
            diary_id,
            entry_date,
            title,
            content,
            status,
            skin_id,
            background_color,
            is_private,
            created_at,
            updated_at
          ) VALUES (
            $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11
          )
        `,
          [entryId, diary.id, entryDate, title, content, DiaryEntryStatus.NEW, finalSkinId, backgroundColor, isPrivate, now, now],
        );

        this.logger.log(`Successfully inserted diary entry with ID ${entryId} and skin ID ${finalSkinId} using raw SQL`);

        // Create a mock entry object for the rest of the code
        const entry = {
          id: entryId,
          diaryId: diary.id,
          entryDate: new Date(entryDate),
          title: title,
          content: content,
          status: DiaryEntryStatus.NEW,
          skinId: finalSkinId,
          backgroundColor: backgroundColor,
          isPrivate: isPrivate,
        };

        const savedEntry = entry;

        // Always create settings for the entry
        if (settingsTemplate) {
          // Use the provided or found template
          const settings = this.diaryEntrySettingsRepository.create({
            diaryEntryId: savedEntry.id,
            settingsTemplateId: settingsTemplate.id,
            title: settingsTemplate.title,
            level: settingsTemplate.level,
            wordLimit: settingsTemplate.wordLimit,
          });

          await queryRunner.manager.save(settings);
        } else {
          // If no template was found, get the default template
          const defaultTemplate = await this.diarySettingsService.getDefaultDiarySettingsTemplate();

          if (!defaultTemplate) {
            // If no default template exists, create a basic one
            this.logger.warn(`No settings template found, creating a basic template for entry ${savedEntry.id}`);

            const basicTemplate = await this.diarySettingsService.createDiarySettingsTemplate({
              title: 'Basic Level',
              level: 1,
              wordLimit: 100,
              minWordLimit: 51,
              description: 'Basic template created automatically',
              isActive: true,
            });

            // Create settings with the basic template
            const settings = this.diaryEntrySettingsRepository.create({
              diaryEntryId: savedEntry.id,
              settingsTemplateId: basicTemplate.id,
              title: basicTemplate.title,
              level: basicTemplate.level,
              wordLimit: basicTemplate.wordLimit,
              minWordLimit: basicTemplate.minWordLimit,
            });

            await queryRunner.manager.save(settings);
          } else {
            // Create settings with the default template
            const settings = this.diaryEntrySettingsRepository.create({
              diaryEntryId: savedEntry.id,
              settingsTemplateId: defaultTemplate.id,
              title: defaultTemplate.title,
              level: defaultTemplate.level,
              wordLimit: defaultTemplate.wordLimit,
            });

            await queryRunner.manager.save(settings);
          }
        }

        // Commit the transaction
        await queryRunner.commitTransaction();

        // Before fetching the complete entry, verify that the skin ID was correctly saved
        // This is a direct database check to ensure the transaction was successful
        const savedSkinCheck = await this.dataSource.query(`SELECT skin_id FROM diary_entry WHERE id = $1`, [savedEntry.id]);

        this.logger.log(`Database check for saved skin ID: ${JSON.stringify(savedSkinCheck)}`);

        if (savedSkinCheck && savedSkinCheck.length > 0 && savedSkinCheck[0].skin_id !== finalSkinId) {
          this.logger.warn(`Skin ID mismatch: expected ${finalSkinId} but found ${savedSkinCheck[0].skin_id} in database`);

          // Force update the skin ID with a direct query
          await this.dataSource.query(`UPDATE diary_entry SET skin_id = $1 WHERE id = $2`, [finalSkinId, savedEntry.id]);

          this.logger.log(`Forced update of skin ID to ${finalSkinId} for entry ${savedEntry.id}`);
        }

        // Instead of using the ORM to load the entry, let's use raw SQL to get the entry data
        // This ensures we get the most up-to-date data from the database
        this.logger.log(`Using raw SQL to get the complete entry with ID ${savedEntry.id}`);

        // First, get the entry data
        const rawEntryData = await this.dataSource.query(
          `
          SELECT
            e.*,
            d.tutor_greeting,
            d.user_id as diary_user_id,
            d.default_skin_id as diary_default_skin_id,
            u.name as user_name
          FROM diary_entry e
          LEFT JOIN diary d ON e.diary_id = d.id
          LEFT JOIN "user" u ON d.user_id = u.id
          WHERE e.id = $1
        `,
          [savedEntry.id],
        );

        if (!rawEntryData || rawEntryData.length === 0) {
          throw new Error(`Failed to retrieve newly created diary entry with ID ${savedEntry.id}`);
        }

        this.logger.log(`Raw entry data: ${JSON.stringify(rawEntryData[0])}`);

        // Now, get the settings data
        const rawSettingsData = await this.dataSource.query(
          `
          SELECT
            s.*,
            t.title as template_title,
            t.level as template_level,
            t.word_limit as template_word_limit,
            t.description as template_description,
            t.is_active as template_is_active,
            t.created_at as template_created_at,
            t.updated_at as template_updated_at
          FROM diary_entry_settings s
          LEFT JOIN diary_settings_template t ON s.settings_template_id = t.id
          WHERE s.diary_entry_id = $1
        `,
          [savedEntry.id],
        );

        // Log the tutor greeting value for debugging
        this.logger.log(`Tutor greeting from database: ${rawEntryData[0].tutor_greeting}`);

        // Create a mock entry object with the raw data
        const completeEntry = {
          id: rawEntryData[0].id,
          diaryId: rawEntryData[0].diary_id,
          entryDate: new Date(rawEntryData[0].entry_date),
          title: rawEntryData[0].title,
          content: rawEntryData[0].content,
          status: rawEntryData[0].status,
          skinId: rawEntryData[0].skin_id,
          backgroundColor: rawEntryData[0].background_color,
          isPrivate: rawEntryData[0].is_private,
          createdAt: new Date(rawEntryData[0].created_at),
          updatedAt: new Date(rawEntryData[0].updated_at),
          diary: {
            id: rawEntryData[0].diary_id,
            userId: rawEntryData[0].diary_user_id,
            tutorGreeting: rawEntryData[0].tutor_greeting,
            defaultSkinId: rawEntryData[0].diary_default_skin_id,
            user: {
              name: rawEntryData[0].user_name,
            },
          },
          feedbacks: [],
          correction: null,
          skin: null, // Initialize with null, will be set later
          reviewStartTime: rawEntryData[0].review_start_time ? new Date(rawEntryData[0].review_start_time) : null,
          reviewExpiryTime: rawEntryData[0].review_expiry_time ? new Date(rawEntryData[0].review_expiry_time) : null,
          reviewingTutorId: rawEntryData[0].reviewing_tutor_id,
          score: rawEntryData[0].score,
          evaluatedAt: rawEntryData[0].evaluated_at ? new Date(rawEntryData[0].evaluated_at) : null,
          evaluatedBy: rawEntryData[0].evaluated_by,
          thanksMessage: rawEntryData[0].thanks_message,
        } as DiaryEntry; // Cast to DiaryEntry to satisfy TypeScript

        // Add settings if available
        if (rawSettingsData && rawSettingsData.length > 0) {
          // Get the settingsTemplateId from the raw data
          const settingsTemplateId = rawSettingsData[0].settings_template_id;

          // Log the settingsTemplateId for debugging
          this.logger.log(`Creating entry with settingsTemplateId: ${settingsTemplateId}`);

          // Create a flattened settings object for the entity
          const entitySettings = {
            id: rawSettingsData[0].id,
            diaryEntryId: rawSettingsData[0].diary_entry_id,
            settingsTemplateId: settingsTemplateId,
            title: rawSettingsData[0].title,
            level: rawSettingsData[0].level,
            wordLimit: rawSettingsData[0].word_limit,
            createdAt: new Date(rawSettingsData[0].created_at),
            updatedAt: new Date(rawSettingsData[0].updated_at),
          } as DiaryEntrySettings;

          // Store the entity settings for internal use
          completeEntry.settings = entitySettings;

          // For debugging purposes, log the settings
          this.logger.log(`Created entity settings with ID ${entitySettings.id} and templateId ${settingsTemplateId}`);

          // Log the created settings
          this.logger.log(
            `Created settings for entry ${savedEntry.id}: ${JSON.stringify({
              id: completeEntry.settings.id,
              settingsTemplateId: completeEntry.settings.settingsTemplateId,
              title: completeEntry.settings.title,
              hasTemplate: !!completeEntry.settings.settingsTemplate,
            })}`,
          );
        } else {
          this.logger.warn(`No settings found for entry ${savedEntry.id} despite creating them. Will be handled in mapEntryToResponseDto.`);

          // Try to get the settingsTemplateId directly from the database
          this.dataSource
            .query(`SELECT settings_template_id FROM diary_entry_settings WHERE diary_entry_id = $1`, [savedEntry.id])
            .then((result) => {
              if (result && result.length > 0 && result[0].settings_template_id) {
                const templateId = result[0].settings_template_id;
                this.logger.log(`Found settingsTemplateId ${templateId} in database for entry ${savedEntry.id}`);

                // Try to load the template
                this.diarySettingsService
                  .getDiarySettingsTemplateById(templateId)
                  .then((template) => {
                    if (template) {
                      this.logger.log(`Successfully loaded template: ${template.title} with ID ${template.id}`);

                      // Create settings object
                      completeEntry.settings = {
                        id: result[0].id || 'placeholder',
                        diaryEntryId: savedEntry.id,
                        settingsTemplateId: templateId,
                        title: template.title,
                        level: template.level,
                        wordLimit: template.wordLimit,
                        createdAt: new Date(),
                        updatedAt: new Date(),
                        settingsTemplate: {
                          id: template.id,
                          title: template.title,
                          level: template.level,
                          wordLimit: template.wordLimit,
                          description: template.description,
                          isActive: template.isActive,
                          createdAt: template.createdAt,
                          updatedAt: template.updatedAt,
                        },
                      } as DiaryEntrySettings;
                    }
                  })
                  .catch((error) => {
                    this.logger.error(`Error loading settings template: ${error.message}`);
                  });
              }
            })
            .catch((error) => {
              this.logger.error(`Error checking settingsTemplateId in database: ${error.message}`);
            });
        }

        if (!completeEntry) {
          throw new Error(`Failed to retrieve newly created diary entry with ID ${savedEntry.id}`);
        }

        // Log the entry and its skin for debugging
        this.logger.log(`Created new diary entry with ID ${completeEntry.id} and skin ID ${completeEntry.skinId}`);

        // Ensure the skin ID is correctly set in the database and entity
        if (completeEntry.skinId !== finalSkinId) {
          this.logger.warn(`Skin ID mismatch after loading complete entry: entity has ${completeEntry.skinId} but should be ${finalSkinId}`);

          // Update the entity's skinId
          completeEntry.skinId = finalSkinId;

          // Force update the skin ID with a direct query
          await this.dataSource.query(`UPDATE diary_entry SET skin_id = $1 WHERE id = $2`, [finalSkinId, completeEntry.id]);

          this.logger.log(`Forced update of skin ID to ${finalSkinId} for entry ${completeEntry.id}`);
        }

        // IMPORTANT: Force load the skin with the finalSkinId to ensure consistency
        this.logger.log(`Force loading skin with ID ${finalSkinId} for response`);

        // Use our improved loadSkinData method to get the skin
        const responseSkin = await this.loadSkinData(finalSkinId, userId);

        if (responseSkin) {
          this.logger.log(`Successfully loaded skin for response: ${responseSkin.name} with ID ${responseSkin.id}`);

          // Set the skin on the entry
          completeEntry.skin = responseSkin;

          // Also update the skinId to ensure consistency
          completeEntry.skinId = finalSkinId;

          // Log the final skin data
          this.logger.log(
            `Final skin data for response: ${JSON.stringify({
              id: responseSkin.id,
              name: responseSkin.name,
              isGlobal: responseSkin.isGlobal,
            })}`,
          );
        } else {
          this.logger.error(`Failed to load skin with ID ${finalSkinId} for response`);

          // If we couldn't load the skin, create a minimal skin object with the correct ID
          this.logger.log(`Creating minimal skin object with ID ${finalSkinId}`);

          completeEntry.skin = {
            id: finalSkinId,
            name: 'Unknown Skin',
            description: 'Skin details could not be loaded',
            previewImagePath: null,
            isActive: true,
            isGlobal: true,
            templateContent: '<div>Default template content</div>',
            createdById: null,
            createdAt: new Date(),
            updatedAt: new Date(),
          } as DiarySkin;

          // Add the isUsedIn property
          (completeEntry.skin as any).isUsedIn = true;

          // Also update the skinId to ensure consistency
          completeEntry.skinId = finalSkinId;
        }

        // Log the settings information for debugging before returning the response
        if (completeEntry.settings) {
          this.logger.log(
            `Final settings for entry ${completeEntry.id} before response: ${JSON.stringify({
              id: completeEntry.settings.id,
              settingsTemplateId: completeEntry.settings.settingsTemplateId,
              title: completeEntry.settings.title,
              hasTemplate: !!completeEntry.settings.settingsTemplate,
            })}`,
          );

          // If settingsTemplate is not loaded but settingsTemplateId exists, try to load it
          if (!completeEntry.settings.settingsTemplate && completeEntry.settings.settingsTemplateId) {
            this.logger.log(`Settings template not loaded, trying to load template with ID ${completeEntry.settings.settingsTemplateId}`);
            try {
              const template = await this.diarySettingsService.getDiarySettingsTemplateById(completeEntry.settings.settingsTemplateId);
              if (template) {
                this.logger.log(`Successfully loaded template: ${template.title} with ID ${template.id}`);
                // Create a settingsTemplate object with the required properties
                completeEntry.settings.settingsTemplate = {
                  id: template.id,
                  title: template.title,
                  level: template.level,
                  wordLimit: template.wordLimit,
                  description: template.description,
                  isActive: template.isActive,
                  createdAt: template.createdAt,
                  updatedAt: template.updatedAt,
                } as any;
              }
            } catch (error) {
              this.logger.error(`Error loading settings template: ${error.message}`);
            }
          }
        } else {
          this.logger.warn(`No settings found for entry ${completeEntry.id} before response`);
        }

        // After creating the entry and settings, upsert attendance (status will be absent by default for NEW)
        await this.upsertAttendanceForEntry(completeEntry, userId, completeEntry.entryDate, completeEntry.content, completeEntry.settings?.wordLimit, queryRunner);

        return this.mapEntryToResponseDto(completeEntry);
      } catch (error) {
        // Rollback the transaction in case of error
        await queryRunner.rollbackTransaction();
        throw error;
      } finally {
        // Release the query runner
        await queryRunner.release();
      }
    } catch (error) {
      this.logger.error(`Error creating diary entry: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get a diary entry by ID
   * @param id The ID of the diary entry
   * @param userId The ID of the user requesting the entry
   * @returns The diary entry
   */
  async getDiaryEntry(id: string, userId: string): Promise<DiaryEntryResponseDto> {
    try {
      const entry = await this.diaryEntryRepository
        .createQueryBuilder('entry')
        .leftJoinAndSelect('entry.diary', 'diary')
        .leftJoinAndSelect('diary.user', 'user')
        .leftJoinAndSelect('entry.skin', 'skin')
        .leftJoinAndSelect('entry.feedbacks', 'feedbacks')
        .leftJoinAndSelect('feedbacks.tutor', 'tutor')
        .leftJoinAndSelect('entry.settings', 'settings')
        .leftJoinAndSelect('settings.settingsTemplate', 'settingsTemplate')
        .leftJoinAndSelect('entry.correction', 'correction')
        .addSelect(['diary.tutorGreeting']) // Explicitly select tutorGreeting field
        .leftJoinAndSelect('entry.originalReviewedVersion', 'originalReviewedVersion')
        .leftJoinAndSelect('correction.tutor', 'correctionTutor')
        .leftJoinAndSelect('entry.likes', 'likes')
        .where('entry.id = :id', { id })
        .getOne();

      if (!entry) {
        throw new NotFoundException(`Diary entry with ID ${id} not found`);
      }

      // Check if the user is the owner of the diary or a tutor
      const user = await this.userRepository.findOne({
        where: { id: userId },
        relations: ['userRoles', 'userRoles.role'],
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      const isOwner = entry.diary.userId === userId;
      const isTutor = user.userRoles.some((userRole) => userRole.role.name === 'tutor');
      const isAdmin = user.userRoles.some((userRole) => userRole.role.name === 'admin');
      const isStudent = user.userRoles.some((userRole) => userRole.role.name === 'student'); // If the user is the owner, a tutor, or an admin, they can view the entry
      if (isOwner || isTutor || isAdmin) {
        return this.mapEntryToResponseDto(entry, userId);
      }

      // If the entry is public, anyone can view it
      if (entry.visibility === DiaryVisibility.PUBLIC) {
        return this.mapEntryToResponseDto(entry, userId);
      }

      // If the entry is friends-only, check if the user is a friend with diary viewing permission
      if (entry.visibility === DiaryVisibility.FRIENDS_ONLY && isStudent) {
        // Check if there's a friendship with canViewDiary=true
        const friendship = await this.studentFriendshipRepository.findOne({
          where: [
            { requesterId: userId, requestedId: entry.diary.userId, status: FriendshipStatus.ACCEPTED, canViewDiary: true },
            { requesterId: entry.diary.userId, requestedId: userId, status: FriendshipStatus.ACCEPTED, canViewDiary: true },
          ],
        });

        if (friendship) {
          return this.mapEntryToResponseDto(entry, userId);
        }
      }

      // ENHANCEMENT: Check if this entry was specifically shared with the user via friend sharing
      if (isStudent) {
        const friendShare = await this.diaryFriendShareRepository.findOne({
          where: [
            { diaryEntryId: id, sharedWithId: userId, isActive: true },
            { diaryEntryId: id, sharedById: userId, isActive: true }, // Allow sharer to view their own shared entries
          ],
        });

        if (friendShare) {
          // Verify that there's still a valid friendship between the users
          const shareUserId = friendShare.sharedById === userId ? friendShare.sharedWithId : friendShare.sharedById;
          const friendship = await this.studentFriendshipRepository.findOne({
            where: [
              { requesterId: userId, requestedId: shareUserId, status: FriendshipStatus.ACCEPTED },
              { requesterId: shareUserId, requestedId: userId, status: FriendshipStatus.ACCEPTED },
            ],
          });

          if (friendship) {
            return this.mapEntryToResponseDto(entry, userId);
          }
        }
      }

      // If we get here, the user doesn't have permission to view the entry
      throw new ForbiddenException('You do not have permission to view this diary entry');
    } catch (error) {
      this.logger.error(`Error getting diary entry: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update a diary entry
   * @param id The ID of the diary entry
   * @param userId The ID of the user updating the entry
   * @param updateDiaryEntryDto Data for updating the diary entry
   * @param request Optional request object for metadata capture
   * @returns The updated diary entry
   */
  async updateDiaryEntry(id: string, userId: string, updateDiaryEntryDto: UpdateDiaryEntryDto, request?: any): Promise<DiaryEntryResponseDto> {
    // Check if user has an active plan before allowing updates
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['userPlans'],
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    const hasActivePlan = user.userPlans && user.userPlans.some((plan) => plan.isActive);
    if (!hasActivePlan) {
      throw new ForbiddenException('You need an active subscription to update diary entries');
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Get the entry with settings for validation
      const entry = await this.diaryEntryRepository.findOne({
        where: { id },
        relations: ['diary', 'skin', 'likes', 'settings', 'settings.settingsTemplate'],
      });

      // Validate settings consistency if settings exist
      if (entry?.settings?.settingsTemplate) {
        await this.validateAndFixSettingsConsistency(entry.settings);
      }

      if (!entry) {
        throw new NotFoundException(`Diary entry with ID ${id} not found`);
      }

      // Check if the user is the owner of the diary
      if (entry.diary.userId !== userId) {
        throw new ForbiddenException('You do not have permission to update this diary entry');
      }

      // Validate word count against existing settings - only check maximum for update (drafting behavior)
      if (updateDiaryEntryDto.content && entry.settings) {
        const wordCount = this.calculateWordCount(updateDiaryEntryDto.content);

        // Check maximum word limit only - update API should allow drafts below minimum
        if (entry.settings.wordLimit && wordCount > entry.settings.wordLimit) {
          throw new BadRequestException(`Content exceeds word limit of ${entry.settings.wordLimit} words for stage ${entry.settings.level} (current: ${wordCount} words)`);
        }
      }

      // Store old data for version history
      const oldData = {
        title: entry.title,
        content: entry.content,
      };

      // NEW REQUIREMENT: Update API should NOT create versions - this is "save as draft"
      // Only update the main entry content, no version history creation
      this.logger.log(`Updating diary entry ${id} as draft (no version created)`);

      // Update the entry (EXISTING LOGIC but NO version tracking)
      if (updateDiaryEntryDto.title !== undefined) {
        entry.title = updateDiaryEntryDto.title;
      }
      if (updateDiaryEntryDto.content !== undefined) {
        entry.content = updateDiaryEntryDto.content;
      }
      if (updateDiaryEntryDto.skinId !== undefined) {
        this.logger.log(`Updating skin ID for diary entry ${id} to ${updateDiaryEntryDto.skinId}`);

        // Use the loadSkinData helper method to verify the skin exists and load it properly
        const skinObject = await this.loadSkinData(updateDiaryEntryDto.skinId, userId);

        if (!skinObject) {
          throw new NotFoundException(`Diary skin with ID ${updateDiaryEntryDto.skinId} not found in either global or user-specific skins`);
        }

        this.logger.log(`Found skin: ${skinObject.name} with ID ${skinObject.id}, isGlobal: ${skinObject.isGlobal}`);

        // Update the entry's skinId
        entry.skinId = updateDiaryEntryDto.skinId;

        // Also update the skin relation for proper response mapping
        entry.skin = skinObject;
      }
      if (updateDiaryEntryDto.backgroundColor !== undefined) {
        entry.backgroundColor = updateDiaryEntryDto.backgroundColor;
      }
      if (updateDiaryEntryDto.isPrivate !== undefined) {
        entry.isPrivate = updateDiaryEntryDto.isPrivate;
      }
      if (updateDiaryEntryDto.decoration !== undefined) {
        entry.decoration = updateDiaryEntryDto.decoration;
      }

      // NEW REQUIREMENT: Mark as draft and update word count
      entry.isDraft = true;
      if (updateDiaryEntryDto.content !== undefined) {
        entry.wordCount = this.calculateWordCount(updateDiaryEntryDto.content);
      }

      // Handle settingsTemplateId if provided
      if (updateDiaryEntryDto.settingsTemplateId) {
        this.logger.log(`Updating settingsTemplateId for diary entry ${id} to ${updateDiaryEntryDto.settingsTemplateId}`);

        // Validate that the settings template exists
        const template = await this.diarySettingsService.getDiarySettingsTemplateById(updateDiaryEntryDto.settingsTemplateId);
        if (!template) {
          throw new NotFoundException(`Diary settings template with ID ${updateDiaryEntryDto.settingsTemplateId} not found`);
        }

        // Get the entry settings
        const settings = await this.diaryEntrySettingsRepository.findOne({
          where: { diaryEntryId: id },
        });

        if (settings) {
          // Update existing settings
          this.logger.log(`Updating existing settings for entry ${id} with template ID ${template.id}`);
          settings.settingsTemplateId = template.id;
          settings.title = template.title;
          settings.level = template.level;
          settings.wordLimit = template.wordLimit;
          await queryRunner.manager.save(settings);

          // Log the updated settings
          this.logger.log(
            `Updated settings: ${JSON.stringify({
              id: settings.id,
              settingsTemplateId: settings.settingsTemplateId,
              title: settings.title,
            })}`,
          );
        } else {
          // Create new settings
          this.logger.log(`Creating new settings for entry ${id} with template ID ${template.id}`);
          const newSettings = this.diaryEntrySettingsRepository.create({
            diaryEntryId: id,
            settingsTemplateId: template.id,
            title: template.title,
            level: template.level,
            wordLimit: template.wordLimit,
          });
          const savedSettings = await queryRunner.manager.save(newSettings);

          // Log the created settings
          this.logger.log(
            `Created new settings: ${JSON.stringify({
              id: savedSettings.id,
              settingsTemplateId: savedSettings.settingsTemplateId,
              title: savedSettings.title,
            })}`,
          );
        }
      }

      // Save the entry with version tracking
      const updatedEntry = await queryRunner.manager.save(entry);

      await queryRunner.commitTransaction();

      // Get the complete entry with all relations using query builder to ensure tutorGreeting is loaded
      const completeEntry = await this.diaryEntryRepository
        .createQueryBuilder('entry')
        .where('entry.id = :id', { id: updatedEntry.id })
        .leftJoinAndSelect('entry.skin', 'skin')
        .leftJoinAndSelect('entry.feedbacks', 'feedbacks')
        .leftJoinAndSelect('feedbacks.tutor', 'feedbackTutor')
        .leftJoinAndSelect('entry.diary', 'diary')
        .leftJoinAndSelect('diary.user', 'user')
        .leftJoinAndSelect('entry.settings', 'settings')
        .leftJoinAndSelect('settings.settingsTemplate', 'settingsTemplate')
        .leftJoinAndSelect('entry.correction', 'correction')
        .leftJoinAndSelect('correction.tutor', 'correctionTutor')
        .leftJoinAndSelect('entry.originalReviewedVersion', 'originalReviewedVersion')
        .addSelect(['diary.tutorGreeting']) // ✅ Explicitly select tutorGreeting field
        .getOne();

      // Debug logging for hasGreeting in update method
      this.logger.log(`Update - Entry ${completeEntry.id}: diary exists: ${!!completeEntry.diary}, tutorGreeting: ${completeEntry.diary?.tutorGreeting}, hasGreeting: ${!!completeEntry.diary?.tutorGreeting}`);

      // Log the settings information for debugging
      if (completeEntry.settings) {
        this.logger.log(
          `Retrieved settings for entry ${completeEntry.id}: ${JSON.stringify({
            id: completeEntry.settings.id,
            settingsTemplateId: completeEntry.settings.settingsTemplateId,
            title: completeEntry.settings.title,
            hasTemplate: !!completeEntry.settings.settingsTemplate,
          })}`,
        );

        // If settingsTemplate is not loaded but settingsTemplateId exists, try to load it
        if (!completeEntry.settings.settingsTemplate && completeEntry.settings.settingsTemplateId) {
          this.logger.log(`Settings template not loaded, trying to load template with ID ${completeEntry.settings.settingsTemplateId}`);
          try {
            const template = await this.diarySettingsService.getDiarySettingsTemplateById(completeEntry.settings.settingsTemplateId);
            if (template) {
              this.logger.log(`Successfully loaded template: ${template.title} with ID ${template.id}`);
              // Create a settingsTemplate object with the required properties
              completeEntry.settings.settingsTemplate = {
                id: template.id,
                title: template.title,
                level: template.level,
                wordLimit: template.wordLimit,
                description: template.description,
                isActive: template.isActive,
                createdAt: template.createdAt,
                updatedAt: template.updatedAt,
              } as any;
            }
          } catch (error) {
            this.logger.error(`Error loading settings template: ${error.message}`);
          }
        }
      } else {
        this.logger.warn(`No settings found for entry ${completeEntry.id} after update`);
      }

      // NEW REQUIREMENT: No notifications for subsequent updates - only log the update
      this.logger.log(`Diary entry ${id} updated by student ${userId}. Status: ${entry.status}. No notification sent per new requirements.`);

      // Log the complete entry and its skin for debugging
      if (completeEntry.skin) {
        this.logger.log(`Returning updated entry ${completeEntry.id} with skin ${completeEntry.skin.name} (ID: ${completeEntry.skin.id})`);
      } else {
        this.logger.warn(`Returning updated entry ${completeEntry.id} but skin relation is not loaded properly`);

        // If the skin relation is not loaded properly, try to load it manually using our helper method
        if (updateDiaryEntryDto.skinId) {
          const skinObject = await this.loadSkinData(updateDiaryEntryDto.skinId, userId);
          if (skinObject) {
            this.logger.log(`Manually loaded skin: ${skinObject.name} with ID ${skinObject.id}`);
            completeEntry.skin = skinObject;
          }
        } else if (completeEntry.skinId) {
          const skinObject = await this.loadSkinData(completeEntry.skinId, userId);
          if (skinObject) {
            this.logger.log(`Manually loaded skin: ${skinObject.name} with ID ${skinObject.id}`);
            completeEntry.skin = skinObject;
          }
        }
      }

      // After updating the entry, upsert attendance (re-evaluate status if content/word count changed)
      await this.upsertAttendanceForEntry(completeEntry, userId, completeEntry.entryDate, completeEntry.content, completeEntry.settings?.wordLimit);

      return this.mapEntryToResponseDto(completeEntry);
    } catch (error) {
      // Rollback the transaction in case of error
      await queryRunner.rollbackTransaction();
      this.logger.error(`Error updating diary entry: ${error.message}`, error.stack);
      throw error;
    } finally {
      // Release the query runner
      await queryRunner.release();
    }
  }

  /**
   * Update diary entry settings (settings template) - OPTIMIZED VERSION
   * Validates content length to prevent downgrades and ensures proper response
   */
  async updateDiaryEntrySettings(entryId: string, userId: string, updateSettingsDto: UpdateDiaryEntrySettingsDto): Promise<DiaryEntryResponseDto> {
    // 🚀 OPTIMIZATION 1: Use single optimized query to get entry, settings, and template data

    const entryData = await this.dataSource.query(`
      SELECT
        e.id, e.title, e.content, e.entry_date, e.status, e.skin_id, e.background_color,
        e.decoration, e.is_private, e.created_at, e.updated_at, e.word_count, e.is_draft,
        e.review_start_time, e.review_expiry_time, e.reviewing_tutor_id, e.score,
        e.evaluated_at, e.evaluated_by, e.thanks_message, e.diary_id,
        d.id as diary_id, d.user_id as diary_user_id, d.tutor_greeting,
        u.name as user_name,
        s.id as settings_id, s.settings_template_id, s.title as settings_title,
        s.level as settings_level, s.word_limit as settings_word_limit,
        s.min_word_limit as settings_min_word_limit,
        st.title as template_title, st.level as template_level,
        st.word_limit as template_word_limit, st.min_word_limit as template_min_word_limit,
        st.is_active as template_is_active
      FROM diary_entry e
      LEFT JOIN diary d ON e.diary_id = d.id
      LEFT JOIN "user" u ON d.user_id = u.id
      LEFT JOIN diary_entry_settings s ON e.id = s.diary_entry_id
      LEFT JOIN diary_settings_template st ON s.settings_template_id = st.id
      WHERE e.id = $1
    `, [entryId]);

    if (!entryData || entryData.length === 0) {
      throw new NotFoundException('Diary entry not found');
    }

    const entry = entryData[0];

    // Additional validation: ensure diary and user data are present
    if (!entry.diary_id || !entry.diary_user_id) {
      this.logger.error(`Diary entry ${entryId} has missing diary relationship data`);
      throw new NotFoundException('Diary entry not found or has invalid diary relationship');
    }

    // OPTIMIZATION 2: Early permission check without loading unnecessary data
    if (entry.diary_user_id !== userId) {
      throw new ForbiddenException('You do not have permission to update this diary entry');
    }

    // OPTIMIZATION 3: Check if template is already set (early exit optimization)
    if (entry.settings_template_id === updateSettingsDto.settingsTemplateId) {
      this.logger.log(`Settings template already set to ${updateSettingsDto.settingsTemplateId}, returning current entry`);
      // Convert raw data to entity-like object for existing mapper
      const mockEntry = this.convertRawDataToEntry(entry);
      return this.mapEntryToResponseDto(mockEntry);
    }

    // OPTIMIZATION 4: Single query to get and validate new template
    const newTemplateData = await this.dataSource.query(`
      SELECT id, title, level, word_limit, min_word_limit, is_active
      FROM diary_settings_template
      WHERE id = $1
    `, [updateSettingsDto.settingsTemplateId]);

    if (!newTemplateData || newTemplateData.length === 0) {
      throw new NotFoundException(`Diary settings template with ID ${updateSettingsDto.settingsTemplateId} not found`);
    }

    const newTemplate = newTemplateData[0];

    if (!newTemplate.is_active) {
      throw new BadRequestException(`Settings template "${newTemplate.title}" is not active and cannot be used`);
    }

    //  OPTIMIZATION 5: Efficient word count validation
    const currentWordCount = this.calculateWordCount(entry.content || '');

    if (newTemplate.word_limit && currentWordCount > newTemplate.word_limit) {
      throw new BadRequestException(
        `Cannot change to "${newTemplate.title}" template. Current content has ${currentWordCount} words, but the new template only allows ${newTemplate.word_limit} words. Please reduce your content first or choose a template with a higher word limit.`
      );
    }

    //  OPTIMIZATION 6: Single atomic update query instead of transaction
    if (entry.settings_id) {
      // Update existing settings
      await this.dataSource.query(`
        UPDATE diary_entry_settings
        SET settings_template_id = $1, title = $2, level = $3, word_limit = $4, min_word_limit = $5, updated_at = NOW()
        WHERE diary_entry_id = $6
      `, [
        updateSettingsDto.settingsTemplateId,
        newTemplate.title,
        newTemplate.level,
        newTemplate.word_limit,
        newTemplate.min_word_limit,
        entryId
      ]);
    } else {
      // Create new settings
      await this.dataSource.query(`
        INSERT INTO diary_entry_settings (diary_entry_id, settings_template_id, title, level, word_limit, min_word_limit, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
      `, [
        entryId,
        updateSettingsDto.settingsTemplateId,
        newTemplate.title,
        newTemplate.level,
        newTemplate.word_limit,
        newTemplate.min_word_limit
      ]);
    }

    // OPTIMIZATION 7: Get updated entry data with single optimized query
    const updatedEntryData = await this.dataSource.query(`
      SELECT
        e.id, e.title, e.content, e.entry_date, e.status, e.skin_id, e.background_color,
        e.decoration, e.is_private, e.created_at, e.updated_at, e.word_count, e.is_draft,
        e.review_start_time, e.review_expiry_time, e.reviewing_tutor_id, e.score,
        e.evaluated_at, e.evaluated_by, e.thanks_message,
        d.id as diary_id, d.user_id as diary_user_id, d.tutor_greeting,
        u.name as user_name,
        s.id as settings_id, s.settings_template_id, s.title as settings_title,
        s.level as settings_level, s.word_limit as settings_word_limit,
        s.min_word_limit as settings_min_word_limit,
        st.title as template_title, st.level as template_level,
        st.word_limit as template_word_limit, st.min_word_limit as template_min_word_limit,
        st.is_active as template_is_active
      FROM diary_entry e
      LEFT JOIN diary d ON e.diary_id = d.id
      LEFT JOIN "user" u ON d.user_id = u.id
      LEFT JOIN diary_entry_settings s ON e.id = s.diary_entry_id
      LEFT JOIN diary_settings_template st ON s.settings_template_id = st.id
      WHERE e.id = $1
    `, [entryId]);

    if (!updatedEntryData || updatedEntryData.length === 0) {
      throw new Error(`Failed to retrieve updated diary entry with ID ${entryId}`);
    }

    // 🚀 OPTIMIZATION 8: Minimal logging for performance
    this.logger.log(`Settings updated: ${entry.settings_title || 'None'} → ${newTemplate.title} (${newTemplate.word_limit} words)`);

    // Convert raw data to entity-like object for existing mapper
    const mockUpdatedEntry = this.convertRawDataToEntry(updatedEntryData[0]);
    return this.mapEntryToResponseDto(mockUpdatedEntry);
  }

  
  private convertRawDataToEntry(rawData: any): any {
    const mockEntry = {
      id: rawData.id,
      title: rawData.title,
      content: rawData.content,
      entryDate: new Date(rawData.entry_date),
      status: rawData.status,
      skinId: rawData.skin_id,
      backgroundColor: rawData.background_color,
      decoration: rawData.decoration,
      isPrivate: rawData.is_private,
      createdAt: new Date(rawData.created_at),
      updatedAt: new Date(rawData.updated_at),
      wordCount: rawData.word_count,
      isDraft: rawData.is_draft,
      reviewStartTime: rawData.review_start_time ? new Date(rawData.review_start_time) : null,
      reviewExpiryTime: rawData.review_expiry_time ? new Date(rawData.review_expiry_time) : null,
      reviewingTutorId: rawData.reviewing_tutor_id,
      score: rawData.score,
      evaluatedAt: rawData.evaluated_at ? new Date(rawData.evaluated_at) : null,
      evaluatedBy: rawData.evaluated_by,
      thanksMessage: rawData.thanks_message,
      diary: {
        id: rawData.diary_id,
        userId: rawData.diary_user_id,
        tutorGreeting: rawData.tutor_greeting,
        user: {
          id: rawData.diary_user_id,
          name: rawData.user_name,
        },
      },
      settings: rawData.settings_id ? {
        id: rawData.settings_id,
        settingsTemplateId: rawData.settings_template_id,
        title: rawData.settings_title,
        level: rawData.settings_level,
        wordLimit: rawData.settings_word_limit,
        minWordLimit: rawData.settings_min_word_limit,
        settingsTemplate: {
          id: rawData.settings_template_id,
          title: rawData.template_title,
          level: rawData.template_level,
          wordLimit: rawData.template_word_limit,
          minWordLimit: rawData.template_min_word_limit,
          isActive: rawData.template_is_active,
        },
      } : null,
      feedbacks: [], // Empty for performance - not needed for settings update
      correction: null, // Not needed for settings update
      skin: null, // Not needed for settings update
      likes: [], // Empty for performance - not needed for settings update
    };

    return mockEntry;
  }

  /**
   * Fix missing original version for a specific entry
   */
  async fixMissingOriginalVersionForEntry(entryId: string): Promise<any> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Get the diary entry with correction
      const entry = await this.diaryEntryRepository.findOne({
        where: { id: entryId },
        relations: ['correction', 'diary'],
      });

      if (!entry) {
        throw new NotFoundException(`Diary entry with ID ${entryId} not found`);
      }

      if (!entry.correction) {
        throw new BadRequestException('Entry has no correction, cannot create original version');
      }

      if (entry.originalReviewedVersionId) {
        throw new BadRequestException('Entry already has an original reviewed version');
      }

      // Calculate word count for the current content
      const wordCount = this.calculateWordCount(entry.content || '');

      // Create original reviewed version
      const originalVersion = new DiaryEntryHistory();
      originalVersion.diaryEntryId = entryId;
      originalVersion.title = entry.title || '';
      originalVersion.content = entry.content || '';
      originalVersion.versionNumber = 1;
      originalVersion.isLatest = false;
      originalVersion.wordCount = wordCount;
      originalVersion.createdAt = entry.correction.createdAt; // Use correction date
      originalVersion.createdBy = entry.diary.userId;
      originalVersion.metaData = {
        updateTrigger: 'submit' as const,
        significantChange: true,
      };

      const savedVersion = await queryRunner.manager.save(originalVersion);

      // Update the entry to reference this as the original reviewed version
      entry.originalReviewedVersionId = savedVersion.id;
      // totalEditHistory is now calculated from actual history records, no need to update counter
      await queryRunner.manager.save(entry);

      await queryRunner.commitTransaction();

      return {
        entryId,
        originalVersionId: savedVersion.id,
        message: 'Original reviewed version created successfully',
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Error fixing original version for entry ${entryId}: ${error.message}`, error.stack);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Calculate word count
   */
  private calculateWordCount(content: string): number {
    if (!content || content.trim().length === 0) {
      return 0;
    }
    return content.trim().split(/\s+/).length;
  }

  /**
   * Get diary entry version history
   */
  async getDiaryEntryHistory(entryId: string, userId: string): Promise<DiaryEntryHistoryResponseDto> {
    return this.diaryEntryHistoryService.getVersionHistory(entryId, userId);
  }

  /**
   * Get a specific diary entry version
   */
  async getDiaryEntryVersion(versionId: string, userId: string): Promise<DiaryEntryVersionDto> {
    return this.diaryEntryHistoryService.getVersion(versionId, userId);
  }

  /**
   * Restore a previous version as the current version
   */
  async restoreDiaryEntryVersion(entryId: string, versionId: string, userId: string): Promise<DiaryEntryResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Get the entry to verify ownership and settings
      const entry = await this.diaryEntryRepository.findOne({
        where: { id: entryId },
        relations: ['diary', 'settings', 'settings.settingsTemplate'],
      });

      if (!entry) {
        throw new NotFoundException(`Diary entry with ID ${entryId} not found`);
      }

      if (entry.diary.userId !== userId) {
        throw new ForbiddenException('You do not have permission to restore versions for this diary entry');
      }

      // Get the version to restore
      const versionToRestore = await this.diaryEntryHistoryRepository.findOne({
        where: { id: versionId, diaryEntryId: entryId },
      });

      if (!versionToRestore) {
        throw new NotFoundException(`Version with ID ${versionId} not found for this diary entry`);
      }

      // Validate restored content against current settings
      if (entry.settings?.wordLimit) {
        const wordCount = this.calculateWordCount(versionToRestore.content);
        if (wordCount > entry.settings.wordLimit) {
          throw new BadRequestException(`Restored content exceeds current word limit of ${entry.settings.wordLimit} words for stage ${entry.settings.level}`);
        }
      }

      // Set the version as latest using the history service
      await this.diaryEntryHistoryService.setLatestVersion(entryId, versionId, userId);

      await queryRunner.commitTransaction();

      // Get the updated entry for response
      const updatedEntry = await this.diaryEntryRepository.findOne({
        where: { id: entryId },
        relations: ['diary', 'skin', 'likes', 'settings', 'feedbacks', 'correction', 'currentVersion'],
      });

      return this.mapEntryToResponseDto(updatedEntry);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Error restoring version ${versionId} for diary entry ${entryId}: ${error.message}`, error.stack);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Submit a diary entry for review
   * @param id The ID of the diary entry
   * @param userId The ID of the user submitting the entry
   * @param submitDto Optional submission data
   * @returns The submitted diary entry
   **/
  async submitDiaryEntry(id: string, userId: string, submitDto?: SubmitDiaryEntryDto): Promise<DiaryEntryResponseDto> {
    try {
      // Optimized: Get entry with minimal relations and validate ownership in one query
      const entry = await this.diaryEntryRepository.findOne({
        where: { id, diary: { userId } }, // Validate ownership in the query
        relations: ['diary', 'settings'],
        select: {
          id: true,
          title: true,
          content: true,
          status: true,
          wordCount: true,
          isDraft: true,
          canSubmitNewVersion: true,
          submittedVersionCount: true,
          isResubmission: true,
          resubmissionType: true,
          previousReviewCount: true,
          backgroundColor: true,
          decoration: true,
          isPrivate: true,
          diary: {
            id: true,
            userId: true,
          },
          settings: {
            id: true,
            wordLimit: true,
            settingsTemplateId: true,
          },
        },
      });

      if (!entry) {
        throw new NotFoundException(`Diary entry with ID ${id} not found or you don't have permission to access it`);
      }

      // NEW REQUIREMENT: Check if user can submit a new version
      // Allow submissions after review or if canSubmitNewVersion is true
      // Also allow resubmissions (entries that have been reviewed before)
      const hasBeenReviewed = entry.lastReviewedAt || entry.previousReviewCount > 0;
      const canSubmit =
        entry.canSubmitNewVersion ||
        entry.status === DiaryEntryStatus.CONFIRM || // Legacy confirmed entries
        entry.status === DiaryEntryStatus.REVIEWED || // New final state
        hasBeenReviewed; // Allow resubmissions after any previous review

      if (!canSubmit) {
        // Provide more specific error message based on current status
        let errorMessage = 'Cannot submit new version.';

        if (entry.status === DiaryEntryStatus.SUBMIT) {
          errorMessage += ' Previous submission is still pending review by tutor.';
        } else {
          errorMessage += ' Previous submission must be reviewed by tutor first.';
        }

        throw new BadRequestException(errorMessage);
      }

      // Validate that the settings template exists
      const template = await this.diarySettingsService.getDiarySettingsTemplateById(submitDto.settingsTemplateId);
      if (!template) {
        throw new NotFoundException(`Diary settings template with ID ${submitDto.settingsTemplateId} not found`);
      }

      // Validate word count against template settings
      const wordCount = this.calculateWordCount(submitDto.content);
      if (template.minWordLimit && wordCount < template.minWordLimit) {
        throw new BadRequestException(`Entry must contain at least ${template.minWordLimit} words before submission (current: ${wordCount} words)`);
      }
      if (template.wordLimit && wordCount > template.wordLimit) {
        throw new BadRequestException(`Entry cannot exceed ${template.wordLimit} words (current: ${wordCount} words)`);
      }

      // Validate skin if provided
      let skin = null;
      if (submitDto.skinId) {
        skin = await this.diarySkinRepository.findOne({
          where: { id: submitDto.skinId },
        });

        if (!skin) {
          throw new NotFoundException(`Diary skin with ID ${submitDto.skinId} not found`);
        }
      }

      // Prepare submission data outside transaction to minimize lock time
      const submissionData = {
        submissionNumber: entry.submittedVersionCount + 1,
        isResubmission: entry.submittedVersionCount > 0,
        previousStatus: entry.status,
        resubmissionType: null as 'after_review' | null,
        wordCount: this.calculateWordCount(submitDto.content),
        submittedAt: new Date(),
      };

      // Determine resubmission type
      if (submissionData.isResubmission) {
        if (entry.status === DiaryEntryStatus.REVIEWED || entry.status === DiaryEntryStatus.CONFIRM) {
          submissionData.resubmissionType = 'after_review';
        }
      }

      // Get settings template outside transaction
      const settingsTemplate = await this.diarySettingsService.getDiarySettingsTemplateById(entry.settings?.settingsTemplateId);
      if (!settingsTemplate) {
        throw new NotFoundException('Settings template not found');
      }

      // Execute optimized transaction with prepared data
      const result = await TransactionHelper.executeInTransaction(
        this.dataSource,
        async (queryRunner) => {

          // Mark all existing versions as not latest before creating new submission
          await queryRunner.manager.update('diary_entry_history', { diaryEntryId: id }, { isLatest: false });

          // Create submitted version with resubmission tracking
          const submittedVersion = await queryRunner.manager.save(
            queryRunner.manager.create('diary_entry_history', {
              diaryEntryId: id,
              title: submitDto.title,
              content: submitDto.content,
              versionNumber: submissionData.submissionNumber,
              isLatest: true,
              isSubmittedVersion: true,
              submissionNumber: submissionData.submissionNumber,
              submittedAt: submissionData.submittedAt,
              wordCount: submissionData.wordCount,
              isResubmission: submissionData.isResubmission,
              resubmissionType: submissionData.resubmissionType,
              previousStatus: submissionData.previousStatus,
              metaData: {
                submissionType: submissionData.isResubmission ? 'resubmission' : 'initial_submit',
                resubmissionType: submissionData.resubmissionType,
                previousStatus: submissionData.previousStatus,
                browserInfo: 'N/A',
                ipAddress: 'N/A',
              },
            }),
          );

          // Update the entry with all the provided fields
          entry.status = DiaryEntryStatus.SUBMIT;
          entry.title = submitDto.title;
          entry.content = submitDto.content;
          entry.wordCount = submissionData.wordCount;

          // NEW REQUIREMENT: Update submission tracking fields
          entry.isDraft = false;
          entry.lastSubmittedAt = submissionData.submittedAt;
          entry.canSubmitNewVersion = false; // Will be set to true when reviewed
          entry.submittedVersionCount = submissionData.submissionNumber;
          entry.currentSubmittedVersionId = (submittedVersion as any).id;

          // Update resubmission tracking fields
          entry.isResubmission = submissionData.isResubmission;
          entry.resubmissionType = submissionData.resubmissionType;
          if (submissionData.resubmissionType === 'after_review') {
            entry.previousReviewCount = (entry.previousReviewCount || 0) + 1;
          }

          // Update optional fields if provided
          if (skin) {
            entry.skinId = submitDto.skinId;
          }

          if (submitDto.backgroundColor) {
            entry.backgroundColor = submitDto.backgroundColor;
          }

          if (submitDto.isPrivate !== undefined) {
            entry.isPrivate = submitDto.isPrivate;
          }

          // Save the entry first to ensure it exists
          const savedEntry = await queryRunner.manager.save(entry);

          // Update or create settings (settings are now mandatory)
          if (entry.settings) {
            // Update existing settings
            entry.settings.settingsTemplateId = settingsTemplate.id;
            entry.settings.title = settingsTemplate.title;
            entry.settings.level = settingsTemplate.level;
            entry.settings.wordLimit = settingsTemplate.wordLimit;
            entry.settings.minWordLimit = settingsTemplate.minWordLimit;
            await queryRunner.manager.save(entry.settings);
          } else {
            // Create new settings
            const settings = this.diaryEntrySettingsRepository.create({
              diaryEntryId: savedEntry.id,
              settingsTemplateId: settingsTemplate.id,
              title: settingsTemplate.title,
              level: settingsTemplate.level,
              wordLimit: settingsTemplate.wordLimit,
            });
            const savedSettings = await queryRunner.manager.save(settings);
            entry.settings = savedSettings;
          }

          return savedEntry.id; // Return the saved entry ID for further processing
        },
        {
          isolationLevel: 'READ COMMITTED', // Optimal for most submission scenarios
          timeout: 15000, // 15 seconds timeout for submission transactions
          retryOnDeadlock: true, // Retry on deadlock for better reliability
        }
      );

      // Get the complete entry with minimal relations needed for response
      const completeEntry = await this.diaryEntryRepository.findOne({
        where: { id: result },
        relations: ['diary', 'diary.user', 'settings', 'settings.settingsTemplate'],
        select: {
          id: true,
          title: true,
          content: true,
          status: true,
          wordCount: true,
          entryDate: true,
          submittedAt: true,
          isDraft: true,
          backgroundColor: true,
          decoration: true,
          isPrivate: true,
          lastSubmittedAt: true,
          canSubmitNewVersion: true,
          submittedVersionCount: true,
          isResubmission: true,
          resubmissionType: true,
          diary: {
            id: true,
            userId: true,
            tutorGreeting: true, // ✅ Add tutorGreeting field
            user: {
              id: true,
              name: true,
            },
          },
          settings: {
            id: true,
            wordLimit: true,
            settingsTemplateId: true,
            settingsTemplate: {
              id: true,
              title: true,
            },
          },
        },
      });

      // Debug logging for hasGreeting in submit method
      this.logger.log(`Submit - Entry ${completeEntry.id}: diary exists: ${!!completeEntry.diary}, tutorGreeting: ${completeEntry.diary?.tutorGreeting}, hasGreeting: ${!!completeEntry.diary?.tutorGreeting}`);

        // Log the settings information for debugging
        if (completeEntry.settings) {
          this.logger.log(
            `Retrieved settings for submitted entry ${completeEntry.id}: ${JSON.stringify({
              id: completeEntry.settings.id,
              settingsTemplateId: completeEntry.settings.settingsTemplateId,
              title: completeEntry.settings.title,
              hasTemplate: !!completeEntry.settings.settingsTemplate,
            })}`,
          );

          // If settingsTemplate is not loaded but settingsTemplateId exists, try to load it
          if (!completeEntry.settings.settingsTemplate && completeEntry.settings.settingsTemplateId) {
            this.logger.log(`Settings template not loaded, trying to load template with ID ${completeEntry.settings.settingsTemplateId}`);
            try {
              const template = await this.diarySettingsService.getDiarySettingsTemplateById(completeEntry.settings.settingsTemplateId);
              if (template) {
                this.logger.log(`Successfully loaded template: ${template.title} with ID ${template.id}`);
                // Create a settingsTemplate object with the required properties
                completeEntry.settings.settingsTemplate = {
                  id: template.id,
                  title: template.title,
                  level: template.level,
                  wordLimit: template.wordLimit,
                  description: template.description,
                  isActive: template.isActive,
                  createdAt: template.createdAt,
                  updatedAt: template.updatedAt,
                } as any;
              }
            } catch (error) {
              this.logger.error(`Error loading settings template: ${error.message}`);
            }
          }
        } else {
          this.logger.warn(`No settings found for entry ${completeEntry.id} after submission`);
        }

        // NEW REQUIREMENT: Always send notification for submissions (unlimited submissions allowed)
        try {
          const submissionType = submissionData.isResubmission ? `resubmission (${submissionData.resubmissionType})` : 'initial submission';
          this.logger.log(`Sending submission notification for diary entry: ${completeEntry.id} (submission #${submissionData.submissionNumber}, ${submissionType})`);

          // Check if word count meets requirements for attendance
          const wordCount = this.calculateWordCount(completeEntry.content);
          const meetsWordCount = !completeEntry.settings?.wordLimit || wordCount >= completeEntry.settings.wordLimit;

          // Log detailed information for debugging
          this.logger.log(`Sending first submission notification for diary entry: ${completeEntry.id}`);
          this.logger.log(`Settings template ID: ${completeEntry.settings?.settingsTemplateId || 'Not set'}`);

          // Settings should always exist now, but just in case
          if (!completeEntry.settings) {
            this.logger.error(`No settings found for diary entry ${completeEntry.id} despite being mandatory. Creating default settings.`);

            // Get a default template
            const defaultTemplate = await this.diarySettingsService.getDefaultDiarySettingsTemplate();
            if (defaultTemplate) {
              this.logger.log(`Using default template: ${defaultTemplate.id} - ${defaultTemplate.title}`);

              // Create settings with default template
              const settings = this.diaryEntrySettingsRepository.create({
                diaryEntryId: completeEntry.id,
                settingsTemplateId: defaultTemplate.id,
                title: defaultTemplate.title,
                level: defaultTemplate.level,
                wordLimit: defaultTemplate.wordLimit,
              });

              await this.diaryEntrySettingsRepository.save(settings);

              // Update the complete entry with the new settings
              completeEntry.settings = settings;

              // Create a settingsTemplate object with the required properties
              // Use type assertion to avoid TypeScript errors
              completeEntry.settings.settingsTemplate = {
                id: defaultTemplate.id,
                title: defaultTemplate.title,
                level: defaultTemplate.level,
                wordLimit: defaultTemplate.wordLimit,
                description: defaultTemplate.description,
                isActive: defaultTemplate.isActive,
                createdAt: defaultTemplate.createdAt,
                updatedAt: defaultTemplate.updatedAt,
                createdBy: null,
                updatedBy: null,
              } as any;

              this.logger.log(`Created default settings with template ID: ${settings.settingsTemplateId}`);
            } else {
              this.logger.error(`No default template found. Creating a basic template.`);

              // Create a basic template
              const basicTemplate = await this.diarySettingsService.createDiarySettingsTemplate({
                title: 'Basic Level',
                level: 1,
                wordLimit: 100,
                minWordLimit: 51,
                description: 'Basic template created automatically',
                isActive: true,
              });

              // Create settings with the basic template
              const settings = this.diaryEntrySettingsRepository.create({
                diaryEntryId: completeEntry.id,
                settingsTemplateId: basicTemplate.id,
                title: basicTemplate.title,
                level: basicTemplate.level,
                wordLimit: basicTemplate.wordLimit,
                minWordLimit: basicTemplate.minWordLimit,
              });

              await this.diaryEntrySettingsRepository.save(settings);

              // Update the complete entry with the new settings
              completeEntry.settings = settings;

              // Create a settingsTemplate object with the required properties
              completeEntry.settings.settingsTemplate = {
                id: basicTemplate.id,
                title: basicTemplate.title,
                level: basicTemplate.level,
                wordLimit: basicTemplate.wordLimit,
                description: basicTemplate.description,
                isActive: basicTemplate.isActive,
                createdAt: basicTemplate.createdAt,
                updatedAt: basicTemplate.updatedAt,
                createdBy: null,
                updatedBy: null,
              } as any;

              this.logger.log(`Created settings with basic template ID: ${settings.settingsTemplateId}`);
            }
          }

          // Get the diary module feature ID instead of using settings template ID
          const diaryModuleId = await this.getDiaryModuleFeatureId();

          // Find the assigned tutor for this student and the diary module
          const studentTutorMapping = await this.tutorMatchingService.getStudentTutorForModule(userId, diaryModuleId);

          if (!studentTutorMapping) {
            this.logger.warn(`No tutor assigned for student ${userId} and diary module ${diaryModuleId}`);

            // Try to get the student's preferred tutor for consistent assignment
            try {
              const preferredTutor = await this.tutorMatchingService.getOrSelectPreferredTutor(userId);
              this.logger.log(`Using preferred tutor ${preferredTutor.name} (${preferredTutor.id}) for diary submission by student ${userId}`);

              // Auto-assign the preferred tutor
              const assignResult = await this.tutorMatchingService.assignTutor({
                studentId: userId,
                tutorId: preferredTutor.id,
                planFeatureId: diaryModuleId,
                notes: 'Auto-assigned during diary submission using preferred tutor logic',
              });

              if (assignResult) {
                // Update the student-tutor mapping
                const newMapping = await this.tutorMatchingService.getStudentTutorForModule(userId, diaryModuleId);

                if (newMapping) {
                  // Continue with the new mapping
                  // Use a new variable instead of reassigning the constant
                  const updatedMapping = newMapping;

                  // If we have a new mapping, use it for notification
                  if (updatedMapping && updatedMapping.tutorId) {
                    // Get student name
                    const student = completeEntry.diary?.user || (await this.userRepository.findOne({ where: { id: userId } }));

                    // Generate a deep link for the tutor to review the diary entry
                    const reviewLink = this.deeplinkService.getWebLink(DeeplinkType.DIARY_ENTRY, {
                      id: completeEntry.id,
                    });

                    // Create HTML content with the review link
                    const htmlContent = `
                      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
                        <div style="text-align: center; margin-bottom: 20px;">
                          <h2 style="color: #333;">New Diary Submission</h2>
                        </div>
                        <div style="margin-bottom: 20px;">
                          <p>Hello,</p>
                          <p>${student.name} has ${submissionData.isResubmission ? `resubmitted` : 'submitted'} a diary entry for review: <strong>"${completeEntry.title}"</strong></p>
                          ${submissionData.isResubmission ? `<p><em>This is a resubmission ${submissionData.resubmissionType === 'after_review' ? 'after review' : 'after confirmation'} (Submission #${submissionData.submissionNumber})</em></p>` : ''}
                          <p>Please review this diary entry at your earliest convenience.</p>
                          <div style="text-align: center; margin: 30px 0;">
                            <a href="${reviewLink}" style="background-color: #4CAF50; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">Review Diary Entry</a>
                          </div>
                        </div>
                        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
                          <p>This is an automated message from the HEC system.</p>
                          <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
                        </div>
                      </div>
                    `;

                    // Send notification to tutor with all channels
                    const notificationTitle = submissionData.isResubmission ? 'Diary Entry Resubmitted' : 'New Diary Submission';
                    const notificationMessage = submissionData.isResubmission
                      ? `${student.name} has resubmitted a diary entry for review: "${completeEntry.title}" (Submission #${submissionData.submissionNumber}, after review)`
                      : `${student.name} has submitted a diary entry for review: "${completeEntry.title}"`;

                    // Send notification asynchronously to avoid blocking submission
                    this.asyncNotificationHelper.notifyAsync(updatedMapping.tutorId, NotificationType.DIARY_SUBMISSION, notificationTitle, notificationMessage, {
                      relatedEntityId: completeEntry.id,
                      relatedEntityType: 'diary_entry',
                      htmlContent: htmlContent,
                      // Use channel control parameters instead of channels array
                      sendEmail: true,
                      sendPush: true,
                      sendInApp: true,
                      sendMobile: true,
                      sendSms: false,
                      sendRealtime: false,
                    }).catch(error => {
                      this.logger.error(`Failed to send async notification: ${error?.message || 'Unknown error'}`, error?.stack);
                    });

                    // Return early since we've already sent the notification
                    return;
                  }
                }
              }
            } catch (preferredTutorError) {
              this.logger.error(`Failed to assign preferred tutor for student ${userId}: ${preferredTutorError.message}`, preferredTutorError.stack);

              // Fallback to original logic - try to get any available tutor for the diary module
              const availableTutors = await this.tutorMatchingService.getAvailableTutorsForModule(diaryModuleId);

              if (availableTutors && availableTutors.length > 0) {
                // Auto-assign the first available tutor as fallback
                const assignResult = await this.tutorMatchingService.assignTutor({
                  studentId: userId,
                  tutorId: availableTutors[0].id,
                  planFeatureId: diaryModuleId,
                  notes: 'Auto-assigned during diary submission (fallback from preferred tutor failure)',
                });

                if (assignResult) {
                  // Update the student-tutor mapping
                  const newMapping = await this.tutorMatchingService.getStudentTutorForModule(userId, diaryModuleId);

                  if (newMapping) {
                    // Continue with the new mapping
                    const updatedMapping = newMapping;

                    // If we have a new mapping, use it for notification
                    if (updatedMapping && updatedMapping.tutorId) {
                      // Get student name
                      const student = completeEntry.diary?.user || (await this.userRepository.findOne({ where: { id: userId } }));

                      // Generate a deep link for the tutor to review the diary entry
                      const reviewLink = this.deeplinkService.getWebLink(DeeplinkType.DIARY_ENTRY, {
                        id: completeEntry.id,
                      });

                      // Create HTML content with the review link
                      const htmlContent = `
                        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
                          <div style="text-align: center; margin-bottom: 20px;">
                            <h2 style="color: #333;">New Diary Submission</h2>
                          </div>
                          <div style="margin-bottom: 20px;">
                            <p>Hello,</p>
                            <p>${student.name} has submitted a diary entry for review: <strong>"${completeEntry.title}"</strong></p>
                            <p>Please review this diary entry at your earliest convenience.</p>
                            <div style="text-align: center; margin: 30px 0;">
                              <a href="${reviewLink}" style="background-color: #4CAF50; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">Review Diary Entry</a>
                            </div>
                          </div>
                          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
                            <p>This is an automated message from the HEC system.</p>
                            <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
                          </div>
                        </div>
                      `;

                      // Send notification asynchronously to avoid blocking submission
                      this.asyncNotificationHelper.notifyAsync(
                        updatedMapping.tutorId,
                        NotificationType.DIARY_SUBMISSION,
                        'New Diary Submission',
                        `${student.name} has submitted a diary entry for review: "${completeEntry.title}"`,
                        {
                          relatedEntityId: completeEntry.id,
                          relatedEntityType: 'diary_entry',
                          htmlContent: htmlContent,
                          sendEmail: true,
                          sendPush: true,
                          sendInApp: true,
                          sendMobile: true,
                          sendSms: false,
                          sendRealtime: false,
                        },
                      ).catch(error => {
                        this.logger.error(`Failed to send async notification: ${error?.message || 'Unknown error'}`, error?.stack);
                      });

                      // Return early since we've already sent the notification
                      return;
                    }
                  }
                }
              }
            }
          }

          if (studentTutorMapping && studentTutorMapping.tutorId) {
            // Get student name
            const student = completeEntry.diary?.user || (await this.userRepository.findOne({ where: { id: userId } }));

            // Generate a deep link for the tutor to review the diary entry
            const reviewLink = this.deeplinkService.getWebLink(DeeplinkType.DIARY_ENTRY, {
              id: completeEntry.id,
            });

            // Create HTML content with the review link
            const htmlContentWithLink = `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
                <div style="text-align: center; margin-bottom: 20px;">
                  <h2 style="color: #333;">New Diary Submission</h2>
                </div>
                <div style="margin-bottom: 20px;">
                  <p>Hello,</p>
                  <p>${student.name} has submitted a diary entry for review:</p>
                  <p><strong>"${completeEntry.title}"</strong></p>
                  <p>Please review this diary entry at your earliest convenience.</p>
                  <div style="text-align: center; margin: 30px 0;">
                    <a href="${reviewLink}" style="background-color: #4CAF50; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">Review Diary Entry</a>
                  </div>
                </div>
                <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
                  <p>This is an automated message from the HEC system.</p>
                  <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
                </div>
              </div>
            `;

            // Send notification asynchronously to avoid blocking submission
            this.asyncNotificationHelper.notifyAsync(
              studentTutorMapping.tutorId,
              NotificationType.DIARY_SUBMISSION,
              'New Diary Submission',
              `${student.name} has submitted a diary entry for review: "${completeEntry.title}"`,
              {
                relatedEntityId: completeEntry.id,
                relatedEntityType: 'diary_entry',
                htmlContent: htmlContentWithLink,
                // Use channel control parameters instead of channels array
                sendEmail: true,
                sendPush: true,
                sendInApp: true,
                sendMobile: true,
                sendSms: false,
                sendRealtime: false,
              },
            ).catch(error => {
              this.logger.error(`Failed to send async notification: ${error?.message || 'Unknown error'}`, error?.stack);
            });
          } else {
            this.logger.warn(`No assigned tutor found for student ${userId} and module ${completeEntry.settings?.settingsTemplateId}`);
          }
        } catch (error) {
          // Log error but don't fail the submission process
          this.logger.error(`Failed to send notification to tutor: ${error.message}`, error.stack);
        }

        // After submitting the entry, upsert attendance (status will be present/absent based on word count)
        await this.upsertAttendanceForEntry(completeEntry, userId, completeEntry.entryDate, completeEntry.content, completeEntry.settings?.wordLimit);

      return this.mapEntryToResponseDto(completeEntry);
    } catch (error) {
      this.logger.error(`Error submitting diary entry: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * NEW REQUIREMENT: Check if tutor should be notified for this submission
   * Only notify on first submission when word count target is met
   */
  private async shouldNotifyTutorForSubmission(entry: DiaryEntry, userId: string): Promise<boolean> {
    try {
      // Check if already notified for first submission
      if (entry.firstSubmissionNotified) {
        this.logger.log(`Entry ${entry.id} already has first submission notification sent`);
        return false;
      }

      // Check if word count meets the target
      const wordCount = this.calculateWordCount(entry.content);
      const wordLimit = entry.settings?.wordLimit || 50; // Default minimum

      if (wordCount < wordLimit) {
        this.logger.log(`Entry ${entry.id} word count (${wordCount}) does not meet target (${wordLimit})`);
        return false;
      }

      this.logger.log(`Entry ${entry.id} qualifies for first submission notification - word count: ${wordCount}/${wordLimit}`);
      return true;
    } catch (error) {
      this.logger.error(`Error checking notification eligibility for entry ${entry.id}: ${error.message}`);
      return false;
    }
  }

  /**
   * NEW REQUIREMENT: Mark entry as having first submission notification sent
   */
  private async markFirstSubmissionNotified(entryId: string): Promise<void> {
    try {
      await this.diaryEntryRepository.update(entryId, {
        firstSubmissionNotified: true,
      });
      this.logger.log(`Marked entry ${entryId} as first submission notified`);
    } catch (error) {
      this.logger.error(`Error marking first submission notification for entry ${entryId}: ${error.message}`);
    }
  }

  /**
   * Helper method to get or create default settings for a diary entry
   * @param entryId The ID of the diary entry
   * @returns The created or existing settings
   */
  private async getOrCreateDefaultSettings(entryId: string): Promise<DiaryEntrySettings> {
    try {
      // Check if settings already exist
      const existingSettings = await this.diaryEntrySettingsRepository.findOne({
        where: { diaryEntryId: entryId },
      });

      if (existingSettings) {
        this.logger.log(`Settings already exist for entry ${entryId}`);
        return existingSettings;
      }

      // Get the default template
      const defaultTemplate = await this.diarySettingsService.getDefaultDiarySettingsTemplate();

      if (!defaultTemplate) {
        this.logger.warn(`No default template found for entry ${entryId}. Creating a basic one.`);

        // Create a basic template
        const basicTemplate = await this.diarySettingsService.createDiarySettingsTemplate({
          title: 'Basic Level',
          level: 1,
          wordLimit: 100,
          minWordLimit: 51,
          description: 'Basic template created automatically',
          isActive: true,
        });

        // Create settings with the basic template
        const settings = this.diaryEntrySettingsRepository.create({
          diaryEntryId: entryId,
          settingsTemplateId: basicTemplate.id,
          title: basicTemplate.title,
          level: basicTemplate.level,
          wordLimit: basicTemplate.wordLimit,
          minWordLimit: basicTemplate.minWordLimit,
        });

        return await this.diaryEntrySettingsRepository.save(settings);
      }

      // Create settings with the default template
      const settings = this.diaryEntrySettingsRepository.create({
        diaryEntryId: entryId,
        settingsTemplateId: defaultTemplate.id,
        title: defaultTemplate.title,
        level: defaultTemplate.level,
        wordLimit: defaultTemplate.wordLimit,
        minWordLimit: defaultTemplate.minWordLimit,
      });

      return await this.diaryEntrySettingsRepository.save(settings);
    } catch (error) {
      this.logger.error(`Error creating default settings for entry ${entryId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Helper method to load skin data consistently across the application
   * @param skinId The ID of the skin to load
   * @param userId The ID of the user (for student-specific skins)
   * @returns The loaded skin object or null if not found
   */
  private async loadSkinData(skinId: string, userId: string): Promise<DiarySkin | null> {
    this.logger.log(`Loading skin data for skin ID ${skinId} and user ${userId}`);

    if (!skinId) {
      this.logger.warn(`No skin ID provided to loadSkinData`);
      return null;
    }

    try {
      // IMPORTANT: Use direct SQL query to get the skin data to bypass any ORM caching issues
      this.logger.log(`Using direct SQL to get skin with ID ${skinId}`);

      // First try to get the skin from the diary_skin table
      const globalSkinResult = await this.dataSource.query(`SELECT * FROM diary_skin WHERE id = $1 LIMIT 1`, [skinId]);

      if (globalSkinResult && globalSkinResult.length > 0) {
        this.logger.log(`Found global skin with ID ${skinId} in diary_skin table`);

        // Create a skin object from the result
        const skinData = {
          id: globalSkinResult[0].id,
          name: globalSkinResult[0].name,
          description: globalSkinResult[0].description,
          templateContent: globalSkinResult[0].template_content,
          previewImagePath: globalSkinResult[0].preview_image_path,
          isActive: globalSkinResult[0].is_active,
          isGlobal: true,
          createdById: globalSkinResult[0].created_by_id,
          createdAt: globalSkinResult[0].created_at || new Date(),
          updatedAt: globalSkinResult[0].updated_at || new Date(),
        };

        // Cast to DiarySkin and add isUsedIn property
        const skin = skinData as unknown as DiarySkin;
        (skin as any).isUsedIn = true;

        this.logger.log(`Successfully created skin object for global skin: ${skin.name} with ID ${skin.id}`);
        return skin;
      }

      // If not found in diary_skin, try student_diary_skin
      this.logger.log(`Skin not found in diary_skin table, checking student_diary_skin`);
      const studentSkinResult = await this.dataSource.query(`SELECT * FROM student_diary_skin WHERE id = $1 AND student_id = $2 LIMIT 1`, [skinId, userId]);

      if (studentSkinResult && studentSkinResult.length > 0) {
        this.logger.log(`Found student skin with ID ${skinId} for user ${userId}`);

        // Create a skin object from the result
        const skinData = {
          id: studentSkinResult[0].id,
          name: studentSkinResult[0].name,
          description: studentSkinResult[0].description,
          templateContent: studentSkinResult[0].template_content,
          previewImagePath: studentSkinResult[0].preview_image_path,
          isActive: studentSkinResult[0].is_active,
          isGlobal: false,
          createdById: studentSkinResult[0].student_id,
          createdAt: studentSkinResult[0].created_at || new Date(),
          updatedAt: studentSkinResult[0].updated_at || new Date(),
        };

        // Cast to DiarySkin and add isUsedIn property
        const skin = skinData as unknown as DiarySkin;
        (skin as any).isUsedIn = true;

        this.logger.log(`Successfully created skin object for student skin: ${skin.name} with ID ${skin.id}`);
        return skin;
      }

      // If not found in student_diary_skin, try shop_item
      this.logger.log(`Skin not found in student_diary_skin table, checking shop_item`);
      try {
        const shopItemResult = await this.dataSource.query(`SELECT * FROM shop_item WHERE id = $1 AND type = 'SKIN' LIMIT 1`, [skinId]);

        if (shopItemResult && shopItemResult.length > 0) {
          this.logger.log(`Found shop item skin with ID ${skinId}`);

          // Check if the user has purchased this skin
          const userPurchaseResult = await this.dataSource.query(`SELECT * FROM user_purchase WHERE user_id = $1 AND item_id = $2 LIMIT 1`, [userId, skinId]);

          if (userPurchaseResult && userPurchaseResult.length > 0) {
            this.logger.log(`User ${userId} has purchased shop item skin ${skinId}`);

            // Create a skin object from the shop item
            const skinData = {
              id: shopItemResult[0].id,
              name: shopItemResult[0].name,
              description: shopItemResult[0].description,
              templateContent: shopItemResult[0].metadata || '<div>Default template content</div>',
              previewImagePath: shopItemResult[0].image_path,
              isActive: true,
              isGlobal: false,
              createdById: null,
              createdAt: shopItemResult[0].created_at || new Date(),
              updatedAt: shopItemResult[0].updated_at || new Date(),
            };

            // Cast to DiarySkin and add isUsedIn property
            const skin = skinData as unknown as DiarySkin;
            (skin as any).isUsedIn = true;

            this.logger.log(`Successfully created skin object for shop item skin: ${skin.name} with ID ${skin.id}`);
            return skin;
          } else {
            this.logger.warn(`User ${userId} has not purchased shop item skin ${skinId}`);
          }
        }
      } catch (shopError) {
        this.logger.error(`Error checking shop items: ${shopError.message}`, shopError.stack);
      }

      // If we get here, the skin wasn't found in any of the tables
      this.logger.warn(`Skin with ID ${skinId} not found in any table (diary_skin, student_diary_skin, shop_item)`);

      // As a last resort, try to get any active skin from the diary_skin table
      this.logger.log(`Trying to get any active skin as a fallback`);
      const fallbackSkinResult = await this.dataSource.query(`SELECT * FROM diary_skin WHERE is_active = true LIMIT 1`);

      if (fallbackSkinResult && fallbackSkinResult.length > 0) {
        this.logger.log(`Found fallback skin: ${fallbackSkinResult[0].name} with ID ${fallbackSkinResult[0].id}`);

        // Create a skin object from the result
        const skinData = {
          id: fallbackSkinResult[0].id,
          name: fallbackSkinResult[0].name,
          description: fallbackSkinResult[0].description,
          templateContent: fallbackSkinResult[0].template_content,
          previewImagePath: fallbackSkinResult[0].preview_image_path,
          isActive: fallbackSkinResult[0].is_active,
          isGlobal: true,
          createdById: fallbackSkinResult[0].created_by_id,
          createdAt: fallbackSkinResult[0].created_at || new Date(),
          updatedAt: fallbackSkinResult[0].updated_at || new Date(),
        };

        // Cast to DiarySkin and add isUsedIn property
        const skin = skinData as unknown as DiarySkin;
        (skin as any).isUsedIn = true;

        this.logger.log(`Successfully created skin object for fallback skin: ${skin.name} with ID ${skin.id}`);
        this.logger.warn(`Using fallback skin ${skin.id} instead of requested skin ${skinId}`);
        return skin;
      }

      this.logger.error(`No skins found in the system. This is a critical error.`);
      return null;
    } catch (error) {
      this.logger.error(`Error loading skin data: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Get today's diary entry for a student, creating one if it doesn't exist
   * @param userId The ID of the user
   * @returns The diary entry for today (either existing or newly created)
   */
  async getTodaysDiaryEntry(userId: string): Promise<DiaryEntryResponseDto> {
    try {
      this.logger.log(`Getting today's diary entry for user ${userId}`);
      const diary = await this.getOrCreateDiary(userId);
      this.logger.log(`Found diary with ID ${diary.id} and default skin ID ${diary.defaultSkinId}`);

      // Get today's date in UTC
      const today = getCurrentUTCDate();
      this.logger.log(`Today's date in UTC: ${today.toISOString()}`);

      // Get start and end of day in UTC
      const startOfDay = getStartOfDayUTC(today);
      const endOfDay = getEndOfDayUTC(today);
      this.logger.log(`Start of day: ${startOfDay.toISOString()}, End of day: ${endOfDay.toISOString()}`);

      // Find entry for today's date using direct SQL query to bypass all caches
      const rawEntry = await this.diaryEntryRepository.query(`SELECT * FROM diary_entry WHERE diary_id = $1 AND entry_date >= $2 AND entry_date <= $3 LIMIT 1`, [
        diary.id,
        startOfDay.toISOString(),
        endOfDay.toISOString(),
      ]);

      // If an entry exists, load it with all relations
      if (rawEntry && rawEntry.length > 0) {
        const entryId = rawEntry[0].id;
        const skinId = rawEntry[0].skin_id;
        this.logger.log(`Found diary entry for today with ID ${entryId} and skin ID ${skinId}`);

        // Load the entry with all relations using query builder
        const entry = await this.diaryEntryRepository
          .createQueryBuilder('entry')
          .where('entry.id = :id', { id: entryId })
          .leftJoinAndSelect('entry.feedbacks', 'feedbacks')
          .leftJoinAndSelect('feedbacks.tutor', 'feedbackTutor')
          .leftJoinAndSelect('entry.diary', 'diary')
          .leftJoinAndSelect('diary.user', 'user')
          .leftJoinAndSelect('entry.settings', 'settings')
          .leftJoinAndSelect('settings.settingsTemplate', 'settingsTemplate')
          .leftJoinAndSelect('entry.correction', 'correction')
          .leftJoinAndSelect('correction.tutor', 'correctionTutor')
          .leftJoinAndSelect('entry.likes', 'likes') // Add likes relation
          .addSelect(['diary.tutorGreeting']) // Explicitly select tutorGreeting field
          .getOne();

        if (!entry) {
          throw new Error(`Failed to load entry with ID ${entryId}`);
        }

        this.logger.log(`Loaded entry with ID ${entry.id} and skin ID ${entry.skinId}`);

        // Load the skin using our helper method
        const skinObject = await this.loadSkinData(skinId, userId);

        if (skinObject) {
          this.logger.log(`Successfully loaded skin: ${skinObject.name} with ID ${skinObject.id}`);
          // Always force update the skin relation with our manually loaded skin
          entry.skin = skinObject;
        } else {
          this.logger.warn(`Failed to load skin with ID ${skinId} for entry ${entry.id}`);
        }

        return this.mapEntryToResponseDto(entry);
      }

      // No entry exists, so create a new one with minimal initialization
      this.logger.log(`No diary entry found for today for user ${userId}. Creating a new one.`);

      // Use the diary's default skin
      const skinId = diary.defaultSkinId;

      // Load the default skin using our helper method
      let defaultSkin = await this.loadSkinData(skinId, userId);

      // If default skin not found, fall back to a global skin
      if (!defaultSkin) {
        this.logger.warn(`Default skin with ID ${skinId} not found in either global or student skins, falling back to a global skin`);

        // Get an active global skin
        const globalSkins = await this.diarySkinRepository.query(`SELECT * FROM diary_skin WHERE is_active = true LIMIT 1`);

        if (globalSkins && globalSkins.length > 0) {
          const skinData = {
            id: globalSkins[0].id,
            name: globalSkins[0].name,
            description: globalSkins[0].description,
            templateContent: globalSkins[0].template_content,
            previewImagePath: globalSkins[0].preview_image_path,
            isActive: globalSkins[0].is_active,
            isGlobal: true,
            createdById: globalSkins[0].created_by_id,
            createdAt: globalSkins[0].created_at || new Date(),
            updatedAt: globalSkins[0].updated_at || new Date(),
          };

          // Cast to DiarySkin
          defaultSkin = skinData as unknown as DiarySkin;

          // Add the isUsedIn property for our DTO mapping later
          (defaultSkin as any).isUsedIn = true; // If we're using it as a default, it's in use
        }

        if (!defaultSkin) {
          throw new NotFoundException('No active diary skins found');
        }
      }

      // Create a transaction to ensure data consistency
      const queryRunner = this.dataSource.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      try {
        // Create the diary entry with minimal fields
        const newEntry = this.diaryEntryRepository.create({
          diaryId: diary.id,
          entryDate: today,
          title: '',
          content: '',
          status: DiaryEntryStatus.NEW,
          skinId: defaultSkin.id,
          backgroundColor: null,
          isPrivate: false,
        });

        // Save the entry
        const savedEntry = await queryRunner.manager.save(newEntry);

        // Commit the transaction
        await queryRunner.commitTransaction();

        // Get the complete entry with all relations using query builder to bypass cache
        const completeEntry = await this.diaryEntryRepository
          .createQueryBuilder('entry')
          .where('entry.id = :id', { id: savedEntry.id })
          .leftJoinAndSelect('entry.feedbacks', 'feedbacks')
          .leftJoinAndSelect('feedbacks.tutor', 'feedbackTutor')
          .leftJoinAndSelect('entry.diary', 'diary')
          .leftJoinAndSelect('diary.user', 'user')
          .leftJoinAndSelect('entry.settings', 'settings')
          .leftJoinAndSelect('settings.settingsTemplate', 'settingsTemplate')
          .leftJoinAndSelect('entry.correction', 'correction')
          .leftJoinAndSelect('correction.tutor', 'correctionTutor')
          .leftJoinAndSelect('entry.likes', 'likes') // Add likes relation
          .addSelect(['diary.tutorGreeting']) // Explicitly select tutorGreeting field
          .getOne();

        if (!completeEntry) {
          throw new Error(`Failed to retrieve newly created diary entry with ID ${savedEntry.id}`);
        }

        // Log the entry and its skin for debugging
        this.logger.log(`Created new diary entry with ID ${completeEntry.id} and skin ID ${completeEntry.skinId}`);

        // Load the skin using our helper method
        const skinObject = await this.loadSkinData(completeEntry.skinId, userId);

        if (skinObject) {
          this.logger.log(`Successfully loaded skin: ${skinObject.name} with ID ${skinObject.id}`);
          // Always force update the skin relation with our manually loaded skin
          completeEntry.skin = skinObject;
        } else {
          this.logger.warn(`Failed to load skin with ID ${completeEntry.skinId} for entry ${completeEntry.id}`);
        }

        return this.mapEntryToResponseDto(completeEntry);
      } catch (error) {
        // Rollback the transaction in case of error
        await queryRunner.rollbackTransaction();
        throw error;
      } finally {
        // Release the query runner
        await queryRunner.release();
      }
    } catch (error) {
      this.logger.error(`Error getting/creating today's diary entry: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update the skin of today's diary entry
   * @param userId The ID of the user
   * @param skinId The ID of the skin to apply
   * @returns The updated diary entry
   */
  async updateTodaysDiarySkin(userId: string, skinId: string): Promise<DiaryEntryResponseDto> {
    try {
      this.logger.log(`Updating today's diary skin for user ${userId} to skin ID ${skinId}`);

      // Get the user's diary
      const diary = await this.getOrCreateDiary(userId);
      this.logger.log(`Found diary with ID ${diary.id}`);

      // Get today's date in UTC
      const today = new Date();
      this.logger.log(`Today's date in UTC: ${today.toISOString()}`);

      // Get start and end of day in UTC
      const startOfDay = getStartOfDayUTC(today);
      const endOfDay = getEndOfDayUTC(today);
      this.logger.log(`Start of day: ${startOfDay.toISOString()}, End of day: ${endOfDay.toISOString()}`);

      // Find entry for today's date using direct SQL query to bypass all caches
      const rawEntry = await this.diaryEntryRepository.query(`SELECT * FROM diary_entry WHERE diary_id = $1 AND entry_date >= $2 AND entry_date <= $3 LIMIT 1`, [
        diary.id,
        startOfDay.toISOString(),
        endOfDay.toISOString(),
      ]);

      // If no entry exists for today, throw an error
      if (!rawEntry || rawEntry.length === 0) {
        throw new NotFoundException('No diary entry found for today. Please create one first.');
      }

      const entryId = rawEntry[0].id;
      const currentStatus = rawEntry[0].status;
      this.logger.log(`Found diary entry for today with ID ${entryId} and status ${currentStatus}`);

      // Allow unlimited updates - students can update entries in any status
      // Tutors will be notified of changes for submitted/reviewed/confirmed entries

      // Verify the skin exists using our helper method
      const skinObject = await this.loadSkinData(skinId, userId);

      if (!skinObject) {
        throw new NotFoundException(`Diary skin with ID ${skinId} not found in either global or user-specific skins`);
      }

      this.logger.log(`Found skin: ${skinObject.name} with ID ${skinObject.id}, isGlobal: ${skinObject.isGlobal}`);

      // Update the entry's skin using direct SQL
      await this.diaryEntryRepository.query(`UPDATE diary_entry SET skin_id = $1, updated_at = NOW() WHERE id = $2`, [skinId, entryId]);

      this.logger.log(`Updated diary entry ${entryId} with skin ID ${skinId}`);

      // Load the updated entry with all relations
      const entry = await this.diaryEntryRepository
        .createQueryBuilder('entry')
        .where('entry.id = :id', { id: entryId })
        .leftJoinAndSelect('entry.feedbacks', 'feedbacks')
        .leftJoinAndSelect('feedbacks.tutor', 'feedbackTutor')
        .leftJoinAndSelect('entry.diary', 'diary')
        .leftJoinAndSelect('diary.user', 'user')
        .leftJoinAndSelect('entry.settings', 'settings')
        .leftJoinAndSelect('settings.settingsTemplate', 'settingsTemplate')
        .leftJoinAndSelect('entry.correction', 'correction')
        .leftJoinAndSelect('correction.tutor', 'correctionTutor')
        .leftJoinAndSelect('entry.likes', 'likes') // Add likes relation
        .addSelect(['diary.tutorGreeting']) // Explicitly select tutorGreeting field
        .getOne();

      if (!entry) {
        throw new Error(`Failed to retrieve updated diary entry with ID ${entryId}`);
      }

      // Force update the skin relation with our manually loaded skin
      if (skinObject) {
        this.logger.log(`Setting skin object for entry: ${JSON.stringify(skinObject)}`);
        entry.skin = skinObject;
      } else {
        this.logger.warn(`No skin object available for entry ${entry.id}`);
      }

      return this.mapEntryToResponseDto(entry);
    } catch (error) {
      this.logger.error(`Error updating today's diary skin: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get all diary entries for a student
   * @param userId The ID of the user
   * @param paginationDto Pagination parameters
   * @param includeFriends Whether to include entries from friends
   * @returns A paged list of diary entries
   */
  async getStudentDiaryEntries(userId: string, paginationDto?: PaginationDto, includeFriends: boolean = false): Promise<PagedListDto<DiaryEntryResponseDto>> {
    try {
      const queryBuilder = this.diaryEntryRepository
        .createQueryBuilder('entry')
        .leftJoinAndSelect('entry.diary', 'diary')
        .leftJoinAndSelect('diary.user', 'user')
        .leftJoinAndSelect('entry.skin', 'skin')
        .leftJoinAndSelect('entry.feedbacks', 'feedbacks')
        .leftJoinAndSelect('feedbacks.tutor', 'tutor')
        .leftJoinAndSelect('entry.settings', 'settings')
        .leftJoinAndSelect('settings.settingsTemplate', 'settingsTemplate')
        .leftJoinAndSelect('entry.correction', 'correction')
        .leftJoinAndSelect('correction.tutor', 'correctionTutor')
        .leftJoinAndSelect('entry.likes', 'likes')
        .addSelect(['diary.tutorGreeting']); // Explicitly select tutorGreeting field

      const diary = await this.getOrCreateDiary(userId);

      // Build the where clause
      let whereClause: any = { diaryId: diary.id };

      // If includeFriends is true, get entries from friends as well
      if (includeFriends) {
        // Get all friendships where the user can view diaries
        const friendships = await this.studentFriendshipRepository.find({
          where: [
            { requesterId: userId, status: FriendshipStatus.ACCEPTED, canViewDiary: true },
            { requestedId: userId, status: FriendshipStatus.ACCEPTED, canViewDiary: true },
          ],
        });

        // Get the IDs of all friends
        const friendIds = friendships.map((f) => (f.requesterId === userId ? f.requestedId : f.requesterId));

        // If there are friends, get their diaries
        if (friendIds.length > 0) {
          const friendDiaries = await this.diaryRepository.find({
            where: { userId: In(friendIds) },
          });

          // Get the IDs of all friend diaries
          const friendDiaryIds = friendDiaries.map((d) => d.id);

          // Update the where clause to include entries from friends that are not private
          if (friendDiaryIds.length > 0) {
            whereClause = [
              { diaryId: diary.id }, // User's own entries
              {
                diaryId: In(friendDiaryIds),
                visibility: DiaryVisibility.FRIENDS_ONLY,
              }, // Friend entries that are shared with friends
              {
                diaryId: In(friendDiaryIds),
                visibility: DiaryVisibility.PUBLIC,
              }, // Friend entries that are public
            ];
          }
        }
      }

      // Get total count for pagination
      const totalCount = await this.diaryEntryRepository.count({
        where: whereClause,
      });

      // Apply pagination if provided
      const options: any = {
        where: whereClause,
        relations: ['skin', 'feedbacks', 'feedbacks.tutor', 'diary', 'diary.user', 'settings', 'settings.settingsTemplate', 'correction', 'correction.tutor', 'likes'],
        order: { entryDate: 'DESC' },
      };

      let page = 1;
      let limit = 10;

      if (paginationDto) {
        page = paginationDto.page || 1;
        limit = paginationDto.limit || 10;
        const { sortBy, sortDirection } = paginationDto;
        const skip = (page - 1) * limit;

        options.skip = skip;
        options.take = limit;

        if (sortBy && sortDirection) {
          options.order = { [sortBy]: sortDirection };
        }
      }

      // Get entries
      const entries = await this.diaryEntryRepository.find(options);

      // Map entries to response DTOs and await all promises
      const entryDtos = await Promise.all(entries.map((entry) => this.mapEntryToResponseDto(entry)));

      return new PagedListDto(entryDtos, totalCount, page, limit);
    } catch (error) {
      this.logger.error(`Error getting student diary entries: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Filter diary entries by date, subject/title, or status
   * @param userId The ID of the user
   * @param filterDto Filter parameters
   * @param paginationDto Pagination parameters
   * @returns A paged list of filtered diary entries
   */
  async filterDiaryEntries(userId: string, filterDto: DiaryEntryFilterDto, paginationDto?: PaginationDto): Promise<PagedListDto<DiaryEntryResponseDto>> {
    try {
      const userDiary = await this.getOrCreateDiary(userId);

      // Basic query builder
      const query = this.diaryEntryRepository
        .createQueryBuilder('entry')
        .leftJoinAndSelect('entry.diary', 'diary')
        .leftJoinAndSelect('diary.user', 'user')
        .leftJoinAndSelect('entry.skin', 'skin')
        .leftJoinAndSelect('entry.feedbacks', 'feedbacks')
        .leftJoinAndSelect('feedbacks.tutor', 'tutor')
        .leftJoinAndSelect('entry.settings', 'settings')
        .leftJoinAndSelect('settings.settingsTemplate', 'settingsTemplate')
        .leftJoinAndSelect('entry.correction', 'correction')
        .leftJoinAndSelect('correction.tutor', 'correctionTutor')
        .leftJoinAndSelect('entry.likes', 'likes')
        .addSelect(['diary.tutorGreeting']) // Explicitly select tutorGreeting field
        .where('diary.id = :diaryId', { diaryId: userDiary.id });

      // Apply all filters first
      if (filterDto.date) {
        try {
          const filterDate = parseYYYYMMDDToUTC(filterDto.date);
          if (!filterDate) {
            throw new BadRequestException(`Invalid date format: ${filterDto.date}. Expected format: YYYY-MM-DD`);
          }

          const startOfDay = getStartOfDayUTC(filterDate);
          const endOfDay = getEndOfDayUTC(filterDate);

          query.andWhere('entry.entryDate BETWEEN :startOfDay AND :endOfDay', {
            startOfDay,
            endOfDay,
          });
        } catch (error) {
          throw new BadRequestException(`Invalid date format: ${filterDto.date}. Expected format: YYYY-MM-DD`);
        }
      }

      if (filterDto.status) {
        query.andWhere('entry.status = :status', { status: filterDto.status });
      }

      if (filterDto.subject) {
        const subject = filterDto.subject.toLowerCase();
        query.andWhere('(LOWER(entry.title) LIKE :subject OR LOWER(entry.content) LIKE :subject)', {
          subject: `%${subject}%`,
        });
      }

      // Get total count before applying pagination
      const totalCount = await query.getCount();

      // Setup default pagination values
      const page = paginationDto?.page ?? 1;
      const limit = paginationDto?.limit ?? 10;
      const sortBy = paginationDto?.sortBy;
      const sortDirection = paginationDto?.sortDirection;

      // Apply sorting with default order
      if (sortBy && sortDirection) {
        query.orderBy(`entry.${sortBy}`, sortDirection);
      } else {
        // Default sorting by entry date descending
        query.orderBy('entry.entryDate', 'DESC');
      }

      // Apply pagination
      const skip = (page - 1) * limit;
      query.skip(skip).take(limit);

      // Execute query and get entries
      const entries = await query.getMany();

      // Map entries to DTOs
      const mappedEntries = await Promise.all(entries.map((entry) => this.mapEntryToResponseDto(entry, userId)));

      // Return paginated result with total pages calculation
      return new PagedListDto(mappedEntries, totalCount, page, limit);
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      this.logger.error(`Error filtering diary entries: ${error.message}`, error.stack);
      throw new BadRequestException('Error filtering diary entries');
    }
  }

  /**
   * Get the plan feature ID for the diary module
   * @returns The plan feature ID for the diary module
   */
  private async getDiaryModuleFeatureId(): Promise<string> {
    try {
      // Find the plan feature for HEC_USER_DIARY
      const diaryFeature = await this.planFeatureRepository.findOne({
        where: { type: FeatureType.HEC_USER_DIARY },
      });

      if (!diaryFeature) {
        this.logger.error('Diary module feature (HEC_USER_DIARY) not found in the database');
        throw new NotFoundException('Diary module feature not found');
      }

      this.logger.log(`Found diary module feature with ID: ${diaryFeature.id}`);
      return diaryFeature.id;
    } catch (error) {
      this.logger.error(`Error getting diary module feature ID: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get or create a diary for a student
   * @param userId The ID of the user
   * @returns The diary
   */
  async getOrCreateDiary(userId: string): Promise<Diary> {
    try {
      // First check if the diary already exists with all needed relations
      let diary = await this.diaryRepository.findOne({
        where: { userId: userId },
        relations: ['defaultSkin', 'user'],
        select: {
          id: true,
          userId: true,
          tutorGreeting: true,
          defaultSkinId: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      // Log the diary and its default skin for debugging
      if (diary) {
        this.logger.log(`Found diary for user ${userId} with default skin ID: ${diary.defaultSkinId}`);
        if (diary.defaultSkin) {
          this.logger.log(`Default skin details: ${diary.defaultSkin.name}, isActive: ${diary.defaultSkin.isActive}, isGlobal: ${diary.defaultSkin.isGlobal}`);
        } else {
          this.logger.warn(`Default skin relation not loaded for diary ${diary.id}`);
        }
      }

      // Check if the user has an active subscription
      const user = await this.userRepository.findOne({
        where: { id: userId },
        relations: ['userPlans', 'userPlans.plan'],
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      const hasActivePlan = user.userPlans && user.userPlans.some((plan) => plan.isActive);
      if (!hasActivePlan) {
        console.log(`User ${userId} attempted to access diary without active subscription`);
        throw new ForbiddenException('You need an active subscription to access the diary feature');
      }

      if (!diary) {
        // Get default skin
        const defaultSkin = await this.diarySkinRepository.findOne({
          where: { isActive: true },
        });

        if (!defaultSkin) {
          throw new NotFoundException('No active diary skins found');
        }

        try {
          // Create the diary without the defaultSkin relation to avoid constraint issues
          diary = this.diaryRepository.create({
            userId: userId,
            defaultSkinId: defaultSkin.id,
          });

          await this.diaryRepository.save(diary);
          console.log(`Created new diary for user ${userId}`); // Now load the diary with all needed relations
          diary = await this.diaryRepository.findOne({
            where: { userId: userId },
            relations: ['defaultSkin', 'user'],
            select: {
              id: true,
              userId: true,
              tutorGreeting: true,
              defaultSkinId: true,
              createdAt: true,
              updatedAt: true,
            },
          });

          if (!diary) {
            throw new Error('Failed to retrieve newly created diary');
          }
        } catch (saveError) {
          // If there's a duplicate key error, try to find the existing diary again
          // This handles race conditions where another process might have created the diary
          if (saveError.code === '23505') {
            // PostgreSQL duplicate key error
            console.log(`Duplicate key error when creating diary for user ${userId}, attempting to retrieve existing diary`);

            // Wait a moment to allow any concurrent transactions to complete
            await new Promise((resolve) => setTimeout(resolve, 500));

            // Try multiple times to find the diary
            for (let i = 0; i < 3; i++) {
              diary = await this.diaryRepository.findOne({
                where: { userId: userId },
                relations: ['defaultSkin'],
              });

              if (diary) {
                console.log(`Found existing diary for user ${userId} after retry ${i + 1}`);
                break;
              }

              // Wait before retrying
              await new Promise((resolve) => setTimeout(resolve, 500));
            }

            // If we still don't have a diary, create one with a different skin
            if (!diary) {
              console.log(`Still couldn't find diary for user ${userId}, trying with a different skin`);

              // Get a different skin
              const skins = await this.diarySkinRepository.find({
                where: { isActive: true },
                take: 2, // Get up to 2 skins
              });

              // If we have more than one skin, use the second one
              const alternativeSkin = skins.length > 1 ? skins[1] : skins[0];

              if (!alternativeSkin) {
                throw new NotFoundException('No active diary skins found');
              }

              // Try creating with the alternative skin
              diary = this.diaryRepository.create({
                userId: userId,
                defaultSkinId: alternativeSkin.id,
              });

              await this.diaryRepository.save(diary);
              console.log(`Created diary with alternative skin for user ${userId}`);

              // Load the diary with relations
              diary = await this.diaryRepository.findOne({
                where: { userId: userId },
                relations: ['defaultSkin'],
              });

              if (!diary) {
                throw new Error('Failed to retrieve diary after creating with alternative skin');
              }
            }
          } else {
            throw saveError;
          }
        }
      }

      // Check if tutor greeting is set
      const hasGreeting = !!diary.tutorGreeting;

      // Add a property to indicate if greeting is set
      (diary as any).hasGreeting = hasGreeting;

      return diary;
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ForbiddenException) {
        throw error;
      }

      // Log the detailed error for debugging
      console.error(`Error in getOrCreateDiary for userId ${userId}:`, error);

      // Throw a more user-friendly error with more specific information
      throw new BadRequestException('Unable to access or create diary. Please ensure you have an active subscription and your account is properly set up.');
    }
  }

  /**
   * Map a diary entry entity to a response DTO
   * This is a simplified version - the full mapping is done in DiaryService
   * @param entry The diary entry entity
   * @returns The diary entry response DTO
   */ async mapEntryToResponseDto(entry: DiaryEntry, userId?: string): Promise<DiaryEntryResponseDto> {
    // Get base response from mapper service
    const baseResponse = await this.diaryMapperService.mapEntryToResponseDto(entry);

    // Update hasLiked if userId is provided
    if (userId && entry.likes) {
      baseResponse.hasLiked = entry.likes.some((like) => like.likerId === userId);
    } // Map feedbacks
    const feedbacks =
      entry.feedbacks?.map((feedback) => ({
        id: feedback.id,
        tutorId: feedback.tutorId,
        tutorName: feedback.tutor?.name || 'Unknown Tutor',
        feedback: feedback.feedback,
        rating: feedback.rating,
        award: feedback.award,
        createdAt: feedback.createdAt,
      })) || [];

    // Map correction
    const correction = entry.correction
      ? {
          id: entry.correction.id,
          diaryEntryId: entry.correction.diaryEntryId,
          tutorId: entry.correction.tutorId,
          tutorName: entry.correction.tutor?.name || 'Unknown Tutor',
          correctionText: entry.correction.correctionText,
          score: entry.correction.score,
          comments: entry.correction.comments,
          createdAt: entry.correction.createdAt,
          updatedAt: entry.correction.updatedAt,
        }
      : null;

    // Map settings - settings are now mandatory
    let settings: any;

    if (entry.settings) {
      // Use existing settings
      // Ensure settingsTemplateId is always included in the response
      const settingsTemplateId = entry.settings.settingsTemplateId;

      // Log the settingsTemplateId for debugging
      this.logger.log(`Mapping entry ${entry.id} with settingsTemplateId: ${settingsTemplateId}`);

      // Create a flattened settings object that combines properties from both settings and settingsTemplate
      settings = {
        id: entry.settings.id,
        settingsTemplateId: settingsTemplateId,
        // Use template properties if available, otherwise fall back to settings properties
        title: entry.settings.settingsTemplate?.title || entry.settings.title || 'Template',
        level: entry.settings.settingsTemplate?.level || entry.settings.level || 1,
        wordLimit: entry.settings.settingsTemplate?.wordLimit || entry.settings.wordLimit || 100,
        minWordLimit: entry.settings.settingsTemplate?.minWordLimit || entry.settings.minWordLimit || 10,
        // Include additional properties from template if available
        description: entry.settings.settingsTemplate?.description || 'Template details not loaded',
        isActive: entry.settings.settingsTemplate?.isActive !== undefined ? entry.settings.settingsTemplate.isActive : true,
      };
    } else {
      // If settings are missing (which shouldn't happen), create a placeholder
      // This ensures the response always includes settings information
      this.logger.warn(`No settings found for entry ${entry.id} in mapEntryToResponseDto. Creating placeholder.`);

      // Create a flattened placeholder settings object
      settings = {
        id: 'placeholder',
        settingsTemplateId: 'placeholder',
        title: 'Default Settings',
        level: 1,
        wordLimit: 100,
        minWordLimit: 10,
        description: 'Default template created as placeholder',
        isActive: true,
      };

      // Try to get the settingsTemplateId from the database directly
      // This is an async operation, but we'll return the placeholder immediately
      // and update it later if needed
      this.dataSource
        .query(`SELECT settings_template_id FROM diary_entry_settings WHERE diary_entry_id = $1`, [entry.id])
        .then((result) => {
          if (result && result.length > 0 && result[0].settings_template_id) {
            const realTemplateId = result[0].settings_template_id;
            this.logger.log(`Found settingsTemplateId ${realTemplateId} in database for entry ${entry.id}`);

            // We can't update the settings object directly since it's already been returned,
            // but we can update the database to ensure future requests get the correct value
            this.diaryEntrySettingsRepository.update({ diaryEntryId: entry.id }, { settingsTemplateId: realTemplateId }).catch((updateError) => {
              this.logger.error(`Error updating settingsTemplateId in database: ${updateError.message}`);
            });
          }
        })
        .catch((error) => {
          this.logger.error(`Error checking settingsTemplateId in database: ${error.message}`);
        });

      // Schedule default settings creation (don't wait for it)
      setTimeout(() => {
        this.getOrCreateDefaultSettings(entry.id).catch((err: Error) => {
          this.logger.error(`Failed to create default settings for entry ${entry.id}: ${err.message}`, err.stack);
        });
      }, 0);
    }

    // Generate skin URL if needed
    const skinUrl = entry.skin?.previewImagePath || null;
    // The fileRegistryService will handle URL generation in the DiaryService's mapEntryToResponseDto method

    // Log skin information for debugging
    if (entry.skin) {
      this.logger.log(`Mapping entry ${entry.id} with skin ${entry.skin.name} (ID: ${entry.skin.id})`);
    } else {
      this.logger.warn(`Mapping entry ${entry.id} with skinId ${entry.skinId} but skin relation is not loaded`);

      // Try to load the skin data if it's not already loaded
      // This is a synchronous method, so we can't use async/await here
      // We'll just log a warning and continue with default values
      this.logger.warn(`Unable to load skin data for skinId ${entry.skinId} in mapEntryToResponseDto - skin information may be incomplete`);

      // Double-check the skin ID in the database
      try {
        // This is a direct database check to ensure we have the correct skin ID
        this.dataSource
          .query(`SELECT skin_id FROM diary_entry WHERE id = $1`, [entry.id])
          .then((result) => {
            if (result && result.length > 0 && result[0].skin_id !== entry.skinId) {
              this.logger.warn(`Skin ID mismatch in mapEntryToResponseDto: entity has ${entry.skinId} but database has ${result[0].skin_id}`);
            }
          })
          .catch((error) => {
            this.logger.error(`Error checking skin ID in database: ${error.message}`);
          });
      } catch (error) {
        this.logger.error(`Error in skin ID verification: ${error.message}`);
      }
    }

    // Create a diary object if needed
    const diary = {
      id: entry.diary?.id || entry.diaryId,
      userId: entry.diary?.userId || '',
      userName: entry.diary?.user?.name || '',
      defaultSkinId: entry.diary?.defaultSkinId || '',
      createdAt: entry.diary?.createdAt || entry.createdAt,
      updatedAt: entry.diary?.updatedAt || entry.updatedAt,
    };

    // Get likes data if userId is provided
    let count = 0;
    let hasLiked = false;
    if (entry.likes) {
      count = entry.likes.length;
      if (userId) {
        hasLiked = entry.likes.some((like) => like.likerId === userId);
      }
    }

    return {
      id: entry.id,
      title: entry.title,
      content: entry.content,
      entryDate: entry.entryDate,
      status: entry.status,
      backgroundColor: entry.backgroundColor,
      decoration: entry.decoration,
      isPrivate: entry.isPrivate,
      createdAt: entry.createdAt,
      updatedAt: entry.updatedAt,
      skin: {
        id: entry.skin?.id || entry.skinId || '',
        name: entry.skin?.name || 'Default Skin',
        description: entry.skin?.description || '',
        previewImagePath: skinUrl,
        isActive: entry.skin?.isActive || true,
        isGlobal: entry.skin?.isGlobal || true,
        createdById: entry.skin?.createdById || null,
        templateContent: entry.skin?.templateContent || '',
        isUsedIn: true, // If it's in an entry, it's definitely in use
      },
      score: entry.score,
      evaluatedAt: entry.evaluatedAt,
      evaluatedBy: entry.evaluatedBy,
      diary: diary,
      feedbacks: feedbacks,
      correction: correction,
      settings: settings,
      thanksMessage: entry.thanksMessage,
      hasGreeting: !!entry.diary?.tutorGreeting,
      likeCount: count,
      hasLiked,
      // Include all fields from base response that might be missing
      shareUrl: baseResponse.shareUrl,
      qrCodeUrl: baseResponse.qrCodeUrl,
      currentVersion: baseResponse.currentVersion,
      totalEditHistory: baseResponse.totalEditHistory,
      versionCreatedAt: baseResponse.versionCreatedAt,
      hasHistory: baseResponse.hasHistory,
      // CRITICAL: Include the originalReviewedVersion field from base response
      originalReviewedVersion: baseResponse.originalReviewedVersion,

      // NEW: Include unified submission/draft tracking fields
      isDraft: baseResponse.isDraft,
      submittedVersionCount: baseResponse.submittedVersionCount,
      canSubmitNewVersion: baseResponse.canSubmitNewVersion,
      lastSubmittedAt: baseResponse.lastSubmittedAt,
      lastReviewedAt: baseResponse.lastReviewedAt,

      // NEW: Include resubmission tracking fields
      isResubmission: baseResponse.isResubmission,
      resubmissionType: baseResponse.resubmissionType,
      previousReviewCount: baseResponse.previousReviewCount,
      previousConfirmationCount: baseResponse.previousConfirmationCount,
    };
  }

  /**
   * Validate and fix settings consistency between entry settings and template
   */
  private async validateAndFixSettingsConsistency(settings: DiaryEntrySettings): Promise<void> {
    try {
      if (!settings.settingsTemplate) {
        this.logger.warn(`Settings ${settings.id} missing template reference`);
        return;
      }

      const template = settings.settingsTemplate;
      let needsUpdate = false;
      const updates: any = {};

      // Check for inconsistencies
      if (settings.level !== template.level) {
        this.logger.warn(`Settings inconsistency: level ${settings.level} != template level ${template.level} for entry ${settings.diaryEntryId}`);
        updates.level = template.level;
        needsUpdate = true;
      }

      if (settings.wordLimit !== template.wordLimit) {
        this.logger.warn(`Settings inconsistency: wordLimit ${settings.wordLimit} != template wordLimit ${template.wordLimit} for entry ${settings.diaryEntryId}`);
        updates.wordLimit = template.wordLimit;
        needsUpdate = true;
      }

      if (settings.minWordLimit !== template.minWordLimit) {
        this.logger.warn(`Settings inconsistency: minWordLimit ${settings.minWordLimit} != template minWordLimit ${template.minWordLimit} for entry ${settings.diaryEntryId}`);
        updates.minWordLimit = template.minWordLimit;
        needsUpdate = true;
      }

      if (settings.title !== template.title) {
        this.logger.warn(`Settings inconsistency: title "${settings.title}" != template title "${template.title}" for entry ${settings.diaryEntryId}`);
        updates.title = template.title;
        needsUpdate = true;
      }

      // Apply fixes if needed
      if (needsUpdate) {
        this.logger.log(`Fixing settings inconsistency for entry ${settings.diaryEntryId}:`, updates);
        await this.diaryEntrySettingsRepository.update(settings.id, {
          ...updates,
          updatedAt: new Date(),
        });

        // Update the in-memory object to reflect the changes
        Object.assign(settings, updates);
      }
    } catch (error) {
      this.logger.error(`Error validating settings consistency: ${error.message}`, error.stack);
    }
  }

  /**
   * Helper to upsert attendance for a diary entry
   */
  private async upsertAttendance(entry: DiaryEntry, userId: string, settings?: DiaryEntrySettings) {
    if (!entry || !userId) return;
    const wordCount = entry.content ? entry.content.trim().split(/\s+/).length : 0;
    let wordLimit = settings?.wordLimit;
    if (!wordLimit && entry.settings) wordLimit = entry.settings.wordLimit;
    const status = wordLimit && wordCount >= wordLimit ? AttendanceStatus.PRESENT : AttendanceStatus.ABSENT;
    const entryDate = entry.entryDate instanceof Date ? entry.entryDate : new Date(entry.entryDate);
    let attendance = await this.diaryEntryAttendanceRepository.findOne({ where: { diaryEntryId: entry.id } });
    if (!attendance) {
      attendance = this.diaryEntryAttendanceRepository.create({
        diaryEntryId: entry.id,
        studentId: userId,
        entryDate,
        status,
        wordCount,
      });
    } else {
      attendance.status = status;
      attendance.wordCount = wordCount;
      attendance.entryDate = entryDate;
    }
    await this.diaryEntryAttendanceRepository.save(attendance);
  }

  /**
   * Upsert attendance for a diary entry event (create, update, submit)
   * @param diaryEntry The diary entry object
   * @param studentId The student/user id
   * @param entryDate The date of the diary entry
   * @param content The diary content (string)
   * @param wordLimit The required word count (number, optional)
   */
  private async upsertAttendanceForEntry(diaryEntry: DiaryEntry, studentId: string, entryDate: Date, content: string, wordLimit?: number, queryRunner?: any) {
    if (!diaryEntry || !studentId || !entryDate) return;
    // Count words
    const wordCount = content ? content.trim().split(/\s+/).length : 0;
    let status = AttendanceStatus.ABSENT;
    if (wordLimit && wordCount >= wordLimit) {
      status = AttendanceStatus.PRESENT;
    }
    // Upsert attendance record
    let attendance = await this.diaryEntryAttendanceRepository.findOne({
      where: { diaryEntryId: diaryEntry.id },
    });
    if (!attendance) {
      attendance = this.diaryEntryAttendanceRepository.create({
        diaryEntryId: diaryEntry.id,
        studentId,
        entryDate,
        status,
        wordCount,
      });
    } else {
      attendance.status = status;
      attendance.wordCount = wordCount;
    }
    await this.diaryEntryAttendanceRepository.save(attendance);
  }

  /**
   * Add a thanks message to a diary entry
   * @param id The ID of the diary entry
   * @param userId The ID of the user adding the thanks message
   * @param thanksMessage The thanks message to add
   * @returns The updated diary entry
   */
  async addThanksMessage(id: string, userId: string, thanksMessage: string): Promise<void> {
    // Get the entry
    const entry = await this.diaryEntryRepository.findOne({
      where: { id },
      relations: ['diary'],
    });

    if (!entry) {
      throw new NotFoundException(`Diary entry with ID ${id} not found`);
    }

    // Check if the user is the owner of the diary
    if (entry.diary.userId !== userId) {
      throw new ForbiddenException('You do not have permission to add a thanks message to this diary entry');
    }

    // Update the entry
    entry.thanksMessage = thanksMessage;
    await this.diaryEntryRepository.save(entry);
  }

  /**
   * Get all diary entries with filtering for admins
   * @param filterDto Filter parameters
   * @param paginationDto Pagination parameters
   * @returns A paged list of diary entries
   */
  async getAllDiaryEntriesAsAdmin(filterDto: AdminDiaryEntryFilterDto, paginationDto?: PaginationDto): Promise<PagedListDto<DiaryEntryResponseDto>> {
    try {
      const { page = 1, limit = 10, sortBy, sortDirection } = paginationDto || {};
      const skip = (page - 1) * limit;

      const queryBuilder = this.diaryEntryRepository
        .createQueryBuilder('entry')
        .leftJoinAndSelect('entry.diary', 'diary')
        .leftJoinAndSelect('diary.user', 'user')
        .leftJoinAndSelect('entry.skin', 'skin')
        .leftJoinAndSelect('entry.feedbacks', 'feedbacks')
        .leftJoinAndSelect('feedbacks.tutor', 'tutor')
        .leftJoinAndSelect('entry.settings', 'settings')
        .leftJoinAndSelect('settings.settingsTemplate', 'settingsTemplate')
        .leftJoinAndSelect('entry.correction', 'correction')
        .leftJoinAndSelect('correction.tutor', 'correctionTutor')
        .leftJoinAndSelect('entry.likes', 'likes')
        .addSelect(['diary.tutorGreeting']); // Explicitly select tutorGreeting field

      // Apply filters
      if (filterDto.studentName) {
        queryBuilder.andWhere('LOWER(user.name) LIKE LOWER(:name)', {
          name: `%${filterDto.studentName}%`,
        });
      }

      if (filterDto.status) {
        queryBuilder.andWhere('entry.status = :status', {
          status: filterDto.status,
        });
      }

      if (filterDto.dateFrom) {
        queryBuilder.andWhere('entry.entryDate >= :dateFrom', {
          dateFrom: new Date(filterDto.dateFrom),
        });
      }

      if (filterDto.dateTo) {
        queryBuilder.andWhere('entry.entryDate <= :dateTo', {
          dateTo: new Date(filterDto.dateTo),
        });
      }

      // Apply sorting
      if (sortBy && sortDirection) {
        queryBuilder.orderBy(`entry.${sortBy}`, sortDirection);
      } else {
        queryBuilder.orderBy('entry.entryDate', 'DESC');
      }

      // Apply pagination
      queryBuilder.skip(skip).take(limit);

      // Get entries and total count
      const [entries, total] = await queryBuilder.getManyAndCount();

      // Map entries to DTOs
      const entryDtos = await Promise.all(entries.map((entry) => this.mapEntryToResponseDto(entry)));

      return new PagedListDto(entryDtos, total, page, limit);
    } catch (error) {
      this.logger.error(`Error getting admin diary entries: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get a diary entry by ID without user restrictions
   * @param id The ID of the diary entry
   * @returns The diary entry
   */
  async getDiaryEntryByIdAsAdmin(id: string): Promise<DiaryEntry> {
    const entry = await this.diaryEntryRepository.findOne({
      where: { id },
      relations: ['diary', 'diary.user', 'skin', 'feedbacks', 'feedbacks.tutor', 'settings', 'settings.settingsTemplate', 'correction', 'correction.tutor', 'likes'],
    });

    if (!entry) {
      throw new NotFoundException(`Diary entry with ID ${id} not found`);
    }

    return entry;
  }

  /**
   * Check if a status transition is valid
   * @param currentStatus Current status of the entry
   * @param newStatus New status to transition to
   * @returns true if the transition is valid, false otherwise
   */
  private isValidStatusTransition(currentStatus: DiaryEntryStatus, newStatus: DiaryEntryStatus): boolean {
    // Define valid transitions - NEW LIFECYCLE: NEW → SUBMIT → REVIEWED (Final State)
    const validTransitions = {
      [DiaryEntryStatus.NEW]: [DiaryEntryStatus.SUBMIT],
      [DiaryEntryStatus.SUBMIT]: [DiaryEntryStatus.REVIEWED],
      [DiaryEntryStatus.REVIEWED]: [DiaryEntryStatus.SUBMIT], // Can resubmit after review
      [DiaryEntryStatus.CONFIRM]: [DiaryEntryStatus.SUBMIT], // Legacy confirm allows resubmit
    };

    // Admin can force any status
    return true;
  }
}
