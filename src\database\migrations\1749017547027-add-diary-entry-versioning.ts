import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddDiaryEntryVersioning1749017547027 implements MigrationInterface {
  name = 'AddDiaryEntryVersioning1749017547027';

  public async up(queryRunner: QueryRunner): Promise<void> {
    console.log('Adding diary entry versioning and notification fields...');

    // Check and create diary_entry_history table if it doesn't exist
    const historyTableExists = await queryRunner.query(`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_name='diary_entry_history'
    `);

    if (historyTableExists.length === 0) {
      await queryRunner.query(`
        CREATE TABLE "diary_entry_history" (
          "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
          "created_at" TIMESTAMP NOT NULL DEFAULT now(),
          "updated_at" TIMESTAMP DEFAULT now(),
          "created_by" character varying(36),
          "updated_by" character varying(36),
          "diary_entry_id" uuid NOT NULL,
          "title" character varying NOT NULL,
          "content" text NOT NULL,
          "version_number" integer NOT NULL,
          "is_latest" boolean NOT NULL DEFAULT false,
          "word_count" integer NOT NULL,
          "meta_data" json,
          CONSTRAINT "PK_diary_entry_history" PRIMARY KEY ("id")
        )
      `);
      console.log('Created diary_entry_history table');
    } else {
      console.log('diary_entry_history table already exists, skipping');
    }

    // Create indexes for diary_entry_history (with existence checks)
    const index1Exists = await queryRunner.query(`
      SELECT indexname
      FROM pg_indexes
      WHERE tablename='diary_entry_history' AND indexname='IDX_diary_entry_history_diary_entry_id_version'
    `);

    if (index1Exists.length === 0) {
      await queryRunner.query(`
        CREATE INDEX "IDX_diary_entry_history_diary_entry_id_version"
        ON "diary_entry_history" ("diary_entry_id", "version_number")
      `);
      console.log('Created index IDX_diary_entry_history_diary_entry_id_version');
    } else {
      console.log('Index IDX_diary_entry_history_diary_entry_id_version already exists, skipping');
    }

    const index2Exists = await queryRunner.query(`
      SELECT indexname
      FROM pg_indexes
      WHERE tablename='diary_entry_history' AND indexname='IDX_diary_entry_history_diary_entry_id_latest'
    `);

    if (index2Exists.length === 0) {
      await queryRunner.query(`
        CREATE INDEX "IDX_diary_entry_history_diary_entry_id_latest"
        ON "diary_entry_history" ("diary_entry_id", "is_latest")
      `);
      console.log('Created index IDX_diary_entry_history_diary_entry_id_latest');
    } else {
      console.log('Index IDX_diary_entry_history_diary_entry_id_latest already exists, skipping');
    }

    const index3Exists = await queryRunner.query(`
      SELECT indexname
      FROM pg_indexes
      WHERE tablename='diary_entry_history' AND indexname='IDX_diary_entry_history_created_at'
    `);

    if (index3Exists.length === 0) {
      await queryRunner.query(`
        CREATE INDEX "IDX_diary_entry_history_created_at"
        ON "diary_entry_history" ("created_at")
      `);
      console.log('Created index IDX_diary_entry_history_created_at');
    } else {
      console.log('Index IDX_diary_entry_history_created_at already exists, skipping');
    }

    // Add foreign key constraint for diary_entry_id (with existence check)
    const historyFkExists = await queryRunner.query(`
      SELECT constraint_name
      FROM information_schema.table_constraints
      WHERE table_name='diary_entry_history' AND constraint_name='FK_diary_entry_history_diary_entry'
    `);

    if (historyFkExists.length === 0) {
      await queryRunner.query(`
        ALTER TABLE "diary_entry_history"
        ADD CONSTRAINT "FK_diary_entry_history_diary_entry"
        FOREIGN KEY ("diary_entry_id")
        REFERENCES "diary_entry"("id")
        ON DELETE CASCADE
      `);
      console.log('Added FK_diary_entry_history_diary_entry constraint');
    } else {
      console.log('FK_diary_entry_history_diary_entry constraint already exists, skipping');
    }

    // Add new fields to diary_entry table (with existence checks)

    // Check and add current_version_id column if it doesn't exist
    const currentVersionExists = await queryRunner.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name='diary_entry' AND column_name='current_version_id'
    `);

    if (currentVersionExists.length === 0) {
      await queryRunner.query(`
        ALTER TABLE "diary_entry"
        ADD COLUMN "current_version_id" uuid
      `);
      console.log('Added current_version_id column');
    } else {
      console.log('current_version_id column already exists, skipping');
    }

    // Check and add total_versions column if it doesn't exist
    const totalVersionsExists = await queryRunner.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name='diary_entry' AND column_name='total_versions'
    `);

    if (totalVersionsExists.length === 0) {
      await queryRunner.query(`
        ALTER TABLE "diary_entry"
        ADD COLUMN "total_versions" integer NOT NULL DEFAULT 0
      `);
      console.log('Added total_versions column');
    } else {
      console.log('total_versions column already exists, skipping');
    }

    // Check and add first_submission_notified column if it doesn't exist
    const firstSubmissionNotifiedExists = await queryRunner.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name='diary_entry' AND column_name='first_submission_notified'
    `);

    if (firstSubmissionNotifiedExists.length === 0) {
      await queryRunner.query(`
        ALTER TABLE "diary_entry"
        ADD COLUMN "first_submission_notified" boolean NOT NULL DEFAULT false
      `);
      console.log('Added first_submission_notified column');
    } else {
      console.log('first_submission_notified column already exists, skipping');
    }

    // Check and add original_reviewed_version_id column if it doesn't exist
    const originalReviewedVersionExists = await queryRunner.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name='diary_entry' AND column_name='original_reviewed_version_id'
    `);

    if (originalReviewedVersionExists.length === 0) {
      await queryRunner.query(`
        ALTER TABLE "diary_entry"
        ADD COLUMN "original_reviewed_version_id" uuid
      `);
      console.log('Added original_reviewed_version_id column');
    } else {
      console.log('original_reviewed_version_id column already exists, skipping');
    }

    // Add foreign key constraints (with existence checks)

    // Check and add current_version foreign key constraint
    const currentVersionConstraintExists = await queryRunner.query(`
      SELECT constraint_name
      FROM information_schema.table_constraints
      WHERE table_name='diary_entry' AND constraint_name='FK_diary_entry_current_version'
    `);

    if (currentVersionConstraintExists.length === 0) {
      await queryRunner.query(`
        ALTER TABLE "diary_entry"
        ADD CONSTRAINT "FK_diary_entry_current_version"
        FOREIGN KEY ("current_version_id")
        REFERENCES "diary_entry_history"("id")
        ON DELETE SET NULL
      `);
      console.log('Added FK_diary_entry_current_version constraint');
    } else {
      console.log('FK_diary_entry_current_version constraint already exists, skipping');
    }

    // Check and add original_reviewed_version foreign key constraint
    const originalVersionConstraintExists = await queryRunner.query(`
      SELECT constraint_name
      FROM information_schema.table_constraints
      WHERE table_name='diary_entry' AND constraint_name='FK_diary_entry_original_reviewed_version'
    `);

    if (originalVersionConstraintExists.length === 0) {
      await queryRunner.query(`
        ALTER TABLE "diary_entry"
        ADD CONSTRAINT "FK_diary_entry_original_reviewed_version"
        FOREIGN KEY ("original_reviewed_version_id")
        REFERENCES "diary_entry_history"("id")
        ON DELETE SET NULL
      `);
      console.log('Added FK_diary_entry_original_reviewed_version constraint');
    } else {
      console.log('FK_diary_entry_original_reviewed_version constraint already exists, skipping');
    }

    // Migrate existing diary entries to create initial versions (only if not already migrated)
    const existingVersionsCount = await queryRunner.query(`
      SELECT COUNT(*) as count FROM diary_entry_history
    `);

    if (parseInt(existingVersionsCount[0].count) === 0) {
      console.log('Migrating existing diary entries to create initial versions...');

      await queryRunner.query(`
        INSERT INTO diary_entry_history (diary_entry_id, title, content, version_number, is_latest, word_count, created_at, created_by)
        SELECT
          id,
          title,
          content,
          1 as version_number,
          true as is_latest,
          CASE
            WHEN content IS NULL OR TRIM(content) = '' THEN 0
            ELSE array_length(string_to_array(TRIM(content), ' '), 1)
          END as word_count,
          created_at,
          created_by
        FROM diary_entry
        WHERE content IS NOT NULL;
      `);

      // Update diary entries with version info
      await queryRunner.query(`
        UPDATE diary_entry
        SET
          current_version_id = (
            SELECT id
            FROM diary_entry_history
            WHERE diary_entry_id = diary_entry.id
            AND is_latest = true
            LIMIT 1
          ),
          total_versions = 1
        WHERE EXISTS (
          SELECT 1
          FROM diary_entry_history
          WHERE diary_entry_id = diary_entry.id
        );
      `);

      console.log('Successfully migrated existing diary entries');
    } else {
      console.log('Diary entries already migrated, skipping data migration');
    }

    console.log('Successfully added diary entry versioning and notification fields');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    console.log('Removing diary entry versioning and notification fields...');

    // Remove foreign key constraints (with existence checks)
    const originalVersionConstraintExists = await queryRunner.query(`
      SELECT constraint_name
      FROM information_schema.table_constraints
      WHERE table_name='diary_entry' AND constraint_name='FK_diary_entry_original_reviewed_version'
    `);

    if (originalVersionConstraintExists.length > 0) {
      await queryRunner.query(`
        ALTER TABLE "diary_entry"
        DROP CONSTRAINT "FK_diary_entry_original_reviewed_version"
      `);
      console.log('Removed FK_diary_entry_original_reviewed_version constraint');
    }

    const currentVersionConstraintExists = await queryRunner.query(`
      SELECT constraint_name
      FROM information_schema.table_constraints
      WHERE table_name='diary_entry' AND constraint_name='FK_diary_entry_current_version'
    `);

    if (currentVersionConstraintExists.length > 0) {
      await queryRunner.query(`
        ALTER TABLE "diary_entry"
        DROP CONSTRAINT "FK_diary_entry_current_version"
      `);
      console.log('Removed FK_diary_entry_current_version constraint');
    }

    const historyConstraintExists = await queryRunner.query(`
      SELECT constraint_name
      FROM information_schema.table_constraints
      WHERE table_name='diary_entry_history' AND constraint_name='FK_diary_entry_history_diary_entry'
    `);

    if (historyConstraintExists.length > 0) {
      await queryRunner.query(`
        ALTER TABLE "diary_entry_history"
        DROP CONSTRAINT "FK_diary_entry_history_diary_entry"
      `);
      console.log('Removed FK_diary_entry_history_diary_entry constraint');
    }

    // Remove new fields from diary_entry (with existence checks)
    const columns = ['original_reviewed_version_id', 'first_submission_notified', 'total_versions', 'current_version_id'];

    for (const column of columns) {
      const columnExists = await queryRunner.query(`
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name='diary_entry' AND column_name='${column}'
      `);

      if (columnExists.length > 0) {
        await queryRunner.query(`
          ALTER TABLE "diary_entry"
          DROP COLUMN "${column}"
        `);
        console.log(`Removed ${column} column`);
      }
    }

    // Drop diary_entry_history table (with existence check)
    const historyTableExists = await queryRunner.query(`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_name='diary_entry_history'
    `);

    if (historyTableExists.length > 0) {
      await queryRunner.query(`DROP TABLE "diary_entry_history"`);
      console.log('Dropped diary_entry_history table');
    }

    console.log('Successfully processed removal of diary entry versioning and notification fields');
  }
}
