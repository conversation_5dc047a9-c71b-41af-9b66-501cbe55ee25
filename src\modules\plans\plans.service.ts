import { Injectable, NotFoundException, ConflictException, BadRequestException, Logger, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, In, LessThan } from 'typeorm';
import { Plan, SubscriptionType } from '../../database/entities/plan.entity';
import { UserPlan } from '../../database/entities/user-plan.entity';
import { EmailTemplateService } from '../../common/services/email-template.service';
import { User, UserType } from '../../database/entities/user.entity';
import { CreatePlanDto, UpdatePlanDto, PlanResponseDto, SubscribeToPlanDto, UserPlanResponseDto, PlanFilterDto } from 'src/database/models/plans.dto';
import { FreeSubscriptionDto } from './dto/free-subscription.dto';
import { SimplifiedPlanDto } from '../../database/models/simplified-plan.dto';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';
import { ApplyPromotionToPlanDto } from '../../database/models/apply-promotion-to-plan.dto';
import { forwardRef, Inject } from '@nestjs/common';
import { PromotionsService } from '../promotions/promotions.service';
import { Cron } from '@nestjs/schedule';
import { DiaryService } from '../diary/diary.service';
import { getCurrentUTCDate, addDaysUTC, addMonthsUTC, addYearsUTC, getStartOfDayUTC, getEndOfDayUTC } from '../../common/utils/date-utils';
import { PaymentService } from '../payment/services/payment.service';
import { InitiatePaymentDto } from '../payment/dto/payment.dto';
import { KcpPaymentMethod, PurchaseType } from '../../database/entities/payment-transaction.entity';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { PlanFeature } from '../../database/entities/plan-feature.entity';
import { TutorMatchingService } from '../tutor-matching/tutor-matching.service';
import { NotificationType } from '../../database/entities/notification.entity';
import { AsyncNotificationHelperService } from '../notification/async-notification-helper.service';
import { RelatedEntityType } from '../../common/enums/related-entity-type.enum';

// Profile paths (will be combined with FRONTEND_URL from environment)
const TUTOR_PROFILE_PATH = 'tutors/profile';
const STUDENT_PROFILE_PATH = 'dashboard/students/profile';

@Injectable()
export class PlansService {
  private readonly logger = new Logger(PlansService.name);
  private readonly frontendUrl: string;

  // Simple in-memory cache for active plans (cache for 5 minutes)
  private activePlansCache: { data: Plan[]; timestamp: number } | null = null;
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes in milliseconds
  private readonly tutorProfileUrl: string;
  private readonly studentProfileUrl: string;

  constructor(
    @InjectRepository(Plan)
    private readonly planRepository: Repository<Plan>,
    @InjectRepository(UserPlan)
    private readonly userPlanRepository: Repository<UserPlan>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(PlanFeature)
    private readonly planFeatureRepository: Repository<PlanFeature>,
    private readonly diaryService: DiaryService,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly tutorMatchingService: TutorMatchingService,
    private readonly asyncNotificationHelper: AsyncNotificationHelperService,
    private readonly emailTemplateService: EmailTemplateService,
    @Inject(forwardRef(() => PromotionsService))
    private readonly promotionsService: PromotionsService,
    @Inject(forwardRef(() => PaymentService))
    private readonly paymentService: PaymentService,
  ) {
    // Get frontend URL from environment variables with fallback
    const envFrontendUrl = this.configService.get<string>('FRONTEND_URL');
    if (!envFrontendUrl) {
      throw new Error('FRONTEND_URL environment variable is not set');
    }
    this.frontendUrl = envFrontendUrl;

    // Normalize frontend URL
    this.frontendUrl = this.frontendUrl.replace(/\/+$/, ''); // Remove trailing slashes

    // Create and validate profile URLs
    this.tutorProfileUrl = `${this.frontendUrl}/${TUTOR_PROFILE_PATH}`;
    this.studentProfileUrl = `${this.frontendUrl}/${STUDENT_PROFILE_PATH}`;

    try {
      new URL(this.tutorProfileUrl);
      new URL(this.studentProfileUrl);
    } catch (error) {
      throw new Error(`Invalid profile URLs generated. Please check FRONTEND_URL configuration. Error: ${error.message}`);
    }

    this.logger.log(`Frontend URL: ${this.frontendUrl}`);
    this.logger.log(`Tutor profile URL: ${this.tutorProfileUrl}`);
    this.logger.log(`Student profile URL: ${this.studentProfileUrl}`);
  }

  /**
   * Get cached active plans or fetch from database if cache is expired
   * @returns Array of active plans
   */
  private async getCachedActivePlans(): Promise<Plan[]> {
    const now = Date.now();

    // Check if cache is valid
    if (this.activePlansCache && (now - this.activePlansCache.timestamp) < this.CACHE_TTL) {
      this.logger.debug('Returning cached active plans');
      return this.activePlansCache.data;
    }

    // Fetch fresh data
    this.logger.debug('Fetching fresh active plans from database');
    const plans = await this.planRepository.find({
      where: { isActive: true },
      relations: ['planFeatures'],
      order: { createdAt: 'DESC' },
      select: ['id', 'name', 'type', 'subscriptionType', 'description', 'price', 'durationDays', 'autoRenew', 'isActive']
    });

    // Update cache
    this.activePlansCache = {
      data: plans,
      timestamp: now
    };

    return plans;
  }

  /**
   * Clear the active plans cache (call this when plans are modified)
   */
  private clearActivePlansCache(): void {
    this.activePlansCache = null;
    this.logger.debug('Active plans cache cleared');
  }

  async findAll(filterDto?: PlanFilterDto): Promise<PagedListDto<PlanResponseDto>> {
    // Default to active plans if no filter provided
    const { status = 'active', page = 1, limit = 10, sortBy, sortDirection } = filterDto || {};

    // OPTIMIZATION: Use cache for simple active plans queries
    if (status === 'active' && page === 1 && limit >= 10 && !sortBy) {
      const cachedPlans = await this.getCachedActivePlans();
      const totalCount = cachedPlans.length;
      const paginatedPlans = cachedPlans.slice(0, limit);

      return new PagedListDto(
        paginatedPlans.map((plan) => this.toPlanResponseDto(plan)),
        totalCount,
        page,
        limit,
      );
    }

    // Validate and map sortBy field to prevent SQL injection and column errors
    const allowedSortFields = ['id', 'name', 'type', 'subscriptionType', 'description', 'price', 'durationDays', 'autoRenew', 'isActive', 'createdAt', 'updatedAt'];
    const validSortBy = sortBy && allowedSortFields.includes(sortBy) ? sortBy : 'createdAt';
    const validSortDirection = sortDirection && ['ASC', 'DESC'].includes(sortDirection) ? sortDirection : 'DESC';

    // Build where condition based on status filter
    const whereCondition: any = {};

    if (status === 'active') {
      whereCondition.isActive = true;
    } else if (status === 'inactive') {
      whereCondition.isActive = false;
    }
    // For 'all', we don't add any isActive condition

    // OPTIMIZATION: Parallel queries for count and data
    const [totalCount, plans] = await Promise.all([
      this.planRepository.count({
        where: whereCondition,
      }),
      this.planRepository.find({
        where: whereCondition,
        relations: ['planFeatures'],
        skip: (page - 1) * limit,
        take: limit,
        order: { [validSortBy]: validSortDirection },
        select: ['id', 'name', 'type', 'subscriptionType', 'description', 'price', 'durationDays', 'autoRenew', 'isActive', 'createdAt', 'updatedAt'] // Include createdAt and updatedAt for sorting
      })
    ]);

    return new PagedListDto(
      plans.map((plan) => this.toPlanResponseDto(plan)),
      totalCount,
      page,
      limit,
    );
  }

  async findById(id: string): Promise<PlanResponseDto> {
    const plan = await this.planRepository.findOne({
      where: { id: id },
      relations: ['planFeatures'],
    });
    if (!plan) {
      throw new NotFoundException(`Plan with ID ${id} not found`);
    }
    return this.toPlanResponseDto(plan);
  }

  async create(createPlanDto: CreatePlanDto): Promise<PlanResponseDto> {
    // Check if plan with same name already exists
    const existingPlan = await this.planRepository.findOne({ where: { name: createPlanDto.name } });
    if (existingPlan) {
      throw new ConflictException(`Plan with name ${createPlanDto.name} already exists`);
    }

    // Set durationDays based on subscription type
    const durationDays = createPlanDto.subscriptionType === SubscriptionType.MONTHLY ? 30 : 365;

    const plan = this.planRepository.create({
      ...createPlanDto,
      durationDays,
    });

    // Handle feature IDs if provided
    if (createPlanDto.featureIds && createPlanDto.featureIds.length > 0) {
      const features = await this.planFeatureRepository.find({
        where: { id: In(createPlanDto.featureIds) },
      });

      if (features.length !== createPlanDto.featureIds.length) {
        throw new BadRequestException('One or more feature IDs are invalid');
      }

      plan.planFeatures = features;
    }

    const savedPlan = await this.planRepository.save(plan);

    // Clear cache since plans were modified
    this.clearActivePlansCache();

    // Fetch the plan with features to return
    const planWithFeatures = await this.planRepository.findOne({
      where: { id: savedPlan.id },
      relations: ['planFeatures'],
    });

    return this.toPlanResponseDto(planWithFeatures);
  }

  async update(id: string, updatePlanDto: UpdatePlanDto): Promise<PlanResponseDto> {
    const plan = await this.planRepository.findOne({
      where: { id: id },
      relations: ['planFeatures'],
    });
    if (!plan) {
      throw new NotFoundException(`Plan with ID ${id} not found`);
    }

    // Check if plan name is being changed and if new name already exists
    if (updatePlanDto.name && updatePlanDto.name !== plan.name) {
      const existingPlan = await this.planRepository.findOne({ where: { name: updatePlanDto.name } });
      if (existingPlan && existingPlan.id !== id) {
        throw new ConflictException(`Plan with name ${updatePlanDto.name} already exists`);
      }
    }

    // Handle feature IDs if provided
    if (updatePlanDto.featureIds && updatePlanDto.featureIds.length > 0) {
      const features = await this.planFeatureRepository.find({
        where: { id: In(updatePlanDto.featureIds) },
      });

      if (features.length !== updatePlanDto.featureIds.length) {
        throw new BadRequestException('One or more feature IDs are invalid');
      }

      plan.planFeatures = features;
    } // If subscription type is being updated, recalculate durationDays
    if (updatePlanDto.subscriptionType) {
      updatePlanDto.durationDays = updatePlanDto.subscriptionType === SubscriptionType.MONTHLY ? 30 : 365;
    }

    // Update plan properties
    Object.assign(plan, updatePlanDto);
    const updatedPlan = await this.planRepository.save(plan);

    // Clear cache since plans were modified
    this.clearActivePlansCache();

    // Fetch the plan with features to return
    const planWithFeatures = await this.planRepository.findOne({
      where: { id: updatedPlan.id },
      relations: ['planFeatures'],
    });

    return this.toPlanResponseDto(planWithFeatures);
  }

  async remove(id: string): Promise<void> {
    const plan = await this.planRepository.findOne({ where: { id: id } });
    if (!plan) {
      throw new NotFoundException(`Plan with ID ${id} not found`);
    }

    // Check if plan is being used by any user
    const userPlans = await this.userPlanRepository.find({ where: { planId: id, isActive: true } });
    if (userPlans.length > 0) {
      throw new BadRequestException(`Cannot delete plan as it is currently being used by ${userPlans.length} users`);
    }

    await this.planRepository.remove(plan);

    // Clear cache since plans were modified
    this.clearActivePlansCache();
  }

  async subscribeToPlan(subscribeToPlanDto: SubscribeToPlanDto, userId?: string, _isAdminRequest: boolean = false): Promise<UserPlanResponseDto> {
    // Use provided userId or the one from DTO
    const userIdToUse = userId || subscribeToPlanDto.userId;
    if (!userIdToUse) {
      throw new BadRequestException('User ID is required');
    }

    // OPTIMIZATION: Single query to get user with validation
    const user = await this.userRepository.findOne({
      where: { id: userIdToUse },
      select: ['id', 'name', 'email', 'type'] // Only select needed fields
    });
    if (!user) {
      throw new NotFoundException(`User with ID ${userIdToUse} not found`);
    }

    // Check if user is a student
    if (user.type !== UserType.STUDENT) {
      throw new BadRequestException('Only students can subscribe to plans');
    }

    // OPTIMIZATION: Parallel queries for plan and active subscriptions with optimized selects
    const [plan, activeUserPlans] = await Promise.all([
      this.planRepository.findOne({
        where: { id: subscribeToPlanDto.planId, isActive: true }, // Add isActive check in query
        relations: ['planFeatures'],
        select: ['id', 'name', 'type', 'subscriptionType', 'description', 'price', 'durationDays', 'autoRenew', 'isActive'] // Only select needed fields
      }),
      this.userPlanRepository.find({
        where: {
          userId: userIdToUse,
          isActive: true,
        },
        select: ['id', 'planId'], // Only select needed fields
        relations: ['plan'],
      })
    ]);

    if (!plan) {
      throw new NotFoundException(`Plan with ID ${subscribeToPlanDto.planId} not found`);
    }

    // Plan active check is now done in the query above for better performance

    if (activeUserPlans.length > 0) {
      const activePlanNames = activeUserPlans.map((up) => up.plan?.name || up.planId).join(', ');
      throw new ConflictException(`User already has active subscription(s): ${activePlanNames}. Please use the upgrade API to change plans.`);
    }

    // Check payment method types
    const isKcpPayment = subscribeToPlanDto.paymentMethod && ['kcp_card', 'kcp_bank', 'kcp_virtual_account', 'kcp_mobile'].includes(subscribeToPlanDto.paymentMethod);
    const isFreePayment = subscribeToPlanDto.paymentMethod === 'free';

    // Check for pending payments to prevent multiple payment attempts
    if (isKcpPayment) {
      const pendingUserPlans = await this.userPlanRepository.find({
        where: {
          userId: userIdToUse,
          isActive: false,
          isPaid: false,
        },
        relations: ['plan'],
      });

      if (pendingUserPlans.length > 0) {
        const pendingPlanNames = pendingUserPlans.map((up) => up.plan?.name || up.planId).join(', ');
        throw new ConflictException(`User has pending payment(s) for: ${pendingPlanNames}. Please complete or cancel the existing payment before starting a new subscription.`);
      }
    }

    // Calculate start and end dates in UTC
    const startDate = getCurrentUTCDate();
    let endDate: Date;

    // Set duration based on subscription type
    if (plan.subscriptionType === SubscriptionType.MONTHLY) {
      endDate = addMonthsUTC(startDate, 1); // 1 month
    } else if (plan.subscriptionType === SubscriptionType.YEARLY) {
      endDate = addYearsUTC(startDate, 1); // 1 year
    } else {
      // Fallback to durationDays if needed
      endDate = addDaysUTC(startDate, plan.durationDays);
    }

    // Set auto-renewal based on plan default or user preference
    const autoRenew = subscribeToPlanDto.autoRenew !== undefined ? subscribeToPlanDto.autoRenew : plan.autoRenew;

    // Calculate next renewal date if auto-renewal is enabled
    const nextRenewalDate = autoRenew ? new Date(endDate.getTime()) : undefined;

    // Environment protection for free payment
    if (isFreePayment) {
      const environment = process.env.NODE_ENV || 'development';
      const freePaymentEnabled = process.env.ENABLE_FREE_PAYMENT === 'true';

      // if (environment === 'production' && !freePaymentEnabled) {
      //     throw new ForbiddenException('Free payment method is not available in production environment');
      // }
    }

    if (isKcpPayment && plan.price > 0) {
      // For KCP payments, initiate payment process
      this.logger.log(`Initiating KCP payment for plan subscription: ${plan.name}, amount: ${plan.price}`);

      // Create a temporary user plan with payment pending status
      const tempUserPlan = this.userPlanRepository.create({
        userId: userIdToUse,
        planId: subscribeToPlanDto.planId,
        startDate: startDate,
        endDate: endDate,
        isActive: false, // Will be activated after payment confirmation
        paymentReference: subscribeToPlanDto.paymentReference,
        isPaid: false, // Will be set to true after payment confirmation
        autoRenew: autoRenew,
        nextRenewalDate: nextRenewalDate,
        lastRenewalDate: startDate,
      });

      const savedTempUserPlan = await this.userPlanRepository.save(tempUserPlan);

      // Prepare payment initiation request
      const paymentRequest: InitiatePaymentDto = {
        orderId: `PLAN-${savedTempUserPlan.id}-${Date.now()}`,
        amount: plan.price,
        currency: 'KRW',
        productName: `${plan.name} - ${plan.subscriptionType} subscription`,
        buyerName: user.email.split('@')[0], // Use email prefix as buyer name
        buyerEmail: user.email,
        buyerPhone: '010-0000-0000', // Default phone number - should be collected from user
        paymentMethod: this.mapPaymentMethodToKcp(subscribeToPlanDto.paymentMethod),
        purchaseType: PurchaseType.PLAN,
        referenceId: savedTempUserPlan.id,
        returnUrl: subscribeToPlanDto.returnUrl || `${this.frontendUrl}/payment/success`,
        cancelUrl: subscribeToPlanDto.cancelUrl || `${this.frontendUrl}/payment/cancel`,
        metadata: {
          planId: plan.id,
          userId: userIdToUse,
          subscriptionType: plan.subscriptionType,
          autoRenew,
        },
      };

      // Initiate payment with KCP
      const paymentResponse = await this.paymentService.initiatePayment(userIdToUse, paymentRequest);

      if (paymentResponse.success) {
        // Update the temporary user plan with payment transaction ID
        savedTempUserPlan.paymentTransactionId = paymentResponse.transactionId;
        await this.userPlanRepository.save(savedTempUserPlan);

        // Generate a new token with updated plan information (but not active yet)
        const newToken = await this.generateTokenWithPlanInfo(userIdToUse, plan, savedTempUserPlan);

        // Create response with user plan and new token - consistent format with user information
        const response = this.toUserPlanResponseDto(savedTempUserPlan, plan, user);
        response.access_token = newToken;
        response.paymentTransactionId = paymentResponse.transactionId;
        response.paymentUrl = paymentResponse.paymentUrl;
        response.expiresAt = paymentResponse.expiresAt;

        return response;
      } else {
        // Payment initiation failed, remove the temporary user plan
        await this.userPlanRepository.remove(savedTempUserPlan);
        throw new BadRequestException(`Payment initiation failed: ${paymentResponse.message}`);
      }
    } else {
      // For non-KCP payments (reward_points, free) or free plans, use existing logic
      const userPlan = this.userPlanRepository.create({
        userId: userIdToUse,
        planId: subscribeToPlanDto.planId,
        startDate: startDate,
        endDate: endDate,
        isActive: true,
        paymentReference: isFreePayment ? `FREE-${Date.now()}` : subscribeToPlanDto.paymentReference,
        isPaid: !!subscribeToPlanDto.paymentReference || plan.price === 0 || isFreePayment, // Mark as paid if payment reference is provided, plan is free, or using free payment
        autoRenew: autoRenew,
        nextRenewalDate: nextRenewalDate,
        lastRenewalDate: startDate, // Set initial subscription date as last renewal date
      });

      const savedUserPlan = await this.userPlanRepository.save(userPlan);

      // Log free payment if applicable
      if (isFreePayment) {
        this.logger.warn(`FREE PAYMENT SUBSCRIPTION: User ${userIdToUse} subscribed to plan ${plan.name} (${plan.id}) using free payment method`);
      }

      // Send async notification about successful subscription
      try {
          const htmlContent = this.emailTemplateService.generateSubscriptionWelcomeTemplate(plan, endDate);        await this.asyncNotificationHelper.notifyAsync(
          userIdToUse,
          NotificationType.SYSTEM,
          'Subscription Successful',
          `Welcome to ${plan.name}! Your subscription is now active. Start exploring your premium features!`,
          {
            relatedEntityId: savedUserPlan.id,
            relatedEntityType: RelatedEntityType.USER_PLAN,
            htmlContent: htmlContent,
            sendEmail: true,
            sendPush: true,
            sendInApp: true,
            sendRealtime: false,
          },
          {
            submissionId: savedUserPlan.id,
            entryType: 'plan_subscription',
            priority: 2, // Medium priority
          }
        );
        this.logger.log(`Sent subscription success notification to user ${userIdToUse}`);
      } catch (notificationError) {
        this.logger.error(`Failed to send subscription notification: ${notificationError.message}`, notificationError.stack);
        // Don't fail the subscription process if notification fails
      }

      // OPTIMIZATION: Create diary and assign tutors asynchronously to avoid blocking subscription
      this.handlePostSubscriptionTasks(userIdToUse, plan).catch(error => {
        this.logger.error(`Failed to handle post-subscription tasks for user ${userIdToUse}: ${error.message}`, error.stack);
      });

      // Generate a new token with updated plan information
      const newToken = await this.generateTokenWithPlanInfo(userIdToUse, plan, savedUserPlan);

      // Create response with user plan and new token including user information
      const response = this.toUserPlanResponseDto(savedUserPlan, plan, user);
      response.access_token = newToken;

      return response;
    }
  }

  /**
   * Upgrade or change user's plan
   * @param userId User ID
   * @param upgradePlanDto Upgrade plan data
   * @returns Updated user plan response
   */
  async upgradePlan(userId: string, upgradePlanDto: SubscribeToPlanDto): Promise<UserPlanResponseDto> {
    // OPTIMIZATION: Parallel queries for user, plan, active subscriptions, and pending payments
    const [user, plan, activeUserPlans, pendingUserPlans] = await Promise.all([
      this.userRepository.findOne({
        where: { id: userId },
        select: ['id', 'name', 'email', 'type'] // Only select needed fields
      }),
      this.planRepository.findOne({
        where: { id: upgradePlanDto.planId, isActive: true }, // Add isActive check in query
        relations: ['planFeatures'],
        select: ['id', 'name', 'type', 'subscriptionType', 'description', 'price', 'durationDays', 'autoRenew', 'isActive'] // Only select needed fields
      }),
      this.userPlanRepository.find({
        where: {
          userId: userId,
          isActive: true,
        },
        relations: ['plan'],
        select: ['id', 'userId', 'planId', 'startDate', 'endDate', 'isActive', 'isPaid', 'autoRenew'] // Only select needed fields
      }),
      this.userPlanRepository.find({
        where: {
          userId: userId,
          isActive: false,
          isPaid: false,
        },
        relations: ['plan'],
        select: ['id', 'userId', 'planId', 'startDate', 'endDate', 'isActive', 'isPaid', 'createdAt'] // Only select needed fields
      })
    ]);

    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    if (!plan) {
      throw new NotFoundException(`Plan with ID ${upgradePlanDto.planId} not found`);
    }

    // Plan active check is now done in the query above for better performance

    // Enhanced validation: Check for both active plans and pending payments
    if (activeUserPlans.length === 0) {
      // If no active plans, check if user has pending payments from previous incomplete upgrades
      if (pendingUserPlans.length > 0) {
        const pendingPlanNames = pendingUserPlans.map((up) => up.plan?.name || up.planId).join(', ');
        throw new ConflictException(`You have incomplete payment(s) for: ${pendingPlanNames}. Please complete or cancel the existing payment before starting a new upgrade. If you need to cancel, contact support or wait for automatic cleanup.`);
      }

      // No active plans and no pending payments - user needs to subscribe first
      throw new BadRequestException(`User has no active subscription to upgrade. Please use the subscribe API for new subscriptions.`);
    }

    // Check if user is trying to upgrade to the same plan
    const currentPlan = activeUserPlans[0];
    if (currentPlan.planId === upgradePlanDto.planId) {
      throw new ConflictException(`User is already subscribed to plan ${plan.name}`);
    }

    this.logger.log(`Upgrading user ${userId} from plan ${currentPlan.plan?.name || currentPlan.planId} to ${plan.name}`);

    // Store previous plan ID for tutor assignment logic
    const previousPlanId = currentPlan.planId;

    // IMPORTANT: Don't deactivate current plans yet - only deactivate after payment confirmation
    // This ensures user retains their current plan if payment fails
    this.logger.log(`Current plan will be deactivated after payment confirmation for user ${userId}`);

    // Calculate start and end dates in UTC
    const startDate = getCurrentUTCDate();
    let endDate: Date;

    // Set duration based on subscription type
    if (plan.subscriptionType === SubscriptionType.MONTHLY) {
      endDate = addMonthsUTC(startDate, 1); // 1 month
    } else if (plan.subscriptionType === SubscriptionType.YEARLY) {
      endDate = addYearsUTC(startDate, 1); // 1 year
    } else {
      // Fallback to durationDays if needed
      endDate = addDaysUTC(startDate, plan.durationDays);
    }

    // Set auto-renewal based on plan default or user preference
    const autoRenew = upgradePlanDto.autoRenew !== undefined ? upgradePlanDto.autoRenew : plan.autoRenew;

    // Calculate next renewal date if auto-renewal is enabled
    const nextRenewalDate = autoRenew ? new Date(endDate.getTime()) : undefined;

    // Check if this is a KCP payment
    const isKcpPayment = upgradePlanDto.paymentMethod && ['kcp_card', 'kcp_bank', 'kcp_virtual_account', 'kcp_mobile'].includes(upgradePlanDto.paymentMethod);

    // Check for pending payments to prevent multiple payment attempts (already checked above)
    if (isKcpPayment && pendingUserPlans.length > 0) {
      const pendingPlanNames = pendingUserPlans.map((up) => up.plan?.name || up.planId).join(', ');
      throw new ConflictException(`User has pending payment(s) for: ${pendingPlanNames}. Please complete or cancel the existing payment before starting a new upgrade.`);
    }

    if (isKcpPayment && plan.price > 0) {
      // For KCP payments, initiate payment process
      this.logger.log(`Initiating KCP payment for plan upgrade: ${plan.name}, amount: ${plan.price}`);

      // Create a temporary user plan with payment pending status
      const tempUserPlan = this.userPlanRepository.create({
        userId: userId,
        planId: upgradePlanDto.planId,
        startDate: startDate,
        endDate: endDate,
        isActive: false, // Will be activated after payment confirmation
        paymentReference: upgradePlanDto.paymentReference,
        isPaid: false, // Will be set to true after payment confirmation
        autoRenew: autoRenew,
        nextRenewalDate: nextRenewalDate,
        lastRenewalDate: startDate,
      });

      const savedTempUserPlan = await this.userPlanRepository.save(tempUserPlan);

      // Prepare payment initiation request
      const paymentRequest: InitiatePaymentDto = {
        orderId: `UPGRADE-${savedTempUserPlan.id}-${Date.now()}`,
        amount: plan.price,
        currency: 'KRW',
        productName: `${plan.name} - ${plan.subscriptionType} upgrade`,
        buyerName: user.email.split('@')[0], // Use email prefix as buyer name
        buyerEmail: user.email,
        buyerPhone: '010-0000-0000', // Default phone number - should be collected from user
        paymentMethod: this.mapPaymentMethodToKcp(upgradePlanDto.paymentMethod),
        purchaseType: PurchaseType.PLAN,
        referenceId: savedTempUserPlan.id,
        returnUrl: upgradePlanDto.returnUrl || `${this.frontendUrl}/payment/success`,
        cancelUrl: upgradePlanDto.cancelUrl || `${this.frontendUrl}/payment/cancel`,
        metadata: {
          planId: plan.id,
          userId: userId,
          subscriptionType: plan.subscriptionType,
          autoRenew,
          isUpgrade: true,
          previousPlanId: previousPlanId,
        },
      };

      // Initiate payment with KCP
      const paymentResponse = await this.paymentService.initiatePayment(userId, paymentRequest);

      if (paymentResponse.success) {
        // Update the temporary user plan with payment transaction ID
        savedTempUserPlan.paymentTransactionId = paymentResponse.transactionId;
        await this.userPlanRepository.save(savedTempUserPlan);

        // Generate a new token with updated plan information (but not active yet)
        const newToken = await this.generateTokenWithPlanInfo(userId, plan, savedTempUserPlan);

        // Create response with user plan and new token - consistent format with user information
        const response = this.toUserPlanResponseDto(savedTempUserPlan, plan, user);
        response.access_token = newToken;
        response.paymentTransactionId = paymentResponse.transactionId;
        response.paymentUrl = paymentResponse.paymentUrl;
        response.expiresAt = paymentResponse.expiresAt;

        return response;
      } else {
        // Payment initiation failed, remove the temporary user plan
        // No need to reactivate previous plan since it was never deactivated
        await this.userPlanRepository.remove(savedTempUserPlan);

        throw new BadRequestException(`Payment initiation failed: ${paymentResponse.message}`);
      }
    } else {
      // For non-KCP payments or free plans, create the new plan immediately
      const userPlan = this.userPlanRepository.create({
        userId: userId,
        planId: upgradePlanDto.planId,
        startDate: startDate,
        endDate: endDate,
        isActive: true,
        paymentReference: upgradePlanDto.paymentReference,
        isPaid: !!upgradePlanDto.paymentReference || plan.price === 0, // Mark as paid if payment reference is provided or plan is free
        autoRenew: autoRenew,
        nextRenewalDate: nextRenewalDate,
        lastRenewalDate: startDate, // Set initial subscription date as last renewal date
      });

      const savedUserPlan = await this.userPlanRepository.save(userPlan);

      // For non-KCP payments, deactivate previous plans immediately since payment is confirmed
      await this.deactivatePreviousPlans(userId, activeUserPlans, plan);

      // Handle tutor assignments for plan upgrade/downgrade
      try {
        // Only handle tutor assignments for student users
        if (user.type === UserType.STUDENT) {
          await this.diaryService.getOrCreateDiary(userId);
          this.logger.log(`Ensured diary exists for student with ID ${userId} after plan upgrade`);

          // Assign tutors with upgrade logic
          await this.assignTutorsForPlan(userId, plan, true, previousPlanId);
        }
      } catch (error) {
        // Log error but don't fail the upgrade process
        this.logger.error(`Failed to handle tutor assignments for user ${userId}: ${error.message}`, error.stack);
      }

      // Send async notification about successful plan upgrade
      try {
        const htmlContent = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #2c3e50;">Plan Upgrade Successful! 🎉</h2>
            <p>Congratulations! Your plan has been successfully upgraded to <strong>${plan.name}</strong>.</p>
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #495057; margin-top: 0;">Your New Plan Details:</h3>
              <ul style="color: #6c757d;">
                <li><strong>Plan:</strong> ${plan.name}</li>
                <li><strong>Type:</strong> ${plan.subscriptionType}</li>
                <li><strong>Valid Until:</strong> ${endDate.toLocaleDateString()}</li>
                <li><strong>Features:</strong> ${plan.planFeatures?.length || 0} premium features included</li>
              </ul>
            </div>
            <p>You can now enjoy all the enhanced features of your new plan. Start exploring your upgraded experience!</p>
            <p style="color: #6c757d; font-size: 14px;">Thank you for choosing our premium services.</p>
          </div>
        `;

        await this.asyncNotificationHelper.notifyAsync(
          userId,
          NotificationType.SYSTEM,
          'Plan Upgrade Successful',
          `Your plan has been successfully upgraded to ${plan.name}. Enjoy your enhanced features!`,
          {
            relatedEntityId: savedUserPlan.id,
            relatedEntityType: RelatedEntityType.USER_PLAN,
            htmlContent: htmlContent,
            sendEmail: true,
            sendPush: true,
            sendInApp: true,

            sendRealtime: false,
          },
          {
            submissionId: savedUserPlan.id,
            entryType: 'plan_upgrade',
            priority: 2, // Medium priority
          }
        );
        this.logger.log(`Sent plan upgrade notification to user ${userId}`);
      } catch (notificationError) {
        this.logger.error(`Failed to send plan upgrade notification: ${notificationError.message}`, notificationError.stack);
        // Don't fail the upgrade process if notification fails
      }

      // Generate a new token with updated plan information
      const newToken = await this.generateTokenWithPlanInfo(userId, plan, savedUserPlan);

      // Create response with user plan and new token including user information
      const response = this.toUserPlanResponseDto(savedUserPlan, plan, user);
      response.access_token = newToken;

      return response;
    }
  }

  /**
   * Subscribe to plan using free payment method (for development/testing)
   * @param freeDto Free subscription data
   * @param userId User ID
   * @returns User plan response with new JWT token
   */
  async subscribeWithFreePayment(freeDto: FreeSubscriptionDto, userId: string): Promise<UserPlanResponseDto> {
    // Environment protection
    // const environment = process.env.NODE_ENV || 'development';
    // const freePaymentEnabled = process.env.ENABLE_FREE_PAYMENT === 'true';

    // if (environment === 'production' && !freePaymentEnabled) {
    //     throw new ForbiddenException('Free payment method is not available in production environment');
    // }

    // 1. Validate plan exists and is active
    const plan = await this.planRepository.findOne({
      where: { id: freeDto.planId, isActive: true },
      relations: ['planFeatures'],
    });

    if (!plan) {
      throw new NotFoundException('Plan not found or inactive');
    }

    // 2. Check if user already has active subscription or pending payments
    const [existingActivePlans, existingPendingPlans] = await Promise.all([
      this.userPlanRepository.find({
        where: { userId, isActive: true },
        relations: ['plan'],
      }),
      this.userPlanRepository.find({
        where: { userId, isActive: false, isPaid: false },
        relations: ['plan'],
      })
    ]);

    if (existingActivePlans.length > 0) {
      const activePlanNames = existingActivePlans.map((up) => up.plan?.name || up.planId).join(', ');
      throw new ConflictException(`User already has active subscription(s): ${activePlanNames}. Use upgrade endpoint to change plans.`);
    }

    if (existingPendingPlans.length > 0) {
      const pendingPlanNames = existingPendingPlans.map((up) => up.plan?.name || up.planId).join(', ');
      throw new ConflictException(`User has pending payment(s) for: ${pendingPlanNames}. Please complete or cancel the existing payment before starting a new subscription.`);
    }

    // 3. Get user details
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['userRoles', 'userRoles.role'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Check if user is a student
    if (user.type !== UserType.STUDENT) {
      throw new BadRequestException('Only students can subscribe to plans');
    }

    // 4. Calculate dates
    const startDate = getCurrentUTCDate();
    let endDate: Date;

    // Set duration based on subscription type
    if (plan.subscriptionType === SubscriptionType.MONTHLY) {
      endDate = addMonthsUTC(startDate, 1); // 1 month
    } else if (plan.subscriptionType === SubscriptionType.YEARLY) {
      endDate = addYearsUTC(startDate, 1); // 1 year
    } else {
      // Fallback to durationDays if needed
      endDate = addDaysUTC(startDate, plan.durationDays);
    }

    // Set auto-renewal based on user preference or plan default
    const autoRenew = freeDto.autoRenew !== undefined ? freeDto.autoRenew : plan.autoRenew;
    const nextRenewalDate = autoRenew ? new Date(endDate.getTime()) : undefined;

    // 5. Create active user plan immediately (similar to reward_points logic)
    const userPlan = this.userPlanRepository.create({
      userId,
      planId: freeDto.planId,
      startDate,
      endDate,
      isActive: true, // Immediately active
      isPaid: true, // Mark as paid (free payment)
      paymentReference: `FREE-${Date.now()}`,
      autoRenew: autoRenew,
      nextRenewalDate,
      lastRenewalDate: startDate,
    });

    const savedUserPlan = await this.userPlanRepository.save(userPlan);

    // 6. Create audit log for free payment
    this.logger.warn(`FREE PAYMENT SUBSCRIPTION: User ${userId} subscribed to plan ${plan.name} (${plan.id}) using free payment method. ` + `Reason: ${freeDto.reason || 'Not provided'}`);

    // Send async notification about successful free subscription
    try {
      const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #2c3e50;">Welcome to ${plan.name}! 🎉</h2>
          <p>Your free subscription has been activated successfully. You now have access to all premium features!</p>
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #495057; margin-top: 0;">Your Subscription Details:</h3>
            <ul style="color: #6c757d;">
              <li><strong>Plan:</strong> ${plan.name}</li>
              <li><strong>Type:</strong> ${plan.subscriptionType}</li>
              <li><strong>Valid Until:</strong> ${endDate.toLocaleDateString()}</li>
              <li><strong>Features:</strong> ${plan.planFeatures?.length || 0} premium features included</li>
              <li><strong>Auto-Renewal:</strong> ${autoRenew ? 'Enabled' : 'Disabled'}</li>
            </ul>
          </div>
          <p>Start exploring all the amazing features your new plan has to offer. We're excited to have you on board!</p>
          <p style="color: #6c757d; font-size: 14px;">Thank you for choosing our services.</p>
        </div>
      `;

      // Generate batch ID for auto-subscription to help with deduplication
      const batchId = `auto_subscription_${userId}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      await this.asyncNotificationHelper.notifyAsync(
        userId,
        NotificationType.SYSTEM,
        'Free Subscription Activated',
        `Welcome to ${plan.name}! Your free subscription is now active. Start exploring your premium features!`,
        {
          relatedEntityId: savedUserPlan.id,
          relatedEntityType: RelatedEntityType.USER_PLAN,
          htmlContent: htmlContent,
          sendEmail: true,
          sendPush: true,
          sendInApp: true,
          sendRealtime: false,
        },
        {
          submissionId: savedUserPlan.id,
          entryType: 'free_subscription',
          priority: 2, // Medium priority
          batchId: batchId
        }
      );
      this.logger.log(`Sent free subscription notification to user ${userId}`);
    } catch (notificationError) {
      this.logger.error(`Failed to send free subscription notification: ${notificationError.message}`, notificationError.stack);
      // Don't fail the subscription process if notification fails
    }

    // 7. Create diary for student if they don't already have one
    // OPTIMIZATION: Handle post-subscription tasks asynchronously
    this.handlePostSubscriptionTasks(userId, plan).catch(error => {
      this.logger.error(`Failed to handle post-subscription tasks for user ${userId}: ${error.message}`, error.stack);
    });

    // 8. Generate new JWT with plan info
    const newToken = await this.generateTokenWithPlanInfo(userId, plan, savedUserPlan);

    // 9. Return response (same format as regular subscription) with user information
    const response = this.toUserPlanResponseDto(savedUserPlan, plan, user);
    response.access_token = newToken;
    response.paymentTransactionId = null;
    response.paymentUrl = null;
    response.expiresAt = null;

    return response;
  }

  /**
   * Upgrade to plan using free payment method (for development/testing)
   * @param freeDto Free subscription data
   * @param userId User ID
   * @returns User plan response with new JWT token
   */
  async upgradeWithFreePayment(freeDto: FreeSubscriptionDto, userId: string): Promise<UserPlanResponseDto> {
    // Environment protection
    const environment = process.env.NODE_ENV || 'development';
    const freePaymentEnabled = process.env.ENABLE_FREE_PAYMENT === 'true';

    // if (environment === 'production' && !freePaymentEnabled) {
    //     throw new ForbiddenException('Free payment method is not available in production environment');
    // }

    // 1. Validate plan exists and is active
    const plan = await this.planRepository.findOne({
      where: { id: freeDto.planId, isActive: true },
      relations: ['planFeatures'],
    });

    if (!plan) {
      throw new NotFoundException('Plan not found or inactive');
    }

    // 2. Check if user has active plans (required for upgrade)
    const activeUserPlans = await this.userPlanRepository.find({
      where: { userId, isActive: true },
      relations: ['plan'],
    });

    if (activeUserPlans.length === 0) {
      throw new BadRequestException('User has no active subscription to upgrade. Please use the subscribe API for new subscriptions.');
    }

    // Check if user is trying to upgrade to the same plan
    const currentPlan = activeUserPlans[0];
    if (currentPlan.planId === freeDto.planId) {
      throw new ConflictException(`User is already subscribed to plan ${plan.name}`);
    }

    // 3. Get user details
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['userRoles', 'userRoles.role'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Check if user is a student
    if (user.type !== UserType.STUDENT) {
      throw new BadRequestException('Only students can subscribe to plans');
    }

    // 4. Calculate dates
    const startDate = getCurrentUTCDate();
    let endDate: Date;

    // Set duration based on subscription type
    if (plan.subscriptionType === SubscriptionType.MONTHLY) {
      endDate = addMonthsUTC(startDate, 1); // 1 month
    } else if (plan.subscriptionType === SubscriptionType.YEARLY) {
      endDate = addYearsUTC(startDate, 1); // 1 year
    } else {
      // Fallback to durationDays if needed
      endDate = addDaysUTC(startDate, plan.durationDays);
    }

    // Set auto-renewal based on user preference or plan default
    const autoRenew = freeDto.autoRenew !== undefined ? freeDto.autoRenew : plan.autoRenew;
    const nextRenewalDate = autoRenew ? new Date(endDate.getTime()) : undefined;

    // 5. Create new user plan
    const userPlan = this.userPlanRepository.create({
      userId,
      planId: freeDto.planId,
      startDate,
      endDate,
      isActive: true, // Immediately active for free payments
      isPaid: true, // Mark as paid (free payment)
      paymentReference: `FREE-UPGRADE-${Date.now()}`,
      autoRenew: autoRenew,
      nextRenewalDate,
      lastRenewalDate: startDate,
    });

    const savedUserPlan = await this.userPlanRepository.save(userPlan);

    // 6. Deactivate previous plans after successful upgrade
    await this.deactivatePreviousPlans(userId, activeUserPlans, plan);

    // 7. Create audit log for free upgrade
    this.logger.warn(`FREE PAYMENT UPGRADE: User ${userId} upgraded to plan ${plan.name} (${plan.id}) using free payment method. ` + `Reason: ${freeDto.reason || 'Not provided'}`);

    // Send async notification about successful free upgrade
    try {
      const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #2c3e50;">Plan Upgrade Successful! 🎉</h2>
          <p>Your plan has been successfully upgraded to <strong>${plan.name}</strong> using free payment method.</p>
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #495057; margin-top: 0;">Your New Plan Details:</h3>
            <ul style="color: #6c757d;">
              <li><strong>Plan:</strong> ${plan.name}</li>
              <li><strong>Type:</strong> ${plan.subscriptionType}</li>
              <li><strong>Valid Until:</strong> ${endDate.toLocaleDateString()}</li>
              <li><strong>Features:</strong> ${plan.planFeatures?.length || 0} premium features included</li>
              <li><strong>Auto-Renewal:</strong> ${autoRenew ? 'Enabled' : 'Disabled'}</li>
            </ul>
          </div>
          <p>Start exploring all the amazing features your new plan has to offer!</p>
          <p style="color: #6c757d; font-size: 14px;">Thank you for choosing our services.</p>
        </div>
      `;

      await this.asyncNotificationHelper.notifyAsync(
        userId,
        NotificationType.SYSTEM,
        'Free Plan Upgrade Successful',
        `Your plan has been successfully upgraded to ${plan.name} using free payment method.`,
        {
          relatedEntityId: savedUserPlan.id,
          relatedEntityType: RelatedEntityType.USER_PLAN,
          htmlContent: htmlContent,
          sendEmail: true,
          sendPush: true,
          sendInApp: true,
          sendRealtime: false,
        },
        {
          submissionId: savedUserPlan.id,
          entryType: 'free_upgrade',
          priority: 2, // Medium priority
        }
      );
      this.logger.log(`Sent free upgrade notification to user ${userId}`);
    } catch (notificationError) {
      this.logger.error(`Failed to send free upgrade notification: ${notificationError.message}`, notificationError.stack);
      // Don't fail the upgrade process if notification fails
    }

    // 8. Handle post-upgrade tasks asynchronously
    this.handlePostSubscriptionTasks(userId, plan).catch(error => {
      this.logger.error(`Failed to handle post-upgrade tasks for user ${userId}: ${error.message}`, error.stack);
    });

    // 9. Generate new JWT with plan info
    const newToken = await this.generateTokenWithPlanInfo(userId, plan, savedUserPlan);

    // 10. Return response (same format as regular upgrade) with user information
    const response = this.toUserPlanResponseDto(savedUserPlan, plan, user);
    response.access_token = newToken;

    return response;
  }

  async getUserPlans(userId: string, paginationDto?: PaginationDto): Promise<PagedListDto<UserPlanResponseDto>> {
    // OPTIMIZATION: Parallel queries for user validation and count
    const [user, totalCount] = await Promise.all([
      this.userRepository.findOne({
        where: { id: userId },
        select: ['id', 'name', 'email', 'type'] // Only select needed fields
      }),
      this.userPlanRepository.count({
        where: { userId: userId },
      })
    ]);

    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    // OPTIMIZATION: Use query builder for better performance with selective loading
    const queryBuilder = this.userPlanRepository.createQueryBuilder('userPlan')
      .leftJoinAndSelect('userPlan.plan', 'plan')
      .leftJoinAndSelect('plan.planFeatures', 'planFeatures')
      .where('userPlan.userId = :userId', { userId })
      .select([
        'userPlan.id',
        'userPlan.startDate',
        'userPlan.endDate',
        'userPlan.isActive',
        'userPlan.isPaid',
        'userPlan.autoRenew',
        'userPlan.nextRenewalDate',
        'userPlan.createdAt',
        'userPlan.updatedAt',
        'plan.id',
        'plan.name',
        'plan.description',
        'plan.price',
        'plan.subscriptionType',
        'planFeatures.id',
        'planFeatures.name'
      ]);

    let page = 1;
    let limit = 10;

    if (paginationDto) {
      page = paginationDto.page || 1;
      limit = paginationDto.limit || 10;
      const { sortBy, sortDirection } = paginationDto;
      const skip = (page - 1) * limit;

      queryBuilder.skip(skip).take(limit);

      if (sortBy && sortDirection) {
        queryBuilder.orderBy(`userPlan.${sortBy}`, sortDirection.toUpperCase() as 'ASC' | 'DESC');
      } else {
        queryBuilder.orderBy('userPlan.createdAt', 'DESC');
      }
    } else {
      queryBuilder.orderBy('userPlan.createdAt', 'DESC');
    }

    const userPlans = await queryBuilder.getMany();

    return new PagedListDto(
      userPlans.map((userPlan) => this.toUserPlanResponseDto(userPlan, userPlan.plan, user)),
      totalCount,
      page,
      limit,
    );
  }

  async getActiveUserPlan(userId: string): Promise<UserPlanResponseDto> {
    // OPTIMIZATION: Parallel queries for user and active plan
    const [user, userPlan] = await Promise.all([
      this.userRepository.findOne({
        where: { id: userId },
        select: ['id', 'name', 'email', 'type'] // Only select needed fields
      }),
      this.userPlanRepository.createQueryBuilder('userPlan')
        .leftJoinAndSelect('userPlan.plan', 'plan')
        .leftJoinAndSelect('plan.planFeatures', 'planFeatures')
        .where('userPlan.userId = :userId AND userPlan.isActive = :isActive', { userId, isActive: true })
        .select([
          'userPlan.id',
          'userPlan.startDate',
          'userPlan.endDate',
          'userPlan.isActive',
          'userPlan.isPaid',
          'userPlan.autoRenew',
          'userPlan.nextRenewalDate',
          'userPlan.createdAt',
          'userPlan.updatedAt',
          'plan.id',
          'plan.name',
          'plan.description',
          'plan.price',
          'plan.subscriptionType',
          'planFeatures.id',
          'planFeatures.name'
        ])
        .getOne()
    ]);

    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    if (!userPlan) {
      throw new NotFoundException(`User does not have an active plan`);
    }

    return this.toUserPlanResponseDto(userPlan, userPlan.plan, user);
  }

  async cancelUserPlan(userId: string, planId: string): Promise<void> {
    // Find user
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    // Find user plan
    const userPlan = await this.userPlanRepository.findOne({
      where: { userId: userId, planId: planId, isActive: true },
      relations: ['plan'],
    });

    if (!userPlan) {
      throw new NotFoundException(`User does not have an active subscription to the specified plan`);
    }

    // Deactivate plan and set cancellation date
    userPlan.isActive = false;
    userPlan.autoRenew = false;
    userPlan.cancellationDate = new Date();
    await this.userPlanRepository.save(userPlan);
  }

  /**
   * Apply a promotion to multiple plans
   * @param applyPromotionToPlanDto Promotion application data
   * @returns Success message
   */
  async applyPromotionToPlans(applyPromotionToPlanDto: ApplyPromotionToPlanDto): Promise<{ success: boolean; message: string }> {
    try {
      const { promotionId, planIds } = applyPromotionToPlanDto;

      // Check if promotion exists and get its details
      let promotion: any;
      try {
        promotion = await this.promotionsService.getPromotionById(promotionId);
      } catch (error) {
        throw new NotFoundException(`Promotion with ID ${promotionId} not found`);
      }

      // Get all plans to update
      const plans = await this.planRepository.find({
        where: { id: In(planIds) },
      });

      if (plans.length === 0) {
        throw new NotFoundException(`No plans found with the provided IDs`);
      }

      // Check if any plans are missing
      if (plans.length !== planIds.length) {
        const foundPlanIds = plans.map((plan) => plan.id);
        const missingPlanIds = planIds.filter((id) => !foundPlanIds.includes(id));
        this.logger.warn(`Some plans were not found: ${missingPlanIds.join(', ')}`);
      }

      // Check if promotion has applicable plan restrictions
      const hasApplicablePlanRestrictions = promotion.applicablePlanIds && promotion.applicablePlanIds.length > 0;

      // Filter plans that are in applicable plan types
      const applicablePlans = hasApplicablePlanRestrictions ? plans.filter((plan) => promotion.applicablePlanIds.includes(plan.type)) : plans;

      // Check if any plans are not in applicable plan types
      const nonApplicablePlans = hasApplicablePlanRestrictions ? plans.filter((plan) => !promotion.applicablePlanIds.includes(plan.type)) : [];

      if (hasApplicablePlanRestrictions && applicablePlans.length === 0) {
        throw new BadRequestException('None of the selected plans are applicable to this promotion');
      }

      // Apply promotion to applicable plans
      for (const plan of applicablePlans) {
        plan.isApplicableForPromotion = true;
        plan.promotionId = promotionId;
      }

      // Save updated plans
      await this.planRepository.save(applicablePlans);

      // Prepare response message based on applicable plans
      let message = `Promotion applied to ${applicablePlans.length} plans`;
      if (nonApplicablePlans.length > 0) {
        message += `. ${nonApplicablePlans.length} plans were skipped because they are not applicable to this promotion.`;
      }

      return {
        success: true,
        message: message,
      };
    } catch (error) {
      this.logger.error(`Error applying promotion to plans: ${error.message}`, error.stack);

      if (error instanceof NotFoundException) {
        throw error;
      }

      throw new BadRequestException(`Failed to apply promotion to plans: ${error.message}`);
    }
  }

  /**
   * Remove promotion from a plan
   * @param planId Plan ID
   * @returns Success message
   */
  async removePromotionFromPlan(planId: string): Promise<{ success: boolean; message: string }> {
    try {
      // Find plan
      const plan = await this.planRepository.findOne({
        where: { id: planId },
      });

      if (!plan) {
        throw new NotFoundException(`Plan with ID ${planId} not found`);
      }

      // Check if plan has a promotion
      if (!plan.promotionId) {
        return {
          success: false,
          message: `Plan does not have an applied promotion`,
        };
      }

      // Remove promotion from plan
      plan.isApplicableForPromotion = false;
      plan.promotionId = null;

      // Save updated plan
      await this.planRepository.save(plan);

      return {
        success: true,
        message: `Promotion removed from plan successfully`,
      };
    } catch (error) {
      this.logger.error(`Error removing promotion from plan: ${error.message}`, error.stack);

      if (error instanceof NotFoundException) {
        throw error;
      }

      throw new BadRequestException(`Failed to remove promotion from plan: ${error.message}`);
    }
  }

  private formatLegacyFeatures(legacyFeatures: any): any[] {
    if (!legacyFeatures) {
      return [];
    }

    // If it's already an array, return it
    if (Array.isArray(legacyFeatures)) {
      return legacyFeatures;
    }

    // If it's an object with boolean values, convert it to array format
    if (typeof legacyFeatures === 'object') {
      return [
        {
          type: 'hec_user_diary',
          name: 'Hec User Diary',
          description: 'Access to the HEC User Diary platform',
          isActive: !!legacyFeatures.hecUserDiary,
        },
        {
          type: 'hec_play',
          name: 'HEC Play',
          description: 'Access to the HEC Play platform',
          isActive: !!legacyFeatures.hecPlay,
        },
        {
          type: 'english_qa_writing',
          name: 'English Q&A Writing Platform',
          description: 'Access to the English Q&A Writing Platform',
          isActive: !!legacyFeatures.englishQA,
        },
        {
          type: 'english_essay',
          name: 'English Essay Platform',
          description: 'Access to the English Essay Platform',
          isActive: !!legacyFeatures.englishEssay,
        },
        {
          type: 'english_novel',
          name: 'English Novel Platform',
          description: 'Access to the English Novel Platform',
          isActive: !!legacyFeatures.englishNovel,
        },
      ];
    }

    // Default to empty array
    return [];
  }

  private toPlanResponseDto(plan: Plan): PlanResponseDto {
    return {
      id: plan.id,
      name: plan.name,
      type: plan.type,
      subscriptionType: plan.subscriptionType,
      autoRenew: plan.autoRenew,
      description: plan.description,
      price: plan.price,
      durationDays: plan.durationDays,
      planFeatures: plan.planFeatures
        ? plan.planFeatures.map((feature) => ({
            id: feature.id,
            type: feature.type,
            name: feature.name,
            description: feature.description,
            createdAt: feature.createdAt,
            updatedAt: feature.updatedAt,
          }))
        : [],
      isActive: plan.isActive,
      isApplicableForPromotion: plan.isApplicableForPromotion,
      promotionId: plan.promotionId,
      createdAt: plan.createdAt,
      updatedAt: plan.updatedAt,
    };
  }

  private toUserPlanResponseDto(userPlan: UserPlan, plan: Plan, user?: User): UserPlanResponseDto {
    const response: UserPlanResponseDto = {
      id: userPlan.id,
      userId: userPlan.userId,
      planId: userPlan.planId,
      planName: plan.name,
      startDate: userPlan.startDate,
      endDate: userPlan.endDate,
      isActive: userPlan.isActive,
      paymentReference: userPlan.paymentReference,
      isPaid: userPlan.isPaid,
      autoRenew: userPlan.autoRenew,
      lastRenewalDate: userPlan.lastRenewalDate,
      nextRenewalDate: userPlan.nextRenewalDate,
      cancellationDate: userPlan.cancellationDate,
      notes: userPlan.notes,
      plan: this.toPlanDto(plan),
      paymentTransactionId: userPlan.paymentTransactionId,
      paymentUrl: null, // Will be set by calling method for KCP payments
      expiresAt: null, // Will be set by calling method for KCP payments
    };

    // Include user information if provided
    if (user) {
      response.user = {
        id: user.id,
        name: user.name,
        userId: user.userId,
        email: user.email,
        type: user.type,
        phoneNumber: user.phoneNumber,
      };
    }

    return response;
  }

  /**
   * Generate a new JWT token with updated plan information
   * @param userId The user ID
   * @param plan The active plan
   * @param userPlan The user plan details
   * @returns A new JWT token
   */
  async generateTokenWithPlanInfo(userId: string, plan: Plan, userPlan: UserPlan): Promise<string> {
    // Find user with roles
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['userRoles', 'userRoles.role'],
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    // Get user roles
    const userRoles = user.userRoles ? user.userRoles.map((userRole) => userRole.role.name) : [];

    // Create plan info for token
    const planInfo = {
      activePlan: plan.name,
      planType: plan.type,
      planId: plan.id,
      planExpiryDate: userPlan.endDate.toISOString(),
      planActive: userPlan.isActive,
    };

    // Generate JWT token with plan details
    const payload = {
      id: user.id,
      username: user.userId, // Use userId instead of email
      sub: user.id,
      name: user.name,
      type: user.type,
      selectedRole: user.type, // Use the user type as selected role
      roles: userRoles,
      ...planInfo,
    };

    // Get JWT secret from config
    const secret = this.configService.get<string>('JWT_SECRET');
    if (!secret) {
      this.logger.error('JWT_SECRET is not defined in environment variables');
      throw new Error('JWT_SECRET is not defined');
    }

    // Sign and return the token
    return this.jwtService.sign(payload, { secret });
  }

  /**
   * Convert a Plan entity to a SimplifiedPlanDto
   */
  private toPlanDto(plan: Plan): SimplifiedPlanDto {
    if (!plan) return null;

    return {
      id: plan.id,
      name: plan.name,
      type: plan.type,
      subscriptionType: plan.subscriptionType,
      description: plan.description,
      price: plan.price,
      features: plan.planFeatures
        ? plan.planFeatures.map((feature) => ({
            id: feature.id,
            type: feature.type,
            name: feature.name,
            description: feature.description,
          }))
        : [],
      isApplicableForPromotion: plan.isApplicableForPromotion,
      promotionId: plan.promotionId,
    };
  }

  /**
   * Handle post-subscription tasks asynchronously (diary creation and tutor assignment)
   * This prevents blocking the subscription response
   */
  private async handlePostSubscriptionTasks(userId: string, plan: Plan): Promise<void> {
    try {
      this.logger.log(`Starting post-subscription tasks for user ${userId}`);

      // Create diary for student if they don't already have one
      await this.diaryService.getOrCreateDiary(userId);
      this.logger.log(`Created diary for student with ID ${userId} after subscription`);

      // Assign tutors for all modules in the plan (new subscription)
      await this.assignTutorsForPlan(userId, plan, false);

      this.logger.log(`Completed post-subscription tasks for user ${userId}`);
    } catch (error) {
      this.logger.error(`Error in post-subscription tasks for user ${userId}: ${error.message}`, error.stack);
      // Don't throw - this is async and shouldn't affect the subscription response
    }
  }

  /**
   * Assign tutors for all modules in a plan
   * @param studentId Student ID
   * @param plan Plan with features
   */
  /**
   * Assign tutors for all modules in a student's active plan with ONE TUTOR PER STUDENT policy
   *
   * TUTOR ASSIGNMENT STRATEGY:
   * - ONE TUTOR PER STUDENT: Each student has exactly one tutor for ALL modules
   * - NEW SUBSCRIPTION: Assign one tutor for all features
   * - UPGRADE/DOWNGRADE: Use existing tutor for new modules, preserve existing assignments
   * - MISSING MODULES: Assign existing tutor to missing modules
   *
   * TUTOR SELECTION PREFERENCE:
   * - Use existing tutor if student already has one
   * - Select new tutor only if student has no existing tutor assignments
   *
   * @param studentId Student ID
   * @param plan Plan with features (optional - if not provided, will fetch the active plan)
   * @param isUpgrade Whether this is a plan change (default: false)
   * @param previousPlanId Previous plan ID for plan change scenarios
   */
  private async assignTutorsForPlan(studentId: string, plan?: Plan, isUpgrade: boolean = false, previousPlanId?: string): Promise<void> {
    try {
      this.logger.log(`Assigning tutors for student ${studentId}`);

      // OPTIMIZATION: Parallel queries for student and user plan
      const [student, userPlan] = await Promise.all([
        this.userRepository.findOne({
          where: { id: studentId },
          select: ['id', 'name', 'email'] // Only select needed fields
        }),
        plan ? null : this.userPlanRepository.findOne({
          where: { userId: studentId, isActive: true },
          relations: ['plan', 'plan.planFeatures'],
        })
      ]);

      if (!student) {
        this.logger.error(`Student with ID ${studentId} not found`);
        return;
      }

      if (!plan) {
        if (!userPlan) {
          this.logger.log(`Student ${studentId} does not have an active plan`);
          return;
        }
        plan = userPlan.plan;
        this.logger.log(`Found active plan ${plan.name} for student ${studentId}`);
      } else {
        this.logger.log(`Using provided plan ${plan.name} for student ${studentId}`);
      }

      // Ensure plan has features loaded
      if (!plan.planFeatures) {
        try {
          // Load plan with features if they weren't loaded
          plan = await this.planRepository.findOne({
            where: { id: plan.id },
            relations: ['planFeatures'],
          });

          if (!plan || !plan.planFeatures) {
            this.logger.error(`Failed to load plan features for plan ${plan?.id}`);
            return;
          }
        } catch (error) {
          this.logger.error(`Error loading plan features for plan ${plan?.id}: ${error.message}`, error.stack);
          return;
        }
      }

      // Determine which features need tutor assignment
      let featuresToAssign: PlanFeature[] = [];
      const existingAssignments: Array<{ tutor: User; module: PlanFeature; mapping: any }> = [];

      if (isUpgrade && previousPlanId) {
        // For plan changes, intelligently handle upgrades and downgrades
        this.logger.log(`Processing plan change from ${previousPlanId} to ${plan.id} for student ${studentId}`);

        // Get previous plan features
        const previousPlan = await this.planRepository.findOne({
          where: { id: previousPlanId },
          relations: ['planFeatures'],
        });

        if (previousPlan && previousPlan.planFeatures) {
          const previousFeatureIds = previousPlan.planFeatures.map((f) => f.id);
          const currentFeatureIds = plan.planFeatures.map((f) => f.id);

          // Find new features (in current plan but not in previous plan) - only for upgrades
          const newFeatureIds = currentFeatureIds.filter((id) => !previousFeatureIds.includes(id));

          // Find removed features (in previous plan but not in current plan) - for downgrades
          const removedFeatureIds = previousFeatureIds.filter((id) => !currentFeatureIds.includes(id));

          const isActualUpgrade = newFeatureIds.length > 0;
          const isDowngrade = removedFeatureIds.length > 0;

          if (isActualUpgrade) {
            this.logger.log(`Plan upgrade detected: ${newFeatureIds.length} new features to assign`);
            featuresToAssign = plan.planFeatures.filter((f) => newFeatureIds.includes(f.id));
          } else if (isDowngrade) {
            this.logger.log(`Plan downgrade detected: ${removedFeatureIds.length} features removed, preserving all existing tutors`);
            featuresToAssign = []; // Don't assign new tutors for downgrades
          } else {
            this.logger.log(`Plan change with same features, no tutor assignment needed`);
            featuresToAssign = [];
          }

          this.logger.log(
            `Plan change: ${previousFeatureIds.length} previous features, ${currentFeatureIds.length} current features, ${newFeatureIds.length} new features, ${removedFeatureIds.length} removed features`,
          );

          // Get existing assignments for ALL current features (including those from removed features)
          // This preserves tutors even for downgraded features in case of future upgrades
          const allExistingFeatureIds = [...new Set([...previousFeatureIds, ...currentFeatureIds])];
          for (const featureId of allExistingFeatureIds) {
            const existingAssignment = await this.tutorMatchingService.getStudentTutorForModule(studentId, featureId);
            if (existingAssignment) {
              const tutor = await this.userRepository.findOne({ where: { id: existingAssignment.tutorId } });
              // Check if this feature is in the current plan
              const feature = plan.planFeatures.find((f) => f.id === featureId);
              if (tutor && feature) {
                // Only include in notifications if feature is in current plan
                existingAssignments.push({
                  tutor,
                  module: feature,
                  mapping: existingAssignment,
                });
                this.logger.log(`Preserving existing assignment: ${tutor.name} for ${feature.name}`);
              } else if (tutor && removedFeatureIds.includes(featureId)) {
                // Log preserved assignments for removed features (not included in notifications)
                this.logger.log(`Preserving tutor assignment for future use: ${tutor.name} for removed feature ${featureId}`);
              }
            }
          }
        } else {
          this.logger.warn(`Could not load previous plan ${previousPlanId}, treating as new subscription`);
          featuresToAssign = plan.planFeatures;
        }
      } else {
        // For new subscriptions, assign tutors for all features
        featuresToAssign = plan.planFeatures;
        this.logger.log(`New subscription: assigning tutors for all ${featuresToAssign.length} features`);
      }

      // CRITICAL FIX: Check for missing tutors in ALL current plan features
      // This ensures students get tutors even if previous assignments failed
      const missingTutorFeatures = [];
      for (const feature of plan.planFeatures) {
        const existingAssignment = await this.tutorMatchingService.getStudentTutorForModule(studentId, feature.id);
        if (!existingAssignment) {
          // Check if this feature is already in featuresToAssign
          const alreadyIncluded = featuresToAssign.some((f) => f.id === feature.id);
          if (!alreadyIncluded) {
            missingTutorFeatures.push(feature);
            this.logger.warn(`MISSING TUTOR DETECTED: Student ${studentId} has no tutor for feature ${feature.name} (${feature.id})`);
          }
        }
      }

      // Add missing tutor features to assignment list
      if (missingTutorFeatures.length > 0) {
        this.logger.log(`Adding ${missingTutorFeatures.length} features with missing tutors to assignment queue`);
        featuresToAssign = [...featuresToAssign, ...missingTutorFeatures];
      }

      this.logger.log(
        `Found ${featuresToAssign.length} features requiring tutor assignment in plan ${plan.name} (${missingTutorFeatures.length} missing, ${featuresToAssign.length - missingTutorFeatures.length} new)`,
      );

      // Track all tutor assignments for consolidated notification
      const tutorAssignments = [...existingAssignments];

      // Get existing tutor IDs for logging purposes
      const existingTutorIds = existingAssignments.map((assignment) => assignment.tutor.id);
      this.logger.log(`Existing tutors for this student: [${existingTutorIds.join(', ')}]`);

      // STEP 3: Get or select the ONE tutor for this student (ONE TUTOR PER STUDENT policy)
      let studentAssignedTutor: User | null = null;

      if (existingAssignments.length > 0) {
        // Student already has a tutor - use the existing one
        studentAssignedTutor = existingAssignments[0].tutor;
        this.logger.log(`Using existing tutor ${studentAssignedTutor.name} (${studentAssignedTutor.id}) for student ${studentId}`);

        // Verify consistency - all assignments should use the same tutor
        const uniqueTutors = [...new Set(existingAssignments.map(a => a.tutor.id))];
        if (uniqueTutors.length > 1) {
          this.logger.warn(`INCONSISTENCY DETECTED: Student ${studentId} has multiple tutors: [${uniqueTutors.join(', ')}]. Using first tutor: ${studentAssignedTutor.id}`);
        }
      } else {
        // Student has no tutor yet - select a new one
        try {
          studentAssignedTutor = await this.tutorMatchingService.getOrSelectPreferredTutor(studentId);
          this.logger.log(`Selected new tutor ${studentAssignedTutor.name} (${studentAssignedTutor.id}) for student ${studentId}`);
        } catch (error) {
          this.logger.error(`Failed to select tutor for student ${studentId}: ${error.message}`, error.stack);
          return; // Cannot proceed without a tutor
        }
      }

      // STEP 4: Assign the ONE tutor to all missing modules
      for (const moduleFeature of featuresToAssign) {
        try {
          if (!moduleFeature || !moduleFeature.id) {
            this.logger.warn(`Invalid module feature found, skipping`);
            continue;
          }

          const moduleId = moduleFeature.id;

          // Check if student already has a tutor for this module
          const existingAssignment = await this.tutorMatchingService.getStudentTutorForModule(studentId, moduleId);

          if (existingAssignment) {
            this.logger.log(`Student ${studentId} already has a tutor for module ${moduleId} (${moduleFeature.name}) - skipping`);
            continue;
          }

          // Assign the student's designated tutor to this module
          this.logger.log(`Assigning tutor ${studentAssignedTutor.name} (${studentAssignedTutor.id}) to module ${moduleId} (${moduleFeature.name}) for student ${studentId}`);

          // Use the autoAssignTutorsWithoutNotifications method to avoid individual notifications
          // This will use the ONE TUTOR PER STUDENT policy automatically
          try {
            const assignments = await this.tutorMatchingService.autoAssignTutorsWithoutNotifications({
              planFeatureId: moduleId,
              studentIds: [studentId],
              reassignExisting: false,
            });

            if (assignments && assignments.length > 0) {
              const assignment = assignments[0];
              tutorAssignments.push({
                tutor: studentAssignedTutor,
                module: moduleFeature,
                mapping: assignment,
              });
              this.logger.log(`Successfully assigned tutor ${studentAssignedTutor.name} to module ${moduleFeature.name} for student ${studentId}`);
            }
          } catch (error) {
            this.logger.error(`Failed to assign tutor to module ${moduleId}: ${error.message}`, error.stack);
            continue;
          }


        } catch (error) {
          // Log error but continue with other modules
          this.logger.error(`Failed to assign tutor for student ${studentId} for module ${moduleFeature.id} (${moduleFeature.name}): ${error.message}`, error.stack);
        }
      }

      // Send consolidated notification to student if there are any assignments
      if (tutorAssignments.length > 0) {
        // Generate a unique batch ID for this subscription event to help with deduplication
        const batchId = `subscription_${studentId}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

        try {
          this.logger.log(`Sending consolidated notification to student ${studentId} for ${tutorAssignments.length} tutor assignments`);
          await this.sendConsolidatedTutorAssignmentNotification(student, tutorAssignments, batchId);
          this.logger.log(`Successfully sent consolidated notification to student ${studentId}`);
        } catch (notificationError) {
          this.logger.error(`Failed to send consolidated notification to student ${studentId}: ${notificationError.message}`, notificationError.stack);
          // Continue with tutor notifications even if student notification fails
        }

        // Send consolidated notifications to tutors
        this.logger.log(`Sending consolidated notifications to tutors for ${tutorAssignments.length} assignments`);
        await this.sendConsolidatedTutorNotifications(student, tutorAssignments, batchId);
      }
    } catch (error) {
      this.logger.error(`CRITICAL: Failed to assign tutors for student ${studentId}: ${error.message}`, error.stack);

      // Log additional context for debugging
      this.logger.error(`Tutor assignment failure context:`, {
        studentId,
        planId: plan?.id,
        planName: plan?.name,
        isUpgrade,
        previousPlanId,
        errorType: error.constructor.name,
        errorMessage: error.message,
      });

      // Don't rethrow to prevent disrupting the subscription process, but ensure visibility
    }
  }

  /**
   * Fix missing tutor assignments for a student
   * This method checks all features in the student's active plan and assigns tutors for any missing assignments
   * @param studentId Student ID to fix assignments for
   * @returns Summary of assignments created
   */
  async fixMissingTutorAssignments(studentId: string): Promise<{
    assignmentsCreated: number;
    featuresChecked: number;
    missingFeatures: string[];
    errors: string[];
  }> {
    try {
      this.logger.log(`Starting missing tutor assignment fix for student ${studentId}`);

      // Get student's active plan
      const activeUserPlan = await this.userPlanRepository.findOne({
        where: { userId: studentId, isActive: true },
        relations: ['plan', 'plan.planFeatures'],
      });

      if (!activeUserPlan || !activeUserPlan.plan) {
        throw new NotFoundException(`No active plan found for student ${studentId}`);
      }

      const plan = activeUserPlan.plan;
      this.logger.log(`Found active plan: ${plan.name} with ${plan.planFeatures.length} features`);

      // Check each feature for missing tutors
      const missingFeatures = [];
      const errors = [];
      let assignmentsCreated = 0;

      for (const feature of plan.planFeatures) {
        try {
          const existingAssignment = await this.tutorMatchingService.getStudentTutorForModule(studentId, feature.id);

          if (!existingAssignment) {
            missingFeatures.push(feature.name);
            this.logger.log(`Missing tutor for feature ${feature.name}, attempting assignment`);

            // Assign tutor for this feature using consistent tutor assignment logic
            const assignments = await this.tutorMatchingService.autoAssignTutorsWithoutNotifications({
              planFeatureId: feature.id,
              studentIds: [studentId],
              reassignExisting: false,
            });

            if (assignments && assignments.length > 0) {
              assignmentsCreated++;
              this.logger.log(`Successfully assigned tutor for feature ${feature.name}`);
            } else {
              errors.push(`No tutors available for feature ${feature.name}`);
              this.logger.warn(`No tutors available for feature ${feature.name}`);
            }
          }
        } catch (featureError) {
          errors.push(`Failed to assign tutor for ${feature.name}: ${featureError.message}`);
          this.logger.error(`Failed to assign tutor for feature ${feature.name}: ${featureError.message}`, featureError.stack);
        }
      }

      const result = {
        assignmentsCreated,
        featuresChecked: plan.planFeatures.length,
        missingFeatures,
        errors,
      };

      this.logger.log(`Missing tutor assignment fix completed for student ${studentId}:`, result);
      return result;
    } catch (error) {
      this.logger.error(`Failed to fix missing tutor assignments for student ${studentId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Send a consolidated notification to a student about all their tutor assignments
   * @param student Student user
   * @param assignments Array of tutor assignments
   * @param batchId Optional batch ID for deduplication
   */
  private async sendConsolidatedTutorAssignmentNotification(student: User, assignments: Array<{ tutor: User; module: PlanFeature; mapping: { id: string } }>, batchId?: string): Promise<void> {
    try {
      if (!student || !student.id) {
        this.logger.error(`Invalid student object provided to sendConsolidatedTutorAssignmentNotification`);
        return;
      }

      if (!assignments || !Array.isArray(assignments) || assignments.length === 0) {
        this.logger.error(`No valid assignments provided for student ${student.id}`);
        return;
      }

      // Validate assignments to ensure they have all required properties
      const validAssignments = assignments.filter(
        (assignment) => assignment && assignment.tutor && assignment.tutor.name && assignment.module && assignment.module.name && assignment.mapping && assignment.mapping.id,
      );

      if (validAssignments.length === 0) {
        this.logger.error(`No valid assignments after filtering for student ${student.id}`);
        return;
      }

      // For email: Create a consolidated HTML message with tutor assignment
      // Since students now have one tutor for all features, group by tutor
      const tutorGroups = new Map<string, { tutor: any; modules: string[] }>();

      validAssignments.forEach((assignment) => {
        const tutorId = assignment.tutor.id;
        if (!tutorGroups.has(tutorId)) {
          tutorGroups.set(tutorId, {
            tutor: assignment.tutor,
            modules: [],
          });
        }
        tutorGroups.get(tutorId).modules.push(assignment.module.name);
      });

      let htmlMessage = '';

      if (tutorGroups.size === 1) {
        // Single tutor for all features
        const tutorGroup = Array.from(tutorGroups.values())[0];
        htmlMessage = this.emailTemplateService.generateStudentTutorAssignmentTemplate(
          tutorGroup.tutor.name, 
          tutorGroup.tutor.id,
          this.tutorProfileUrl,
          tutorGroup.modules
        );
      }

      // For in-app: Send consolidated notification instead of individual ones
      try {
        // Create consolidated in-app message
        let inAppMessage: string;
        let inAppTitle: string;

        if (tutorGroups.size === 1) {
          // Single tutor for all features
          const tutorGroup = Array.from(tutorGroups.values())[0];
          inAppTitle = 'New Tutor Assignment';
          inAppMessage = `You have been assigned ${tutorGroup.tutor.name} as your tutor for ${tutorGroup.modules.length} module${tutorGroup.modules.length > 1 ? 's' : ''}.`;
        } else {
          // Multiple tutors (shouldn't happen with current logic, but handle gracefully)
          inAppTitle = 'New Tutor Assignments';
          inAppMessage = `You have been assigned tutors for ${validAssignments.length} modules.`;
        }

        // Send single consolidated in-app notification
        // Send single consolidated in-app notification with deep linking to tutor profile
        const notificationOptions = {
          relatedEntityId: Array.from(tutorGroups.values())[0].tutor.id, // Use tutor ID for profile link
          relatedEntityType: RelatedEntityType.STUDENT_TUTOR_MAPPING, // Required for proper deep linking
          sendInApp: true,
          sendEmail: false,
          sendPush: false,
          sendRealtime: false,
        };

        await this.asyncNotificationHelper.notifyAsync(
          student.id,
          NotificationType.TUTOR_ASSIGNMENT,
          inAppTitle,
          inAppMessage,
          notificationOptions,
          { batchId: batchId }
        );
      } catch (inAppError) {
        this.logger.error(`Failed to send consolidated in-app notification to student ${student.id}: ${inAppError.message}`, inAppError.stack);
        // Continue with other notifications
      }

      try {
        // Send consolidated email notification
        // Create a text message for email (matching the HTML logic)
        let textMessage = '';

        if (tutorGroups.size === 1) {
          // Single tutor for all features
          const tutorGroup = Array.from(tutorGroups.values())[0];
          textMessage = `You have been assigned ${tutorGroup.tutor.name} as your tutor for all features.\n\nYour tutor will help you with:\n`;

          tutorGroup.modules.forEach((moduleName) => {
            textMessage += `• ${moduleName}\n`;
          });

          textMessage += `\nYou can view your tutor's profile in the system.`;
        } 

        // Create appropriate title based on number of tutors
        const emailTitle =  'Your Tutor Assignment';

        // Send consolidated email notification with deep linking to tutor profile
        const emailOptions = {
          relatedEntityId: Array.from(tutorGroups.values())[0].tutor.id, // Use tutor ID for profile link
          relatedEntityType: RelatedEntityType.STUDENT_TUTOR_MAPPING, // Required for proper deep linking
          htmlContent: htmlMessage,
          sendEmail: true,
          sendInApp: false,
          sendPush: false,
          sendRealtime: false,
        };

        await this.asyncNotificationHelper.notifyAsync(
          student.id,
          NotificationType.TUTOR_ASSIGNMENT,
          emailTitle,
          textMessage,
          emailOptions,
          { batchId: batchId }
        );
      } catch (emailError) {
        this.logger.error(`Failed to send email notification to student ${student.id}: ${emailError.message}`, emailError.stack);
        // Continue with push notification
      }

      try {
        // Create detailed push message consistent with email
        let pushMessage: string;
        let pushTitle: string;

        if (tutorGroups.size === 1) {
          // Single tutor for all features
          const tutorGroup = Array.from(tutorGroups.values())[0];
          pushTitle = 'New Tutor Assignment';
          if (tutorGroup.modules.length === 1) {
            pushMessage = `You have been assigned ${tutorGroup.tutor.name} as your tutor for ${tutorGroup.modules[0]}. Your tutor will guide you through your learning journey and provide personalized feedback.`;
          } else {
            pushMessage = `You have been assigned ${tutorGroup.tutor.name} as your tutor for ${tutorGroup.modules.length} modules. Your tutor will guide you through your learning journey and provide personalized feedback.`;
          }
        } else {
          // Multiple tutors (shouldn't happen with current logic, but handle gracefully)
          pushTitle = 'New Tutor Assignments';
          pushMessage = `You have been assigned tutors for ${validAssignments.length} modules. Your tutors will guide you through your learning journey and provide personalized feedback.`;
        }

        await this.asyncNotificationHelper.notifyAsync(student.id, NotificationType.TUTOR_ASSIGNMENT, pushTitle, pushMessage, {
          relatedEntityId: validAssignments[0].mapping.id,
          relatedEntityType: RelatedEntityType.STUDENT_TUTOR_MAPPING,
          sendPush: true,
          sendEmail: false,
          sendInApp: false,
          sendRealtime: false,
        }, {
          batchId: batchId
        });
      } catch (pushError) {
        this.logger.error(`Failed to send push notification to student ${student.id}: ${pushError.message}`, pushError.stack);
        // Continue execution
      }

      this.logger.log(`Sent tutor assignment notifications to student ${student.id}`);
    } catch (error) {
      this.logger.error(`Error sending notifications to student ${student.id}: ${error.message}`, error.stack);
      // Rethrow to allow the caller to handle the error
      throw error;
    }
  }

  /**
   * Send consolidated notifications to tutors about student assignments
   * Groups assignments by tutor to send one notification per tutor
   * @param student Student user
   * @param tutorAssignments Array of tutor assignments
   * @param batchId Optional batch ID for deduplication
   */
  private async sendConsolidatedTutorNotifications(student: User, tutorAssignments: Array<{
    tutor: User;
    module: PlanFeature;
    mapping: any;
  }>, batchId?: string): Promise<void> {
    try {
      // Group assignments by tutor
      const tutorGroups = new Map<string, {
        tutor: User;
        modules: string[];
        mappings: any[];
      }>();

      for (const assignment of tutorAssignments) {
        if (!assignment.tutor || !assignment.module || !assignment.mapping) {
          this.logger.warn(`Invalid assignment data, skipping tutor notification`);
          continue;
        }

        const tutorId = assignment.tutor.id;
        if (!tutorGroups.has(tutorId)) {
          tutorGroups.set(tutorId, {
            tutor: assignment.tutor,
            modules: [],
            mappings: []
          });
        }

        const tutorGroup = tutorGroups.get(tutorId);
        tutorGroup.modules.push(assignment.module.name);
        tutorGroup.mappings.push(assignment.mapping);
      }

      // Send consolidated notification to each tutor
      for (const [tutorId, tutorGroup] of tutorGroups) {
        try {
          await this.sendConsolidatedTutorNotification(tutorGroup.tutor, student, tutorGroup.modules, tutorGroup.mappings[0].id, batchId);
        } catch (tutorNotificationError) {
          this.logger.error(`Failed to send consolidated notification to tutor ${tutorId}: ${tutorNotificationError.message}`, tutorNotificationError.stack);
          // Continue with other tutor notifications
        }
      }
    } catch (error) {
      this.logger.error(`Error sending consolidated tutor notifications: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Send a consolidated notification to a tutor about a student assignment
   * @param tutor Tutor user
   * @param student Student user
   * @param modules Array of module names
   * @param mappingId Primary mapping ID for reference
   * @param batchId Optional batch ID for deduplication
   */
  private async sendConsolidatedTutorNotification(tutor: User, student: User, modules: string[], mappingId: string, batchId?: string): Promise<void> {
    try {
      // Validate input parameters
      if (!tutor || !tutor.id) {
        this.logger.error(`Invalid tutor object provided to sendConsolidatedTutorNotification`);
        return;
      }

      if (!student || !student.id || !student.name) {
        this.logger.error(`Invalid student object provided to sendConsolidatedTutorNotification for tutor ${tutor.id}`);
        return;
      }

      if (!modules || modules.length === 0) {
        this.logger.error(`Invalid modules provided to sendConsolidatedTutorNotification for tutor ${tutor.id}`);
        return;
      }

      if (!mappingId) {
        this.logger.error(`Invalid mappingId provided to sendConsolidatedTutorNotification for tutor ${tutor.id}`);
        return;
      }

      // Create consolidated message
      let message: string;
      let htmlMessage: string;
      let emailTitle: string;

      message = modules.length === 1 
        ? `You have been assigned as a tutor for ${student.name} in ${modules[0]}.`
        : `You have been assigned as a tutor for ${student.name} across ${modules.length} modules.`;
      emailTitle = `New Student Assignment: ${student.name}`;
      htmlMessage = this.emailTemplateService.generateTutorAssignmentTemplate(
        student.name, 
        student.id, 
        this.studentProfileUrl, 
        modules
      );

      // Send single consolidated in-app notification instead of per-module notifications
      try {
        let inAppMessage: string;
        let inAppTitle: string;

        if (modules.length === 1) {
          // Single module
          inAppMessage = `You have been assigned as a tutor for ${student.name} in ${modules[0]}.`;
          inAppTitle = `New Student Assignment: ${student.name}`;
        } else {
          // Multiple modules
          inAppMessage = `You have been assigned as a tutor for ${student.name} across ${modules.length} modules.`;
          inAppTitle = `New Student Assignment: ${student.name}`;
        }

        await this.asyncNotificationHelper.notifyAsync(tutor.id, NotificationType.TUTOR_ASSIGNMENT, inAppTitle, inAppMessage, {
          relatedEntityId: mappingId,
          relatedEntityType: RelatedEntityType.STUDENT_TUTOR_MAPPING,
          sendInApp: true,
          sendEmail: false,
          sendPush: false,
          sendRealtime: false,
        }, {
          batchId: batchId
        });
      } catch (inAppError) {
        this.logger.error(`Failed to send consolidated in-app notification to tutor ${tutor.id}: ${inAppError.message}`, inAppError.stack);
        // Continue with other notifications
      }

      // Send consolidated email notification
      try {
        await this.asyncNotificationHelper.notifyAsync(tutor.id, NotificationType.TUTOR_ASSIGNMENT, emailTitle, message, {
          relatedEntityId: mappingId,
          relatedEntityType: RelatedEntityType.STUDENT_TUTOR_MAPPING,
          htmlContent: htmlMessage,
          sendEmail: true,
          sendInApp: false,
          sendPush: false,
          sendRealtime: false,
        }, {
          batchId: batchId
        });
      } catch (emailError) {
        this.logger.error(`Failed to send email notification to tutor ${tutor.id}: ${emailError.message}`, emailError.stack);
        // Continue with push notification
      }

      // Send consolidated push notification
      try {
        const pushTitle = `New Student Assignment: ${student.name}`;
        let pushMessage: string;

        if (modules.length === 1) {
          pushMessage = `You have been assigned as a tutor for ${student.name} in ${modules[0]}. Please review their profile and begin providing guidance and support for their learning journey.`;
        } else {
          pushMessage = `You have been assigned as a tutor for ${student.name} across ${modules.length} modules. Please review their profile and begin providing guidance and support for their learning journey.`;
        }

        await this.asyncNotificationHelper.notifyAsync(tutor.id, NotificationType.TUTOR_ASSIGNMENT, pushTitle, pushMessage, {
          relatedEntityId: mappingId,
          relatedEntityType: RelatedEntityType.STUDENT_TUTOR_MAPPING,
          sendPush: true,
          sendEmail: false,
          sendInApp: false,
          sendRealtime: false,
        }, {
          batchId: batchId
        });
      } catch (pushError) {
        this.logger.error(`Failed to send push notification to tutor ${tutor.id}: ${pushError.message}`, pushError.stack);
        // Continue execution
      }

      this.logger.log(`Sent consolidated tutor assignment notifications to tutor ${tutor.id} for student ${student.id} across ${modules.length} modules: ${modules.join(', ')}`);
    } catch (error) {
      this.logger.error(`Error sending consolidated notifications to tutor ${tutor.id}: ${error.message}`, error.stack);
      // Rethrow to allow the caller to handle the error
      throw error;
    }
  }

  /**
   * Send a notification to a tutor about a student assignment (DEPRECATED - use sendConsolidatedTutorNotifications instead)
   * @param tutor Tutor user
   * @param student Student user
   * @param module Module (PlanFeature)
   * @param mappingId Mapping ID
   */
  private async sendTutorNotification(tutor: User, student: User, module: PlanFeature, mappingId: string): Promise<void> {
    // This method is deprecated and should not be used for new assignments
    // It's kept for backward compatibility with existing code that might still call it
    this.logger.warn(`sendTutorNotification is deprecated. Use sendConsolidatedTutorNotifications instead.`);

    // For backward compatibility, convert to consolidated format
    await this.sendConsolidatedTutorNotification(tutor, student, [module.name], mappingId);
  }

  // Cron job to handle auto-renewals (runs daily at midnight)
  @Cron('0 0 * * *')
  async handleAutoRenewals() {
    try {
      // Get today's date in UTC
      const today = getCurrentUTCDate();
      const startOfDay = getStartOfDayUTC(today);
      const endOfDay = getEndOfDayUTC(today);

      this.logger.log(`Checking for subscriptions due for renewal between ${startOfDay.toISOString()} and ${endOfDay.toISOString()}`);

      // Find all active subscriptions that are due for renewal
      let subscriptionsDueForRenewal: UserPlan[] = [];
      try {
        subscriptionsDueForRenewal = await this.userPlanRepository.find({
          where: {
            isActive: true,
            autoRenew: true,
            nextRenewalDate: Between(startOfDay, endOfDay),
          },
          relations: ['plan'],
        });
      } catch (dbError) {
        this.logger.error(`Database error when fetching subscriptions due for renewal: ${dbError.message}`, dbError.stack);
        return;
      }

      if (!subscriptionsDueForRenewal || !Array.isArray(subscriptionsDueForRenewal)) {
        this.logger.error(`Invalid result when fetching subscriptions due for renewal`);
        return;
      }

      this.logger.log(`Found ${subscriptionsDueForRenewal.length} subscriptions due for renewal`);

      for (const subscription of subscriptionsDueForRenewal) {
        try {
          // Validate subscription data
          if (!subscription || !subscription.id || !subscription.userId || !subscription.planId) {
            this.logger.error(`Invalid subscription data, skipping renewal`);
            continue;
          }

          if (!subscription.plan) {
            this.logger.error(`Subscription ${subscription.id} has no associated plan, skipping renewal`);
            continue;
          }

          // Process renewal (in a real system, this would handle payment processing)
          this.logger.log(`Processing renewal for subscription ${subscription.id} (User: ${subscription.userId}, Plan: ${subscription.planId})`);

          // Calculate new end date based on subscription type
          let newEndDate: Date;

          if (subscription.plan.subscriptionType === SubscriptionType.MONTHLY) {
            newEndDate = addMonthsUTC(subscription.endDate, 1);
          } else if (subscription.plan.subscriptionType === SubscriptionType.YEARLY) {
            newEndDate = addYearsUTC(subscription.endDate, 1);
          } else {
            newEndDate = addDaysUTC(subscription.endDate, subscription.plan.durationDays);
          }

          // Update subscription
          subscription.lastRenewalDate = getCurrentUTCDate();
          subscription.endDate = newEndDate;
          subscription.nextRenewalDate = new Date(newEndDate.getTime());

          try {
            await this.userPlanRepository.save(subscription);
            this.logger.log(`Successfully renewed subscription ${subscription.id} until ${newEndDate.toISOString()}`);
          } catch (saveError) {
            this.logger.error(`Failed to save renewed subscription ${subscription.id}: ${saveError.message}`, saveError.stack);
            continue;
          }

          // Check if user is a student and assign tutors if needed
          try {
            const user = await this.userRepository.findOne({ where: { id: subscription.userId } });
            if (!user) {
              this.logger.warn(`User ${subscription.userId} not found for subscription ${subscription.id}, skipping tutor assignment`);
              continue;
            }

            if (user.type === UserType.STUDENT) {
              // Get the plan with features
              const plan = await this.planRepository.findOne({
                where: { id: subscription.planId },
                relations: ['planFeatures'],
              });

              if (!plan) {
                this.logger.warn(`Plan ${subscription.planId} not found for subscription ${subscription.id}, skipping tutor assignment`);
                continue;
              }

              // Ensure plan has features loaded before assigning tutors
              if (!plan.planFeatures) {
                // Load plan with features if they weren't loaded
                const planWithFeatures = await this.planRepository.findOne({
                  where: { id: plan.id },
                  relations: ['planFeatures'],
                });

                if (planWithFeatures) {
                  // Assign tutors for the renewed plan
                  await this.assignTutorsForPlan(subscription.userId, planWithFeatures);
                } else {
                  // If we can't load the plan with features, just pass the user ID
                  await this.assignTutorsForPlan(subscription.userId);
                }
              } else {
                // Plan already has features loaded
                await this.assignTutorsForPlan(subscription.userId, plan);
              }
            }
          } catch (tutorError) {
            // Log error but don't fail the renewal process
            this.logger.error(`Failed to assign tutors after renewal for user ${subscription.userId}: ${tutorError.message}`, tutorError.stack);
          }
        } catch (error) {
          // Log error and continue with next subscription
          this.logger.error(`Failed to renew subscription ${subscription?.id || 'unknown'}: ${error.message}`, error.stack);
        }
      }
    } catch (error) {
      this.logger.error(`Unhandled error in handleAutoRenewals: ${error.message}`, error.stack);
    }
  }

  /**
   * Map payment method string to KCP payment method enum
   * Based on KCP official documentation: https://developer.kcp.co.kr/en/page/document/web
   */
  private mapPaymentMethodToKcp(paymentMethod: string): KcpPaymentMethod {
    switch (paymentMethod) {
      case 'kcp_card':
        return KcpPaymentMethod.CARD;
      case 'kcp_bank':
        return KcpPaymentMethod.BANK;
      case 'kcp_mobile':
        return KcpPaymentMethod.MOBILE;
      case 'kcp_virtual_account':
        return KcpPaymentMethod.VACCT;
      default:
        return KcpPaymentMethod.CARD;
    }
  }

  /**
   * Deactivate previous active plans when a new plan is activated
   * This should only be called after payment confirmation
   */
  private async deactivatePreviousPlans(userId: string, activeUserPlans: UserPlan[], newPlan: Plan): Promise<void> {
    for (const activePlan of activeUserPlans) {
      // Set plan as inactive
      activePlan.isActive = false;

      // Disable auto-renewal
      activePlan.autoRenew = false;

      // Set cancellation date in UTC
      activePlan.cancellationDate = getCurrentUTCDate();

      // Add a note about the cancellation reason
      activePlan.notes = activePlan.notes || '';
      if (activePlan.notes) activePlan.notes += '\n';
      activePlan.notes += `Automatically cancelled due to plan upgrade to ${newPlan.name} (${newPlan.id}) on ${getCurrentUTCDate().toISOString()}`;

      // Save the updated plan
      await this.userPlanRepository.save(activePlan);

      this.logger.log(`Deactivated plan ${activePlan.plan?.name || activePlan.planId} for user ${userId} after payment confirmation`);
    }
  }

  /**
   * Clean up expired pending payments (older than 1 hour)
   * This helps maintain clean state and allows users to retry payments
   */
  async cleanupExpiredPendingPayments(): Promise<void> {
    try {
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000); // 1 hour ago

      const expiredPendingPlans = await this.userPlanRepository.find({
        where: {
          isActive: false,
          isPaid: false,
          createdAt: LessThan(oneHourAgo),
        },
      });

      if (expiredPendingPlans.length > 0) {
        await this.userPlanRepository.remove(expiredPendingPlans);
        this.logger.log(`Cleaned up ${expiredPendingPlans.length} expired pending payment(s)`);
      }
    } catch (error) {
      this.logger.error(`Failed to cleanup expired pending payments: ${error.message}`, error.stack);
    }
  }

  /**
   * Cancel pending payment for a user
   * This allows users to cancel their pending payment and try again
   */
  async cancelPendingPayment(userId: string, userPlanId: string): Promise<void> {
    // Find the pending user plan
    const pendingUserPlan = await this.userPlanRepository.findOne({
      where: {
        id: userPlanId,
        userId: userId,
        isActive: false,
        isPaid: false,
      },
      relations: ['plan'],
    });

    if (!pendingUserPlan) {
      throw new NotFoundException(`No pending payment found for the specified plan`);
    }

    // Remove the pending user plan
    await this.userPlanRepository.remove(pendingUserPlan);

    this.logger.log(`Cancelled pending payment for user ${userId}, plan: ${pendingUserPlan.plan?.name || pendingUserPlan.planId}`);
  }

  /**
   * Get user's pending payments
   */
  async getUserPendingPayments(userId: string): Promise<UserPlanResponseDto[]> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      select: ['id', 'name', 'email', 'type']
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    const pendingUserPlans = await this.userPlanRepository.find({
      where: {
        userId: userId,
        isActive: false,
        isPaid: false,
      },
      relations: ['plan', 'plan.planFeatures'],
      order: { createdAt: 'DESC' },
    });

    return pendingUserPlans.map(userPlan => this.toUserPlanResponseDto(userPlan, userPlan.plan, user));
  }

  /**
   * Retry pending payment for a specific user plan
   * This allows users to continue with their existing pending payment instead of creating a new one
   */
  async retryPendingPayment(userId: string, userPlanId: string, paymentMethod: string): Promise<any> {
    // Find the pending user plan
    const pendingUserPlan = await this.userPlanRepository.findOne({
      where: {
        id: userPlanId,
        userId: userId,
        isActive: false,
        isPaid: false,
      },
      relations: ['plan', 'plan.planFeatures'],
    });

    if (!pendingUserPlan) {
      throw new NotFoundException(`No pending payment found for the specified plan`);
    }

    // Get user details
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['userRoles', 'userRoles.role'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Check if this is a KCP payment
    const isKcpPayment = paymentMethod && ['kcp_card', 'kcp_bank', 'kcp_virtual_account', 'kcp_mobile'].includes(paymentMethod);
    const isFreePayment = paymentMethod === 'free';

    if (isKcpPayment && pendingUserPlan.plan.price > 0) {
      // For KCP payments, initiate payment process
      const kcpPaymentData = {
        userPlanId: pendingUserPlan.id,
        planId: pendingUserPlan.plan.id,
        planName: pendingUserPlan.plan.name,
        amount: pendingUserPlan.plan.price,
        paymentMethod: this.mapPaymentMethodToKcp(paymentMethod),
        metadata: {
          isUpgrade: true, // Mark as upgrade since this is retrying a pending upgrade
          originalUserPlanId: userPlanId,
          retryAttempt: true,
        },
      };

      // Create payment transaction for retry
      const initiatePaymentDto = {
        orderId: `RETRY-${pendingUserPlan.id}-${Date.now()}`,
        amount: pendingUserPlan.plan.price,
        currency: 'KRW',
        productName: pendingUserPlan.plan.name,
        buyerName: user.name,
        buyerEmail: user.email,
        buyerPhone: user.phone || '010-0000-0000',
        paymentMethod: this.mapPaymentMethodToKcp(paymentMethod),
        purchaseType: PurchaseType.PLAN,
        referenceId: pendingUserPlan.id,
        returnUrl: `${process.env.FRONTEND_URL}/payment/success`,
        cancelUrl: `${process.env.FRONTEND_URL}/payment/cancel`,
        metadata: kcpPaymentData.metadata,
      };

      const paymentResult = await this.paymentService.initiatePayment(userId, initiatePaymentDto);

      this.logger.log(`Retry payment initiated for user ${userId}, plan: ${pendingUserPlan.plan.name}, userPlanId: ${userPlanId}`);

      return {
        paymentUrl: paymentResult.paymentUrl,
        transactionId: paymentResult.transactionId,
        userPlan: this.toUserPlanResponseDto(pendingUserPlan, pendingUserPlan.plan, user),
        message: 'Payment retry initiated successfully. Please complete the payment.',
      };
    } else if (isFreePayment) {
      // For free payments, activate immediately and handle upgrade logic
      const activeUserPlans = await this.userPlanRepository.find({
        where: { userId, isActive: true },
        relations: ['plan'],
      });

      // Activate the pending plan
      pendingUserPlan.isActive = true;
      pendingUserPlan.isPaid = true;
      pendingUserPlan.paymentReference = `FREE-RETRY-${Date.now()}`;

      const savedUserPlan = await this.userPlanRepository.save(pendingUserPlan);

      // Deactivate previous plans after successful activation
      await this.deactivatePreviousPlans(userId, activeUserPlans, pendingUserPlan.plan);

      // Create audit log for free retry
      this.logger.warn(`FREE PAYMENT RETRY: User ${userId} completed pending payment for plan ${pendingUserPlan.plan.name} (${pendingUserPlan.plan.id}) using free payment method.`);

      // Send async notification about successful retry
      try {
        const htmlContent = this.emailTemplateService.generatePaymentConfirmationTemplate(pendingUserPlan.plan, pendingUserPlan.endDate);

        await this.asyncNotificationHelper.notifyAsync(
          userId,
          NotificationType.SYSTEM,
          'Payment Completed Successfully',
          `Your pending payment for ${pendingUserPlan.plan.name} has been completed successfully.`,
          {
            relatedEntityId: savedUserPlan.id,
            relatedEntityType: RelatedEntityType.USER_PLAN,
            htmlContent: htmlContent,
            sendEmail: true,
            sendPush: true,
            sendInApp: true,
            sendRealtime: false,
          },
          {
            submissionId: savedUserPlan.id,
            entryType: 'payment_retry_success',
            priority: 2, // Medium priority
          }
        );
        this.logger.log(`Sent payment retry success notification to user ${userId}`);
      } catch (notificationError) {
        this.logger.error(`Failed to send payment retry notification: ${notificationError.message}`, notificationError.stack);
        // Don't fail the process if notification fails
      }

      // Handle post-activation tasks asynchronously
      this.handlePostSubscriptionTasks(userId, pendingUserPlan.plan).catch(error => {
        this.logger.error(`Failed to handle post-activation tasks for user ${userId}: ${error.message}`, error.stack);
      });

      // Generate new JWT with plan info
      const newToken = await this.generateTokenWithPlanInfo(userId, pendingUserPlan.plan, savedUserPlan);

      // Return response with user information
      const response = this.toUserPlanResponseDto(savedUserPlan, pendingUserPlan.plan, user);
      response.access_token = newToken;

      return {
        userPlan: response,
        message: 'Payment completed successfully using free payment method.',
      };
    } else {
      throw new BadRequestException('Invalid payment method for retry. Please use a valid KCP payment method or free payment.');
    }
  }
}
