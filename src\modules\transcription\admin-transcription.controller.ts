import { Controller, Get, Post, Body, Param, Delete, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags, ApiOperation } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { AdminGuard } from '../../common/guards/admin.guard';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiOkResponseWithArrayType } from '../../common/decorators/api-response.decorator';
import { BookService } from './services/book.service';
import { SentenceService } from './services/sentence.service';
import { CreateBookDto, BookResponseDto, SentenceResponseDto } from '../../database/models/transcription.dto';

@ApiTags('transcription-admin')
@Controller('transcription/admin')
@UseGuards(JwtAuthGuard, AdminGuard)
@ApiBearerAuth('JWT-auth')
export class AdminTranscriptionController {
  constructor(
    private readonly bookService: BookService,
    private readonly sentenceService: SentenceService,
  ) {}

  @Post('books')
  @ApiOperation({ summary: 'Create a new book (Admin only)' })
  @ApiOkResponseWithType(BookResponseDto, 'Book created successfully')
  async createBook(@Body() createBookDto: CreateBookDto): Promise<ApiResponse<BookResponseDto>> {
    const result = await this.bookService.create(createBookDto);
    return ApiResponse.success(result, 'Book created successfully', 201);
  }

  @Get('books')
  @ApiOperation({ summary: 'Get all books (Admin only)' })
  @ApiOkResponseWithArrayType(BookResponseDto, 'Books retrieved successfully')
  async getAllBooks(): Promise<ApiResponse<BookResponseDto[]>> {
    const result = await this.bookService.findAll();
    return ApiResponse.success(result, 'Books retrieved successfully');
  }

  @Get('books/:id/sentences')
  @ApiOperation({ summary: 'Get sentences for a book (Admin only)' })
  @ApiOkResponseWithArrayType(SentenceResponseDto, 'Sentences retrieved successfully')
  async getSentences(@Param('id') bookId: string): Promise<ApiResponse<SentenceResponseDto[]>> {
    const result = await this.sentenceService.findByBookId(bookId);
    return ApiResponse.success(result, 'Sentences retrieved successfully');
  }

  @Post('books/:id/sentences')
  @ApiOperation({ summary: 'Extract sentences from book content (Admin only)' })
  @ApiOkResponseWithArrayType(SentenceResponseDto, 'Sentences extracted successfully')
  async extractSentences(@Param('id') bookId: string): Promise<ApiResponse<SentenceResponseDto[]>> {
    const book = await this.bookService.findById(bookId);
    const result = await this.sentenceService.extractSentencesFromBook(bookId, book.content || '');
    return ApiResponse.success(result, 'Sentences extracted successfully');
  }

  @Delete('books/:id')
  @ApiOperation({ summary: 'Delete a book (Admin only)' })
  @ApiOkResponseWithType(Object, 'Book deleted successfully')
  async deleteBook(@Param('id') id: string): Promise<ApiResponse<void>> {
    await this.bookService.delete(id);
    return ApiResponse.success(null, 'Book deleted successfully');
  }
}