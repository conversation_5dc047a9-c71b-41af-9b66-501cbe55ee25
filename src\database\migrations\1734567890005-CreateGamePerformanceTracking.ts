import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateGamePerformanceTracking1734567890005 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'game_performance_tracking',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'gen_random_uuid()',
          },
          {
            name: 'student_id',
            type: 'uuid',
          },
          {
            name: 'game_type',
            type: 'enum',
            enum: ['waterfall', 'block'],
          },
          {
            name: 'game_source',
            type: 'enum',
            enum: ['admin', 'tutor'],
          },
          {
            name: 'game_id',
            type: 'uuid',
          },
          {
            name: 'score',
            type: 'integer',
          },
          {
            name: 'total_score',
            type: 'integer',
          },
          {
            name: 'completion_time_seconds',
            type: 'integer',
            isNullable: true,
          },
          {
            name: 'played_at',
            type: 'timestamp',
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'created_by',
            type: 'uuid',
            isNullable: true,
          },
          {
            name: 'updated_by',
            type: 'uuid',
            isNullable: true,
          },
        ],
        foreignKeys: [
          {
            columnNames: ['student_id'],
            referencedTableName: 'user',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
          },
        ],
        indices: [
          {
            columnNames: ['student_id', 'game_type'],
          },
          {
            columnNames: ['played_at'],
          },
        ],
      }),
      true,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('game_performance_tracking');
  }
}