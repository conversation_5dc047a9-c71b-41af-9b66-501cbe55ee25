import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { Book } from './book.entity';
import { TranscriptionAttempt } from './transcription-attempt.entity';

@Entity('sentences')
export class Sentence extends AuditableBaseEntity {
  @Column({ type: 'text' })
  content: string;

  @Column({ name: 'order_index', type: 'int' })
  orderIndex: number;

  @Column({ name: 'difficulty_level', type: 'varchar', length: 50, nullable: true })
  difficultyLevel: string;

  @Column({ name: 'grammar_pattern', type: 'varchar', length: 100, nullable: true })
  grammarPattern: string;

  @Column({ name: 'book_id', type: 'uuid' })
  bookId: string;

  @ManyToOne(() => Book, book => book.sentences)
  @JoinColumn({ name: 'book_id' })
  book: Book;

  @OneToMany(() => TranscriptionAttempt, attempt => attempt.sentence)
  attempts: TranscriptionAttempt[];
}