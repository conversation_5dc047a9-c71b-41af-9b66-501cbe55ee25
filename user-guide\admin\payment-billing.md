# Payment & Billing Management

## 💳 Payment System Overview

The HEC platform integrates with multiple payment providers to handle subscription billing, one-time purchases, and international transactions. This guide covers comprehensive payment and billing management for administrators.

## 🏦 Payment Provider Configuration

### KCP Payment Integration

#### KCP Configuration Setup
```javascript
{
  "kcp_settings": {
    "merchant_configuration": {
      "site_cd": "T0000",
      "site_key": "3grptw1.zW0GSo4PQdaGvsF__",
      "site_name": "HEC Platform",
      "currency": "KRW",
      "environment": "production", // or "sandbox"
      "api_version": "v2.0"
    },
    "payment_methods": {
      "credit_cards": ["VISA", "MASTERCARD", "JCB", "AMEX"],
      "local_cards": ["BC", "KB", "HANA", "SAMSUNG", "SHINHAN"],
      "digital_wallets": ["KAKAOPAY", "PAYCO", "SSGPAY"],
      "bank_transfers": ["VIRTUAL_ACCOUNT", "REAL_TIME_TRANSFER"]
    },
    "security_settings": {
      "encryption_enabled": true,
      "3d_secure": true,
      "fraud_detection": true,
      "pci_compliance": "Level 1"
    }
  }
}
```

#### KCP Transaction Processing
```javascript
// Payment initiation
{
  "payment_request": {
    "order_id": "HEC_2024_001234",
    "amount": 29900, // KRW
    "currency": "KRW",
    "product_name": "HEC Premium Monthly Subscription",
    "customer_info": {
      "user_id": "user_123",
      "name": "김철수",
      "email": "<EMAIL>",
      "phone": "010-1234-5678"
    },
    "payment_method": "CREDIT_CARD",
    "return_url": "https://hecplatform.com/payment/success",
    "cancel_url": "https://hecplatform.com/payment/cancel",
    "webhook_url": "https://api.hecplatform.com/webhooks/kcp"
  }
}
```

### International Payment Support

#### Multi-Currency Configuration
```javascript
{
  "currency_settings": {
    "supported_currencies": {
      "KRW": {
        "symbol": "₩",
        "decimal_places": 0,
        "payment_providers": ["KCP"],
        "default_for_regions": ["KR"]
      },
      "USD": {
        "symbol": "$",
        "decimal_places": 2,
        "payment_providers": ["STRIPE", "PAYPAL"],
        "default_for_regions": ["US", "CA"]
      },
      "EUR": {
        "symbol": "€",
        "decimal_places": 2,
        "payment_providers": ["STRIPE"],
        "default_for_regions": ["DE", "FR", "ES", "IT"]
      }
    },
    "exchange_rate_provider": "FIXER_IO",
    "rate_update_frequency": "daily",
    "rate_margin": 0.02 // 2% margin on exchange rates
  }
}
```

## 📋 Subscription Management

### Plan Configuration

#### Subscription Plans Setup
```javascript
{
  "subscription_plans": {
    "free_plan": {
      "plan_id": "free",
      "name": "HEC Free",
      "price": 0,
      "currency": "KRW",
      "billing_cycle": "monthly",
      "features": {
        "diary_entries": "unlimited",
        "story_maker_plays": 5,
        "block_games": 10,
        "themes": "basic_only",
        "tutor_feedback": true,
        "storage_limit": "100MB"
      },
      "limitations": {
        "premium_themes": false,
        "advanced_analytics": false,
        "priority_support": false
      }
    },
    "premium_monthly": {
      "plan_id": "premium_monthly",
      "name": "HEC Premium Monthly",
      "price": 29900,
      "currency": "KRW",
      "billing_cycle": "monthly",
      "trial_period_days": 7,
      "features": {
        "diary_entries": "unlimited",
        "story_maker_plays": "unlimited",
        "block_games": "unlimited",
        "themes": "all_premium",
        "tutor_feedback": true,
        "storage_limit": "1GB",
        "advanced_analytics": true,
        "priority_support": true
      }
    },
    "premium_yearly": {
      "plan_id": "premium_yearly",
      "name": "HEC Premium Yearly",
      "price": 299000,
      "currency": "KRW",
      "billing_cycle": "yearly",
      "discount_percentage": 17, // 2 months free
      "trial_period_days": 14,
      "features": "same_as_premium_monthly"
    }
  }
}
```

### Subscription Lifecycle Management

#### Subscription Status Tracking
```javascript
{
  "subscription_statuses": {
    "active": {
      "description": "Subscription is active and current",
      "billing_enabled": true,
      "feature_access": "full"
    },
    "trial": {
      "description": "In trial period",
      "billing_enabled": false,
      "feature_access": "full",
      "trial_end_actions": ["convert_to_paid", "downgrade_to_free"]
    },
    "past_due": {
      "description": "Payment failed, grace period active",
      "billing_enabled": true,
      "feature_access": "limited",
      "grace_period_days": 7
    },
    "cancelled": {
      "description": "Cancelled but still active until period end",
      "billing_enabled": false,
      "feature_access": "full_until_period_end"
    },
    "expired": {
      "description": "Subscription has expired",
      "billing_enabled": false,
      "feature_access": "free_tier_only"
    }
  }
}
```

#### Subscription Operations
```javascript
// Upgrade subscription
// POST /api/admin/subscriptions/{subscriptionId}/upgrade
{
  "new_plan_id": "premium_yearly",
  "proration_handling": "immediate_charge",
  "effective_date": "immediate",
  "notification_settings": {
    "notify_user": true,
    "send_invoice": true
  }
}

// Cancel subscription
// POST /api/admin/subscriptions/{subscriptionId}/cancel
{
  "cancellation_type": "end_of_period", // or "immediate"
  "reason": "user_request",
  "refund_policy": "prorated_refund",
  "retention_offer": {
    "discount_percentage": 20,
    "discount_duration_months": 3
  }
}
```

## 💰 Billing & Invoicing

### Invoice Management

#### Invoice Generation
```javascript
{
  "invoice_configuration": {
    "invoice_settings": {
      "auto_generation": true,
      "generation_timing": "subscription_renewal",
      "invoice_numbering": "HEC-{YYYY}-{MM}-{NNNNNN}",
      "payment_terms_days": 7,
      "late_fee_percentage": 5
    },
    "invoice_content": {
      "company_info": {
        "name": "HEC Education Platform",
        "address": "123 Education Street, Seoul, Korea",
        "tax_id": "123-45-67890",
        "contact_email": "<EMAIL>"
      },
      "line_items": [
        {
          "description": "HEC Premium Monthly Subscription",
          "quantity": 1,
          "unit_price": 29900,
          "tax_rate": 0.1,
          "total": 32890
        }
      ],
      "payment_instructions": {
        "methods": ["credit_card", "bank_transfer"],
        "due_date": "2024-12-27",
        "late_fee_policy": "5% after 7 days"
      }
    }
  }
}
```

#### Invoice Status Management
```javascript
{
  "invoice_statuses": {
    "draft": "Invoice created but not sent",
    "sent": "Invoice sent to customer",
    "viewed": "Customer has viewed the invoice",
    "paid": "Payment received and processed",
    "overdue": "Payment past due date",
    "cancelled": "Invoice cancelled before payment",
    "refunded": "Payment refunded to customer"
  }
}
```

### Payment Processing

#### Transaction Monitoring
```javascript
{
  "transaction_monitoring": {
    "real_time_tracking": {
      "successful_payments": 1247,
      "failed_payments": 23,
      "pending_payments": 5,
      "success_rate": "98.2%"
    },
    "payment_analytics": {
      "total_revenue_today": ********, // KRW
      "average_transaction_value": 29900,
      "most_popular_payment_method": "credit_card",
      "peak_payment_hours": ["19:00-21:00", "12:00-14:00"]
    },
    "failure_analysis": {
      "common_failure_reasons": [
        {"reason": "insufficient_funds", "percentage": 45},
        {"reason": "expired_card", "percentage": 23},
        {"reason": "invalid_cvv", "percentage": 18},
        {"reason": "network_timeout", "percentage": 14}
      ],
      "retry_success_rate": "67%"
    }
  }
}
```

#### Payment Retry Logic
```javascript
{
  "retry_configuration": {
    "retry_schedule": [
      {"attempt": 1, "delay_hours": 24},
      {"attempt": 2, "delay_hours": 72},
      {"attempt": 3, "delay_hours": 168} // 7 days
    ],
    "retry_conditions": {
      "retryable_errors": [
        "insufficient_funds",
        "temporary_network_error",
        "issuer_unavailable"
      ],
      "non_retryable_errors": [
        "invalid_card",
        "fraud_detected",
        "card_blocked"
      ]
    },
    "escalation_actions": {
      "after_3_failures": "suspend_subscription",
      "notification_schedule": ["immediate", "day_3", "day_7"],
      "grace_period_days": 7
    }
  }
}
```

## 🔄 Refund Management

### Refund Policies

#### Refund Rules Configuration
```javascript
{
  "refund_policies": {
    "subscription_refunds": {
      "trial_period": {
        "refund_eligible": true,
        "refund_percentage": 100,
        "processing_time_days": 3
      },
      "monthly_subscription": {
        "refund_window_days": 7,
        "refund_type": "prorated",
        "minimum_usage_for_partial": "3_days"
      },
      "yearly_subscription": {
        "refund_window_days": 30,
        "refund_type": "prorated",
        "cancellation_fee": 0
      }
    },
    "one_time_purchases": {
      "digital_items": {
        "refund_window_hours": 24,
        "condition": "item_not_used",
        "refund_percentage": 100
      },
      "premium_features": {
        "refund_window_days": 3,
        "condition": "feature_not_accessed",
        "refund_percentage": 100
      }
    }
  }
}
```

#### Refund Processing
```javascript
// Process refund
// POST /api/admin/payments/{paymentId}/refund
{
  "refund_amount": 29900,
  "refund_reason": "customer_request",
  "refund_type": "full", // or "partial"
  "admin_notes": "Customer unsatisfied with service quality",
  "notification_settings": {
    "notify_customer": true,
    "send_receipt": true,
    "update_subscription": true
  },
  "processing_options": {
    "refund_method": "original_payment_method",
    "expected_processing_days": 3,
    "reference_number": "REF-2024-001234"
  }
}
```

## 📊 Financial Analytics

### Revenue Analytics

#### Revenue Tracking
```javascript
{
  "revenue_analytics": {
    "daily_metrics": {
      "gross_revenue": 2847500, // KRW
      "net_revenue": 2562750, // after fees and taxes
      "transaction_count": 95,
      "average_order_value": 29900,
      "refund_amount": 89700
    },
    "monthly_trends": {
      "revenue_growth": "+12.5%",
      "new_subscriptions": 234,
      "subscription_renewals": 1456,
      "churn_rate": "3.2%",
      "customer_lifetime_value": 358800
    },
    "payment_method_breakdown": {
      "credit_card": {"revenue": 2278000, "percentage": 80},
      "bank_transfer": {"revenue": 455500, "percentage": 16},
      "digital_wallet": {"revenue": 114000, "percentage": 4}
    }
  }
}
```

#### Financial Reporting
```javascript
{
  "financial_reports": {
    "monthly_revenue_report": {
      "total_revenue": ********, // KRW
      "subscription_revenue": ********,
      "one_time_purchases": 8542500,
      "refunds_issued": 1247500,
      "net_revenue": ********,
      "payment_processing_fees": 1687550,
      "taxes_collected": 8417750
    },
    "subscription_metrics": {
      "monthly_recurring_revenue": ********,
      "annual_recurring_revenue": *********,
      "average_revenue_per_user": 29900,
      "customer_acquisition_cost": 15000,
      "payback_period_months": 1.5
    }
  }
}
```

## 🔒 Payment Security

### Security Measures

#### PCI Compliance
```javascript
{
  "security_configuration": {
    "pci_compliance": {
      "level": "Level 1 Merchant",
      "certification_date": "2024-06-15",
      "next_assessment": "2025-06-15",
      "compliance_requirements": [
        "secure_network_maintenance",
        "cardholder_data_protection",
        "vulnerability_management",
        "access_control_implementation",
        "network_monitoring",
        "information_security_policy"
      ]
    },
    "data_protection": {
      "encryption_at_rest": "AES-256",
      "encryption_in_transit": "TLS 1.3",
      "tokenization": "enabled",
      "data_retention_days": 90,
      "secure_deletion": "automatic"
    }
  }
}
```

#### Fraud Detection
```javascript
{
  "fraud_detection": {
    "risk_scoring": {
      "enabled": true,
      "threshold_decline": 80,
      "threshold_review": 60,
      "factors": [
        "geolocation_mismatch",
        "unusual_spending_pattern",
        "multiple_failed_attempts",
        "blacklisted_card_bin",
        "velocity_checks"
      ]
    },
    "automated_actions": {
      "high_risk_transactions": "require_3d_secure",
      "suspicious_patterns": "flag_for_manual_review",
      "known_fraud_indicators": "automatic_decline"
    }
  }
}
```

## 🌍 International Billing

### Multi-Region Support

#### Regional Configuration
```javascript
{
  "regional_billing": {
    "korea": {
      "currency": "KRW",
      "tax_rate": 0.1,
      "payment_providers": ["KCP"],
      "required_fields": ["korean_name", "phone_number"],
      "invoice_language": "korean"
    },
    "united_states": {
      "currency": "USD",
      "tax_calculation": "by_state",
      "payment_providers": ["STRIPE", "PAYPAL"],
      "required_fields": ["zip_code"],
      "invoice_language": "english"
    },
    "european_union": {
      "currency": "EUR",
      "vat_handling": "reverse_charge",
      "payment_providers": ["STRIPE"],
      "required_fields": ["vat_number"],
      "gdpr_compliance": true
    }
  }
}
```

### Tax Management

#### Tax Configuration
```javascript
{
  "tax_settings": {
    "tax_calculation": {
      "method": "automatic_by_region",
      "service_provider": "AVALARA",
      "real_time_calculation": true,
      "tax_inclusive_pricing": false
    },
    "tax_rates": {
      "korea_vat": 0.1,
      "us_sales_tax": "variable_by_state",
      "eu_vat": "variable_by_country",
      "digital_services_tax": "applicable_regions_only"
    },
    "tax_reporting": {
      "automated_filing": true,
      "reporting_frequency": "monthly",
      "compliance_monitoring": true
    }
  }
}
```

## 📋 Payment Management Checklist

### Daily Tasks
- [ ] Monitor payment processing status
- [ ] Review failed payment attempts
- [ ] Check fraud detection alerts
- [ ] Process refund requests
- [ ] Verify daily revenue reconciliation

### Weekly Tasks
- [ ] Analyze payment method performance
- [ ] Review subscription churn rates
- [ ] Update payment retry configurations
- [ ] Generate weekly financial reports
- [ ] Monitor compliance status

### Monthly Tasks
- [ ] Comprehensive revenue analysis
- [ ] Payment provider performance review
- [ ] Tax calculation and reporting
- [ ] Customer payment behavior analysis
- [ ] Security audit and compliance check

---

**Next Steps**: After configuring payment and billing, proceed to [Promotions Management](promotions-management.md) to set up marketing campaigns, or explore [Financial Reporting](financial-reporting.md) for advanced analytics.

*For payment integration technical details and API documentation, refer to the Payment API Guide or contact the development team.*