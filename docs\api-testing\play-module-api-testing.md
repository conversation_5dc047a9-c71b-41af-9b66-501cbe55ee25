# Play Module API Testing Flow

This document outlines the testing flow for the Play Module API endpoints, covering Story Maker, Block Game, and Waterfall games.

## Prerequisites

Before testing the Play Module APIs:

1. Ensure the HEC backend is running
2. Have valid authentication tokens for students
3. Set up your API testing tool (<PERSON><PERSON> recommended)
4. Ensure test games are available in the system

## Story Maker Game Testing Flow

### Game Discovery Testing

#### Test Case 1: Get Available Story Games

1. Authenticate with a student token
2. Send a GET request to `/play/story-maker/play/list` with pagination:
   ```json
   {
     "page": 1,
     "limit": 10
   }
   ```
3. Verify HTTP status code is 200 OK
4. Verify response contains paginated list of story games
5. Verify games include title, instruction, picture URL
6. Verify played status is correctly indicated

#### Test Case 2: Get Shared Stories

1. Send a GET request to `/play/story-maker/play/shared-stories`
2. Verify HTTP status code is 200 OK
3. Verify response contains evaluated story submissions
4. Verify stories include student info, content, and evaluation scores
5. Verify only public/shared stories are returned

### Game Details Testing

#### Test Case 1: Get Story Game Details (Unplayed)

1. Send a GET request to `/play/story-maker/play/{id}` for unplayed game
2. Verify HTTP status code is 200 OK
3. Verify response contains:
   - Basic story details
   - is_played: false
   - latest_submission: null
   - No evaluation data

#### Test Case 2: Get Story Game Details (Submitted, Not Evaluated)

1. Submit a story for a game
2. Send a GET request to `/play/story-maker/play/{id}`
3. Verify response contains:
   - is_played: true
   - latest_submission with content and metadata
   - No evaluation data yet

#### Test Case 3: Get Story Game Details (Evaluated)

1. Wait for story evaluation to complete
2. Send a GET request to `/play/story-maker/play/{id}`
3. Verify response contains:
   - is_played: true
   - latest_submission with content
   - evaluation with detailed scoring breakdown

#### Test Case 4: Story Being Prepared

1. Send request for story with image analysis in progress
2. Verify HTTP status code is 400
3. Verify response includes retryAfter field
4. Verify appropriate error message

### Story Writing Testing

#### Test Case 1: Update Story Draft

1. Send a PUT request to `/play/story-maker/play/{id}`:
   ```json
   {
     "content": "Once upon a time, there was a brave knight..."
   }
   ```
2. Verify HTTP status code is 200 OK
3. Verify draft is saved successfully
4. Verify response contains updated content and metadata

#### Test Case 2: Auto-Save Functionality

1. Send a PATCH request to `/play/story-maker/play/{id}/auto-save`:
   ```json
   {
     "content": "Auto-saved content during typing..."
   }
   ```
2. Verify HTTP status code is 200 OK
3. Verify content is auto-saved
4. Test rapid successive auto-saves

#### Test Case 3: Get Draft Content

1. Send a GET request to `/play/story-maker/play/{id}/draft`
2. Verify HTTP status code is 200 OK
3. Verify response contains current draft content
4. Verify empty content if no draft exists

### Story Submission Testing

#### Test Case 1: Submit Story for Evaluation

1. Complete story writing
2. Send a POST request to `/play/story-maker/play/{id}/submit`:
   ```json
   {
     "content": "Final story content for submission..."
   }
   ```
3. Verify HTTP status code is 200 OK
4. Verify submission confirmation response
5. Verify AI evaluation is triggered
6. Verify notification is sent to student

#### Test Case 2: Submission Validation

1. Test submitting empty content
2. Test submitting without draft
3. Test submitting already submitted story
4. Verify appropriate error responses

### Story Evaluation Testing

#### Test Case 1: Get Student Submissions

1. Send a GET request to `/play/story-maker/play/{id}/submissions`
2. Verify HTTP status code is 200 OK
3. Verify response contains all student submissions for the story
4. Verify submissions include evaluation details if available

#### Test Case 2: Get Evaluation History

1. Send a GET request to `/play/story-maker/play/{id}/evaluations`
2. Verify HTTP status code is 200 OK
3. Verify response contains detailed evaluation history
4. Verify scoring breakdown is included

### Story Interaction Testing

#### Test Case 1: Like Story Submission

1. Send a POST request to `/play/story-maker/play/submissions/{id}/like`
2. Verify HTTP status code is 200 OK
3. Verify like is recorded
4. Verify like count is updated

#### Test Case 2: Unlike Story Submission

1. Send a DELETE request to `/play/story-maker/play/submissions/{id}/like`
2. Verify HTTP status code is 200 OK
3. Verify like is removed
4. Verify like count is decremented

#### Test Case 3: Get Like Details

1. Send a GET request to `/play/story-maker/play/submissions/{id}/likes`
2. Verify HTTP status code is 200 OK
3. Verify response contains like count and user's like status

#### Test Case 4: Get Popularity Statistics

1. Send a GET request to `/play/story-maker/play/submissions/{id}/popularity`
2. Verify HTTP status code is 200 OK
3. Verify response contains detailed popularity metrics
4. Verify 24-hour like counts and scoring thresholds

## Block Game Testing Flow

### Game Access Testing

#### Test Case 1: Get Random Block Game

1. Authenticate with a student token
2. Send a GET request to `/play/block/play`
3. Verify HTTP status code is 200 OK
4. Verify response contains:
   - Game details
   - Randomized word blocks
   - Target sentences or patterns

#### Test Case 2: Get Specific Block Game

1. Send a GET request to `/play/block/games/{id}`
2. Verify HTTP status code is 200 OK
3. Verify response contains specific game details
4. Verify word blocks are randomized for the student

#### Test Case 3: No Games Available

1. Test when no active games exist
2. Verify HTTP status code is 404
3. Verify appropriate error message

### Block Game Submission Testing

#### Test Case 1: Submit Block Game Solution

1. Complete block game puzzle
2. Send a POST request to `/play/block/submit`:
   ```json
   {
     "gameId": "123e4567-e89b-12d3-a456-426614174000",
     "sentences": [
       {
         "blocks": ["The", "cat", "sat", "on", "the", "mat"],
         "sentence": "The cat sat on the mat",
         "isCorrect": true
       },
       {
         "blocks": ["A", "dog", "runs", "fast"],
         "sentence": "A dog runs fast",
         "isCorrect": true
       }
     ],
     "timeSpent": 120,
     "attempts": 3
   }
   ```
3. Verify HTTP status code is 200 OK
4. Verify response contains detailed results with scoring
5. Verify completion statistics are recorded

#### Test Case 2: Submission Validation

1. Test submitting with invalid game ID
2. Test submitting incomplete solutions
3. Test submitting with malformed sentence data
4. Verify appropriate validation errors

#### Test Case 3: Scoring Verification

1. Submit solutions with different accuracy levels
2. Verify scoring algorithm works correctly
3. Verify time bonus calculations
4. Verify attempt penalty calculations

## Waterfall Game Testing Flow

### Game Management Testing

#### Test Case 1: Get Waterfall Games (Admin)

1. Authenticate with admin token
2. Send a GET request to `/api/admin/waterfall/games`
3. Verify HTTP status code is 200 OK
4. Verify response contains all waterfall games
5. Verify admin can see inactive games

#### Test Case 2: Create Waterfall Game (Admin)

1. Send a POST request to `/api/admin/waterfall/games`:
   ```json
   {
     "title": "Grammar Waterfall",
     "description": "Complete sentences by filling in missing words",
     "difficulty": "INTERMEDIATE",
     "questions": [
       {
         "text": "The cat ___ on the mat",
         "correctAnswer": "sat",
         "options": ["sat", "sit", "sits", "sitting"]
       }
     ],
     "timeLimit": 300,
     "isActive": true
   }
   ```
2. Verify HTTP status code is 201 Created
3. Verify game is created with correct details

### Student Waterfall Game Testing

#### Test Case 1: Get Available Waterfall Games

1. Authenticate with student token
2. Send a GET request to `/api/play/waterfall/games`
3. Verify HTTP status code is 200 OK
4. Verify only active games are returned
5. Verify student progress is included

#### Test Case 2: Start Waterfall Game

1. Send a POST request to `/api/play/waterfall/games/{id}/start`
2. Verify HTTP status code is 200 OK
3. Verify game session is created
4. Verify timer starts if applicable

#### Test Case 3: Submit Waterfall Answers

1. Send a POST request to `/api/play/waterfall/games/{id}/submit`:
   ```json
   {
     "answers": [
       {
         "questionId": "q1",
         "answer": "sat",
         "timeSpent": 15
       },
       {
         "questionId": "q2",
         "answer": "running",
         "timeSpent": 20
       }
     ],
     "totalTime": 180
   }
   ```
2. Verify HTTP status code is 200 OK
3. Verify answers are evaluated
4. Verify score is calculated correctly

## Integration Testing Flow

### Test Case 1: Complete Play Module Journey

1. **Story Maker Flow**
   - Browse available stories
   - Select story and view details
   - Write story with auto-save
   - Submit for evaluation
   - View results and share

2. **Block Game Flow**
   - Get random block game
   - Solve word puzzles
   - Submit solutions
   - View scoring results

3. **Cross-Game Progress**
   - Verify progress tracking across games
   - Check achievement unlocks
   - Verify reward point accumulation

### Test Case 2: Multi-User Interaction

1. Multiple students play same story maker
2. Verify independent progress tracking
3. Test like/unlike interactions
4. Verify popularity calculations

## Performance Testing Flow

### Test Case 1: Story Evaluation Performance

1. Submit multiple stories simultaneously
2. Verify AI evaluation queue handling
3. Test evaluation completion notifications
4. Verify system performance under load

### Test Case 2: Game Loading Performance

1. Test rapid game switching
2. Verify image loading performance
3. Test auto-save frequency impact
4. Verify response times remain acceptable

## Security Testing Flow

### Test Case 1: Game Access Control

1. Attempt to access other students' submissions
2. Verify proper authorization checks
3. Test game modification attempts
4. Verify data isolation between students

### Test Case 2: Content Security

1. Test XSS prevention in story content
2. Test script injection in submissions
3. Verify content sanitization
4. Test malicious file uploads

## Error Handling Testing Flow

### Test Case 1: Network Interruption

1. Start game session
2. Simulate network interruption
3. Verify graceful error handling
4. Verify progress recovery

### Test Case 2: Evaluation Failures

1. Submit story for evaluation
2. Simulate AI service failure
3. Verify fallback mechanisms
4. Verify user notification of issues

### Test Case 3: Concurrent Access

1. Multiple students access same game
2. Verify proper resource management
3. Test simultaneous submissions
4. Verify data consistency

## Edge Cases Testing Flow

### Test Case 1: Boundary Conditions

1. Test with maximum story length
2. Test with minimum valid content
3. Test rapid successive submissions
4. Verify system limits are enforced

### Test Case 2: Time-based Scenarios

1. Submit exactly at game deadline
2. Test auto-save during submission
3. Verify timestamp accuracy
4. Test timezone handling

This comprehensive testing flow ensures all Play Module games function correctly and provide engaging, secure experiences for students.