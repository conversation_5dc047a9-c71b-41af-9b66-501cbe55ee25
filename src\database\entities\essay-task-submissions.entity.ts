import { <PERSON><PERSON><PERSON>, <PERSON>umn, OneToMany, ManyToOne, <PERSON>in<PERSON><PERSON>umn, Index, OneToOne } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { IsOptional, IsUUID } from 'class-validator';
import { EssayMissionTasks } from './essay-mission-tasks.entity';
import { EssayTaskSubmissionHistory } from './essay-task-submission-history.entity';
import type { EssayTaskSubmissionMarking } from './essay-task-submission-marking.entity';
import { DiarySkin } from './diary-skin.entity';
import { StudentDiarySkin } from './student-diary-skin.entity';
import { SubmissionStatus } from '../../constants/submission.enum';

@Entity()
@Index(['task', 'createdBy'])
export class EssayTaskSubmissions extends AuditableBaseEntity {
  @Column({
    name: 'title',
    type: 'text',
    nullable: true,
  })
  title?: string;

  @Column({
    name: 'status',
    type: 'enum',
    enum: SubmissionStatus,
    default: SubmissionStatus.DRAFT,
  })
  status: SubmissionStatus;

  @Column({
    name: 'current_revision',
    type: 'int',
    default: 1,
  })
  currentRevision: number;

  @ManyToOne(() => EssayMissionTasks, (task) => task.submissions, { nullable: true })
  @JoinColumn({ name: 'task' })
  task?: EssayMissionTasks;

  @Column({
    name: 'task_id',
    type: 'uuid',
    nullable: true,
  })
  @IsUUID()
  @IsOptional()
  taskId?: string;

  @Column({
    name: 'is_active',
    type: 'boolean',
    default: true,
  })
  isActive?: boolean;

  @OneToMany(() => EssayTaskSubmissionHistory, (submission) => submission.submission, { nullable: true })
  submissionHistory: EssayTaskSubmissionHistory[];

  @Column({
    name: 'latest_submission_id',
    type: 'uuid',
    nullable: true,
  })
  latestSubmissionId?: string;

  @Column({
    name: 'total_revisions',
    type: 'int',
    default: 0,
  })
  totalRevisions: number;

  @Column({
    name: 'first_submitted_at',
    type: 'timestamp',
    nullable: true,
  })
  firstSubmittedAt?: Date;

  @Column({
    name: 'last_submitted_at',
    type: 'timestamp',
    nullable: true,
  })
  lastSubmittedAt?: Date;

  @Column({
    name: 'first_revision_progress',
    type: 'float',
    default: 0,
  })
  firstRevisionProgress: number;

  @Column({
    name: 'is_first_revision',
    type: 'boolean',
    default: false,
  })
  isFirstRevision: boolean;

  @OneToOne('EssayTaskSubmissionMarking', (marking: EssayTaskSubmissionMarking) => marking.submission)
  @JoinColumn({ name: 'submission_mark_id' })
  submissionMark: EssayTaskSubmissionMarking;

  @Column({
    name: 'submission_skin_id',
    type: 'uuid',
    nullable: true,
  })
  @IsUUID()
  submissionSkinId?: string;

  @ManyToOne(() => DiarySkin, { nullable: true })
  @JoinColumn({ name: 'submission_skin' })
  submissionSkin?: DiarySkin;

  @ManyToOne(() => StudentDiarySkin, { nullable: true })
  @JoinColumn({ name: 'student_diary_skin' })
  studentDiarySkin?: StudentDiarySkin;

  latestHistory?: EssayTaskSubmissionHistory;

}
