# HEC Backend - Project Overview

## System Overview

The HEC (Hangeul Education Center) Backend is a comprehensive educational platform designed to support English language learning through various interactive modules. The system provides a complete learning ecosystem with writing practice, interactive games, assessment tools, and progress tracking.

## Core Architecture

### Technology Stack
- **Framework**: NestJS (Node.js)
- **Database**: PostgreSQL with TypeORM
- **Authentication**: JWT-based authentication
- **File Storage**: Local filesystem / AWS S3 (configurable)
- **Real-time Communication**: WebSocket (Socket.IO)
- **API Documentation**: Swagger/OpenAPI

### System Components
- **Authentication & User Management**
- **Subscription & Plan Management**
- **Writing Modules** (Diary, Essay, Novel)
- **Interactive Games** (Story Maker, Block Game, Waterfall)
- **Assessment System** (QA, QA Missions)
- **Communication** (Chat System)
- **Achievement System** (Awards, Hall of Fame)
- **Commerce** (Shop, Payments, Promotions)

## User Roles & Permissions

### Students
- Create and manage writing content
- Participate in interactive games
- Complete assessments and missions
- Chat with tutors and admins
- Purchase items and earn rewards
- View progress and achievements

### Tutors
- Review and provide feedback on student work
- Manage assigned students
- Access tutor-specific tools and dashboards
- Participate in chat communications

### Admins
- Full system management capabilities
- User and content management
- System configuration and monitoring
- Award and promotion management

## Key Features

### Writing System
- **Diary Module**: Daily writing practice with tutor feedback
- **Essay Module**: Structured essay assignments and missions
- **Novel Module**: Creative writing with version history and topics

### Interactive Learning
- **Story Maker**: AI-powered story creation and evaluation
- **Block Game**: Sentence construction puzzles
- **Waterfall Game**: Grammar and vocabulary challenges

### Assessment & Progress
- **QA System**: Question and answer assessments
- **QA Missions**: Structured learning missions
- **Awards System**: Achievement recognition and rewards
- **Hall of Fame**: Leaderboards and recognition

### Communication & Support
- **Real-time Chat**: Student-tutor communication
- **File Sharing**: Document and media exchange
- **Notifications**: Multi-channel notification system

### Commerce & Engagement
- **Shop System**: Digital items and resources
- **Payment Integration**: KCP payment gateway
- **Promotions**: Discount and campaign management
- **Reward Points**: Gamification and incentives

## File Storage System

### Dual Storage Support
- **Local Storage**: Development and small deployments
- **AWS S3**: Production-ready cloud storage with CDN

### Supported File Types
- Profile pictures and avatars
- Shop items and digital assets
- Diary skins and themes
- QR codes for sharing
- Story maker images
- Chat attachments

## API Architecture

### RESTful Design
- Consistent API response format
- Comprehensive error handling
- Swagger documentation
- Version management support

### Authentication & Security
- JWT-based authentication
- Role-based access control
- Input validation and sanitization
- Rate limiting and security headers

### Real-time Features
- WebSocket connections for chat
- Live notifications
- Real-time progress updates

## Database Design

### Entity Relationships
- User management and roles
- Content creation and versioning
- Assessment and scoring
- Commerce and transactions
- Communication and notifications

### Data Integrity
- Foreign key constraints
- Audit logging
- Soft deletes for important data
- Transaction management

## Development Workflow

### Code Organization
- Modular architecture with NestJS modules
- Service-oriented design
- Repository pattern for data access
- Dependency injection

### Quality Assurance
- Comprehensive testing strategy
- API testing flows
- Performance monitoring
- Security best practices

### Deployment
- Docker containerization
- Environment-based configuration
- Database migrations
- CI/CD pipeline support

## Integration Points

### External Services
- AWS S3 for file storage
- CloudFront CDN for content delivery
- KCP payment gateway
- Email service providers
- Push notification services

### Frontend Integration
- RESTful API endpoints
- WebSocket connections
- File upload/download
- Authentication flows

## Monitoring & Analytics

### System Health
- Application logging
- Error tracking
- Performance metrics
- Database monitoring

### Business Intelligence
- User engagement analytics
- Learning progress tracking
- Commerce metrics
- System usage statistics

## Scalability Considerations

### Performance Optimization
- Database indexing and query optimization
- Caching strategies
- File storage optimization
- API response optimization

### Infrastructure Scaling
- Horizontal scaling support
- Load balancing capabilities
- Database replication
- CDN integration

## Security Framework

### Data Protection
- Encryption at rest and in transit
- Secure file storage
- PII data handling
- GDPR compliance considerations

### Access Control
- Multi-level authentication
- Role-based permissions
- API rate limiting
- Session management

## Future Roadmap

### Planned Enhancements
- Advanced AI integration
- Mobile app support
- Multi-language support
- Advanced analytics dashboard

### Technical Improvements
- Microservices architecture
- Enhanced caching
- Real-time collaboration features
- Advanced security features

This comprehensive platform provides a robust foundation for English language education with room for continuous growth and enhancement.