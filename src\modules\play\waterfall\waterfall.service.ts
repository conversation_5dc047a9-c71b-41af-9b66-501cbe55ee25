import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { WaterfallSet } from '../../../database/entities/waterfall-set.entity';
import { WaterfallQuestion } from '../../../database/entities/waterfall-question.entity';
import { WaterfallTrueFalseQuestion } from '../../../database/entities/waterfall-true-false-question.entity';
import { WaterfallMultipleChoiceQuestion } from '../../../database/entities/waterfall-multiple-choice-question.entity';
import { TutorWaterfallSet, WaterfallQuestionType } from '../../../database/entities/tutor-waterfall-set.entity';
import { TutorBlockGame } from '../../../database/entities/tutor-block-game.entity';
import { BlockGame } from '../../../database/entities/block-game.entity';
import { GameType, GameSource } from '../../../database/entities/game-performance-tracking.entity';
import { WaterfallParticipation } from '../../../database/entities/waterfall-participation.entity';
import { WaterfallAnswer } from '../../../database/entities/waterfall-answer.entity';
import { SubmitWaterfallGameDto, WaterfallParticipationResponseDto, WaterfallAnswerResponseDto } from '../../../database/models/waterfall/waterfall-submission.dto';
import { WaterfallGameResponseDto } from '../../../database/models/waterfall/waterfall-response.dto';
import { EnhancedWaterfallGameResponseDto, EnhancedSubmitWaterfallGameDto } from '../../../database/models/waterfall/enhanced-waterfall.dto';

@Injectable()
export class WaterfallService {
  private readonly logger = new Logger(WaterfallService.name);

  constructor(
    @InjectRepository(WaterfallSet)
    private readonly setRepository: Repository<WaterfallSet>,
    @InjectRepository(WaterfallQuestion)
    private readonly questionRepository: Repository<WaterfallQuestion>,
    @InjectRepository(WaterfallTrueFalseQuestion)
    private readonly trueFalseRepository: Repository<WaterfallTrueFalseQuestion>,
    @InjectRepository(WaterfallMultipleChoiceQuestion)
    private readonly multipleChoiceRepository: Repository<WaterfallMultipleChoiceQuestion>,
    @InjectRepository(TutorWaterfallSet)
    private readonly tutorSetRepository: Repository<TutorWaterfallSet>,
    @InjectRepository(TutorBlockGame)
    private readonly tutorBlockGameRepository: Repository<TutorBlockGame>,
    @InjectRepository(BlockGame)
    private readonly adminBlockGameRepository: Repository<BlockGame>,
    @InjectRepository(WaterfallParticipation)
    private readonly participationRepository: Repository<WaterfallParticipation>,
    @InjectRepository(WaterfallAnswer)
    private readonly answerRepository: Repository<WaterfallAnswer>,
    private readonly dataSource: DataSource,
  ) {}

  // Get all waterfall sets
  async getAllSets(): Promise<WaterfallSet[]> {
    return this.setRepository.find({
      order: {
        createdAt: 'DESC',
      },
    });
  }

  // Get a specific waterfall set by ID with its questions
  async getSetById(setId: string): Promise<WaterfallSet | null> {
    // Remove relations to avoid FK constraint issues
    return this.setRepository.findOne({
      where: { id: setId },
    });
  }

  // Get questions for a specific set
  async getQuestionsBySetId(setId: string): Promise<WaterfallQuestion[]> {
    return this.questionRepository.find({
      where: { setId },
      order: {
        id: 'ASC',
      },
    });
  }

  /**
   * Get a random waterfall set with questions for a student to play
   * Enhanced to prioritize tutor games over admin games with smart selection
   * @param studentId The ID of the student
   * @returns A random waterfall set with questions
   */
  async getRandomSet(studentId: string): Promise<EnhancedWaterfallGameResponseDto> {
    try {
      // Priority 1: Tutor games with smart selection
      const tutorGame = await this.getTutorWaterfallSet(studentId);
      if (tutorGame) {
        return { ...tutorGame, source: 'tutor' };
      }

      // Priority 2: Admin games with smart selection
      return this.getAdminWaterfallSet(studentId);
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      this.logger.error(`Failed to get random waterfall set for student ${studentId}: ${error.message}`, error.stack);
      throw new BadRequestException('Could not retrieve a game at this time. Please try again later.');
    }
  }

  /**
   * Get a specific waterfall set for playing again
   * @param setId The ID of the set to play
   * @param studentId The ID of the student
   * @returns The specific waterfall set with questions
   */
  async getSetForPlay(setId: string, studentId: string): Promise<EnhancedWaterfallGameResponseDto> {
    // Check if it's a tutor set first
    const tutorSet = await this.tutorSetRepository.findOne({
      where: { id: setId, isActive: true },
    });

    if (tutorSet) {
      return this.getTutorSet(setId);
    }

    // Check if it's an admin set
    const adminSet = await this.setRepository.findOne({
      where: { id: setId, isActive: true },
    });

    if (!adminSet) {
      throw new NotFoundException('Game not found or not available');
    }

    // Always get all question types regardless of set's questionType
    const questions = await this.getAllQuestionTypes(adminSet.id);

    return {
      id: adminSet.id,
      title: adminSet.title,
      total_score: adminSet.totalScore,
      total_questions: adminSet.totalQuestions,
      question_type: adminSet.questionType || WaterfallQuestionType.FILL_IN_BLANK,
      source: 'admin',
      questions,
    };
  }

  /**
   * Legacy method for backward compatibility
   */
  async getRandomSetLegacy(studentId: string): Promise<WaterfallGameResponseDto> {
    try {
      // Get all active sets
      const queryBuilder = this.setRepository
        .createQueryBuilder('set')
        .where('set.totalQuestions > 0')
        .andWhere('set.isActive = :isActive', { isActive: true })
        .orderBy('RANDOM()'); // PostgreSQL random ordering

      // Get a random set
      const set = await queryBuilder.getOne();

      if (!set) {
        this.logger.warn('No waterfall sets with questions found');
        throw new NotFoundException('No games available at the moment. Please try again later.');
      }

      // Get questions separately
      const questions = await this.getLegacyQuestions(set.id);

      const response: WaterfallGameResponseDto = {
        id: set.id,
        title: set.title,
        total_score: set.totalScore,
        total_questions: set.totalQuestions,
        questions,
      };

      return response;
    } catch (error) {
      this.logger.error(`Failed to get random waterfall set: ${error.message}`, error.stack);

      // Rethrow the error if it's already a NestJS exception
      if (error instanceof NotFoundException) {
        throw error;
      }

      // Otherwise, wrap it in a BadRequestException with a user-friendly message
      throw new BadRequestException("We couldn't find a game for you right now. Please try again later.");
    }
  }

  private async getTutorWaterfallSet(studentId: string): Promise<EnhancedWaterfallGameResponseDto | null> {
    // Get all tutor sets for this student
    const allTutorSets = await this.tutorSetRepository.find({
      where: { studentId, isActive: true },
    });

    if (!allTutorSets || allTutorSets.length === 0) {
      return null;
    }

    // Filter sets that have questions
    const setsWithQuestions = [];
    for (const set of allTutorSets) {
      const questionCount = await this.getQuestionCount(set.id);
      if (questionCount > 0) {
        // Get play count for this student
        const playCount = await this.participationRepository.count({
          where: { setId: set.id, studentId },
        });
        setsWithQuestions.push({ ...set, playCount });
      }
    }

    if (setsWithQuestions.length === 0) {
      return null;
    }

    // Find minimum play count
    const minPlayCount = Math.min(...setsWithQuestions.map(s => s.playCount));
    
    // Get sets with minimum play count
    const leastPlayedSets = setsWithQuestions.filter(s => s.playCount === minPlayCount);
    
    // Randomly select from least played sets
    const randomIndex = Math.floor(Math.random() * leastPlayedSets.length);
    const tutorSet = leastPlayedSets[randomIndex];

    // Get questions for the tutor set
    const questions = await this.getAllQuestionTypes(tutorSet.id);

    return {
      id: tutorSet.id,
      title: tutorSet.title,
      total_score: tutorSet.totalScore,
      total_questions: tutorSet.totalQuestions,
      question_type: tutorSet.questionType,
      source: 'tutor',
      questions,
    };
  }

  private async getTutorSet(setId: string): Promise<EnhancedWaterfallGameResponseDto> {
    const tutorSet = await this.tutorSetRepository.findOne({
      where: { id: setId, isActive: true },
    });

    if (!tutorSet) {
      throw new NotFoundException('Game not found');
    }

    const questions = await this.getAllQuestionTypes(setId);

    return {
      id: tutorSet.id,
      title: tutorSet.title,
      total_score: tutorSet.totalScore,
      total_questions: tutorSet.totalQuestions,
      question_type: tutorSet.questionType,
      source: 'tutor',
      questions,
    };
  }

  private async getQuestionCount(setId: string): Promise<number> {
    const [fillInBlankCount, trueFalseCount, mcCount] = await Promise.all([
      this.questionRepository.count({ where: { setId, isActive: true } }),
      this.trueFalseRepository.count({ where: { setId, isActive: true } }),
      this.multipleChoiceRepository.count({ where: { setId, isActive: true } }),
    ]);
    return fillInBlankCount + trueFalseCount + mcCount;
  }

  private async getAdminWaterfallSet(studentId: string): Promise<EnhancedWaterfallGameResponseDto> {
    // Get all active admin sets
    const allSets = await this.setRepository.find({
      where: { isActive: true },
    });

    if (!allSets || allSets.length === 0) {
      throw new NotFoundException('No admin games available');
    }

    // Filter sets that have questions
    const setsWithQuestions = [];
    for (const set of allSets) {
      const questionCount = await this.getQuestionCount(set.id);
      if (questionCount > 0) {
        // Get play count for this student
        const playCount = await this.participationRepository.count({
          where: { setId: set.id, studentId },
        });
        setsWithQuestions.push({ ...set, playCount });
      }
    }

    if (setsWithQuestions.length === 0) {
      throw new NotFoundException('No admin games with questions available');
    }

    // Find minimum play count
    const minPlayCount = Math.min(...setsWithQuestions.map(s => s.playCount));
    
    // Get sets with minimum play count
    const leastPlayedSets = setsWithQuestions.filter(s => s.playCount === minPlayCount);
    
    // Randomly select from least played sets
    const randomIndex = Math.floor(Math.random() * leastPlayedSets.length);
    const waterfallSet = leastPlayedSets[randomIndex];

    // Always get all question types regardless of set's questionType
    const questions = await this.getAllQuestionTypes(waterfallSet.id);

    return {
      id: waterfallSet.id,
      title: waterfallSet.title,
      total_score: waterfallSet.totalScore,
      total_questions: waterfallSet.totalQuestions,
      question_type: waterfallSet.questionType || WaterfallQuestionType.FILL_IN_BLANK,
      source: 'admin',
      questions,
    };
  }

  private async getLegacyQuestions(setId: string): Promise<any[]> {
    // Use direct query to avoid relationship issues
    const questions = await this.questionRepository.find({
      where: { setId, isActive: true },
      order: { createdAt: 'ASC' },
    });
    
    return questions.map(q => ({
      id: q.id,
      question_text: q.questionText,
      question_text_plain: q.questionTextPlain,
      options: q.options,
      correct_answers: q.correctAnswers,
      time_limit_in_seconds: q.timeLimitInSeconds,
      level: q.level,
    }));
  }

  private async getAllQuestionTypes(setId: string): Promise<any[]> {
    // Use raw queries to avoid foreign key constraint issues
    const [fillInBlankQuestions, trueFalseQuestions, mcQuestions] = await Promise.all([
      this.questionRepository.find({
        where: { setId, isActive: true },
        order: { createdAt: 'ASC' },
      }),
      this.trueFalseRepository.find({
        where: { setId, isActive: true },
        order: { createdAt: 'ASC' },
      }),
      this.multipleChoiceRepository.find({
        where: { setId, isActive: true },
        order: { createdAt: 'ASC' },
      }),
    ]);

    const allQuestions = [];

    // Add fill-in-blank questions
    allQuestions.push(...fillInBlankQuestions.map(q => ({
      id: q.id,
      type: 'fill_in_blank',
      question_text: q.questionText,
      question_text_plain: q.questionTextPlain,
      options: q.options,
      correct_answers: q.correctAnswers,
      time_limit_in_seconds: q.timeLimitInSeconds,
      level: q.level,
    })));

    // Add true/false questions
    allQuestions.push(...trueFalseQuestions.map(q => ({
      id: q.id,
      type: 'true_false',
      statement: q.statement,
      correct_answer: q.correctAnswer,
      time_limit_in_seconds: q.timeLimitInSeconds,
      level: q.level,
    })));

    // Add multiple choice questions
    allQuestions.push(...mcQuestions.map(q => ({
      id: q.id,
      type: q.allowMultipleSelection ? 'multiple_choice_multiple' : 'multiple_choice_single',
      question_text: q.questionText,
      options: q.options,
      allow_multiple_selection: q.allowMultipleSelection,
      min_selections: q.minSelections,
      max_selections: q.maxSelections,
      correct_option_indices: q.correctOptionIndices,
      time_limit_in_seconds: q.timeLimitInSeconds,
      level: q.level,
    })));

    // Sort by creation date to maintain consistent order
    return allQuestions.sort((a, b) => {
      const aQuestion = fillInBlankQuestions.find(q => q.id === a.id) || 
                       trueFalseQuestions.find(q => q.id === a.id) || 
                       mcQuestions.find(q => q.id === a.id);
      const bQuestion = fillInBlankQuestions.find(q => q.id === b.id) || 
                       trueFalseQuestions.find(q => q.id === b.id) || 
                       mcQuestions.find(q => q.id === b.id);
      return new Date(aQuestion.createdAt).getTime() - new Date(bQuestion.createdAt).getTime();
    });
  }

  private async getQuestionsByType(setId: string, questionType: WaterfallQuestionType): Promise<any[]> {
    switch (questionType) {
      case WaterfallQuestionType.TRUE_FALSE:
        const trueFalseQuestions = await this.trueFalseRepository.find({
          where: { setId, isActive: true },
          order: { createdAt: 'ASC' },
        });
        return trueFalseQuestions.map(q => ({
          id: q.id,
          type: 'true_false',
          statement: q.statement,
          correct_answer: q.correctAnswer,
          time_limit_in_seconds: q.timeLimitInSeconds,
          level: q.level,
        }));

      case WaterfallQuestionType.MULTIPLE_CHOICE_SINGLE:
      case WaterfallQuestionType.MULTIPLE_CHOICE_MULTIPLE:
        const mcQuestions = await this.multipleChoiceRepository.find({
          where: { setId, isActive: true },
          order: { createdAt: 'ASC' },
        });
        return mcQuestions.map(q => ({
          id: q.id,
          type: q.allowMultipleSelection ? 'multiple_choice_multiple' : 'multiple_choice_single',
          question_text: q.questionText,
          options: q.options,
          allow_multiple_selection: q.allowMultipleSelection,
          min_selections: q.minSelections,
          max_selections: q.maxSelections,
          correct_option_indices: q.correctOptionIndices,
          time_limit_in_seconds: q.timeLimitInSeconds,
          level: q.level,
        }));

      case WaterfallQuestionType.FILL_IN_BLANK:
        const fillInBlankQuestions = await this.getLegacyQuestions(setId);
        return fillInBlankQuestions.map(q => ({ ...q, type: 'fill_in_blank' }));

      case WaterfallQuestionType.MIXED:
        return this.getAllQuestionTypes(setId);

      default:
        return [];
    }
  }

  async submitEnhancedGame(userId: string, setId: string, dto: EnhancedSubmitWaterfallGameDto): Promise<any> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const tutorSet = await this.tutorSetRepository.findOne({ where: { id: setId } });
      let correctCount = 0;
      const totalQuestions = dto.answers.length;

      for (const answer of dto.answers) {
        const isCorrect = await this.checkEnhancedAnswer(answer, dto.question_type);
        if (isCorrect) correctCount++;
      }

      const totalScore = tutorSet?.totalScore || 100;
      const score = Math.round((correctCount / totalQuestions) * totalScore);

      await queryRunner.commitTransaction();

      return {
        score,
        total_score: totalScore,
        total_correct_answers: correctCount,
        total_questions: totalQuestions,
        submitted_at: new Date(),
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  private async checkEnhancedAnswer(answer: any, questionType: WaterfallQuestionType): Promise<boolean> {
    // For mixed types, determine the actual question type from the answer or question
    if (questionType === WaterfallQuestionType.MIXED) {
      return this.checkMixedAnswer(answer);
    }

    switch (questionType) {
      case WaterfallQuestionType.TRUE_FALSE:
        const tfQuestion = await this.trueFalseRepository.findOne({ where: { id: answer.question_id } });
        return tfQuestion?.correctAnswer === answer.answer;

      case WaterfallQuestionType.MULTIPLE_CHOICE_SINGLE:
      case WaterfallQuestionType.MULTIPLE_CHOICE_MULTIPLE:
        const mcQuestion = await this.multipleChoiceRepository.findOne({ where: { id: answer.question_id } });
        if (!mcQuestion) return false;
        
        const sortedCorrect = [...mcQuestion.correctOptionIndices].sort();
        const sortedStudent = [...answer.selected_indices].sort();
        return JSON.stringify(sortedCorrect) === JSON.stringify(sortedStudent);

      case WaterfallQuestionType.FILL_IN_BLANK:
        const fibQuestion = await this.questionRepository.findOne({ where: { id: answer.question_id } });
        return fibQuestion ? this.checkAnswers(fibQuestion.correctAnswers, answer.answers) : false;

      default:
        return false;
    }
  }

  private async checkMixedAnswer(answer: any): Promise<boolean> {
    // Try to find the question in each repository to determine its type
    const [tfQuestion, mcQuestion, fibQuestion] = await Promise.all([
      this.trueFalseRepository.findOne({ where: { id: answer.question_id } }),
      this.multipleChoiceRepository.findOne({ where: { id: answer.question_id } }),
      this.questionRepository.findOne({ where: { id: answer.question_id } })
    ]);

    if (tfQuestion) {
      const studentAnswer = answer.answers && answer.answers.length > 0 ? answer.answers[0] : false;
      return tfQuestion.correctAnswer === studentAnswer;
    }
    
    if (mcQuestion) {
      const studentIndices = [];
      for (const studentAnswer of answer.answers) {
        const index = mcQuestion.options.findIndex(option => 
          option.toLowerCase().trim() === studentAnswer.toLowerCase().trim()
        );
        if (index !== -1) {
          studentIndices.push(index);
        }
      }
      const sortedCorrect = [...mcQuestion.correctOptionIndices].sort();
      const sortedStudent = [...studentIndices].sort();
      return JSON.stringify(sortedCorrect) === JSON.stringify(sortedStudent);
    }
    
    if (fibQuestion) {
      return this.checkAnswers(fibQuestion.correctAnswers, answer.answers);
    }

    return false;
  }

  // This method has been replaced by submitGame

  // Helper method to check if the student's answers match the correct answers
  private checkAnswers(correctAnswers: string[], studentAnswers: string[]): boolean {
    if (correctAnswers.length !== studentAnswers.length) {
      return false;
    }

    for (let i = 0; i < correctAnswers.length; i++) {
      // Case-insensitive comparison
      if (correctAnswers[i].toLowerCase() !== studentAnswers[i].toLowerCase()) {
        return false;
      }
    }

    return true;
  }

  /**
   * Submit a waterfall game result - handles all question types
   * @param userId The ID of the user submitting the game
   * @param setId The ID of the set being played
   * @param dto The submission data
   * @returns The participation record with detailed results
   */
  async submitGame(userId: string, setId: string, dto: any): Promise<WaterfallParticipationResponseDto> {
    // Handle both legacy and new submission formats
    const actualSetId = dto.set_id || setId;
    const questionType = dto.question_type;
    
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Check if it's a tutor set or admin set
      const tutorSet = await this.tutorSetRepository.findOne({ where: { id: actualSetId } });
      const adminSet = tutorSet ? null : await this.setRepository.findOne({ where: { id: actualSetId } });
      
      if (!tutorSet && !adminSet) {
        throw new NotFoundException('Game not found. Please try a different game.');
      }

      const set = tutorSet || adminSet;
      const setQuestionType = tutorSet?.questionType || adminSet?.questionType || WaterfallQuestionType.FILL_IN_BLANK;
      
      // Get questions based on type and source
      let totalQuestions = 0;
      let correctCount = 0;
      
      if (tutorSet) {
        ({ totalQuestions, correctCount } = await this.processTutorSubmission(dto, tutorSet));
      } else {
        ({ totalQuestions, correctCount } = await this.processAdminSubmission(dto, adminSet));
      }

      // Process answers and create answer records
      const answerResults = [];
      const answerEntities = [];
      
      // Calculate score
      const totalScore = set.totalScore || 100;
      const score = totalQuestions > 0 ? Math.round((correctCount / totalQuestions) * totalScore) : 0;

      // Create participation record
      const participation = new WaterfallParticipation();
      participation.studentId = userId;
      participation.setId = actualSetId;
      participation.totalQuestions = totalQuestions;
      participation.totalCorrectAnswers = correctCount;
      participation.score = score;
      participation.createdBy = userId;
      participation.updatedBy = userId;

      const savedParticipation = await queryRunner.manager.save(participation);
      
      // Create answer records and response
      if (tutorSet) {
        answerResults.push(...await this.createTutorAnswerRecords(queryRunner, savedParticipation.id, dto, tutorSet, userId));
      } else {
        answerResults.push(...await this.createAdminAnswerRecords(queryRunner, savedParticipation.id, dto, adminSet, userId));
      }
      
      await queryRunner.commitTransaction();

      return {
        id: savedParticipation.id,
        set_id: actualSetId,
        set_title: set.title,
        total_correct_answers: correctCount,
        total_questions: totalQuestions,
        score: score,
        submitted_at: savedParticipation.createdAt,
        answers: answerResults,
      };
    } catch (error) {
      // Rollback the transaction in case of error
      await queryRunner.rollbackTransaction();

      this.logger.error(`Failed to submit waterfall game for set ${setId}: ${error.message}`, error.stack);

      // Rethrow NestJS exceptions as they already have appropriate status codes and messages
      if (error instanceof BadRequestException || error instanceof NotFoundException) {
        throw error;
      }

      // For unexpected errors, provide a generic user-friendly message
      throw new BadRequestException("We couldn't process your answers. Please check your submission and try again.");
    } finally {
      await queryRunner.release();
    }
  }

  private async processTutorSubmission(dto: any, tutorSet: any): Promise<{ totalQuestions: number; correctCount: number }> {
    let correctCount = 0;
    const totalQuestions = dto.answers.length;

    for (const answer of dto.answers) {
      // Always use mixed type checking since we return all question types
      const isCorrect = await this.checkEnhancedAnswer(answer, WaterfallQuestionType.MIXED);
      if (isCorrect) correctCount++;
    }

    return { totalQuestions, correctCount };
  }

  private async processAdminSubmission(dto: any, adminSet: any): Promise<{ totalQuestions: number; correctCount: number }> {
    // Always process as mixed type since we return all question types
    return this.processTutorSubmission(dto, adminSet);
  }

  private async createTutorAnswerRecords(queryRunner: any, participationId: string, dto: any, tutorSet: any, userId: string): Promise<any[]> {
    const answerResults = [];
    
    for (const answer of dto.answers) {
      // Always use mixed type checking since we return all question types
      const isCorrect = await this.checkEnhancedAnswer(answer, WaterfallQuestionType.MIXED);
      
      // Create answer entity
      const answerEntity = new WaterfallAnswer();
      answerEntity.participationId = participationId;
      answerEntity.questionId = answer.question_id;
      answerEntity.submittedAnswers = this.formatSubmittedAnswer(answer, WaterfallQuestionType.MIXED);
      answerEntity.isCorrect = isCorrect;
      answerEntity.createdBy = userId;
      answerEntity.updatedBy = userId;
      
      await queryRunner.manager.save(answerEntity);
      
      // Get correct answer for response
      const correctAnswer = await this.getCorrectAnswer(answer.question_id, WaterfallQuestionType.MIXED);
      
      answerResults.push({
        question_id: answer.question_id,
        submitted_answers: answerEntity.submittedAnswers,
        is_correct: isCorrect,
        correct_answers: correctAnswer,
      });
    }
    
    return answerResults;
  }

  private async createAdminAnswerRecords(queryRunner: any, participationId: string, dto: any, adminSet: any, userId: string): Promise<any[]> {
    // Always process as mixed type since we return all question types
    return this.createTutorAnswerRecords(queryRunner, participationId, dto, adminSet, userId);
  }

  private formatSubmittedAnswer(answer: any, questionType: WaterfallQuestionType): string[] {
    // Always use answers array format for consistency
    return answer.answers ? answer.answers.map(a => a.toString()) : [];
  }

  private async getCorrectAnswer(questionId: string, questionType: WaterfallQuestionType): Promise<string[]> {
    // For mixed types, determine the actual question type
    if (questionType === WaterfallQuestionType.MIXED) {
      return this.getMixedCorrectAnswer(questionId);
    }

    switch (questionType) {
      case WaterfallQuestionType.TRUE_FALSE:
        const tfQuestion = await this.trueFalseRepository.findOne({ where: { id: questionId } });
        return [tfQuestion?.correctAnswer.toString() || 'false'];
      case WaterfallQuestionType.MULTIPLE_CHOICE_SINGLE:
      case WaterfallQuestionType.MULTIPLE_CHOICE_MULTIPLE:
        const mcQuestion = await this.multipleChoiceRepository.findOne({ where: { id: questionId } });
        return mcQuestion?.correctOptionIndices.map(i => mcQuestion.options[i]) || [];
      case WaterfallQuestionType.FILL_IN_BLANK:
        const fibQuestion = await this.questionRepository.findOne({ where: { id: questionId } });
        return fibQuestion?.correctAnswers || [];
      default:
        return [];
    }
  }

  private async getMixedCorrectAnswer(questionId: string): Promise<string[]> {
    // Try to find the question in each repository to determine its type
    const [tfQuestion, mcQuestion, fibQuestion] = await Promise.all([
      this.trueFalseRepository.findOne({ where: { id: questionId } }),
      this.multipleChoiceRepository.findOne({ where: { id: questionId } }),
      this.questionRepository.findOne({ where: { id: questionId } })
    ]);

    if (tfQuestion) {
      return [tfQuestion.correctAnswer.toString()];
    }
    
    if (mcQuestion) {
      // Return actual option text instead of indices
      return mcQuestion.correctOptionIndices.map(i => mcQuestion.options[i]);
    }
    
    if (fibQuestion) {
      return fibQuestion.correctAnswers;
    }

    return [];
  }
}
