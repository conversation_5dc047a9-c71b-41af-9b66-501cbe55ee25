import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveBlockGameAttemptForeignKey1736447927000 implements MigrationInterface {
  name = 'RemoveBlockGameAttemptForeignKey1736447927000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop the foreign key constraint
    await queryRunner.query(`ALTER TABLE "block_game_attempt" DROP CONSTRAINT "FK_block_game_attempt_block_game"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Recreate the foreign key constraint
    await queryRunner.query(`ALTER TABLE "block_game_attempt" ADD CONSTRAINT "FK_block_game_attempt_block_game" FOREIGN KEY ("block_game_id") REFERENCES "block_game"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
  }
}