import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ContactController } from './contact.controller';
import { ContactService } from './contact.service';
import { ContactUs } from '../../database/entities/contact-us.entity';
import EmailService from '../../common/services/email.service';
import LoggerService from '../../common/services/logger.service';
import { DeeplinkService } from '../../common/utils/deeplink.service';

@Module({
  imports: [TypeOrmModule.forFeature([ContactUs])],
  controllers: [ContactController],
  providers: [ContactService, EmailService, LoggerService, DeeplinkService],
  exports: [ContactService],
})
export class ContactModule {}