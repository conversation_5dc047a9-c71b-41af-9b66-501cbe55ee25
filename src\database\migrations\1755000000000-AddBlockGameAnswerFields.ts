import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddBlockGameAnswerFields1755000000000 implements MigrationInterface {
  name = 'AddBlockGameAnswerFields1755000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add new columns for gap answers
    await queryRunner.query(`
      ALTER TABLE "block_game_sentence" 
      ADD COLUMN "starting_part_answers" text[] DEFAULT '{}',
      ADD COLUMN "expanding_part_answers" text[] DEFAULT '{}'
    `);

    // Migrate existing data: convert sentences to gap format
    // This is a simple migration - extract last word as gap
    await queryRunner.query(`
      UPDATE "block_game_sentence" 
      SET 
        "starting_part" = CASE 
          WHEN trim("starting_part") = '' THEN "starting_part"
          ELSE regexp_replace(trim("starting_part"), '\\s+\\S+$', ' [[gap]]')
        END,
        "starting_part_answers" = CASE 
          WHEN trim("starting_part") = '' THEN '{}'
          ELSE ARRAY[regexp_replace(trim("starting_part"), '^.*\\s+', '')]
        END,
        "expanding_part" = CASE 
          WHEN trim("expanding_part") = '' THEN "expanding_part"
          ELSE regexp_replace(trim("expanding_part"), '\\s+\\S+$', ' [[gap]]')
        END,
        "expanding_part_answers" = CASE 
          WHEN trim("expanding_part") = '' THEN '{}'
          ELSE ARRAY[regexp_replace(trim("expanding_part"), '^.*\\s+', '')]
        END
      WHERE "starting_part_answers" = '{}' AND "expanding_part_answers" = '{}'
    `);

    // Make columns NOT NULL after migration
    await queryRunner.query(`
      ALTER TABLE "block_game_sentence" 
      ALTER COLUMN "starting_part_answers" SET NOT NULL,
      ALTER COLUMN "expanding_part_answers" SET NOT NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "block_game_sentence" 
      DROP COLUMN "starting_part_answers",
      DROP COLUMN "expanding_part_answers"
    `);
  }
}