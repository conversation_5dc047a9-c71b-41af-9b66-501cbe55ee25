import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { FileRegistryService } from './file-registry.service';
import { BaseFileRegistry } from '../../database/entities/base-file-registry.entity';
import { SecureTestDataFactory } from '../../../test/fixtures/factories/secure-test-data.factory';

describe('FileRegistryService Security Tests', () => {
  let service: FileRegistryService;
  let mockRepository: any;

  beforeEach(async () => {
    mockRepository = {
      findOne: jest.fn(),
      find: jest.fn(),
      save: jest.fn(),
      delete: jest.fn(),
      create: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FileRegistryService,
        {
          provide: getRepositoryToken(BaseFileRegistry),
          useValue: mockRepository,
        },
      ],
    }).compile();

    service = module.get<FileRegistryService>(FileRegistryService);
  });

  describe('Path Traversal Prevention', () => {
    it('should prevent path traversal attacks', async () => {
      const unsafePaths = SecureTestDataFactory.createUnsafeTestFilePaths();
      
      for (const unsafePath of unsafePaths) {
        await expect(
          service.validateFilePath(unsafePath)
        ).rejects.toThrow('Invalid file path');
      }
    });

    it('should allow safe file paths', async () => {
      const safePaths = SecureTestDataFactory.createSafeTestFilePaths();
      
      for (const safePath of safePaths) {
        expect(() => {
          service.validateFilePath(safePath);
        }).not.toThrow();
      }
    });

    it('should reject absolute paths', () => {
      const absolutePaths = [
        '/etc/passwd',
        'C:\\Windows\\System32\\config\\SAM',
        '/var/log/auth.log',
        'D:\\sensitive\\data.txt',
      ];

      absolutePaths.forEach(path => {
        expect(() => {
          service.validateFilePath(path);
        }).toThrow('Invalid file path');
      });
    });

    it('should reject file:// URLs', () => {
      const fileUrls = [
        'file:///etc/passwd',
        'file://C:/Windows/System32/config/SAM',
        'file:///var/log/auth.log',
      ];

      fileUrls.forEach(url => {
        expect(() => {
          service.validateFilePath(url);
        }).toThrow('Invalid file path');
      });
    });
  });

  describe('File Extension Validation', () => {
    it('should validate allowed file extensions', () => {
      const allowedExtensions = ['.jpg', '.png', '.pdf', '.doc', '.txt'];
      const allowedFiles = [
        'document.pdf',
        'image.jpg',
        'photo.png',
        'text.txt',
        'report.doc',
      ];

      allowedFiles.forEach(file => {
        expect(() => {
          service.validateFileExtension(file, allowedExtensions);
        }).not.toThrow();
      });
    });

    it('should reject dangerous file extensions', () => {
      const dangerousExtensions = [
        'malware.exe',
        'script.bat',
        'virus.scr',
        'trojan.com',
        'backdoor.pif',
      ];

      dangerousExtensions.forEach(file => {
        expect(() => {
          service.validateFileExtension(file, ['.jpg', '.png']);
        }).toThrow('Invalid file extension');
      });
    });

    it('should handle case-insensitive extensions', () => {
      const files = ['IMAGE.JPG', 'document.PDF', 'photo.PNG'];
      
      files.forEach(file => {
        expect(() => {
          service.validateFileExtension(file, ['.jpg', '.pdf', '.png']);
        }).not.toThrow();
      });
    });
  });

  describe('File Size Validation', () => {
    it('should validate file size limits', () => {
      const mockFile = SecureTestDataFactory.createMockFileUpload();
      mockFile.size = 1024 * 1024; // 1MB
      
      expect(() => {
        service.validateFileSize(mockFile, 2 * 1024 * 1024); // 2MB limit
      }).not.toThrow();
    });

    it('should reject files exceeding size limit', () => {
      const mockFile = SecureTestDataFactory.createMockFileUpload();
      mockFile.size = 10 * 1024 * 1024; // 10MB
      
      expect(() => {
        service.validateFileSize(mockFile, 5 * 1024 * 1024); // 5MB limit
      }).toThrow('File size exceeds limit');
    });

    it('should handle zero-byte files', () => {
      const mockFile = SecureTestDataFactory.createMockFileUpload();
      mockFile.size = 0;
      
      expect(() => {
        service.validateFileSize(mockFile, 1024);
      }).toThrow('Invalid file size');
    });
  });

  describe('MIME Type Validation', () => {
    it('should validate allowed MIME types', () => {
      const allowedMimeTypes = ['image/jpeg', 'image/png', 'application/pdf'];
      const mockFile = SecureTestDataFactory.createMockFileUpload();
      mockFile.mimetype = 'image/jpeg';
      
      expect(() => {
        service.validateMimeType(mockFile, allowedMimeTypes);
      }).not.toThrow();
    });

    it('should reject dangerous MIME types', () => {
      const dangerousMimeTypes = [
        'application/x-executable',
        'application/x-msdownload',
        'application/x-msdos-program',
      ];
      
      dangerousMimeTypes.forEach(mimeType => {
        const mockFile = SecureTestDataFactory.createMockFileUpload();
        mockFile.mimetype = mimeType;
        
        expect(() => {
          service.validateMimeType(mockFile, ['image/jpeg', 'image/png']);
        }).toThrow('Invalid MIME type');
      });
    });

    it('should detect MIME type spoofing', () => {
      const mockFile = SecureTestDataFactory.createMockFileUpload();
      mockFile.originalname = 'image.jpg';
      mockFile.mimetype = 'application/x-executable'; // Spoofed MIME type
      
      expect(() => {
        service.validateMimeType(mockFile, ['image/jpeg', 'image/png']);
      }).toThrow('Invalid MIME type');
    });
  });

  describe('File Content Validation', () => {
    it('should scan for malicious content patterns', () => {
      const maliciousPatterns = [
        Buffer.from('MZ'), // PE executable header
        Buffer.from('PK'), // ZIP/JAR header that could contain malware
        Buffer.from('<?php'), // PHP script
        Buffer.from('<script>'), // JavaScript
      ];

      maliciousPatterns.forEach(pattern => {
        const mockFile = SecureTestDataFactory.createMockFileUpload();
        mockFile.buffer = pattern;
        
        expect(() => {
          service.validateFileContent(mockFile);
        }).toThrow('Potentially malicious file content detected');
      });
    });

    it('should allow safe file content', () => {
      const safeContent = Buffer.from('This is a safe text file content');
      const mockFile = SecureTestDataFactory.createMockFileUpload();
      mockFile.buffer = safeContent;
      
      expect(() => {
        service.validateFileContent(mockFile);
      }).not.toThrow();
    });
  });

  describe('File Registry Operations', () => {
    it('should create file registry entry securely', async () => {
      const mockFile = SecureTestDataFactory.createMockFileUpload();
      const testUser = SecureTestDataFactory.createMockUser();
      
      mockRepository.create.mockReturnValue({
        id: 'registry-1',
        originalName: mockFile.originalname,
        fileName: mockFile.filename,
        filePath: mockFile.path,
        fileSize: mockFile.size,
        mimeType: mockFile.mimetype,
        userId: testUser.id,
      });
      
      mockRepository.save.mockResolvedValue({
        id: 'registry-1',
        originalName: mockFile.originalname,
        fileName: mockFile.filename,
        filePath: mockFile.path,
        fileSize: mockFile.size,
        mimeType: mockFile.mimetype,
        userId: testUser.id,
      });

      const result = await service.createFileRegistry(mockFile, testUser.id);

      expect(result).toBeDefined();
      expect(result.userId).toBe(testUser.id);
      expect(mockRepository.save).toHaveBeenCalled();
    });

    it('should prevent unauthorized file access', async () => {
      const testUser1 = SecureTestDataFactory.createMockUser();
      const testUser2 = SecureTestDataFactory.createMockUser();
      
      mockRepository.findOne.mockResolvedValue({
        id: 'registry-1',
        userId: testUser1.id,
        filePath: 'uploads/test-file.jpg',
      });

      await expect(
        service.getFileRegistry('registry-1', testUser2.id)
      ).rejects.toThrow('Unauthorized file access');
    });

    it('should sanitize file names', () => {
      const dangerousNames = [
        '../../../etc/passwd',
        'file<script>alert(1)</script>.jpg',
        'file\x00.jpg',
        'CON.jpg', // Windows reserved name
        'file|rm -rf /.jpg',
      ];

      dangerousNames.forEach(name => {
        const sanitized = service.sanitizeFileName(name);
        
        expect(sanitized).not.toContain('..');
        expect(sanitized).not.toContain('<script>');
        expect(sanitized).not.toContain('\x00');
        expect(sanitized).not.toContain('|');
        expect(sanitized).not.toBe('CON.jpg');
      });
    });
  });

  describe('Access Control', () => {
    it('should enforce user-based file access', async () => {
      const testUser = SecureTestDataFactory.createMockUser();
      const fileId = 'file-123';
      
      mockRepository.findOne.mockResolvedValue({
        id: fileId,
        userId: testUser.id,
        filePath: 'uploads/user-file.jpg',
      });

      const result = await service.getFileRegistry(fileId, testUser.id);
      
      expect(result).toBeDefined();
      expect(result.userId).toBe(testUser.id);
    });

    it('should prevent cross-user file access', async () => {
      const testUser1 = SecureTestDataFactory.createMockUser();
      const testUser2 = SecureTestDataFactory.createMockUser();
      const fileId = 'file-123';
      
      mockRepository.findOne.mockResolvedValue({
        id: fileId,
        userId: testUser1.id,
        filePath: 'uploads/user1-file.jpg',
      });

      await expect(
        service.getFileRegistry(fileId, testUser2.id)
      ).rejects.toThrow('Unauthorized file access');
    });
  });
});