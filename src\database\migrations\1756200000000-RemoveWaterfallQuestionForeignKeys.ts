import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveWaterfallQuestionForeignKeys1756200000000 implements MigrationInterface {
  name = 'RemoveWaterfallQuestionForeignKeys1756200000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Remove foreign key constraints from waterfall question tables
    // This allows them to reference both WaterfallSet and TutorWaterfallSet IDs
    
    await queryRunner.query(`
      ALTER TABLE "waterfall_question" 
      DROP CONSTRAINT IF EXISTS "FK_waterfall_question_set_id"
    `);

    await queryRunner.query(`
      ALTER TABLE "waterfall_true_false_question" 
      DROP CONSTRAINT IF EXISTS "FK_48780dba8d9da4250e52f5d2821"
    `);

    await queryRunner.query(`
      ALTER TABLE "waterfall_multiple_choice_question" 
      DROP CONSTRAINT IF EXISTS "FK_waterfall_multiple_choice_question_set_id"
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Re-add foreign key constraints (this might fail if there are tutor questions)
    await queryRunner.query(`
      ALTER TABLE "waterfall_question" 
      ADD CONSTRAINT "FK_waterfall_question_set_id" 
      FOREIGN KEY ("set_id") REFERENCES "waterfall_set"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "waterfall_true_false_question" 
      ADD CONSTRAINT "FK_48780dba8d9da4250e52f5d2821" 
      FOREIGN KEY ("set_id") REFERENCES "waterfall_set"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "waterfall_multiple_choice_question" 
      ADD CONSTRAINT "FK_waterfall_multiple_choice_question_set_id" 
      FOREIGN KEY ("set_id") REFERENCES "waterfall_set"("id") ON DELETE CASCADE
    `);
  }
}