import { PipeTransform, Injectable, BadRequestException, ExecutionContext } from '@nestjs/common';
import { TimezoneService } from '../services/timezone.service';
import { JwtService } from '@nestjs/jwt';

@Injectable()
export class TimezoneDatePipe implements PipeTransform {
  constructor(
    private readonly timezoneService: TimezoneService,
    private readonly jwtService: JwtService,
  ) {}

  async transform(value: any, metadata: any): Promise<Date | { start: Date; end: Date } | null> {
    if (!value) {
      return null;
    }

    try {
      // Get user timezone from request context
      const request = this.getRequestFromContext();
      const userId = await this.extractUserIdFromRequest(request);
      const userTimezone = userId 
        ? await this.timezoneService.getUserTimezone(userId)
        : 'Asia/Seoul';

      // Handle date range (for full day queries)
      if (this.isDateString(value)) {
        return this.timezoneService.getUserDateRange(value, userTimezone);
      }

      // Handle single date
      if (value instanceof Date) {
        return this.timezoneService.convertUserTimezoneToUTC(value.toISOString(), userTimezone);
      }

      // Handle date string
      if (typeof value === 'string') {
        return this.timezoneService.convertUserTimezoneToUTC(value, userTimezone);
      }

      return new Date(value);
    } catch (error) {
      throw new BadRequestException(`Invalid date format: ${value}`);
    }
  }

  private isDateString(value: string): boolean {
    // Check if it's a date in YYYY-MM-DD format (for full day queries)
    return /^\d{4}-\d{2}-\d{2}$/.test(value);
  }

  private getRequestFromContext(): any {
    // This is a simplified approach - in real implementation,
    // you might need to use REQUEST scope or pass request through context
    return null;
  }

  private async extractUserIdFromRequest(request: any): Promise<string | null> {
    if (!request) return null;
    
    try {
      const authHeader = request.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return null;
      }

      const token = authHeader.substring(7);
      const payload = this.jwtService.verify(token);
      return payload.id || payload.sub || null;
    } catch (error) {
      return null;
    }
  }
}