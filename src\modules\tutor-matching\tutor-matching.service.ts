import { Injectable, Logger, NotFoundException, BadRequestException, ConflictException } from '@nestjs/common';
import { RelatedEntityType } from '../../common/enums/related-entity-type.enum';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { DataSource } from 'typeorm';

import { StudentTutorMapping, MappingStatus } from '../../database/entities/student-tutor-mapping.entity';
import { User, UserType } from '../../database/entities/user.entity';
import { PlanFeature, FeatureType } from '../../database/entities/plan-feature.entity';
import { DiaryEntry, DiaryEntryStatus } from '../../database/entities/diary-entry.entity';
import { UserPlan } from '../../database/entities/user-plan.entity';
import { TutorApproval, TutorApprovalStatus } from '../../database/entities/tutor-approval.entity';
import {
  AssignTutorDto,
  UpdateTutorAssignmentDto,
  TutorAssignmentResponseDto,
  StudentTutorDto,
  TutorStudentDto,
  TutorStudentFlattenedDto,
  StudentModuleDto,
  TutorWorkloadDto,
  AssignmentFilterDto,
  AutoAssignTutorsDto,
  StudentTutorFilterDto,
  ChangeStudentTutorDto,
  ChangeStudentTutorResponseDto,
} from '../../database/models/tutor-matching.dto';
import { ConversationByFriendResponseDto } from '../../database/models/student-friendship.dto';
import { Conversation } from '../../database/entities/conversation.entity';
import { ProfilePictureService } from '../../common/services/profile-picture.service';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';
import { AsyncNotificationHelperService } from '../notification/async-notification-helper.service';
import { NotificationType } from '../../database/entities/notification.entity';
import { getCurrentUTCDate } from '../../common/utils/date-utils';
import { ChatService } from '../chat/chat.service';
import { EmailTemplateService } from '../../common/services/email-template.service';
import { ProfileLinkService } from '../../common/utils/profile-link.utils';

@Injectable()
export class TutorMatchingService {
  private readonly logger = new Logger(TutorMatchingService.name);

  constructor(
    @InjectRepository(StudentTutorMapping)
    private readonly studentTutorMappingRepository: Repository<StudentTutorMapping>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(PlanFeature)
    private readonly planFeatureRepository: Repository<PlanFeature>,
    @InjectRepository(DiaryEntry)
    private readonly diaryEntryRepository: Repository<DiaryEntry>,
    @InjectRepository(Conversation)
    private readonly conversationRepository: Repository<Conversation>,
    @InjectRepository(UserPlan)
    private readonly userPlanRepository: Repository<UserPlan>,
    @InjectRepository(TutorApproval)
    private readonly tutorApprovalRepository: Repository<TutorApproval>,
    private readonly asyncNotificationHelper: AsyncNotificationHelperService,
    private readonly chatService: ChatService,
    private readonly profilePictureService: ProfilePictureService,
    private readonly emailTemplateService: EmailTemplateService,
    private readonly profileLinkService: ProfileLinkService,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * Assign a tutor to a student for a specific module
   * @param assignTutorDto Assignment data
   * @returns Created assignment
   */
  async assignTutor(assignTutorDto: AssignTutorDto, skipNotifications: boolean = false): Promise<TutorAssignmentResponseDto> {
    try {
      // Validate student
      const student = await this.userRepository.findOne({ where: { id: assignTutorDto.studentId } });
      if (!student) {
        throw new NotFoundException(`Student with ID ${assignTutorDto.studentId} not found`);
      }
      if (student.type !== UserType.STUDENT) {
        throw new BadRequestException(`User with ID ${assignTutorDto.studentId} is not a student`);
      }

      // Validate tutor
      const tutor = await this.userRepository.findOne({
        where: { 
          id: assignTutorDto.tutorId,
          isActive: true,
          isConfirmed: true
        },
        relations: ['userRoles', 'userRoles.role'],
      });
      if (!tutor) {
        throw new NotFoundException(`Tutor with ID ${assignTutorDto.tutorId} not found or not active/confirmed`);
      }
      const isTutor = tutor.userRoles.some((userRole) => userRole.role.name === 'tutor');
      if (!isTutor) {
        throw new BadRequestException(`User with ID ${assignTutorDto.tutorId} is not a tutor`);
      }

      // Check tutor approval status
      await this.validateTutorApprovalStatus(assignTutorDto.tutorId);

      // Validate module feature
      const moduleFeature = await this.planFeatureRepository.findOne({
        where: { id: assignTutorDto.planFeatureId },
      });
      if (!moduleFeature) {
        throw new NotFoundException(`Module feature with ID ${assignTutorDto.planFeatureId} not found`);
      }

      // Check if student already has a tutor for this module
      const existingMapping = await this.studentTutorMappingRepository.findOne({
        where: {
          studentId: assignTutorDto.studentId,
          planFeatureId: assignTutorDto.planFeatureId,
          status: MappingStatus.ACTIVE,
        },
      });

      if (existingMapping) {
        throw new ConflictException(`Student already has an active tutor for this module`);
      }

      // Create mapping
      const mapping = this.studentTutorMappingRepository.create({
        studentId: assignTutorDto.studentId,
        tutorId: assignTutorDto.tutorId,
        planFeatureId: assignTutorDto.planFeatureId,
        status: MappingStatus.ACTIVE,
        assignedDate: getCurrentUTCDate(),
        notes: assignTutorDto.notes,
      });

      let savedMapping;
      try {
        savedMapping = await this.studentTutorMappingRepository.save(mapping);
      } catch (saveError) {
        if (saveError.code === '23505') { // Unique constraint violation
          this.logger.warn(`Duplicate assignment detected for student ${assignTutorDto.studentId} and module ${assignTutorDto.planFeatureId}`);
          throw new ConflictException(`Student already has an active tutor for this module`);
        }
        throw saveError;
      }

      // Create a chat conversation between student and tutor
      try {
        const conversation = await this.chatService.getOrCreateConversation(student.id, tutor.id);
        this.logger.log(`Created or retrieved chat conversation ${conversation.id} between student ${student.id} and tutor ${tutor.id}`);
      } catch (error) {
        // Log the error but don't fail the whole operation
        this.logger.error(`Error creating chat conversation: ${error.message}`, error.stack);
      }

      // Send notifications only if not skipped (for bulk assignments, notifications are handled separately)
      if (!skipNotifications) {
        // Note: Individual notifications are skipped here as consolidated notifications
        // are handled by the calling service (PlansService) to avoid duplicate emails
        this.logger.log(`Skipping individual notifications for assignment ${savedMapping.id} - will be handled by consolidated notification system`);
      }

      return this.mapToAssignmentResponseDto(savedMapping, student, tutor, moduleFeature);
    } catch (error) {
      this.logger.error(`Error assigning tutor: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update a tutor assignment
   * @param id Assignment ID
   * @param updateDto Update data
   * @returns Updated assignment
   */
  async updateAssignment(id: string, updateDto: UpdateTutorAssignmentDto): Promise<TutorAssignmentResponseDto> {
    try {
      // Find assignment
      const assignment = await this.studentTutorMappingRepository.findOne({
        where: { id },
        relations: ['student', 'tutor', 'planFeature'],
      });

      if (!assignment) {
        throw new NotFoundException(`Assignment with ID ${id} not found`);
      }

      // If changing tutor, validate new tutor
      if (updateDto.tutorId && updateDto.tutorId !== assignment.tutorId) {
        const newTutor = await this.userRepository.findOne({
          where: { 
            id: updateDto.tutorId,
            isActive: true,
            isConfirmed: true
          },
          relations: ['userRoles', 'userRoles.role'],
        });

        if (!newTutor) {
          throw new NotFoundException(`Tutor with ID ${updateDto.tutorId} not found or not active/confirmed`);
        }

        const isTutor = newTutor.userRoles.some((userRole) => userRole.role.name === 'tutor');
        if (!isTutor) {
          throw new BadRequestException(`User with ID ${updateDto.tutorId} is not a tutor`);
        }

        // Check tutor approval status
        await this.validateTutorApprovalStatus(updateDto.tutorId);

        assignment.tutorId = updateDto.tutorId;
        assignment.tutor = newTutor;

        // Send notification to student about tutor change with detailed message
        // Generate student notification
        const studentTitle = `Meet Your New Tutor for ${assignment.planFeature.name}`;
        const studentHtmlContent = this.emailTemplateService.generateStudentTutorAssignmentTemplate(
          newTutor.name,
          newTutor.id,
          this.profileLinkService.getProfileLink(newTutor.id, UserType.TUTOR),
          [assignment.planFeature.name]
        );

        await this.asyncNotificationHelper.notifyAsync(
          assignment.studentId,
          NotificationType.TUTOR_ASSIGNMENT,
          studentTitle,
          studentTitle, // Use title as fallback plain text
          {
            relatedEntityId: assignment.id,
            relatedEntityType: RelatedEntityType.STUDENT_TUTOR_MAPPING,
            sendEmail: true,
            sendPush: true,
            sendInApp: true,
            sendRealtime: false,
            htmlContent: studentHtmlContent
          },
        );

        // Send notification to new tutor
        const tutorTitle = `New Student Assignment: ${assignment.student.name}`;
        const tutorHtmlContent = this.emailTemplateService.generateTutorAssignmentTemplate(
          assignment.student.name,
          assignment.student.id,
          this.profileLinkService.getProfileLink(assignment.student.id, UserType.STUDENT),
          [assignment.planFeature.name]
        );

        await this.asyncNotificationHelper.notifyAsync(
          newTutor.id,
          NotificationType.TUTOR_ASSIGNMENT,
          tutorTitle,
          tutorTitle, // Use title as fallback plain text
          {
            relatedEntityId: assignment.id,
            relatedEntityType: RelatedEntityType.STUDENT_TUTOR_MAPPING,
            sendEmail: true,
            sendPush: true,
            sendInApp: true,
            sendRealtime: false,
            htmlContent: tutorHtmlContent
          },
        );
      }

      // Update status if provided
      if (updateDto.status) {
        assignment.status = updateDto.status;
      }

      // Update notes if provided
      if (updateDto.notes !== undefined) {
        assignment.notes = updateDto.notes;
      }

      const updatedAssignment = await this.studentTutorMappingRepository.save(assignment);

      return this.mapToAssignmentResponseDto(updatedAssignment, assignment.student, assignment.tutor, assignment.planFeature);
    } catch (error) {
      this.logger.error(`Error updating assignment: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get all tutor assignments with filtering and pagination
   * @param filterDto Filter criteria
   * @param paginationDto Pagination options
   * @returns Paged list of assignments
   */
  async getAllAssignments(filterDto?: AssignmentFilterDto, paginationDto?: PaginationDto): Promise<PagedListDto<TutorAssignmentResponseDto>> {
    try {
      // Build query
      let query = this.studentTutorMappingRepository
        .createQueryBuilder('mapping')
        .leftJoinAndSelect('mapping.student', 'student')
        .leftJoinAndSelect('mapping.tutor', 'tutor')
        .leftJoinAndSelect('mapping.planFeature', 'planFeature');

      // Apply filters
      if (filterDto) {
        if (filterDto.studentName) {
          query = query.andWhere('student.name ILIKE :studentName', {
            studentName: `%${filterDto.studentName}%`,
          });
        }

        if (filterDto.tutorName) {
          query = query.andWhere('tutor.name ILIKE :tutorName', {
            tutorName: `%${filterDto.tutorName}%`,
          });
        }

        if (filterDto.planFeatureId) {
          query = query.andWhere('mapping.planFeatureId = :planFeatureId', {
            planFeatureId: filterDto.planFeatureId,
          });
        }

        if (filterDto.status) {
          query = query.andWhere('mapping.status = :status', {
            status: filterDto.status,
          });
        }
      }

      // Get total count
      const totalCount = await query.getCount();

      // Apply pagination
      if (paginationDto) {
        const { page = 1, limit = 10, sortBy, sortDirection } = paginationDto;
        const skip = (page - 1) * limit;

        query = query.skip(skip).take(limit);

        if (sortBy && sortDirection) {
          query = query.orderBy(`mapping.${sortBy}`, sortDirection);
        } else {
          query = query.orderBy('mapping.createdAt', 'DESC');
        }
      } else {
        query = query.orderBy('mapping.createdAt', 'DESC');
      }

      // Execute query
      const assignments = await query.getMany();

      // Map to response DTOs
      const assignmentDtos = assignments.map((assignment) => this.mapToAssignmentResponseDto(assignment, assignment.student, assignment.tutor, assignment.planFeature));

      return new PagedListDto(assignmentDtos, totalCount);
    } catch (error) {
      this.logger.error(`Error getting assignments: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get a specific assignment by ID
   * @param id Assignment ID
   * @returns Assignment details
   */
  async getAssignmentById(id: string): Promise<TutorAssignmentResponseDto> {
    try {
      const assignment = await this.studentTutorMappingRepository.findOne({
        where: { id },
        relations: ['student', 'tutor', 'planFeature'],
      });

      if (!assignment) {
        throw new NotFoundException(`Assignment with ID ${id} not found`);
      }

      return this.mapToAssignmentResponseDto(assignment, assignment.student, assignment.tutor, assignment.planFeature);
    } catch (error) {
      this.logger.error(`Error getting assignment: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get the tutor assigned to a student for a specific module
   * @param studentId Student ID
   * @param moduleId Module ID (PlanFeature ID)
   * @returns Tutor assignment or null if none exists
   */
  async getStudentTutorForModule(studentId: string, moduleId: string): Promise<StudentTutorMapping | null> {
    try {
      if (!moduleId) {
        this.logger.warn(`No module ID provided for student ${studentId}`);
        return null;
      }

      return await this.studentTutorMappingRepository.findOne({
        where: {
          studentId,
          planFeatureId: moduleId,
          status: MappingStatus.ACTIVE,
        },
        relations: ['tutor', 'planFeature'],
      });
    } catch (error) {
      this.logger.error(`Error getting tutor for student ${studentId} and module ${moduleId}: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Get available tutors for a specific module
   * @param moduleId Module ID (PlanFeature ID)
   * @returns List of available tutors
   */
  async getAvailableTutorsForModule(moduleId: string): Promise<User[]> {
    try {
      if (!moduleId) {
        this.logger.warn('No module ID provided for getting available tutors');
        return [];
      }

      this.logger.log(`Finding available tutors for module ${moduleId}`);

      // Get all users with tutor role and approved status
      const tutors = await this.userRepository
        .createQueryBuilder('user')
        .innerJoin('user.userRoles', 'userRole')
        .innerJoin('userRole.role', 'role')
        .innerJoin('tutor_approval', 'approval', 'approval.user_id::uuid = user.id')
        .where('role.name = :roleName', { roleName: 'tutor' })
        .andWhere('user.isActive = :isActive', { isActive: true })
        .andWhere('user.isConfirmed = :isConfirmed', { isConfirmed: true })
        .andWhere('approval.status = :approvalStatus', { approvalStatus: TutorApprovalStatus.APPROVED })
        .getMany();

      this.logger.log(`Found ${tutors.length} active tutors`);

      // Filter tutors by workload (in a real implementation, you might want to check their current assignments)
      // For now, just return all active tutors
      return tutors;
    } catch (error) {
      this.logger.error(`Error getting available tutors for module ${moduleId}: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * Get all tutors assigned to a student with filtering and pagination
   * @param studentId Student ID
   * @param filterDto Filter criteria
   * @param paginationDto Pagination options
   * @returns Paged list of tutors
   */
  async getStudentTutors(studentId: string, filterDto?: StudentTutorFilterDto, paginationDto?: PaginationDto): Promise<PagedListDto<StudentTutorDto>> {
    try {
      // Check if student exists
      const student = await this.userRepository.findOne({ where: { id: studentId } });
      if (!student) {
        throw new NotFoundException(`Student with ID ${studentId} not found`);
      }

      // Build query
      let query = this.studentTutorMappingRepository
        .createQueryBuilder('mapping')
        .leftJoinAndSelect('mapping.tutor', 'tutor')
        .leftJoinAndSelect('mapping.planFeature', 'planFeature')
        .where('mapping.studentId = :studentId', { studentId });

      // Apply filters
      if (filterDto) {
        if (filterDto.tutorName) {
          query = query.andWhere('tutor.name ILIKE :tutorName', {
            tutorName: `%${filterDto.tutorName}%`,
          });
        }

        if (filterDto.planFeatureId) {
          query = query.andWhere('mapping.planFeatureId = :planFeatureId', {
            planFeatureId: filterDto.planFeatureId,
          });
        }

        if (filterDto.status) {
          query = query.andWhere('mapping.status = :status', {
            status: filterDto.status,
          });
        }

        if (filterDto.planFeatureType) {
          query = query.andWhere('planFeature.type = :planFeatureType', {
            planFeatureType: filterDto.planFeatureType,
          });
        }
      }

      // Get total count
      const totalCount = await query.getCount();

      // Apply pagination
      if (paginationDto) {
        const { page = 1, limit = 10, sortBy, sortDirection } = paginationDto;
        const skip = (page - 1) * limit;

        query = query.skip(skip).take(limit);

        if (sortBy && sortDirection) {
          if (sortBy === 'name') {
            query = query.orderBy('tutor.name', sortDirection);
          } else if (sortBy === 'moduleName') {
            query = query.orderBy('planFeature.name', sortDirection);
          } else {
            query = query.orderBy(`mapping.${sortBy}`, sortDirection);
          }
        } else {
          query = query.orderBy('mapping.assignedDate', 'DESC');
        }
      } else {
        query = query.orderBy('mapping.assignedDate', 'DESC');
      }

      // Execute query
      const assignments = await query.getMany();

      // Map to DTOs
      const tutorDtos = assignments.map((assignment) => ({
        id: assignment.tutorId,
        name: assignment.tutor.name,
        email: assignment.tutor.email,
        profilePicture: assignment.tutor.profilePicture,
        bio: assignment.tutor.bio,
        planFeatureId: assignment.planFeatureId,
        moduleName: assignment.planFeature.name,
        moduleType: assignment.planFeature.type,
        status: assignment.status,
        assignedDate: assignment.assignedDate,
      }));

      return new PagedListDto<StudentTutorDto>(tutorDtos, totalCount);
    } catch (error) {
      this.logger.error(`Error getting student tutors: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Check if a tutor has access to a specific student
   * @param tutorId Tutor ID
   * @param studentId Student ID
   * @returns True if tutor has access to student, false otherwise
   */
  async hasTutorStudentAccess(tutorId: string, studentId: string): Promise<boolean> {
    try {
      const mapping = await this.studentTutorMappingRepository.findOne({
        where: {
          tutorId,
          studentId,
          status: MappingStatus.ACTIVE,
        },
      });
      return !!mapping;
    } catch (error) {
      this.logger.error(`Error checking tutor-student access for tutor ${tutorId} and student ${studentId}: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Get all students assigned to a tutor
   * @param tutorId Tutor ID
   * @returns List of students
   */
  async getTutorStudents(tutorId: string, planFeatureId?: string): Promise<TutorStudentDto[]> {
    try {
      // Check if tutor exists
      const tutor = await this.userRepository.findOne({ where: { id: tutorId } });
      if (!tutor) {
        throw new NotFoundException(`Tutor with ID ${tutorId} not found`);
      }
      if (tutor.type !== UserType.TUTOR) {
        throw new BadRequestException(`User with ID ${tutorId} is not a tutor`);
      }

      const where = {};
      if (planFeatureId) {
        where['planFeatureId'] = planFeatureId;
      }
      // Get assignments
      const assignments = await this.studentTutorMappingRepository.find({
        where: { ...where, tutorId: tutorId, status: MappingStatus.ACTIVE },
        relations: ['student', 'planFeature'],
      });

      // Map to DTOs
      return assignments.map((assignment) => ({
        id: assignment.studentId,
        name: assignment.student.name,
        email: assignment.student.email,
        profilePicture: assignment.student.profilePicture,
        planFeatureId: assignment.planFeatureId,
        moduleName: assignment.planFeature.name,
        moduleType: assignment.planFeature.type,
        status: assignment.status,
        assignedDate: assignment.assignedDate,
        lastActivityDate: assignment.lastActivityDate,
      }));
    } catch (error) {
      this.logger.error(`Error getting tutor students: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get all students assigned to a tutor with pagination and flattened by user
   * @param tutorId Tutor ID
   * @param paginationDto Pagination parameters
   * @returns Paginated list of students grouped by user with modules
   */
  async getTutorStudentsFlattened(tutorId: string, paginationDto: PaginationDto): Promise<PagedListDto<TutorStudentFlattenedDto>> {
    try {
      // Check if tutor exists
      const tutor = await this.userRepository.findOne({ where: { id: tutorId } });
      if (!tutor) {
        throw new NotFoundException(`Tutor with ID ${tutorId} not found`);
      }
      if (tutor.type !== UserType.TUTOR) {
        throw new BadRequestException(`User with ID ${tutorId} is not a tutor`);
      }

      // Set default pagination values
      const page = paginationDto.page || 1;
      const limit = paginationDto.limit || 10;

      // First, get all assignments to group by student (only for students with active plans)
      const allAssignments = await this.studentTutorMappingRepository
        .createQueryBuilder('mapping')
        .leftJoinAndSelect('mapping.student', 'student')
        .leftJoinAndSelect('mapping.planFeature', 'planFeature')
        .leftJoin('student.userPlans', 'userPlan')
        .leftJoin('userPlan.plan', 'plan')
        .where('mapping.tutorId = :tutorId', { tutorId })
        .andWhere('mapping.status = :status', { status: MappingStatus.ACTIVE })
        .andWhere('student.type = :userType', { userType: UserType.STUDENT })
        .andWhere('userPlan.isActive = :isActive', { isActive: true })
        .andWhere('plan.isActive = :planActive', { planActive: true })
        .getMany();

      // Group assignments by student
      const studentMap = new Map<string, TutorStudentFlattenedDto>();

      allAssignments.forEach((assignment) => {
        const studentId = assignment.studentId;

        if (!studentMap.has(studentId)) {
          studentMap.set(studentId, {
            id: assignment.studentId,
            name: assignment.student.name,
            email: assignment.student.email,
            profilePicture: assignment.student.profilePicture,
            modules: [],
          });
        }

        const student = studentMap.get(studentId);
        student.modules.push({
          planFeatureId: assignment.planFeatureId,
          moduleName: assignment.planFeature.name,
          moduleType: assignment.planFeature.type,
          status: assignment.status,
          assignedDate: assignment.assignedDate,
          lastActivityDate: assignment.lastActivityDate,
        });
      });

      // Convert map to array and apply pagination to students
      const allStudents = Array.from(studentMap.values());
      const totalCount = allStudents.length;
      const skip = (page - 1) * limit;
      const students = allStudents.slice(skip, skip + limit);

      // Return PagedListDto using constructor
      return new PagedListDto(students, totalCount, page, limit);
    } catch (error) {
      this.logger.error(`Error getting tutor students flattened: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get tutor workload statistics
   * @returns List of tutors with workload stats
   */
  async getTutorWorkloads(): Promise<TutorWorkloadDto[]> {
    try {
      // Get all tutors
      const tutors = await this.userRepository
        .createQueryBuilder('user')
        .leftJoinAndSelect('user.userRoles', 'userRoles')
        .leftJoinAndSelect('userRoles.role', 'role')
        .where('role.name = :roleName', { roleName: 'tutor' })
        .getMany();

      // Get workload for each tutor
      const workloads = await Promise.all(
        tutors.map(async (tutor) => {
          // Count active students
          const activeStudentCount = await this.studentTutorMappingRepository.count({
            where: {
              tutorId: tutor.id,
              status: MappingStatus.ACTIVE,
            },
          });

          // Count pending reviews
          const pendingReviewCount = await this.diaryEntryRepository.count({
            where: {
              status: DiaryEntryStatus.SUBMIT,
              diary: {
                user: {
                  id: In(
                    await this.studentTutorMappingRepository
                      .createQueryBuilder('mapping')
                      .select('mapping.studentId')
                      .where('mapping.tutorId = :tutorId', { tutorId: tutor.id })
                      .andWhere('mapping.status = :status', { status: MappingStatus.ACTIVE })
                      .getMany()
                      .then((mappings) => mappings.map((m) => m.studentId)),
                  ),
                },
              },
            },
          });

          // Get last activity date
          const lastMapping = await this.studentTutorMappingRepository.findOne({
            where: { tutorId: tutor.id },
            order: { lastActivityDate: 'DESC' },
          });

          return {
            id: tutor.id,
            name: tutor.name,
            activeStudentCount,
            pendingReviewCount,
            lastActivityDate: lastMapping?.lastActivityDate,
          };
        }),
      );

      return workloads;
    } catch (error) {
      this.logger.error(`Error getting tutor workloads: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Auto-assign tutors to students based on workload balancing
   * @param autoAssignDto Auto-assignment parameters
   * @returns List of created assignments
   */
  async autoAssignTutors(autoAssignDto: AutoAssignTutorsDto): Promise<TutorAssignmentResponseDto[]> {
    try {
      // Validate module feature
      const moduleFeature = await this.planFeatureRepository.findOne({
        where: { id: autoAssignDto.planFeatureId, type: FeatureType.MODULE },
      });
      if (!moduleFeature) {
        throw new NotFoundException(`Module feature with ID ${autoAssignDto.planFeatureId} not found`);
      }

      // Filter students who need assignment
      const studentsToAssign = [];
      for (const studentId of autoAssignDto.studentIds) {
        // Check if student exists
        const student = await this.userRepository.findOne({ where: { id: studentId } });
        if (!student) {
          this.logger.warn(`Student with ID ${studentId} not found, skipping`);
          continue;
        }
        if (student.type !== UserType.STUDENT) {
          this.logger.warn(`User with ID ${studentId} is not a student, skipping`);
          continue;
        }

        // Check if student already has a tutor for this module
        const existingMapping = await this.studentTutorMappingRepository.findOne({
          where: {
            studentId,
            planFeatureId: autoAssignDto.planFeatureId,
            status: MappingStatus.ACTIVE,
          },
        });

        if (existingMapping && !autoAssignDto.reassignExisting) {
          this.logger.warn(`Student ${studentId} already has a tutor for this module, skipping`);
          continue;
        }

        studentsToAssign.push(student);
      }

      if (studentsToAssign.length === 0) {
        throw new BadRequestException('No students available for assignment');
      }

      // Assign tutors to each student
      const assignments = [];

      for (const student of studentsToAssign) {
        try {
          // Get the student's assigned tutor (ONE TUTOR PER STUDENT policy)
          let tutor: User;
          try {
            tutor = await this.getOrSelectPreferredTutor(student.id);
            this.logger.log(`Using assigned tutor ${tutor.name} (${tutor.id}) for student ${student.id} in module ${moduleFeature.name} (ONE TUTOR PER STUDENT policy)`);
          } catch (tutorError) {
            // Fallback to module-specific selection if tutor selection fails
            this.logger.warn(`Failed to get assigned tutor for student ${student.id}, falling back to module-specific selection: ${tutorError.message}`);
            tutor = await this.selectTutorForModule(autoAssignDto.planFeatureId);
            this.logger.log(`Using fallback tutor ${tutor.name} (${tutor.id}) for student ${student.id} in module ${moduleFeature.name}`);
          }

          // Create assignment
          const mapping = this.studentTutorMappingRepository.create({
            studentId: student.id,
            tutorId: tutor.id,
            planFeatureId: autoAssignDto.planFeatureId,
            status: MappingStatus.ACTIVE,
            assignedDate: getCurrentUTCDate(),
            notes: 'Auto-assigned by system with preferred tutor logic',
          });

          let savedMapping;
          try {
            savedMapping = await this.studentTutorMappingRepository.save(mapping);
          } catch (saveError) {
            if (saveError.code === '23505') { // Unique constraint violation
              this.logger.warn(`Duplicate assignment detected for student ${student.id} and module ${autoAssignDto.planFeatureId}, skipping`);
              continue;
            }
            throw saveError;
          }
          
          this.logger.log(`Successfully created assignment ${savedMapping.id} for student ${student.id} with tutor ${tutor.name}`);

          // Create a chat conversation between student and tutor
          try {
            const conversation = await this.chatService.getOrCreateConversation(student.id, tutor.id);
            this.logger.log(`Created or retrieved chat conversation ${conversation.id} between student ${student.id} and tutor ${tutor.id}`);
          } catch (error) {
            // Log the error but don't fail the whole operation
            this.logger.error(`Error creating chat conversation: ${error.message}`, error.stack);
          }

          // Note: Notifications are handled by the calling service (PlansService)
          // to send consolidated notifications instead of individual ones per module

          // Add to result
          assignments.push(this.mapToAssignmentResponseDto(savedMapping, student, tutor, moduleFeature));
        } catch (studentError) {
          this.logger.error(`Failed to assign tutor for student ${student.id} in module ${moduleFeature.name}: ${studentError.message}`, studentError.stack);
          // Continue with other students - don't fail the entire batch
        }
      }

      return assignments;
    } catch (error) {
      this.logger.error(`Error auto-assigning tutors: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Select the most appropriate tutor for a module with preference for different tutors
   * @param moduleId Module ID to assign tutor for
   * @param excludeTutorIds Tutor IDs to avoid (prefer different tutors)
   * @returns Selected tutor with the lowest workload, preferring non-excluded tutors
   */
  async selectTutorForModuleWithPreference(moduleId: string, excludeTutorIds: string[] = []): Promise<User> {
    // Get all active, confirmed and approved tutors
    const tutors = await this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.userRoles', 'userRoles')
      .leftJoinAndSelect('userRoles.role', 'role')
      .innerJoin('tutor_approval', 'approval', 'approval.user_id::uuid = user.id')
      .where('role.name = :roleName', { roleName: 'tutor' })
      .andWhere('user.isActive = :isActive', { isActive: true })
      .andWhere('user.isConfirmed = :isConfirmed', { isConfirmed: true })
      .andWhere('approval.status = :approvalStatus', { approvalStatus: TutorApprovalStatus.APPROVED })
      .getMany();

    this.logger.log(`Found ${tutors.length} active and confirmed tutors for module ${moduleId} with preference logic`);

    if (tutors.length === 0) {
      this.logger.error(`No active and confirmed tutors available for assignment to module ${moduleId}`);
      throw new BadRequestException('No active and confirmed tutors available for assignment');
    }

    // Get tutor workloads
    const tutorWorkloads = await Promise.all(
      tutors.map(async (tutor) => {
        // Count active students for this tutor
        const activeStudentCount = await this.studentTutorMappingRepository.count({
          where: {
            tutorId: tutor.id,
            status: MappingStatus.ACTIVE,
          },
        });

        // Count active students for this tutor in this specific module
        const moduleStudentCount = await this.studentTutorMappingRepository.count({
          where: {
            tutorId: tutor.id,
            planFeatureId: moduleId,
            status: MappingStatus.ACTIVE,
          },
        });

        const isExcluded = excludeTutorIds.includes(tutor.id);

        return {
          tutor,
          activeStudentCount,
          moduleStudentCount,
          isExcluded,
        };
      }),
    );

    // Sort tutors with preference logic:
    // 1. Prefer non-excluded tutors
    // 2. Then by module-specific workload
    // 3. Then by overall workload
    tutorWorkloads.sort((a, b) => {
      // First, prefer non-excluded tutors
      if (a.isExcluded !== b.isExcluded) {
        return a.isExcluded ? 1 : -1; // Non-excluded tutors come first
      }

      // If both are excluded or both are not excluded, compare workloads
      // First compare module-specific workload
      if (a.moduleStudentCount !== b.moduleStudentCount) {
        return a.moduleStudentCount - b.moduleStudentCount;
      }
      // If module workloads are equal, compare overall workload
      return a.activeStudentCount - b.activeStudentCount;
    });

    const selectedTutor = tutorWorkloads[0];

    if (selectedTutor.isExcluded && excludeTutorIds.length > 0) {
      this.logger.warn(`All preferred tutors are unavailable, assigning excluded tutor ${selectedTutor.tutor.name} for module ${moduleId}`);
    } else if (!selectedTutor.isExcluded && excludeTutorIds.length > 0) {
      this.logger.log(`Successfully assigned different tutor ${selectedTutor.tutor.name} for module ${moduleId}, avoiding [${excludeTutorIds.join(', ')}]`);
    }

    // Return the tutor with the best preference and lowest workload
    return selectedTutor.tutor;
  }

  /**
   * Get all active tutor assignments for a student
   * @param studentId Student ID
   * @returns List of active tutor assignments
   */
  async getStudentTutorAssignments(studentId: string): Promise<StudentTutorMapping[]> {
    try {
      return await this.studentTutorMappingRepository.find({
        where: {
          studentId,
          status: MappingStatus.ACTIVE,
        },
        relations: ['tutor', 'planFeature'],
        order: { assignedDate: 'ASC' }, // Oldest assignment first (preferred tutor)
      });
    } catch (error) {
      this.logger.error(`Error getting student tutor assignments for ${studentId}: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * Get or select a preferred tutor for a student using ONE TUTOR PER STUDENT policy
   * If student has existing tutors, returns the assigned tutor (should be same for all modules)
   * If no existing tutors, selects a new tutor based on workload
   * @param studentId Student ID
   * @returns The assigned tutor for the student
   */
  async getOrSelectPreferredTutor(studentId: string): Promise<User> {
    try {
      // First, check if student already has any tutor assignments (ONE TUTOR PER STUDENT policy)
      const existingAssignments = await this.getStudentTutorAssignments(studentId);

      if (existingAssignments.length > 0) {
        // Return the student's assigned tutor (should be the same for all modules)
        const assignedTutor = existingAssignments[0].tutor;
        this.logger.log(`Found existing assigned tutor ${assignedTutor.name} (${assignedTutor.id}) for student ${studentId}`);

        // Verify consistency - all assignments should use the same tutor
        const uniqueTutorIds = [...new Set(existingAssignments.map(a => a.tutorId))];
        if (uniqueTutorIds.length > 1) {
          this.logger.warn(`INCONSISTENCY DETECTED: Student ${studentId} has multiple tutors: [${uniqueTutorIds.join(', ')}]. Using first tutor: ${assignedTutor.id}`);
        }

        return assignedTutor;
      }

      // No existing assignments, select a new tutor based on overall workload
      this.logger.log(`No existing tutors for student ${studentId}, selecting new tutor using ONE TUTOR PER STUDENT policy`);
      return await this.selectTutorByOverallWorkload();
    } catch (error) {
      this.logger.error(`Error getting or selecting preferred tutor for student ${studentId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Select a tutor based on overall workload (not module-specific)
   * Used for selecting a preferred tutor for new students
   * @returns Selected tutor with the lowest overall workload
   */
  async selectTutorByOverallWorkload(): Promise<User> {
    // Get all active, confirmed and approved tutors
    const tutors = await this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.userRoles', 'userRoles')
      .leftJoinAndSelect('userRoles.role', 'role')
      .innerJoin('tutor_approval', 'approval', 'approval.user_id::uuid = user.id')
      .where('role.name = :roleName', { roleName: 'tutor' })
      .andWhere('user.isActive = :isActive', { isActive: true })
      .andWhere('user.isConfirmed = :isConfirmed', { isConfirmed: true })
      .andWhere('approval.status = :approvalStatus', { approvalStatus: TutorApprovalStatus.APPROVED })
      .getMany();

    if (tutors.length === 0) {
      throw new NotFoundException('No active tutors available');
    }

    // Calculate overall workload for each tutor
    const tutorWorkloads = [];
    for (const tutor of tutors) {
      const activeStudentCount = await this.studentTutorMappingRepository.count({
        where: {
          tutorId: tutor.id,
          status: MappingStatus.ACTIVE,
        },
      });

      tutorWorkloads.push({
        tutor,
        activeStudentCount,
      });
    }

    // Sort tutors by overall workload
    tutorWorkloads.sort((a, b) => a.activeStudentCount - b.activeStudentCount);

    // Return the tutor with the lowest overall workload
    return tutorWorkloads[0].tutor;
  }

  /**
   * Select the most appropriate tutor for a module based on workload balancing
   * @param moduleId Module ID to assign tutor for
   * @returns Selected tutor with the lowest workload
   */
  async selectTutorForModule(moduleId: string): Promise<User> {
    // Get all active, confirmed and approved tutors
    const tutors = await this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.userRoles', 'userRoles')
      .leftJoinAndSelect('userRoles.role', 'role')
      .innerJoin('tutor_approval', 'approval', 'approval.user_id::uuid = user.id')
      .where('role.name = :roleName', { roleName: 'tutor' })
      .andWhere('user.isActive = :isActive', { isActive: true })
      .andWhere('user.isConfirmed = :isConfirmed', { isConfirmed: true })
      .andWhere('approval.status = :approvalStatus', { approvalStatus: TutorApprovalStatus.APPROVED })
      .getMany();

    this.logger.log(`Found ${tutors.length} active and confirmed tutors for module ${moduleId}`);

    if (tutors.length === 0) {
      this.logger.error(`No active and confirmed tutors available for assignment to module ${moduleId}`);
      throw new BadRequestException('No active and confirmed tutors available for assignment');
    }

    // Get tutor workloads
    const tutorWorkloads = await Promise.all(
      tutors.map(async (tutor) => {
        // Count active students for this tutor
        const activeStudentCount = await this.studentTutorMappingRepository.count({
          where: {
            tutorId: tutor.id,
            status: MappingStatus.ACTIVE,
          },
        });

        // Count active students for this tutor in this specific module
        const moduleStudentCount = await this.studentTutorMappingRepository.count({
          where: {
            tutorId: tutor.id,
            planFeatureId: moduleId,
            status: MappingStatus.ACTIVE,
          },
        });

        return {
          tutor,
          activeStudentCount,
          moduleStudentCount,
        };
      }),
    );

    // Sort tutors by module-specific workload first, then by overall workload
    tutorWorkloads.sort((a, b) => {
      // First compare module-specific workload
      if (a.moduleStudentCount !== b.moduleStudentCount) {
        return a.moduleStudentCount - b.moduleStudentCount;
      }
      // If module workloads are equal, compare overall workload
      return a.activeStudentCount - b.activeStudentCount;
    });

    // Return the tutor with the lowest workload
    return tutorWorkloads[0].tutor;
  }

  /**
   * Auto-assign tutors to students with preference for different tutors (for plan upgrades)
   * @param autoAssignDto Auto-assignment parameters with excludeTutorIds
   * @returns List of created assignments
   */
  async autoAssignTutorsWithPreference(autoAssignDto: AutoAssignTutorsDto & { excludeTutorIds?: string[] }): Promise<TutorAssignmentResponseDto[]> {
    try {
      // Validate module feature
      const moduleFeature = await this.planFeatureRepository.findOne({
        where: { id: autoAssignDto.planFeatureId },
      });
      if (!moduleFeature) {
        throw new NotFoundException(`Module feature with ID ${autoAssignDto.planFeatureId} not found`);
      }

      this.logger.log(
        `Starting tutor assignment with preference for module ${moduleFeature.name} (${autoAssignDto.planFeatureId}), excluding tutors: [${(autoAssignDto.excludeTutorIds || []).join(', ')}]`,
      );

      // Filter students who need assignment
      const studentsToAssign = [];
      for (const studentId of autoAssignDto.studentIds) {
        // Check if student exists
        const student = await this.userRepository.findOne({ where: { id: studentId } });
        if (!student) {
          this.logger.warn(`Student with ID ${studentId} not found, skipping`);
          continue;
        }
        if (student.type !== UserType.STUDENT) {
          this.logger.warn(`User with ID ${studentId} is not a student, skipping`);
          continue;
        }

        // Check if student already has a tutor for this module
        const existingMapping = await this.studentTutorMappingRepository.findOne({
          where: {
            studentId,
            planFeatureId: autoAssignDto.planFeatureId,
            status: MappingStatus.ACTIVE,
          },
        });

        if (existingMapping && !autoAssignDto.reassignExisting) {
          this.logger.warn(`Student ${studentId} already has a tutor for this module, skipping`);
          continue;
        }

        studentsToAssign.push(student);
      }

      if (studentsToAssign.length === 0) {
        throw new BadRequestException('No students available for assignment');
      }

      // Assign tutors to each student
      const assignments = [];

      for (const student of studentsToAssign) {
        try {
          // Get the student's assigned tutor (ONE TUTOR PER STUDENT policy), but respect excludeTutorIds
          let tutor: User;
          try {
            const assignedTutor = await this.getOrSelectPreferredTutor(student.id);
            const excludeTutorIds = autoAssignDto.excludeTutorIds || [];

            // Check if assigned tutor is in the exclude list
            if (excludeTutorIds.includes(assignedTutor.id)) {
              this.logger.log(`Assigned tutor ${assignedTutor.name} (${assignedTutor.id}) is excluded for student ${student.id}, selecting alternative`);
              tutor = await this.selectTutorForModuleWithPreference(autoAssignDto.planFeatureId, excludeTutorIds);
            } else {
              tutor = assignedTutor;
              this.logger.log(`Using assigned tutor ${tutor.name} (${tutor.id}) for student ${student.id} in module ${moduleFeature.name} (ONE TUTOR PER STUDENT policy)`);
            }
          } catch (tutorError) {
            // Fallback to preference-based selection if tutor selection fails
            this.logger.warn(`Failed to get assigned tutor for student ${student.id}, falling back to preference-based selection: ${tutorError.message}`);
            tutor = await this.selectTutorForModuleWithPreference(autoAssignDto.planFeatureId, autoAssignDto.excludeTutorIds || []);
          }

          this.logger.log(`Selected tutor ${tutor.name} (${tutor.id}) for student ${student.id} in module ${moduleFeature.name}`);

          // Double-check for existing assignment to prevent race conditions
          const existingAssignment = await this.studentTutorMappingRepository.findOne({
            where: {
              studentId: student.id,
              planFeatureId: autoAssignDto.planFeatureId,
              status: MappingStatus.ACTIVE,
            },
          });

          if (existingAssignment) {
            this.logger.warn(`Race condition detected: Student ${student.id} already has assignment for module ${autoAssignDto.planFeatureId}, skipping`);
            continue;
          }

          // Create assignment
          const mapping = this.studentTutorMappingRepository.create({
            studentId: student.id,
            tutorId: tutor.id,
            planFeatureId: autoAssignDto.planFeatureId,
            status: MappingStatus.ACTIVE,
            assignedDate: getCurrentUTCDate(),
            notes: 'Auto-assigned by system with preference logic',
          });

          let savedMapping;
          try {
            savedMapping = await this.studentTutorMappingRepository.save(mapping);
          } catch (saveError) {
            if (saveError.code === '23505') { // Unique constraint violation
              this.logger.warn(`Duplicate assignment detected for student ${student.id} and module ${autoAssignDto.planFeatureId}, skipping`);
              continue;
            }
            throw saveError;
          }
          
          this.logger.log(`Successfully created assignment ${savedMapping.id} for student ${student.id} with tutor ${tutor.name}`);

          // Create a chat conversation between student and tutor
          try {
            const conversation = await this.chatService.getOrCreateConversation(student.id, tutor.id);
            this.logger.log(`Created or retrieved chat conversation ${conversation.id} between student ${student.id} and tutor ${tutor.id}`);
          } catch (error) {
            // Log the error but don't fail the whole operation
            this.logger.error(`Error creating chat conversation: ${error.message}`, error.stack);
          }

          // Add to result (no notifications sent here)
          assignments.push(this.mapToAssignmentResponseDto(savedMapping, student, tutor, moduleFeature));
        } catch (studentError) {
          this.logger.error(`Failed to assign tutor for student ${student.id} in module ${moduleFeature.name}: ${studentError.message}`, studentError.stack);
          // Continue with other students - don't fail the entire batch
        }
      }

      return assignments;
    } catch (error) {
      this.logger.error(`Error auto-assigning tutors with preference: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Auto-assign tutors to students based on workload balancing without sending notifications
   * This is used when we want to collect all assignments first and then send consolidated notifications
   * @param autoAssignDto Auto-assignment parameters
   * @returns List of created assignments
   */
  async autoAssignTutorsWithoutNotifications(autoAssignDto: AutoAssignTutorsDto): Promise<TutorAssignmentResponseDto[]> {
    try {
      // Validate module feature
      const moduleFeature = await this.planFeatureRepository.findOne({
        where: { id: autoAssignDto.planFeatureId },
      });
      if (!moduleFeature) {
        throw new NotFoundException(`Module feature with ID ${autoAssignDto.planFeatureId} not found`);
      }

      // Filter students who need assignment
      const studentsToAssign = [];
      for (const studentId of autoAssignDto.studentIds) {
        // Check if student exists
        const student = await this.userRepository.findOne({ where: { id: studentId } });
        if (!student) {
          this.logger.warn(`Student with ID ${studentId} not found, skipping`);
          continue;
        }
        if (student.type !== UserType.STUDENT) {
          this.logger.warn(`User with ID ${studentId} is not a student, skipping`);
          continue;
        }

        // Check if student already has a tutor for this module
        const existingMapping = await this.studentTutorMappingRepository.findOne({
          where: {
            studentId,
            planFeatureId: autoAssignDto.planFeatureId,
            status: MappingStatus.ACTIVE,
          },
        });

        if (existingMapping && !autoAssignDto.reassignExisting) {
          this.logger.warn(`Student ${studentId} already has a tutor for this module, skipping`);
          continue;
        }

        studentsToAssign.push(student);
      }

      if (studentsToAssign.length === 0) {
        throw new BadRequestException('No students available for assignment');
      }

      // Assign tutors to each student
      const assignments = [];

      for (const student of studentsToAssign) {
        try {
          // Get the student's assigned tutor (ONE TUTOR PER STUDENT policy)
          let tutor: User;
          try {
            tutor = await this.getOrSelectPreferredTutor(student.id);
            this.logger.log(`Using assigned tutor ${tutor.name} (${tutor.id}) for student ${student.id} in module ${moduleFeature.name} (ONE TUTOR PER STUDENT policy)`);
          } catch (tutorError) {
            // Fallback to module-specific selection if tutor selection fails
            this.logger.warn(`Failed to get assigned tutor for student ${student.id}, falling back to module-specific selection: ${tutorError.message}`);
            tutor = await this.selectTutorForModule(autoAssignDto.planFeatureId);
            this.logger.log(`Using fallback tutor ${tutor.name} (${tutor.id}) for student ${student.id} in module ${moduleFeature.name}`);
          }

          // Create assignment
          const mapping = this.studentTutorMappingRepository.create({
            studentId: student.id,
            tutorId: tutor.id,
            planFeatureId: autoAssignDto.planFeatureId,
            status: MappingStatus.ACTIVE,
            assignedDate: getCurrentUTCDate(),
            notes: 'Auto-assigned by system with preferred tutor logic',
          });

          let savedMapping;
          try {
            savedMapping = await this.studentTutorMappingRepository.save(mapping);
          } catch (saveError) {
            if (saveError.code === '23505') { // Unique constraint violation
              this.logger.warn(`Duplicate assignment detected for student ${student.id} and module ${autoAssignDto.planFeatureId}, skipping`);
              continue;
            }
            throw saveError;
          }
          
          this.logger.log(`Successfully created assignment ${savedMapping.id} for student ${student.id} with tutor ${tutor.name}`);

          // Create a chat conversation between student and tutor
          try {
            const conversation = await this.chatService.getOrCreateConversation(student.id, tutor.id);
            this.logger.log(`Created or retrieved chat conversation ${conversation.id} between student ${student.id} and tutor ${tutor.id}`);
          } catch (error) {
            // Log the error but don't fail the whole operation
            this.logger.error(`Error creating chat conversation: ${error.message}`, error.stack);
          }

          // Add to result
          assignments.push(this.mapToAssignmentResponseDto(savedMapping, student, tutor, moduleFeature));
        } catch (studentError) {
          this.logger.error(`Failed to assign tutor for student ${student.id} in module ${moduleFeature.name}: ${studentError.message}`, studentError.stack);
          // Continue with other students - don't fail the entire batch
        }
      }

      return assignments;
    } catch (error) {
      this.logger.error(`Error auto-assigning tutors without notifications: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update the last activity date for a student-tutor mapping
   * @param studentId Student ID
   * @param moduleId Module ID
   * @returns Updated mapping
   */
  async updateLastActivityDate(studentId: string, moduleId: string): Promise<void> {
    try {
      const mapping = await this.studentTutorMappingRepository.findOne({
        where: {
          studentId,
          planFeatureId: moduleId,
          status: MappingStatus.ACTIVE,
        },
      });

      if (mapping) {
        mapping.lastActivityDate = getCurrentUTCDate();
        await this.studentTutorMappingRepository.save(mapping);
      }
    } catch (error) {
      this.logger.error(`Error updating last activity date: ${error.message}`, error.stack);
      // Don't throw error, just log it
    }
  }

  /**
   * Get or create conversation with a student
   * @param tutorId Current tutor ID
   * @param studentId Student user ID
   * @returns Conversation data with student information
   */
  async getConversationWithStudent(tutorId: string, studentId: string): Promise<ConversationByFriendResponseDto> {
    try {
      // Validate that both users exist and have correct types
      const [tutor, student] = await Promise.all([
        this.userRepository.findOne({
          where: { id: tutorId, type: UserType.TUTOR },
          select: ['id', 'name', 'profilePicture', 'type'],
        }),
        this.userRepository.findOne({
          where: { id: studentId, type: UserType.STUDENT },
          select: ['id', 'name', 'profilePicture', 'type'],
        }),
      ]);

      if (!tutor) {
        throw new NotFoundException('Tutor not found');
      }

      if (!student) {
        throw new NotFoundException('Student not found');
      }

      // Verify that there's an active mapping between tutor and student
      const mapping = await this.studentTutorMappingRepository.findOne({
        where: {
          tutorId,
          studentId,
          status: MappingStatus.ACTIVE,
        },
      });

      if (!mapping) {
        throw new NotFoundException('Student is not assigned to this tutor or mapping is not active');
      }

      // Get student's profile picture
      let studentProfilePicture = null;
      if (student.id) {
        studentProfilePicture = await this.profilePictureService.getProfilePictureUrl(student.id);
      }

      // Check if conversation already exists
      let conversation: Conversation;
      let isNewConversation = false;

      try {
        // First check if conversation already exists
        const existingConversation = await this.conversationRepository.findOne({
          where: [
            { participant1Id: tutorId, participant2Id: studentId },
            { participant1Id: studentId, participant2Id: tutorId },
          ],
        });

        if (existingConversation) {
          conversation = existingConversation;
          isNewConversation = false;
          this.logger.log(`Found existing conversation ${conversation.id} between tutor ${tutorId} and student ${studentId}`);
        } else {
          // Create new conversation using ChatService which handles all validation
          conversation = await this.chatService.getOrCreateConversation(tutorId, studentId);
          isNewConversation = true;
          this.logger.log(`Created new conversation ${conversation.id} between tutor ${tutorId} and student ${studentId}`);
        }
      } catch (error) {
        this.logger.error(`Error getting conversation between tutor ${tutorId} and student ${studentId}: ${error.message}`, error.stack);
        throw error; // Re-throw the original error from ChatService which has better error messages
      }

      return {
        conversationId: conversation.id,
        friendId: student.id,
        friendName: student.name,
        friendProfilePicture: studentProfilePicture,
        isNewConversation,
      };
    } catch (error) {
      this.logger.error(`Error getting conversation with student: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Change a student's tutor for all modules in their active plan (Admin only)
   * This implements the ONE TUTOR PER STUDENT policy by changing the tutor for all modules
   * @param changeDto Change tutor request data
   * @returns Details of the tutor change
   */
  async changeStudentTutor(changeDto: ChangeStudentTutorDto): Promise<ChangeStudentTutorResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    
    try {
      this.logger.log(`Admin request to change tutor for student ${changeDto.studentId} to tutor ${changeDto.newTutorId}`);

      // Validate student exists and is a student
      const student = await queryRunner.manager.findOne(User, {
        where: { id: changeDto.studentId, type: UserType.STUDENT, isActive: true },
      });
      if (!student) {
        throw new NotFoundException(`Student with ID ${changeDto.studentId} not found or not active`);
      }

      // Validate new tutor exists and is a tutor
      const newTutor = await queryRunner.manager.findOne(User, {
        where: { id: changeDto.newTutorId, type: UserType.TUTOR, isActive: true, isConfirmed: true },
        relations: ['userRoles', 'userRoles.role'],
      });
      if (!newTutor) {
        throw new NotFoundException(`Tutor with ID ${changeDto.newTutorId} not found, not active, or not confirmed`);
      }
      
      // Additional validation to ensure user has tutor role
      const isTutor = newTutor.userRoles?.some((userRole) => userRole.role?.name === 'tutor');
      if (!isTutor) {
        throw new BadRequestException(`User with ID ${changeDto.newTutorId} does not have tutor role`);
      }

      // Check tutor approval status
      await this.validateTutorApprovalStatus(changeDto.newTutorId);

      // Get student's active plan to determine which modules to update
      const userPlan = await queryRunner.manager.findOne(UserPlan, {
        where: { userId: changeDto.studentId, isActive: true },
        relations: ['plan', 'plan.planFeatures'],
      });
      if (!userPlan || !userPlan.plan) {
        throw new NotFoundException(`Student ${changeDto.studentId} does not have an active plan`);
      }

      // Get all current tutor assignments for this student
      const currentAssignments = await queryRunner.manager.find(StudentTutorMapping, {
        where: { studentId: changeDto.studentId, status: MappingStatus.ACTIVE },
        relations: ['tutor', 'planFeature'],
      });

      let previousTutor: User | null = null;
      if (currentAssignments.length > 0) {
        previousTutor = currentAssignments[0].tutor;

        // Verify consistency - all assignments should use the same tutor
        const uniqueTutorIds = [...new Set(currentAssignments.map(a => a.tutorId))];
        if (uniqueTutorIds.length > 1) {
          this.logger.warn(`INCONSISTENCY DETECTED: Student ${changeDto.studentId} has multiple tutors: [${uniqueTutorIds.join(', ')}]. Proceeding with change.`);
        }
      }

      // Check if the new tutor is the same as the current tutor
      if (previousTutor && previousTutor.id === changeDto.newTutorId) {
        throw new BadRequestException(`Student ${changeDto.studentId} is already assigned to tutor ${changeDto.newTutorId}`);
      }

      const changedModules: Array<{ moduleId: string; moduleName: string; moduleType: string }> = [];
      const changeDate = getCurrentUTCDate();

      // Process each module in the student's active plan
      for (const planFeature of userPlan.plan.planFeatures) {
        try {
          // Check if student has an existing assignment for this module
          const existingAssignment = currentAssignments.find(a => a.planFeatureId === planFeature.id);

          if (existingAssignment) {
            // Update existing assignment using atomic update operation
            const updateResult = await queryRunner.manager
              .createQueryBuilder()
              .update(StudentTutorMapping)
              .set({
                tutorId: changeDto.newTutorId,
                assignedDate: changeDate,
                notes: `Tutor changed by admin. ${changeDto.reason ? `Reason: ${changeDto.reason}` : ''}`,
                updatedAt: changeDate
              })
              .where('id = :id AND tutorId = :oldTutorId', { 
                id: existingAssignment.id, 
                oldTutorId: existingAssignment.tutorId 
              })
              .execute();

            if (updateResult.affected === 0) {
              throw new Error(`Failed to update assignment ${existingAssignment.id} - concurrent modification detected`);
            }

            this.logger.log(`Updated assignment for module ${planFeature.name} from tutor ${previousTutor?.id} to ${changeDto.newTutorId}`);
          } else {
            // Create new assignment for this module
            const newAssignment = queryRunner.manager.create(StudentTutorMapping, {
              studentId: changeDto.studentId,
              tutorId: changeDto.newTutorId,
              planFeatureId: planFeature.id,
              status: MappingStatus.ACTIVE,
              assignedDate: changeDate,
              notes: `Assigned by admin during tutor change. ${changeDto.reason ? `Reason: ${changeDto.reason}` : ''}`,
            });
            await queryRunner.manager.save(StudentTutorMapping, newAssignment);

            this.logger.log(`Created new assignment for module ${planFeature.name} with tutor ${changeDto.newTutorId}`);
          }

          changedModules.push({
            moduleId: planFeature.id,
            moduleName: planFeature.name,
            moduleType: planFeature.type,
          });
        } catch (error) {
          this.logger.error(`Failed to update assignment for module ${planFeature.name}: ${error.message}`, error.stack);
          // Continue with other modules
        }
      }

      // Send notifications about the tutor change
      try {
        // Get list of module names for templates
        const moduleNames = changedModules.map(m => m.moduleName);

        // Generate student notification with changed tutor
        const studentTitle = previousTutor
          ? `Tutor Changed to ${newTutor.name}`
          : `New Tutor Assigned: ${newTutor.name}`;
        const studentHtmlContent = this.emailTemplateService.generateStudentTutorAssignmentTemplate(
          newTutor.name,
          newTutor.id,
          this.profileLinkService.getProfileLink(newTutor.id, UserType.TUTOR),
          moduleNames
        );

        await this.asyncNotificationHelper.notifyAsync(
          changeDto.studentId,
          NotificationType.TUTOR_ASSIGNMENT,
          studentTitle,
          studentTitle, // Use title as fallback plain text
          {
            relatedEntityId: changeDto.studentId,
            relatedEntityType: RelatedEntityType.STUDENT_TUTOR_CHANGE,
            sendEmail: true,
            sendPush: true,
            sendInApp: true,
            sendRealtime: false,
            htmlContent: studentHtmlContent
          },
        );

        // Notify new tutor about student assignment
        const newTutorTitle = `New Student Assignment: ${student.name}`;
        const tutorHtmlContent = this.emailTemplateService.generateTutorAssignmentTemplate(
          student.name,
          student.id,
          this.profileLinkService.getProfileLink(student.id, UserType.STUDENT),
          moduleNames
        );

        await this.asyncNotificationHelper.notifyAsync(
          changeDto.newTutorId,
          NotificationType.TUTOR_ASSIGNMENT,
          newTutorTitle,
          newTutorTitle, // Use title as fallback plain text
          {
            relatedEntityId: changeDto.studentId,
            relatedEntityType: RelatedEntityType.STUDENT_TUTOR_CHANGE,
            sendEmail: true,
            sendPush: true,
            sendInApp: true,
            sendRealtime: false,
            htmlContent: tutorHtmlContent
          },
        );

        // Notify previous tutor if exists
        if (previousTutor) {
          const previousTutorTitle = `Student Reassigned: ${student.name}`;
          const previousTutorMessage = `Student ${student.name} has been reassigned to another tutor. Thank you for your guidance and support.`;

          await this.asyncNotificationHelper.notifyAsync(
            previousTutor.id,
            NotificationType.TUTOR_ASSIGNMENT,
            previousTutorTitle,
            previousTutorMessage,
            {
              relatedEntityId: changeDto.studentId,
              relatedEntityType: RelatedEntityType.STUDENT_TUTOR_CHANGE,
              sendEmail: true,
              sendPush: true,
              sendInApp: true,
              sendRealtime: false,
            },
          );
        }
      } catch (notificationError) {
        this.logger.error(`Failed to send notifications for tutor change: ${notificationError.message}`, notificationError.stack);
        // Don't fail the operation for notification errors
      }

      // Commit all changes
      await queryRunner.commitTransaction();

      const response: ChangeStudentTutorResponseDto = {
        studentId: changeDto.studentId,
        studentName: student.name,
        previousTutorId: previousTutor?.id,
        previousTutorName: previousTutor?.name,
        newTutorId: changeDto.newTutorId,
        newTutorName: newTutor.name,
        changedModules,
        totalModulesAffected: changedModules.length,
        reason: changeDto.reason,
        changeDate,
      };

      this.logger.log(`Successfully changed tutor for student ${changeDto.studentId} from ${previousTutor?.name || 'none'} to ${newTutor.name} for ${changedModules.length} modules`);
      return response;
    } catch (error) {
      this.logger.error(`Error changing tutor for student ${changeDto.studentId}: ${error.message}`, error.stack);
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Validate tutor approval status
   * @param tutorId Tutor ID to validate
   * @throws BadRequestException if tutor is not approved
   */
  private async validateTutorApprovalStatus(tutorId: string): Promise<void> {
    const tutorApproval = await this.tutorApprovalRepository.findOne({
      where: { userId: tutorId },
      order: { createdAt: 'DESC' },
    });

    if (!tutorApproval || tutorApproval.status !== TutorApprovalStatus.APPROVED) {
      const status = tutorApproval?.status || 'no approval record';
      throw new BadRequestException(`Tutor with ID ${tutorId} is not approved for assignment (status: ${status})`);
    }
  }

  /**
   * Map entities to assignment response DTO
   * @param mapping Assignment entity
   * @param student Student entity
   * @param tutor Tutor entity
   * @param planFeature Plan feature entity
   * @returns Assignment response DTO
   */
  private mapToAssignmentResponseDto(mapping: StudentTutorMapping, student: User, tutor: User, planFeature: PlanFeature): TutorAssignmentResponseDto {
    return {
      id: mapping.id,
      studentId: mapping.studentId,
      studentName: student.name,
      tutorId: mapping.tutorId,
      tutorName: tutor.name,
      planFeatureId: mapping.planFeatureId,
      moduleName: planFeature.name,
      moduleType: planFeature.type,
      status: mapping.status,
      assignedDate: mapping.assignedDate,
      lastActivityDate: mapping.lastActivityDate,
      notes: mapping.notes,
      createdAt: mapping.createdAt,
      updatedAt: mapping.updatedAt,
    };
  }
}
