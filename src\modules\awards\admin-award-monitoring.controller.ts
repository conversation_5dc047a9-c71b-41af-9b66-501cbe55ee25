import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { Api<PERSON><PERSON><PERSON>, ApiBearerAuth, ApiOperation, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { RolesGuard } from '../../common/guards/roles.guard';
import { Roles } from '../../common/decorators/roles.decorator';
import { UserType } from '../../database/entities/user.entity';
import { AwardJobService } from './award-job.service';
import { 
  AwardJobResponseDto, 
  AwardJobStatsDto, 
  AwardJobFilterDto 
} from '../../database/models/award-job.dto';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';
import { ApiOkResponseWithType, ApiErrorResponse, ApiOkResponseWithArrayType } from '../../common/decorators/api-response.decorator';
import { AwardJobType, AwardJobStatus } from '../../database/entities/award-job.entity';

@ApiTags('admin-award-monitoring')
@Controller('admin/awards/monitoring')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
@Roles(UserType.ADMIN)
export class AdminAwardJobController {
  constructor(
    private readonly awardJobService: AwardJobService,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Get all award calculation jobs (admin only)' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'sortBy', required: false, type: String })
  @ApiQuery({ name: 'sortDirection', required: false, enum: ['ASC', 'DESC'] })
  @ApiQuery({ name: 'jobType', required: false, enum: AwardJobType })
  @ApiQuery({ name: 'status', required: false, enum: AwardJobStatus })
  @ApiQuery({ name: 'startDate', required: false, type: String })
  @ApiQuery({ name: 'endDate', required: false, type: String })
  @ApiOkResponseWithType(PagedListDto, 'Award jobs retrieved successfully')
  @ApiErrorResponse(500, 'Internal server error')
  async getAllJobs(
    @Query('jobType') jobType?: AwardJobType,
    @Query('status') status?: AwardJobStatus,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('sortBy') sortBy?: string,
    @Query('sortDirection') sortDirection?: 'ASC' | 'DESC',
  ): Promise<ApiResponse<PagedListDto<AwardJobResponseDto>>> {
    const filterDto: AwardJobFilterDto = {
      jobType,
      status,
      startDate,
      endDate,
    };

    const paginationDto: PaginationDto = {
      page: page ? +page : 1,
      limit: limit ? +limit : 10,
      sortBy,
      sortDirection: sortDirection || 'DESC',
    };

    const jobs = await this.awardJobService.getAllJobs(filterDto, paginationDto);
    return ApiResponse.success(jobs, 'Award jobs retrieved successfully');
  }

  @Get('stats')
  @ApiOperation({ summary: 'Get award job statistics (admin only)' })
  @ApiOkResponseWithType(AwardJobStatsDto, 'Award job statistics retrieved successfully')
  @ApiErrorResponse(500, 'Internal server error')
  async getJobStats(): Promise<ApiResponse<AwardJobStatsDto>> {
    const stats = await this.awardJobService.getJobStats();
    return ApiResponse.success(stats, 'Award job statistics retrieved successfully');
  }





  @Get('jobs/:id')
  @ApiOperation({ summary: 'Get specific award job details (admin only)' })
  @ApiOkResponseWithType(AwardJobResponseDto, 'Award job retrieved successfully')
  @ApiErrorResponse(404, 'Award job not found')
  @ApiErrorResponse(500, 'Internal server error')
  async getJobById(@Param('id') id: string): Promise<ApiResponse<AwardJobResponseDto>> {
    const job = await this.awardJobService.getJobById(id);
    return ApiResponse.success(job, 'Award job retrieved successfully');
  }

  @Get('status/:jobType')
  @ApiOperation({ summary: 'Get current status of a specific job type (admin only)' })
  @ApiOkResponseWithType(AwardJobResponseDto, 'Job status retrieved successfully')
  @ApiErrorResponse(404, 'No jobs found for this type')
  @ApiErrorResponse(500, 'Internal server error')
  async getJobStatus(@Param('jobType') jobType: AwardJobType): Promise<ApiResponse<AwardJobResponseDto>> {
    const job = await this.awardJobService.getLatestJobByType(jobType);
    return ApiResponse.success(job, 'Job status retrieved successfully');
  }

  @Get('running')
  @ApiOperation({ summary: 'Get all currently running jobs (admin only)' })
  @ApiOkResponseWithArrayType(AwardJobResponseDto, 'Running jobs retrieved successfully')
  @ApiErrorResponse(500, 'Internal server error')
  async getRunningJobs(): Promise<ApiResponse<AwardJobResponseDto[]>> {
    const jobs = await this.awardJobService.getRunningJobs();
    return ApiResponse.success(jobs, 'Running jobs retrieved successfully');
  }
}