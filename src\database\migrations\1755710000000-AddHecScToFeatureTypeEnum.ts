import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddHecScToFeatureTypeEnum1755710000000 implements MigrationInterface {
  name = 'AddHecScToFeatureTypeEnum1755710000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add the new enum value to the existing enum type
    await queryRunner.query(`ALTER TYPE "plan_feature_type_enum" ADD VALUE 'hec_sc'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Note: PostgreSQL doesn't support removing enum values directly
    // This would require recreating the enum type and updating all references
    // For simplicity, we'll leave the enum value in place during rollback
    // In production, you might want to implement a more complex rollback strategy
  }
}