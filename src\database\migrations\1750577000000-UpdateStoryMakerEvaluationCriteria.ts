import { MigrationInterface, QueryRunner } from 'typeorm';

/**
 * Migration to update Story Maker evaluation system from legacy 1-5 scoring to new 9 criteria system.
 * 
 * This migration adds 9 new evaluation criteria columns based on international writing assessment standards:
 * 1. Content & Task Fulfillment (0-100)
 * 2. Organization & Coherence (0-100) 
 * 3. Grammar & Accuracy (0-100)
 * 4. Vocabulary (Lexical Resource) (0-100)
 * 5. Sentence Fluency & Style (0-100)
 * 6. Clarity & Cohesion (0-100)
 * 7. Creativity (0-100)
 * 8. Critical Thinking (0-100)
 * 9. Expressiveness (0-100)
 * 
 * Legacy columns (creativity_score, sentence_power_score, etc.) are REMOVED - no backward compatibility.
 */
export class UpdateStoryMakerEvaluationCriteria1750577000000 implements MigrationInterface {
  name = 'UpdateStoryMakerEvaluationCriteria1750577000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add new 9 criteria columns with 0-100 scale (replacing legacy 1-5 scoring)
    await queryRunner.query(`
      ALTER TABLE "story_maker_evaluation" 
      ADD COLUMN "content_task_fulfillment" integer NOT NULL DEFAULT 60 CHECK ("content_task_fulfillment" >= 0 AND "content_task_fulfillment" <= 100),
      ADD COLUMN "organization_coherence" integer NOT NULL DEFAULT 60 CHECK ("organization_coherence" >= 0 AND "organization_coherence" <= 100),
      ADD COLUMN "grammar_accuracy" integer NOT NULL DEFAULT 60 CHECK ("grammar_accuracy" >= 0 AND "grammar_accuracy" <= 100),
      ADD COLUMN "vocabulary_lexical" integer NOT NULL DEFAULT 60 CHECK ("vocabulary_lexical" >= 0 AND "vocabulary_lexical" <= 100),
      ADD COLUMN "sentence_fluency_style" integer NOT NULL DEFAULT 60 CHECK ("sentence_fluency_style" >= 0 AND "sentence_fluency_style" <= 100),
      ADD COLUMN "clarity_cohesion" integer NOT NULL DEFAULT 60 CHECK ("clarity_cohesion" >= 0 AND "clarity_cohesion" <= 100),
      ADD COLUMN "creativity" integer NOT NULL DEFAULT 60 CHECK ("creativity" >= 0 AND "creativity" <= 100),
      ADD COLUMN "critical_thinking" integer NOT NULL DEFAULT 70 CHECK ("critical_thinking" >= 0 AND "critical_thinking" <= 100),
      ADD COLUMN "expressiveness" integer NOT NULL DEFAULT 60 CHECK ("expressiveness" >= 0 AND "expressiveness" <= 100)
    `);

    // Migrate existing data from legacy columns if they exist
    const hasLegacyColumns = await queryRunner.hasColumn('story_maker_evaluation', 'creativity_score');
    
    if (hasLegacyColumns) {
      // Convert legacy scores to new 0-100 scale (capped at 100)
      await queryRunner.query(`
        UPDATE "story_maker_evaluation" 
        SET 
          "content_task_fulfillment" = LEAST(COALESCE("creativity_score" * 20, 60), 100),
          "organization_coherence" = LEAST(COALESCE("sentence_power_score" * 33, 60), 100),
          "grammar_accuracy" = LEAST(COALESCE("accuracy_score" * 33, 60), 100),
          "vocabulary_lexical" = LEAST(COALESCE("participation_score" * 20, 60), 100),
          "sentence_fluency_style" = LEAST(COALESCE("sentence_power_score" * 33, 60), 100),
          "clarity_cohesion" = LEAST(COALESCE("sentence_power_score" * 33, 60), 100),
          "creativity" = LEAST(COALESCE("creativity_score" * 20, 60), 100),
          "critical_thinking" = 70,
          "expressiveness" = LEAST(COALESCE("creativity_score" * 20, 60), 100)
      `);
      
      // Drop legacy columns
      await queryRunner.query(`
        ALTER TABLE "story_maker_evaluation" 
        DROP COLUMN IF EXISTS "creativity_score",
        DROP COLUMN IF EXISTS "sentence_power_score",
        DROP COLUMN IF EXISTS "participation_score",
        DROP COLUMN IF EXISTS "accuracy_score",
        DROP COLUMN IF EXISTS "relevance_score",
        DROP COLUMN IF EXISTS "sentence_score",
        DROP COLUMN IF EXISTS "sentence_count"
      `);
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Restore legacy columns
    await queryRunner.query(`
      ALTER TABLE "story_maker_evaluation" 
      ADD COLUMN "creativity_score" integer,
      ADD COLUMN "sentence_power_score" integer,
      ADD COLUMN "participation_score" integer,
      ADD COLUMN "accuracy_score" integer,
      ADD COLUMN "relevance_score" integer,
      ADD COLUMN "sentence_score" integer,
      ADD COLUMN "sentence_count" integer
    `);
    
    // Remove the new columns
    await queryRunner.query(`
      ALTER TABLE "story_maker_evaluation" 
      DROP COLUMN "content_task_fulfillment",
      DROP COLUMN "organization_coherence",
      DROP COLUMN "grammar_accuracy",
      DROP COLUMN "vocabulary_lexical",
      DROP COLUMN "sentence_fluency_style",
      DROP COLUMN "clarity_cohesion",
      DROP COLUMN "creativity",
      DROP COLUMN "critical_thinking",
      DROP COLUMN "expressiveness"
    `);
  }
}