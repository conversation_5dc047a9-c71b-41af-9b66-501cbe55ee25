import { <PERSON><PERSON><PERSON>, Column, <PERSON>ToMany } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { BlockGameSentence } from './block-game-sentence.entity';
import { BlockGameAttempt } from './block-game-attempt.entity';

@Entity()
export class BlockGame extends AuditableBaseEntity {
  @Column()
  title: string;

  @Column()
  score: number;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  // Note: Sentences relationship removed due to shared BlockGameSentence table
  // Note: Attempts relationship removed since BlockGameAttempt supports both admin and tutor games
}
