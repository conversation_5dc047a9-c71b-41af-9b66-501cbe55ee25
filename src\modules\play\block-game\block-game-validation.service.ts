import { Injectable, BadRequestException } from '@nestjs/common';
import { BlockGameSentenceDto } from '../../../database/models/block-game/block-game.dto';

@Injectable()
export class BlockGameValidationService {
  /**
   * Validate block game sentence with gap functionality
   */
  validateSentence(sentence: BlockGameSentenceDto, index: number): string[] {
    const errors: string[] = [];

    // Validate starting part gaps
    const startingGapCount = (sentence.starting_part.match(/\[\[gap\]\]/g) || []).length;
    if (startingGapCount !== sentence.starting_part_answers.length) {
      errors.push(`Sentence ${index + 1}: Starting part has ${startingGapCount} gap(s) but ${sentence.starting_part_answers.length} answer(s)`);
    }

    // Validate expanding part gaps
    const expandingGapCount = (sentence.expanding_part.match(/\[\[gap\]\]/g) || []).length;
    if (expandingGapCount !== sentence.expanding_part_answers.length) {
      errors.push(`Sentence ${index + 1}: Expanding part has ${expandingGapCount} gap(s) but ${expandingGapCount} answer(s)`);
    }

    // Validate that at least one part has content after removing gaps
    const startingContent = sentence.starting_part.replace(/\[\[gap\]\]/g, '').trim();
    const expandingContent = sentence.expanding_part.replace(/\[\[gap\]\]/g, '').trim();
    
    if (!startingContent && !expandingContent) {
      errors.push(`Sentence ${index + 1}: Both parts cannot be empty after removing gaps`);
    }

    // Validate answers are not empty
    sentence.starting_part_answers.forEach((answer, i) => {
      if (!answer.trim()) {
        errors.push(`Sentence ${index + 1}: Starting part answer ${i + 1} cannot be empty`);
      }
    });

    sentence.expanding_part_answers.forEach((answer, i) => {
      if (!answer.trim()) {
        errors.push(`Sentence ${index + 1}: Expanding part answer ${i + 1} cannot be empty`);
      }
    });

    return errors;
  }

  /**
   * Validate all sentences in a block game
   */
  validateSentences(sentences: BlockGameSentenceDto[]): void {
    const allErrors: string[] = [];

    sentences.forEach((sentence, index) => {
      const errors = this.validateSentence(sentence, index);
      allErrors.push(...errors);
    });

    if (allErrors.length > 0) {
      throw new BadRequestException({
        message: 'Validation failed',
        errors: allErrors,
      });
    }
  }

  /**
   * Check if student gap answers match correct answers
   */
  checkGapAnswers(studentAnswers: string[], correctAnswers: string[]): boolean {
    if (studentAnswers.length !== correctAnswers.length) {
      return false;
    }

    for (let i = 0; i < correctAnswers.length; i++) {
      if (studentAnswers[i].toLowerCase().trim() !== correctAnswers[i].toLowerCase().trim()) {
        return false;
      }
    }

    return true;
  }
}