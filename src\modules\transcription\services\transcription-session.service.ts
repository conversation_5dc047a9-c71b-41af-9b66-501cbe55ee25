import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TranscriptionSession } from '../../../database/entities/transcription-session.entity';
import { CreateTranscriptionSessionDto, TranscriptionSessionResponseDto } from '../../../database/models/transcription.dto';

@Injectable()
export class TranscriptionSessionService {
  constructor(
    @InjectRepository(TranscriptionSession)
    private sessionRepository: Repository<TranscriptionSession>,
  ) {}

  async create(studentId: string, createSessionDto: CreateTranscriptionSessionDto): Promise<TranscriptionSessionResponseDto> {
    const session = this.sessionRepository.create({
      studentId,
      bookId: createSessionDto.bookId,
      startedAt: new Date(),
    });
    const savedSession = await this.sessionRepository.save(session);
    return this.mapToResponseDto(savedSession);
  }

  async findById(id: string): Promise<TranscriptionSessionResponseDto> {
    const session = await this.sessionRepository.findOne({
      where: { id }
    });
    if (!session) {
      throw new NotFoundException('Session not found');
    }
    return this.mapToResponseDto(session);
  }

  async findByStudentId(studentId: string): Promise<TranscriptionSessionResponseDto[]> {
    const sessions = await this.sessionRepository.find({
      where: { studentId },
      order: { startedAt: 'DESC' },
      take: 50 // Limit to recent 50 sessions for performance
    });
    return sessions.map(session => this.mapToResponseDto(session));
  }

  async completeSession(id: string): Promise<TranscriptionSessionResponseDto> {
    const session = await this.sessionRepository.findOne({ where: { id } });
    if (!session) {
      throw new NotFoundException('Session not found');
    }
    
    session.isCompleted = true;
    session.completedAt = new Date();
    const updatedSession = await this.sessionRepository.save(session);
    return this.mapToResponseDto(updatedSession);
  }

  async updateSessionStats(sessionId: string, isCorrect: boolean): Promise<void> {
    const session = await this.sessionRepository.findOne({ where: { id: sessionId } });
    if (session) {
      session.totalAttempts += 1;
      if (isCorrect) {
        session.correctAttempts += 1;
      }
      await this.sessionRepository.save(session);
    }
  }

  private mapToResponseDto(session: TranscriptionSession): TranscriptionSessionResponseDto {
    return {
      id: session.id,
      studentId: session.studentId,
      bookId: session.bookId,
      startedAt: session.startedAt,
      completedAt: session.completedAt,
      totalAttempts: session.totalAttempts,
      correctAttempts: session.correctAttempts,
      isCompleted: session.isCompleted,
    };
  }
}