import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsN<PERSON>ber, IsOptional, Min, IsUUID, IsBoolean, IsEnum } from 'class-validator';
import { Transform, Type } from 'class-transformer';

/**
 * DTO for admin to evaluate a student's story submission
 */
export class StoryMakerEvaluationDto {
  @ApiProperty({
    description: 'The score assigned to the submission',
    example: 45,
    minimum: 1,
  })
  @IsNumber()
  @Min(1, { message: 'Score must be greater than 0' })
  score: number;

  @ApiPropertyOptional({
    description: 'Feedback on the submission',
    example: 'Great use of descriptive language and creative storyline.',
  })
  @IsString()
  @IsOptional()
  feedback?: string;
}

/**
 * DTO for listing student participations for admin
 */
export class StoryMakerParticipationListItemDto {
  @ApiProperty({
    description: 'The ID of the participation',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'The ID of the student',
    example: '123e4567-e89b-12d3-a456-************',
  })
  student_id: string;

  @ApiProperty({
    description: 'The name of the student',
    example: 'John Doe',
  })
  student_name: string;

  @ApiProperty({
    description: 'The email of the student',
    example: '<EMAIL>',
  })
  student_email: string;

  @ApiProperty({
    description: 'The ID of the story maker',
    example: '123e4567-e89b-12d3-a456-************',
  })
  story_maker_id: string;

  @ApiProperty({
    description: 'The title of the story maker',
    example: 'Adventure in the Forest',
  })
  story_maker_title: string;

  @ApiProperty({
    description: 'Whether the submission has been evaluated',
    example: true,
  })
  is_evaluated: boolean;

  @ApiPropertyOptional({
    description: 'The total score assigned to the submission (only available after evaluation)',
    example: 85,
  })
  score?: number;

  @ApiProperty({
    description: 'When the story was submitted',
    example: '2023-01-01T00:00:00.000Z',
  })
  submitted_at: Date;

  @ApiPropertyOptional({
    description: 'When the story was evaluated (only available after evaluation)',
    example: '2023-01-02T00:00:00.000Z',
  })
  evaluated_at?: Date;

  @ApiPropertyOptional({
    description: 'The ID of the admin who evaluated the submission',
    example: '123e4567-e89b-12d3-a456-************',
  })
  evaluated_by?: string;
}

/**
 * DTO for the response containing a list of participations
 */
export class StoryMakerParticipationListResponseDto {
  @ApiProperty({
    description: 'List of story maker participations',
    type: [StoryMakerParticipationListItemDto],
  })
  participations: StoryMakerParticipationListItemDto[];

  @ApiProperty({
    description: 'Total number of participations',
    example: 25,
  })
  total_count: number;
}

/**
 * Enum for participation sort fields
 */
export enum ParticipationSortField {
  SUBMITTED_AT = 'submitted_at',
  EVALUATED_AT = 'evaluated_at',
  SCORE = 'score',
  STUDENT_NAME = 'student_name',
}

/**
 * DTO for querying participations
 */
export class StoryMakerParticipationQueryDto {
  @ApiPropertyOptional({
    description: 'Page number (1-based)',
    example: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @Min(1)
  page?: number;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    example: 10,
  })
  @IsOptional()
  @Type(() => Number)
  @Min(1)
  limit?: number;

  @ApiPropertyOptional({
    description: 'Search term for student name or email',
    example: 'john',
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    description: 'Filter by story maker ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  story_maker_id?: string;

  @ApiPropertyOptional({
    description: 'Filter by evaluation status',
    example: false,
    type: Boolean,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  is_evaluated?: boolean;

  @ApiPropertyOptional({
    description: 'Sort by field',
    example: 'submitted_at',
    enum: ParticipationSortField,
  })
  @IsOptional()
  @IsEnum(ParticipationSortField)
  sort_by?: ParticipationSortField = ParticipationSortField.SUBMITTED_AT;

  @ApiPropertyOptional({
    description: 'Sort direction',
    example: 'DESC',
    enum: ['ASC', 'DESC'],
  })
  @IsOptional()
  @IsString()
  @IsEnum(['ASC', 'DESC'])
  sort_direction?: 'ASC' | 'DESC' = 'DESC';
}

/**
 * DTO for detailed participation view
 */
export class StoryMakerParticipationDetailDto {
  @ApiProperty({
    description: 'The ID of the participation',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'The ID of the student',
    example: '123e4567-e89b-12d3-a456-************',
  })
  student_id: string;

  @ApiProperty({
    description: 'The name of the student',
    example: 'John Doe',
  })
  student_name: string;

  @ApiProperty({
    description: 'The email of the student',
    example: '<EMAIL>',
  })
  student_email: string;

  @ApiProperty({
    description: 'The ID of the story maker',
    example: '123e4567-e89b-12d3-a456-************',
  })
  story_maker_id: string;

  @ApiProperty({
    description: 'The title of the story maker',
    example: 'Adventure in the Forest',
  })
  story_maker_title: string;

  @ApiProperty({
    description: 'The content of the submitted story in rich text format',
    example: '<p>Once upon a time in a magical forest...</p>',
  })
  content: string;

  @ApiProperty({
    description: 'Whether the submission has been evaluated',
    example: true,
  })
  is_evaluated: boolean;

  @ApiPropertyOptional({
    description: 'The total score assigned to the submission (only available after evaluation)',
    example: 85,
  })
  score?: number;

  @ApiPropertyOptional({
    description: 'Content & Task Fulfillment score (0-100)',
    example: 88,
  })
  contentTaskFulfillment?: number;

  @ApiPropertyOptional({
    description: 'Organization & Coherence score (0-100)',
    example: 82,
  })
  organizationCoherence?: number;

  @ApiPropertyOptional({
    description: 'Grammar & Accuracy score (0-100)',
    example: 90,
  })
  grammarAccuracy?: number;

  @ApiPropertyOptional({
    description: 'Vocabulary score (0-100)',
    example: 85,
  })
  vocabularyLexical?: number;

  @ApiPropertyOptional({
    description: 'Sentence Fluency & Style score (0-100)',
    example: 87,
  })
  sentenceFluencyStyle?: number;

  @ApiPropertyOptional({
    description: 'Clarity & Cohesion score (0-100)',
    example: 83,
  })
  clarityCohesion?: number;

  @ApiPropertyOptional({
    description: 'Creativity score (0-100)',
    example: 92,
  })
  creativity?: number;

  @ApiPropertyOptional({
    description: 'Critical Thinking score (0-100)',
    example: 78,
  })
  criticalThinking?: number;

  @ApiPropertyOptional({
    description: 'Expressiveness score (0-100)',
    example: 89,
  })
  expressiveness?: number;

  @ApiPropertyOptional({
    description: 'AI-generated feedback on the submission (only available after evaluation)',
    example: 'Great use of descriptive language and creative storyline. Consider improving sentence variety.',
  })
  feedback?: string;

  @ApiProperty({
    description: 'When the story was submitted',
    example: '2023-01-01T00:00:00.000Z',
  })
  submitted_at: Date;

  @ApiPropertyOptional({
    description: 'When the story was evaluated (only available after evaluation)',
    example: '2023-01-02T00:00:00.000Z',
  })
  evaluated_at?: Date;

  @ApiPropertyOptional({
    description: 'The ID of the admin who evaluated the submission',
    example: '123e4567-e89b-12d3-a456-************',
  })
  evaluated_by?: string;

  @ApiPropertyOptional({
    description: 'The name of the admin who evaluated the submission',
    example: 'Admin User',
  })
  evaluated_by_name?: string;
}
