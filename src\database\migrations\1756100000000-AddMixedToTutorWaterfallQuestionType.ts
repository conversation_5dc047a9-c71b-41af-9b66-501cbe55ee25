import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddMixedToTutorWaterfallQuestionType1756100000000 implements MigrationInterface {
  name = 'AddMixedToTutorWaterfallQuestionType1756100000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add 'mixed' value to the tutor_waterfall_set_question_type_enum
    await queryRunner.query(`
      ALTER TYPE tutor_waterfall_set_question_type_enum ADD VALUE 'mixed'
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Note: PostgreSQL doesn't support removing enum values directly
    // This would require recreating the enum and updating all references
    // For now, we'll leave the enum value in place
    console.log('Cannot remove enum value in PostgreSQL. Manual intervention required if rollback is needed.');
  }
}