import { Controller, Post, Get, Patch, Delete, Body, Param, UseGuards, UseInterceptors, UploadedFile, BadRequestException, Req, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiConsumes, ApiOperation, ApiTags, ApiQuery, ApiParam } from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { AdminGuard } from '../../common/guards/admin.guard';
import { DiaryService } from './diary.service';
import { CreateDiarySkinDto, UpdateDiarySkinDto, DiarySkinResponseDto, DiaryEntryResponseDto, ToggleSkinStatusDto } from '../../database/models/diary.dto';
import { AdminDiaryEntryFilterDto, AdminUpdateDiaryEntryDto } from '../../database/models/admin-diary.dto';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiOkResponseWithPagedListType, ApiErrorResponse } from '../../common/decorators/api-response.decorator';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';

@ApiTags('admin/diary')
@Controller('admin/diary')
@UseGuards(JwtAuthGuard, AdminGuard)
@ApiBearerAuth('JWT-auth')
export class AdminDiaryController {
  constructor(private readonly diaryService: DiaryService) {}

  @Post('skins')
  @UseInterceptors(FileInterceptor('previewImage'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Create a new diary skin (Admin only)',
    description: 'Creates a new diary skin/template. Only accessible by admins.',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          example: 'Modern Blue',
          description: 'Name of the diary skin',
        },
        description: {
          type: 'string',
          example: 'A modern blue theme with clean typography',
          description: 'Description of the diary skin',
        },
        templateContent: {
          type: 'string',
          example: '<div class="diary-template">{{content}}</div>',
          description: 'HTML template content for the skin',
        },
        isActive: {
          type: 'boolean',
          example: true,
          description: 'Whether the skin is active and available for use',
        },
        previewImage: {
          type: 'string',
          format: 'binary',
          description: 'Preview image file for the diary skin (JPEG, PNG, or GIF)',
        },
      },
      required: ['name', 'description', 'templateContent'],
    },
  })
  @ApiOkResponseWithType(DiarySkinResponseDto, 'Diary skin created successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(409, 'Diary skin with the same name already exists')
  async createDiarySkin(@Req() req: any, @Body() createDiarySkinDto: CreateDiarySkinDto, @UploadedFile() previewImage?: any): Promise<ApiResponse<DiarySkinResponseDto>> {
    // Preview image is required for a better user experience
    if (!previewImage) {
      throw new BadRequestException('Preview image is required for diary skins');
    }

    try {
      const adminId = req.user.id;
      const skin = await this.diaryService.createDiarySkin(adminId, createDiarySkinDto, previewImage);
      return ApiResponse.success(skin, 'Diary skin created successfully', 201);
    } catch (error) {
      // Log the error for debugging
      console.error('Error creating diary skin:', error);

      // Re-throw the error to be handled by the global exception filter
      throw error;
    }
  }

  @Get('skins')
  @ApiOperation({
    summary: 'Get all diary skins (Admin only)',
    description: 'Get a list of all diary skins/templates, including inactive ones. Only accessible by admins.',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by',
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction (ASC or DESC)',
  })
  @ApiOkResponseWithPagedListType(DiarySkinResponseDto, 'List of diary skins retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(500, 'Internal server error')
  async getAllDiarySkins(@Query() paginationDto?: PaginationDto): Promise<ApiResponse<PagedListDto<DiarySkinResponseDto>>> {
    const skins = await this.diaryService.getDiarySkins(true, undefined, paginationDto); // true to include inactive skins
    return ApiResponse.success(skins, 'Diary skins retrieved successfully');
  }

  @Get('skins/:id')
  @ApiOperation({
    summary: 'Get a diary skin by ID (Admin only)',
    description: 'Get details of a specific diary skin by ID. Only accessible by admins.',
  })
  @ApiOkResponseWithType(DiarySkinResponseDto, 'Diary skin retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Diary skin not found')
  @ApiErrorResponse(500, 'Internal server error')
  async getDiarySkinById(@Param('id') id: string): Promise<ApiResponse<DiarySkinResponseDto>> {
    const skin = await this.diaryService.getDiarySkinById(id);
    return ApiResponse.success(skin, 'Diary skin retrieved successfully');
  }

  @Patch('skins/:id')
  @UseInterceptors(FileInterceptor('previewImage'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Update a diary skin (Admin only)',
    description: 'Update an existing diary skin. Only accessible by the admins. Skins can be edited even when in use.',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          example: 'Modern Blue Updated',
          description: 'Name of the diary skin',
        },
        description: {
          type: 'string',
          example: 'An updated modern blue theme with clean typography',
          description: 'Description of the diary skin',
        },
        templateContent: {
          type: 'string',
          example: '<div class="diary-template-updated">{{content}}</div>',
          description: 'HTML template content for the skin',
        },
        isActive: {
          type: 'boolean',
          example: false,
          description: 'Whether the skin is active and available for use',
        },
        previewImage: {
          type: 'string',
          format: 'binary',
          description: 'Preview image file for the diary skin (JPEG, PNG, or GIF)',
        },
      },
    },
  })
  @ApiOkResponseWithType(DiarySkinResponseDto, 'Diary skin updated successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Diary skin not found')
  @ApiErrorResponse(409, 'Diary skin with the same name already exists')
  @ApiErrorResponse(500, 'Internal server error')
  async updateDiarySkin(@Param('id') id: string, @Req() req: any, @Body() updateDiarySkinDto: UpdateDiarySkinDto, @UploadedFile() previewImage?: any): Promise<ApiResponse<DiarySkinResponseDto>> {
    const adminId = req.user.id;
    const skin = await this.diaryService.updateDiarySkin(id, adminId, updateDiarySkinDto, previewImage);
    return ApiResponse.success(skin, 'Diary skin updated successfully');
  }

  @Delete('skins/:id')
  @ApiOperation({
    summary: 'Delete a diary skin (Admin only)',
    description: 'Delete a diary skin. Only accessible by admins. Cannot delete skins that are in use by diaries or diary entries.',
  })
  @ApiOkResponseWithType(Object, 'Diary skin deleted successfully')
  @ApiErrorResponse(400, 'Cannot delete skin as it is being used by diaries or diary entries')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Diary skin not found')
  @ApiErrorResponse(500, 'Internal server error')
  async deleteDiarySkin(@Param('id') id: string, @Req() req: any): Promise<ApiResponse<null>> {
    const adminId = req.user.id;
    await this.diaryService.deleteDiarySkin(id, adminId);
    return ApiResponse.success(null, 'Diary skin deleted successfully');
  }

  @Get('entries')
  @ApiOperation({
    summary: 'Get all diary entries',
    description: 'Get all diary entries with filtering and pagination. Only accessible by admins.',
  })
  @ApiQuery({
    name: 'studentName',
    required: false,
    type: String,
    description: 'Filter entries by student name',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: ['NEW', 'SUBMIT', 'REVIEWED', 'CONFIRM'],
    description: 'Filter entries by status',
  })
  @ApiQuery({
    name: 'dateFrom',
    required: false,
    type: String,
    description: 'Filter entries from this date (YYYY-MM-DD)',
  })
  @ApiQuery({
    name: 'dateTo',
    required: false,
    type: String,
    description: 'Filter entries until this date (YYYY-MM-DD)',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by',
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction',
  })
  @ApiOkResponseWithPagedListType(DiaryEntryResponseDto, 'Diary entries retrieved successfully')
  @ApiErrorResponse(400, 'Invalid filter parameters')
  @ApiErrorResponse(403, 'Forbidden - Only admins can access this endpoint')
  @ApiErrorResponse(500, 'Internal server error')
  async getAllEntries(@Query() filterDto: AdminDiaryEntryFilterDto): Promise<PagedListDto<DiaryEntryResponseDto>> {
    const { page, limit, sortBy, ...filters } = filterDto;
    return this.diaryService.getAllDiaryEntries(filters, { page, limit, sortBy });
  }

  @Get('entries/:id')
  @ApiOperation({
    summary: 'Get a specific diary entry',
    description: 'Get details of a specific diary entry. Only accessible by admins.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the diary entry',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiOkResponseWithType(DiaryEntryResponseDto, 'Diary entry retrieved successfully')
  @ApiErrorResponse(404, 'Diary entry not found')
  @ApiErrorResponse(403, 'Forbidden - Only admins can access this endpoint')
  @ApiErrorResponse(500, 'Internal server error')
  async getEntry(@Param('id') id: string): Promise<DiaryEntryResponseDto> {
    return this.diaryService.getDiaryEntryAsAdmin(id);
  }

  @Patch('skins/:skinId/status')
  @ApiOperation({
    summary: 'Toggle admin diary skin active status',
    description: 'Activate or deactivate an admin diary skin. Only accessible by admins.',
  })
  @ApiParam({
    name: 'skinId',
    description: 'The ID of the skin to toggle',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiBody({
    type: ToggleSkinStatusDto,
    description: 'Status toggle data',
    examples: {
      activate: {
        summary: 'Activate skin',
        description: 'Set skin as active',
        value: {
          isActive: true,
        },
      },
      deactivate: {
        summary: 'Deactivate skin',
        description: 'Set skin as inactive',
        value: {
          isActive: false,
        },
      },
    },
  })
  @ApiOkResponseWithType(DiarySkinResponseDto, 'Skin status updated successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(403, 'Forbidden - Only admins can access this endpoint')
  @ApiErrorResponse(404, 'Skin not found')
  @ApiErrorResponse(500, 'Internal server error')
  async toggleAdminSkinStatus(
    @Param('skinId') skinId: string,
    @Body() toggleSkinStatusDto: ToggleSkinStatusDto,
  ): Promise<ApiResponse<DiarySkinResponseDto>> {
    const result = await this.diaryService.toggleAdminSkinStatus(skinId, toggleSkinStatusDto);
    return ApiResponse.success(result, `Skin ${toggleSkinStatusDto.isActive ? 'activated' : 'deactivated'} successfully`);
  }
}
