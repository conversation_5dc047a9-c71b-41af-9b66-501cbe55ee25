import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BlockGame } from '../../../database/entities/block-game.entity';
import { BlockGameSentence } from '../../../database/entities/block-game-sentence.entity';
import { BlockGameAttempt } from '../../../database/entities/block-game-attempt.entity';
import { TutorBlockGame } from '../../../database/entities/tutor-block-game.entity';
import { TutorWaterfallSet } from '../../../database/entities/tutor-waterfall-set.entity';
import { WaterfallSet } from '../../../database/entities/waterfall-set.entity';
import { GamePerformanceTracking } from '../../../database/entities/game-performance-tracking.entity';
import { User } from '../../../database/entities/user.entity';
import { BlockGameAdminController } from './block-game-admin.controller';
import { BlockGameAdminService } from './block-game-admin.service';
import { BlockGameController } from './block-game.controller';
import { BlockGameService } from './block-game.service';
import { BlockGameValidationService } from './block-game-validation.service';
import { TutorBlockGameController } from '../tutor/tutor-block-game.controller';
import { TutorBlockGameService } from '../tutor/tutor-block-game.service';


@Module({
  imports: [
    TypeOrmModule.forFeature([
      BlockGame,
      BlockGameSentence,
      BlockGameAttempt,
      TutorBlockGame,
      TutorWaterfallSet,
      WaterfallSet,
      GamePerformanceTracking,
      User,
    ])
  ],
  controllers: [BlockGameAdminController, BlockGameController, TutorBlockGameController],
  providers: [
    BlockGameAdminService,
    BlockGameService,
    BlockGameValidationService,
    TutorBlockGameService,
  ],
  exports: [
    BlockGameAdminService,
    BlockGameService,
    TutorBlockGameService,
  ],
})
export class BlockGameModule {}
