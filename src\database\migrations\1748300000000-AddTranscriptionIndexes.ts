import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddTranscriptionIndexes1748300000000 implements MigrationInterface {
  name = 'AddTranscriptionIndexes1748300000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add indexes for better query performance
    
    // Books table indexes
    await queryRunner.query(`
      CREATE INDEX "IDX_books_is_active" ON "books" ("is_active")
    `);
    
    await queryRunner.query(`
      CREATE INDEX "IDX_books_created_at" ON "books" ("created_at")
    `);

    // Sentences table indexes
    await queryRunner.query(`
      CREATE INDEX "IDX_sentences_book_id" ON "sentences" ("book_id")
    `);
    
    await queryRunner.query(`
      CREATE INDEX "IDX_sentences_book_id_order_index" ON "sentences" ("book_id", "order_index")
    `);

    // Transcription sessions table indexes
    await queryRunner.query(`
      CREATE INDEX "IDX_transcription_sessions_student_id" ON "transcription_sessions" ("student_id")
    `);
    
    await queryRunner.query(`
      CREATE INDEX "IDX_transcription_sessions_student_id_started_at" ON "transcription_sessions" ("student_id", "started_at" DESC)
    `);

    // Transcription attempts table indexes
    await queryRunner.query(`
      CREATE INDEX "IDX_transcription_attempts_session_id" ON "transcription_attempts" ("session_id")
    `);
    
    await queryRunner.query(`
      CREATE INDEX "IDX_transcription_attempts_session_id_attempted_at" ON "transcription_attempts" ("session_id", "attempted_at")
    `);
    
    await queryRunner.query(`
      CREATE INDEX "IDX_transcription_attempts_sentence_id" ON "transcription_attempts" ("sentence_id")
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes in reverse order
    await queryRunner.query(`DROP INDEX "IDX_transcription_attempts_sentence_id"`);
    await queryRunner.query(`DROP INDEX "IDX_transcription_attempts_session_id_attempted_at"`);
    await queryRunner.query(`DROP INDEX "IDX_transcription_attempts_session_id"`);
    await queryRunner.query(`DROP INDEX "IDX_transcription_sessions_student_id_started_at"`);
    await queryRunner.query(`DROP INDEX "IDX_transcription_sessions_student_id"`);
    await queryRunner.query(`DROP INDEX "IDX_sentences_book_id_order_index"`);
    await queryRunner.query(`DROP INDEX "IDX_sentences_book_id"`);
    await queryRunner.query(`DROP INDEX "IDX_books_created_at"`);
    await queryRunner.query(`DROP INDEX "IDX_books_is_active"`);
  }
}