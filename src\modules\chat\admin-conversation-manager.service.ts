import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { AdminConversation } from '../../database/entities/admin-conversation.entity';
import { AdminConversationParticipant } from '../../database/entities/admin-conversation-participant.entity';
import { User, UserType } from '../../database/entities/user.entity';
import { Message } from '../../database/entities/message.entity';
import { VirtualAdminService } from './virtual-admin.service';
import { AdminConversationService } from './admin-conversation.service';
import { getCurrentUTCDate } from '../../common/utils/date-utils';

/**
 * Comprehensive service for managing admin conversations and participants
 * Handles all admin-to-user chat operations with proper event emission and participant tracking
 */
@Injectable()
export class AdminConversationManagerService {
  private readonly logger = new Logger(AdminConversationManagerService.name);

  constructor(
    @InjectRepository(AdminConversation)
    private readonly adminConversationRepository: Repository<AdminConversation>,
    @InjectRepository(AdminConversationParticipant)
    private readonly adminConversationParticipantRepository: Repository<AdminConversationParticipant>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Message)
    private readonly messageRepository: Repository<Message>,
    private readonly dataSource: DataSource,
    private readonly virtualAdminService: VirtualAdminService,
    private readonly adminConversationService: AdminConversationService,
  ) {}

  /**
   * Get or create admin conversation and ensure admin participant tracking
   */
  async getOrCreateAdminConversationWithParticipant(adminId: string, userId: string): Promise<{
    conversation: AdminConversation;
    isNewParticipant: boolean;
  }> {
    try {
      // Validate admin user
      const admin = await this.userRepository.findOne({ where: { id: adminId } });
      if (!admin || admin.type !== UserType.ADMIN) {
        throw new BadRequestException('Only admins can access admin conversations');
      }

      // Get or create admin conversation
      const conversation = await this.adminConversationService.getOrCreateAdminConversation(userId);

      // Ensure admin is tracked as participant
      const isNewParticipant = await this.ensureAdminParticipant(conversation.id, adminId);

      return { conversation, isNewParticipant };
    } catch (error) {
      this.logger.error(`Error getting/creating admin conversation: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Ensure admin is properly tracked as participant
   */
  async ensureAdminParticipant(conversationId: string, adminId: string): Promise<boolean> {
    try {
      const existingParticipant = await this.adminConversationParticipantRepository.findOne({
        where: { conversationId, adminId },
      });

      if (!existingParticipant) {
        const adminParticipant = this.adminConversationParticipantRepository.create({
          conversationId,
          adminId,
          isActive: true,
          lastAccessedAt: getCurrentUTCDate(),
          unreadCount: 0,
        });

        await this.adminConversationParticipantRepository.save(adminParticipant);
        this.logger.log(`Added admin ${adminId} as participant in conversation ${conversationId}`);
        return true;
      } else if (!existingParticipant.isActive) {
        // Reactivate if inactive
        await this.adminConversationParticipantRepository.update(
          { conversationId, adminId },
          { isActive: true, lastAccessedAt: getCurrentUTCDate() },
        );
        this.logger.log(`Reactivated admin ${adminId} in conversation ${conversationId}`);
        return true;
      }

      return false;
    } catch (error) {
      this.logger.error(`Error ensuring admin participant: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get all active admin participants for a conversation
   */
  async getActiveAdminParticipants(conversationId: string): Promise<AdminConversationParticipant[]> {
    return await this.adminConversationParticipantRepository.find({
      where: { conversationId, isActive: true },
      relations: ['admin'],
    });
  }

  /**
   * Update admin participant's last accessed time and reset unread count
   */
  async markAdminParticipantAsRead(conversationId: string, adminId: string): Promise<void> {
    try {
      await this.adminConversationParticipantRepository.update(
        { conversationId, adminId },
        {
          lastAccessedAt: getCurrentUTCDate(),
          unreadCount: 0,
        },
      );

      this.logger.log(`Marked admin ${adminId} as read in conversation ${conversationId}`);
    } catch (error) {
      this.logger.error(`Error marking admin participant as read: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Increment unread count for admin participants (except sender)
   */
  async incrementUnreadCountForAdminParticipants(conversationId: string, senderAdminId?: string): Promise<void> {
    try {
      const query = this.adminConversationParticipantRepository
        .createQueryBuilder()
        .update(AdminConversationParticipant)
        .set({ unreadCount: () => 'unread_count + 1' })
        .where('conversation_id = :conversationId', { conversationId })
        .andWhere('is_active = true');

      if (senderAdminId) {
        query.andWhere('admin_id != :senderAdminId', { senderAdminId });
      }

      await query.execute();

      this.logger.log(`Incremented unread count for admin participants in conversation ${conversationId}`);
    } catch (error) {
      this.logger.error(`Error incrementing unread count for admin participants: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get admin conversations with participant information for an admin user
   */
  async getAdminConversationsForAdmin(adminId: string, page: number = 1, limit: number = 10) {
    try {
      const skip = (page - 1) * limit;

      const [participantRecords, total] = await this.adminConversationParticipantRepository.findAndCount({
        where: { adminId, isActive: true },
        relations: ['conversation', 'conversation.user'],
        order: { 
          conversation: { lastMessageAt: 'DESC' },
          lastAccessedAt: 'DESC' 
        },
        skip,
        take: limit,
      });

      const conversations = participantRecords.map(participant => ({
        ...participant.conversation,
        adminUnreadCount: participant.unreadCount,
        lastAccessedAt: participant.lastAccessedAt,
      }));

      return { conversations, total, page, limit };
    } catch (error) {
      this.logger.error(`Error getting admin conversations for admin ${adminId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Validate admin access to conversation
   * Automatically creates admin participant if admin is valid and conversation exists
   */
  async validateAdminAccess(adminId: string, conversationId: string): Promise<boolean> {
    try {
      const admin = await this.userRepository.findOne({ where: { id: adminId } });
      if (!admin || admin.type !== UserType.ADMIN) {
        return false;
      }

      // Check if conversation exists
      const conversation = await this.adminConversationRepository.findOne({
        where: { id: conversationId },
      });
      if (!conversation) {
        return false;
      }

      // Check if admin participant exists
      let participant = await this.adminConversationParticipantRepository.findOne({
        where: { conversationId, adminId, isActive: true },
      });

      // If no active participant found, create one (auto-enroll admin)
      if (!participant) {
        await this.ensureAdminParticipant(conversationId, adminId);
        participant = await this.adminConversationParticipantRepository.findOne({
          where: { conversationId, adminId, isActive: true },
        });
      }

      return !!participant;
    } catch (error) {
      this.logger.error(`Error validating admin access: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Remove admin from conversation (deactivate)
   */
  async removeAdminFromConversation(conversationId: string, adminId: string): Promise<void> {
    try {
      await this.adminConversationParticipantRepository.update(
        { conversationId, adminId },
        { isActive: false },
      );

      this.logger.log(`Removed admin ${adminId} from conversation ${conversationId}`);
    } catch (error) {
      this.logger.error(`Error removing admin from conversation: ${error.message}`, error.stack);
      throw error;
    }
  }
}