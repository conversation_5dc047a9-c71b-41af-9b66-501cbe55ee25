import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveOldQ<PERSON>ubmissionFields1748318319483 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // Remove the old tracking columns
        await queryRunner.query(`
            ALTER TABLE "qa_submission"
            DROP COLUMN IF EXISTS "is_first_revision",
            DROP COLUMN IF EXISTS "first_submitted_at",
            DROP COLUMN IF EXISTS "last_submitted_at";
        `);

        await queryRunner.query(`
            ALTER TABLE "qa_task_submissions"
            DROP COLUMN IF EXISTS "is_first_revision",
            DROP COLUMN IF EXISTS "first_submitted_at",
            DROP COLUMN IF EXISTS "last_submitted_at";
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Add back the old tracking columns 
        await queryRunner.query(`
            ALTER TABLE "qa_submission"
            ADD COLUMN IF NOT EXISTS "is_first_revision" boolean DEFAULT true,
            ADD COLUMN IF NOT EXISTS "first_submitted_at" TIMESTAMP,
            ADD COLUMN IF NOT EXISTS "last_submitted_at" TIMESTAMP;
        `);

        await queryRunner.query(`
            ALTER TABLE "qa_task_submissions"
            ADD COLUMN IF NOT EXISTS "is_first_revision" boolean DEFAULT true,
            ADD COLUMN IF NOT EXISTS "first_submitted_at" TIMESTAMP,
            ADD COLUMN IF NOT EXISTS "last_submitted_at" TIMESTAMP;
        `);
    }
}
