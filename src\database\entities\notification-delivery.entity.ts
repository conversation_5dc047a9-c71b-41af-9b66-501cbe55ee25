import { Entity, Column, <PERSON>To<PERSON>ne, JoinColumn } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { Notification } from './notification.entity';

/**
 * Delivery channels for notifications
 * @enum {string}
 */
export enum NotificationChannel {
  /** In-app notification */
  IN_APP = 'in_app',
  /** Email notification */
  EMAIL = 'email',
  /** Push notification */
  PUSH = 'push',
  /** Real-time messaging */
  REALTIME_MESSAGE = 'realtime_message',
}

/**
 * Status of notification delivery
 * @enum {string}
 */
export enum DeliveryStatus {
  /** Notification is pending delivery */
  PENDING = 'pending',
  /** Notification has been sent */
  SENT = 'sent',
  /** Notification delivery failed */
  FAILED = 'failed',
  /** Notification delivery failed permanently */
  FAILED_PERMANENT = 'failed_permanent',
  /** Notification is scheduled for retry */
  RETRY_SCHEDULED = 'retry_scheduled',
  /** Notification has been delivered and read */
  READ = 'read',
}

@Entity()
export class NotificationDelivery extends AuditableBaseEntity {
  @Column({ name: 'notification_id' })
  notificationId: string;

  @ManyToOne(() => Notification, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'notification_id' })
  notification: Notification;

  @Column({
    name: 'channel',
    type: 'enum',
    enum: NotificationChannel,
  })
  channel: NotificationChannel;

  @Column({
    name: 'status',
    type: 'enum',
    enum: DeliveryStatus,
    default: DeliveryStatus.PENDING,
  })
  status: DeliveryStatus;

  @Column({ name: 'sent_at', nullable: true })
  sentAt: Date;

  @Column({ name: 'error_message', nullable: true, type: 'text' })
  errorMessage: string;

  @Column({ name: 'error_code', nullable: true })
  errorCode: string;

  @Column({ name: 'error_details', nullable: true, type: 'text' })
  errorDetails: string;

  @Column({ name: 'retry_count', default: 0 })
  retryCount: number;

  @Column({ name: 'max_retries', default: 3 })
  maxRetries: number;

  @Column({ name: 'next_retry_at', nullable: true })
  nextRetryAt: Date;

  @Column({ name: 'last_retry_at', nullable: true })
  lastRetryAt: Date;

  @Column({ name: 'retry_strategy', nullable: true, default: 'exponential' })
  retryStrategy: string;

  @Column({ name: 'priority', default: 1 })
  priority: number;

  @Column({ name: 'payload', nullable: true, type: 'text' })
  payload: string;

  /**
   * Check if this delivery can be retried
   * @returns True if the delivery can be retried, false otherwise
   */
  canRetry(): boolean {
    return this.status === DeliveryStatus.FAILED && this.retryCount < this.maxRetries;
  }

  /**
   * Mark this delivery as permanently failed
   * @param errorMessage Error message
   * @param errorCode Error code
   * @param errorDetails Error details
   */
  markAsPermanentlyFailed(errorMessage: string, errorCode?: string, errorDetails?: string): void {
    this.status = DeliveryStatus.FAILED_PERMANENT;
    this.errorMessage = errorMessage;
    if (errorCode) this.errorCode = errorCode;
    if (errorDetails) this.errorDetails = errorDetails;
    this.nextRetryAt = null;
  }

  /**
   * Schedule this delivery for retry
   * @param nextRetryAt When to retry
   * @param errorMessage Error message
   * @param errorCode Error code
   * @param errorDetails Error details
   */
  scheduleForRetry(nextRetryAt: Date, errorMessage: string, errorCode?: string, errorDetails?: string): void {
    this.status = DeliveryStatus.RETRY_SCHEDULED;
    this.nextRetryAt = nextRetryAt;
    this.retryCount++;
    this.errorMessage = errorMessage;
    if (errorCode) this.errorCode = errorCode;
    if (errorDetails) this.errorDetails = errorDetails;
  }
}
