import { Test, TestingModule } from '@nestjs/testing';
import { EmailService } from './email.service';
import { EmailTemplateService } from './email-template.service';
import { ConfigService } from '@nestjs/config';
import { LoggerService } from './logger.service';

describe('EmailService', () => {
  let service: EmailService;
  let mockEmailTemplateService: jest.Mocked<EmailTemplateService>;
  let mockConfigService: jest.Mocked<ConfigService>;
  let mockLoggerService: jest.Mocked<LoggerService>;

  beforeEach(async () => {
    const mockEmailTemplate = {
      generateTemplate: jest.fn(),
      getTemplate: jest.fn(),
    };

    const mockConfig = {
      get: jest.fn(),
    };

    const mockLogger = {
      log: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EmailService,
        {
          provide: EmailTemplateService,
          useValue: mockEmailTemplate,
        },
        {
          provide: ConfigService,
          useValue: mockConfig,
        },
        {
          provide: LoggerService,
          useValue: mockLogger,
        },
      ],
    }).compile();

    service = module.get<EmailService>(EmailService);
    mockEmailTemplateService = module.get(EmailTemplateService);
    mockConfigService = module.get(ConfigService);
    mockLoggerService = module.get(LoggerService);
  });

  describe('Core Functionality', () => {
    it('should be defined', () => {
      expect(service).toBeDefined();
    });

    it('should send email successfully', async () => {
      const emailData = {
        to: '<EMAIL>',
        subject: 'Test Subject',
        template: 'welcome',
        data: { name: 'Test User' },
      };

      mockConfigService.get.mockReturnValue('smtp://localhost:587');
      mockEmailTemplateService.generateTemplate.mockResolvedValue('<html>Test</html>');

      const result = await service.sendEmail(emailData);
      
      expect(result).toBeTruthy();
      expect(mockEmailTemplateService.generateTemplate).toHaveBeenCalledWith(
        emailData.template,
        emailData.data
      );
    });

    it('should handle email sending failure', async () => {
      const emailData = {
        to: 'invalid@email',
        subject: 'Test Subject',
        template: 'welcome',
        data: {},
      };

      mockEmailTemplateService.generateTemplate.mockRejectedValue(new Error('Template error'));

      await expect(service.sendEmail(emailData)).rejects.toThrow();
      expect(mockLoggerService.error).toHaveBeenCalled();
    });
  });

  describe('Security & Validation', () => {
    it('should validate email addresses', () => {
      expect(service.validateEmail('<EMAIL>')).toBe(true);
      expect(service.validateEmail('invalid-email')).toBe(false);
      expect(service.validateEmail('')).toBe(false);
      expect(service.validateEmail(null)).toBe(false);
    });

    it('should sanitize email content', () => {
      const maliciousContent = '<script>alert("xss")</script>Hello';
      const sanitized = service.sanitizeContent(maliciousContent);
      
      expect(sanitized).not.toContain('<script>');
      expect(sanitized).toContain('Hello');
    });

    it('should prevent email injection', () => {
      const maliciousSubject = 'Test\nBCC: <EMAIL>';
      const sanitized = service.sanitizeSubject(maliciousSubject);
      
      expect(sanitized).not.toContain('\n');
      expect(sanitized).not.toContain('BCC:');
    });
  });

  describe('Edge Cases', () => {
    it('should handle null/undefined inputs', async () => {
      await expect(service.sendEmail(null)).rejects.toThrow();
      await expect(service.sendEmail(undefined)).rejects.toThrow();
    });

    it('should handle empty recipient list', async () => {
      const emailData = {
        to: '',
        subject: 'Test',
        template: 'welcome',
        data: {},
      };

      await expect(service.sendEmail(emailData)).rejects.toThrow();
    });

    it('should handle template not found', async () => {
      const emailData = {
        to: '<EMAIL>',
        subject: 'Test',
        template: 'non-existent',
        data: {},
      };

      mockEmailTemplateService.generateTemplate.mockRejectedValue(
        new Error('Template not found')
      );

      await expect(service.sendEmail(emailData)).rejects.toThrow();
    });
  });
});