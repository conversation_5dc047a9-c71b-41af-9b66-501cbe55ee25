import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddAuditColumnsToTranscriptionTables1748100000000 implements MigrationInterface {
  name = 'AddAuditColumnsToTranscriptionTables1748100000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add audit columns to books table
    await queryRunner.query(`
      ALTER TABLE "books" 
      ADD COLUMN "created_by" character varying(36),
      ADD COLUMN "updated_by" character varying(36)
    `);

    // Add audit columns to sentences table
    await queryRunner.query(`
      ALTER TABLE "sentences" 
      ADD COLUMN "created_by" character varying(36),
      ADD COLUMN "updated_by" character varying(36)
    `);

    // Add audit columns to transcription_sessions table
    await queryRunner.query(`
      ALTER TABLE "transcription_sessions" 
      ADD COLUMN "created_by" character varying(36),
      ADD COLUMN "updated_by" character varying(36)
    `);

    // Add audit columns to transcription_attempts table
    await queryRunner.query(`
      ALTER TABLE "transcription_attempts" 
      ADD COLUMN "created_by" character varying(36),
      ADD COLUMN "updated_by" character varying(36)
    `);

    // Rename timestamp columns to match audit pattern
    await queryRunner.query(`
      ALTER TABLE "books" 
      RENAME COLUMN "createdAt" TO "created_at"
    `);

    await queryRunner.query(`
      ALTER TABLE "books" 
      RENAME COLUMN "updatedAt" TO "updated_at"
    `);

    await queryRunner.query(`
      ALTER TABLE "sentences" 
      RENAME COLUMN "createdAt" TO "created_at"
    `);

    await queryRunner.query(`
      ALTER TABLE "sentences" 
      RENAME COLUMN "updatedAt" TO "updated_at"
    `);

    await queryRunner.query(`
      ALTER TABLE "transcription_sessions" 
      RENAME COLUMN "createdAt" TO "created_at"
    `);

    await queryRunner.query(`
      ALTER TABLE "transcription_sessions" 
      RENAME COLUMN "updatedAt" TO "updated_at"
    `);

    await queryRunner.query(`
      ALTER TABLE "transcription_attempts" 
      RENAME COLUMN "createdAt" TO "created_at"
    `);

    await queryRunner.query(`
      ALTER TABLE "transcription_attempts" 
      RENAME COLUMN "updatedAt" TO "updated_at"
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Rename columns back
    await queryRunner.query(`
      ALTER TABLE "transcription_attempts" 
      RENAME COLUMN "created_at" TO "createdAt"
    `);

    await queryRunner.query(`
      ALTER TABLE "transcription_attempts" 
      RENAME COLUMN "updated_at" TO "updatedAt"
    `);

    await queryRunner.query(`
      ALTER TABLE "transcription_sessions" 
      RENAME COLUMN "created_at" TO "createdAt"
    `);

    await queryRunner.query(`
      ALTER TABLE "transcription_sessions" 
      RENAME COLUMN "updated_at" TO "updatedAt"
    `);

    await queryRunner.query(`
      ALTER TABLE "sentences" 
      RENAME COLUMN "created_at" TO "createdAt"
    `);

    await queryRunner.query(`
      ALTER TABLE "sentences" 
      RENAME COLUMN "updated_at" TO "updatedAt"
    `);

    await queryRunner.query(`
      ALTER TABLE "books" 
      RENAME COLUMN "created_at" TO "createdAt"
    `);

    await queryRunner.query(`
      ALTER TABLE "books" 
      RENAME COLUMN "updated_at" TO "updatedAt"
    `);

    // Remove audit columns
    await queryRunner.query(`ALTER TABLE "transcription_attempts" DROP COLUMN "updated_by"`);
    await queryRunner.query(`ALTER TABLE "transcription_attempts" DROP COLUMN "created_by"`);
    await queryRunner.query(`ALTER TABLE "transcription_sessions" DROP COLUMN "updated_by"`);
    await queryRunner.query(`ALTER TABLE "transcription_sessions" DROP COLUMN "created_by"`);
    await queryRunner.query(`ALTER TABLE "sentences" DROP COLUMN "updated_by"`);
    await queryRunner.query(`ALTER TABLE "sentences" DROP COLUMN "created_by"`);
    await queryRunner.query(`ALTER TABLE "books" DROP COLUMN "updated_by"`);
    await queryRunner.query(`ALTER TABLE "books" DROP COLUMN "created_by"`);
  }
}