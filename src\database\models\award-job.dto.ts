import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString, IsNumber, IsDateString } from 'class-validator';
import { AwardJobType, AwardJobStatus } from '../entities/award-job.entity';

export class TriggerAwardJobDto {
  @ApiProperty({ enum: AwardJobType, description: 'Type of award job to trigger' })
  @IsEnum(AwardJobType)
  jobType: AwardJobType;

  @ApiProperty({ required: false, description: 'Admin notes for manual trigger' })
  @IsOptional()
  @IsString()
  notes?: string;
}

export class AwardJobResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty({ enum: AwardJobType })
  jobType: AwardJobType;

  @ApiProperty({ enum: AwardJobStatus })
  status: AwardJobStatus;

  @ApiProperty({ required: false })
  nextRunAt?: Date;

  @ApiProperty({ required: false })
  lastRunAt?: Date;

  @ApiProperty({ required: false })
  startedAt?: Date;

  @ApiProperty({ required: false })
  completedAt?: Date;

  @ApiProperty()
  processedCount: number;

  @ApiProperty({ required: false })
  errorMessage?: string;

  @ApiProperty({ required: false })
  executionTimeMs?: number;

  @ApiProperty({ required: false })
  triggeredBy?: string;

  @ApiProperty({ required: false })
  summary?: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}

export class AwardJobStatsDto {
  @ApiProperty()
  totalJobs: number;

  @ApiProperty()
  completedJobs: number;

  @ApiProperty()
  failedJobs: number;

  @ApiProperty()
  runningJobs: number;

  @ApiProperty()
  averageExecutionTime: number;

  @ApiProperty({ required: false })
  nextScheduledRun?: Date;

  @ApiProperty()
  lastSuccessfulRun?: Date;

  @ApiProperty()
  successRate: number;

  @ApiProperty({ type: [AwardJobResponseDto] })
  recentJobs: AwardJobResponseDto[];

  @ApiProperty({ type: [AwardJobResponseDto] })
  currentlyRunning: AwardJobResponseDto[];
}

export class AwardJobFilterDto {
  @ApiProperty({ enum: AwardJobType, required: false })
  @IsOptional()
  @IsEnum(AwardJobType)
  jobType?: AwardJobType;

  @ApiProperty({ enum: AwardJobStatus, required: false })
  @IsOptional()
  @IsEnum(AwardJobStatus)
  status?: AwardJobStatus;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsDateString()
  endDate?: string;
}