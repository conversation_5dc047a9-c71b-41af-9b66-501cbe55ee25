# Comprehensive API Testing Flows

This document provides comprehensive testing flows for all HEC backend API modules, covering complete user journeys and minimum test cases for each module.

## Table of Contents

1. [Authentication & User Management](#authentication--user-management)
2. [Plans & Subscriptions](#plans--subscriptions)
3. [Diary Module](#diary-module)
4. [Essay Module](#essay-module)
5. [Novel Module](#novel-module)
6. [Play Module](#play-module)
7. [Shop & Purchases](#shop--purchases)
8. [Chat System](#chat-system)
9. [QA & QA Missions](#qa--qa-missions)
10. [Awards & Hall of Fame](#awards--hall-of-fame)
11. [Notifications](#notifications)
12. [Tutor Management](#tutor-management)
13. [Dashboard & Analytics](#dashboard--analytics)
14. [File Management](#file-management)
15. [Payment System](#payment-system)
16. [Promotions & Categories](#promotions--categories)

---

## Authentication & User Management

### User Registration Journey
**Endpoint**: `POST /api/auth/register`

**Test Flow**:
1. **Valid Registration**
   - Submit complete registration data
   - Verify 201 status and user creation
   - Check email verification sent
   
2. **Email Verification**
   - Use verification token from email/logs
   - `GET /api/auth/verify-email?token={token}`
   - Verify account activation

3. **Validation Tests**
   - Missing required fields
   - Invalid email format
   - Weak password
   - Terms not agreed
   - Duplicate userId/email

### Login & Session Management
**Endpoints**: `POST /api/auth/login`, `POST /api/auth/refresh-token`

**Test Flow**:
1. **Successful Login**
   - Valid credentials → access_token + refresh_token
   - Test with rememberMe=true for extended session
   
2. **Token Management**
   - Use access token for protected endpoints
   - Refresh expired tokens
   - Logout and invalidate tokens

3. **Security Tests**
   - Invalid credentials
   - Brute force protection
   - Concurrent sessions

### Password Management
**Endpoints**: `POST /api/auth/forgot-password`, `POST /api/auth/reset-password`

**Test Flow**:
1. Request password reset with valid email
2. Use reset token to set new password
3. Verify login with new password
4. Test invalid/expired tokens

### Profile Management
**Endpoints**: `GET /api/users/profile`, `PUT /api/users/profile`

**Test Flow**:
1. **Profile Retrieval**
   - Get current user profile
   - Verify all fields present
   
2. **Profile Updates**
   - Update name, phone, bio
   - Upload profile picture
   - Test validation rules

---

## Plans & Subscriptions

### Plan Discovery
**Endpoint**: `GET /api/plans`

**Test Flow**:
1. Get all available plans
2. Verify plan features and pricing
3. Test filtering by plan type

### Subscription Management
**Endpoints**: `POST /api/plans/subscribe`, `GET /api/plans/my-subscription`

**Test Flow**:
1. **Free Plan Subscription**
   - Subscribe to free plan
   - Verify immediate activation
   
2. **Paid Plan Subscription**
   - Initiate paid subscription
   - Complete payment flow
   - Verify feature access

3. **Subscription Status**
   - Check current subscription
   - Verify feature entitlements
   - Test subscription expiry

---

## Diary Module

### Diary Entry Lifecycle
**Base Endpoints**: `/api/diary/entries`

**Complete User Journey**:

1. **Entry Creation**
   ```
   POST /api/diary/entries
   {
     "title": "My First Entry",
     "content": "Today I learned...",
     "entryDate": "2024-01-15",
     "skinId": "uuid",
     "isPrivate": false
   }
   ```

2. **Draft Management**
   - Update entry multiple times
   - Auto-save functionality
   - Retrieve draft content

3. **Submission Flow**
   ```
   POST /api/diary/entries/{id}/submit
   ```
   - Entry status: NEW → SUBMIT
   - Tutor assignment notification

4. **Tutor Review Process**
   ```
   POST /api/diary/entries/{id}/start-review  (Tutor)
   POST /api/diary/entries/{id}/corrections   (Tutor)
   POST /api/diary/entries/{id}/confirm       (Tutor)
   ```

5. **Student Confirmation**
   - Review tutor feedback
   - Confirm understanding
   - Entry status: REVIEWED → CONFIRM

### Diary Skins & Customization
**Endpoints**: `/api/diary/skins`, `/api/diary/settings`

**Test Flow**:
1. Get available skins
2. Set default skin preference
3. Apply skin to specific entry
4. Test custom background colors

### Diary Sharing
**Endpoints**: `/api/diary/entries/{id}/share`

**Test Flow**:
1. Generate share link/QR code
2. Access shared entry without auth
3. Test privacy settings
4. Verify share analytics

---

## Essay Module

### Essay Mission System
**Base Endpoints**: `/api/student-essay`

**Complete User Journey**:

1. **Mission Discovery**
   ```
   GET /api/student-essay/getMission
   ```
   - Browse available essay missions
   - Filter by difficulty/topic

2. **Task Initiation**
   ```
   POST /api/student-essay/start/task
   {
     "taskId": "uuid"
   }
   ```

3. **Essay Writing Process**
   ```
   POST /api/student-essay/submit/essay/update
   {
     "submissionId": "uuid",
     "content": "Essay content...",
     "title": "Essay Title",
     "skinId": "uuid"
   }
   ```

4. **Final Submission**
   ```
   POST /api/student-essay/submit/essay
   ```

5. **Review & Feedback**
   - Tutor evaluation
   - Grade assignment
   - Feedback delivery

### Essay Management
**Test Flow**:
1. **Active Essays**
   - `GET /api/student-essay/activeEssay`
   - Resume writing session
   
2. **Essay History**
   - `GET /api/student-essay/myEssays`
   - View past submissions
   - Access specific essay details

---

## Novel Module

### Novel Writing Journey
**Base Endpoints**: `/api/student/novel`

**Complete User Journey**:

1. **Topic Selection**
   ```
   GET /api/student/novel/topics?category=monthly
   ```
   - Browse monthly/quarterly topics
   - Select writing theme

2. **Entry Creation/Retrieval**
   ```
   GET /api/student/novel/entries/topic/{topicId}
   ```
   - Auto-create entry if not exists
   - Resume existing entry

3. **Writing Process**
   ```
   PUT /api/student/novel/entries/{id}
   {
     "content": "Chapter content...",
     "skinId": "uuid",
     "backgroundColor": "#f5f5f5"
   }
   ```

4. **Submission for Review**
   ```
   POST /api/student/novel/entries/submit
   {
     "entryId": "uuid",
     "content": "Final content..."
   }
   ```

5. **Version History**
   - `GET /api/student/novel/entries/{id}/history`
   - `PUT /api/student/novel/entries/{id}/versions/{versionId}/restore`

### Novel Customization
**Test Flow**:
1. **Skin Management**
   - Get available skins
   - Set default novel skin
   - Apply skin to entry

2. **Tutor Greeting**
   ```
   POST /api/student/novel/greeting
   {
     "greeting": "Hello, please review my novel!"
   }
   ```

### Novel Suggestions
**Test Flow**:
1. **Create Suggestion**
   ```
   POST /api/student/novel/suggestions
   {
     "description": "A story about magical creatures..."
   }
   ```

2. **View Suggestions**
   - `GET /api/student/novel/suggestions`

---

## Play Module

### Story Maker Game
**Base Endpoints**: `/api/play/story-maker/play`

**Complete User Journey**:

1. **Game Discovery**
   ```
   GET /api/play/story-maker/play/list
   ```
   - Browse available story games
   - Check completion status

2. **Game Details**
   ```
   GET /api/play/story-maker/play/{id}
   ```
   - Get story prompt and image
   - Check if already played

3. **Story Writing**
   ```
   PUT /api/play/story-maker/play/{id}
   {
     "content": "Story content..."
   }
   ```
   - Draft management
   - Auto-save functionality

4. **Story Submission**
   ```
   POST /api/play/story-maker/play/{id}/submit
   {
     "content": "Final story content..."
   }
   ```
   - AI evaluation triggered
   - Scoring calculation

5. **Results & Sharing**
   - View evaluation results
   - Check scoring breakdown
   - Browse shared stories

### Story Interaction
**Test Flow**:
1. **Like System**
   ```
   POST /api/play/story-maker/play/submissions/{id}/like
   DELETE /api/play/story-maker/play/submissions/{id}/like
   GET /api/play/story-maker/play/submissions/{id}/likes
   ```

2. **Popularity Tracking**
   - `GET /api/play/story-maker/play/submissions/{id}/popularity`

### Block Game
**Base Endpoints**: `/api/play/block`

**Test Flow**:
1. **Get Random Game**
   ```
   GET /api/play/block/play
   ```

2. **Submit Solution**
   ```
   POST /api/play/block/submit
   {
     "gameId": "uuid",
     "sentences": [
       {
         "blocks": ["The", "cat", "sat", "on", "the", "mat"],
         "sentence": "The cat sat on the mat"
       }
     ]
   }
   ```

---

## Shop & Purchases

### Shop Browsing
**Base Endpoints**: `/api/shop`

**Complete User Journey**:

1. **Category Exploration**
   ```
   GET /api/shop/categories
   GET /api/shop/categories/{id}/items
   ```

2. **Item Discovery**
   ```
   GET /api/shop/items
   GET /api/shop/items/available
   GET /api/shop/items/with-promotions
   ```

3. **Item Details**
   ```
   GET /api/shop/items/{id}
   GET /api/shop/items/{id}/can-access
   ```

### Purchase Flow
**Test Flow**:
1. **Free Item Acquisition**
   ```
   POST /api/shop/purchase
   {
     "shopItemId": "uuid",
     "paymentMethod": "FREE"
   }
   ```

2. **Paid Item Purchase**
   ```
   POST /api/shop/purchase
   {
     "shopItemId": "uuid",
     "paymentMethod": "REWARD_POINTS",
     "quantity": 1
   }
   ```

3. **Purchase History**
   ```
   GET /api/shop/purchases
   GET /api/shop/purchases/{id}
   ```

### Item Usage
**Test Flow**:
1. **Skin Application**
   ```
   POST /api/shop/items/{id}/apply-skin
   ```

2. **File Access**
   - Verify secure file URLs
   - Test download permissions

---

## Chat System

### Conversation Management
**Base Endpoints**: `/api/chat`

**Complete User Journey**:

1. **Contact Discovery**
   ```
   GET /api/chat/contacts
   ```

2. **Conversation Creation**
   ```
   POST /api/chat/messages
   {
     "recipientId": "uuid",
     "content": "Hello!",
     "type": "TEXT"
   }
   ```

3. **Message Exchange**
   ```
   GET /api/chat/conversations
   GET /api/chat/conversations/{id}/messages
   ```

4. **File Sharing**
   ```
   POST /api/chat/upload (multipart/form-data)
   GET /api/chat/files/{id}
   ```

5. **Message Status**
   ```
   POST /api/chat/messages/{id}/read
   ```

### Chat Features
**Test Flow**:
1. **Message Types**
   - Text messages
   - File attachments
   - System messages

2. **Real-time Updates**
   - WebSocket connections
   - Message delivery status
   - Typing indicators

---

## Awards & Hall of Fame

### Award Management System
**Base Endpoints**: `/api/awards`

**Complete User Journey**:

1. **Award Discovery (Student)**
   ```
   GET /api/awards/available
   GET /api/awards/criteria
   GET /api/awards/criteria?module=diary
   ```

2. **Personal Awards**
   ```
   GET /api/awards/my-awards
   GET /api/awards/my-points
   ```

3. **Admin Award Management**
   ```
   POST /api/awards
   {
     "name": "Diary Excellence Award",
     "description": "Outstanding diary entries",
     "module": "diary",
     "criteria": "diary_score",
     "frequency": "monthly",
     "rewardPoints": 100
   }
   ```

4. **Award Winner Management**
   ```
   POST /api/awards/winners
   {
     "userId": "uuid",
     "awardId": "uuid",
     "period": "2024-01",
     "score": 95.5
   }
   ```

5. **Reward Points System**
   ```
   POST /api/awards/reward-points
   GET /api/awards/reward-points/{userId}
   ```

### Hall of Fame System
**Base Endpoints**: `/api/hall-of-fame`

**Test Flow**:
1. **Module-Specific Hall of Fame**
   ```
   GET /api/hall-of-fame/diary?limit=50
   GET /api/hall-of-fame/novel?limit=25
   GET /api/hall-of-fame/essay?limit=30
   ```

2. **Ongoing Awards**
   ```
   GET /api/hall-of-fame/diary/ongoing?frequency=weekly
   GET /api/hall-of-fame/novel/ongoing?frequency=monthly
   GET /api/hall-of-fame/essay/ongoing?frequency=yearly
   ```

3. **Award Criteria Types**
   - Diary: score-based, completion-based, consistency awards
   - Novel: creativity, length, quality awards
   - Essay: analytical, research, writing quality awards
   - Play: game performance, creativity, participation awards

---

## Notifications

### Notification Management
**Base Endpoints**: `/api/notifications`

**Complete User Journey**:

1. **Notification Retrieval**
   ```
   GET /api/notifications
   GET /api/notifications/unread-count
   ```

2. **Notification Actions**
   ```
   POST /api/notifications/{id}/mark-read
   POST /api/notifications/mark-all-read
   ```

3. **Preferences**
   ```
   GET /api/notifications/preferences
   PUT /api/notifications/preferences
   ```

### Notification Types
**Test Flow**:
1. **System Notifications**
   - Diary review completed
   - Essay graded
   - Award received

2. **Communication Notifications**
   - New chat message
   - Tutor assignment
   - Friend request

---

## QA & QA Missions

### QA Assignment System
**Base Endpoints**: `/api/qa`

**Complete User Journey**:

1. **Assignment Discovery**
   ```
   GET /api/qa/latest-assignments/{id}
   ```
   - Get latest QA assignment for student
   - View questions and requirements

2. **Answer Submission Process**
   ```
   POST /api/qa/submissions
   {
     "assignmentId": "uuid",
     "answers": [
       {
         "questionId": "q1",
         "answer": "Detailed answer text",
         "confidence": 4
       }
     ],
     "isDraft": true
   }
   ```

3. **Final Submission**
   ```
   PUT /api/qa/submissions/{id}/submit
   ```

### QA Mission System
**Base Endpoints**: `/api/student-qa-mission`

**Complete User Journey**:

1. **Mission Discovery**
   ```
   GET /api/student-qa-mission/getMissionList
   ```
   - Browse available QA missions
   - Filter by difficulty and topic

2. **Task Execution**
   ```
   POST /api/student-qa-mission/start/task
   {
     "taskId": "uuid"
   }
   ```

3. **Content Creation**
   ```
   POST /api/student-qa-mission/submit/task
   {
     "taskId": "uuid",
     "content": "QA response content",
     "wordCount": 150,
     "metaData": {
       "timeSpent": 300
     }
   }
   ```

4. **Auto-Save & Updates**
   ```
   POST /api/student-qa-mission/submit/task/update
   {
     "submissionId": "uuid",
     "content": "Updated content",
     "wordCount": 175
   }
   ```

5. **Active Task Management**
   ```
   GET /api/student-qa-mission/activeTask
   GET /api/student-qa-mission/task/{id}
   ```

---

## Tutor Management

### Tutor Assignment
**Base Endpoints**: `/api/tutor-matching`

**Test Flow**:
1. **Automatic Assignment**
   - Diary submission triggers assignment
   - Load balancing verification

2. **Manual Assignment**
   ```
   POST /api/admin/tutor-matching/assign
   {
     "studentId": "uuid",
     "tutorId": "uuid"
   }
   ```

### Tutor Permissions
**Base Endpoints**: `/api/tutor-permissions`

**Test Flow**:
1. **Permission Management**
   ```
   GET /api/tutor-permissions
   POST /api/tutor-permissions/request
   ```

---

## Dashboard & Analytics

### Student Dashboard
**Base Endpoints**: `/api/dashboard`

**Test Flow**:
1. **Progress Overview**
   ```
   GET /api/dashboard/student/overview
   ```
   - Writing statistics
   - Award progress
   - Recent activities

2. **Performance Metrics**
   - Completion rates
   - Score trends
   - Time spent writing

### Admin Dashboard
**Test Flow**:
1. **System Metrics**
   ```
   GET /api/dashboard/admin/metrics
   ```
   - User activity
   - Content statistics
   - System health

---

## File Management

### File Upload & Access
**Base Endpoints**: `/api/media`

**Test Flow**:
1. **File Upload**
   ```
   POST /api/media/upload (multipart/form-data)
   ```
   - Profile pictures
   - Diary attachments
   - Chat files

2. **File Access**
   ```
   GET /api/media/files/{id}
   ```
   - Secure URL generation
   - Permission verification
   - CDN integration

---

## Payment System

### KCP Payment Integration
**Base Endpoints**: `/api/payment`

**Test Flow**:
1. **Payment Initiation**
   ```
   POST /api/payment/initiate
   {
     "planId": "uuid",
     "paymentMethod": "CARD",
     "amount": 29900
   }
   ```

2. **Payment Processing**
   - KCP gateway integration
   - Payment confirmation
   - Subscription activation

3. **Payment History**
   ```
   GET /api/payment/history
   GET /api/payment/status/{transactionId}
   ```

---

## Promotions & Categories

### Promotion Management
**Base Endpoints**: `/api/promotions`

**Test Flow**:
1. **Active Promotions**
   ```
   GET /api/promotions/active
   GET /api/promotions/{id}
   ```

2. **Promotion Application**
   - Discount calculations
   - Eligibility checks
   - Usage tracking

### Category Management
**Base Endpoints**: `/api/categories`

**Test Flow**:
1. **Category Hierarchy**
   ```
   GET /api/categories
   GET /api/categories/{id}/subcategories
   ```

---

## Cross-Module Integration Tests

### Complete Student Journey
**End-to-End Test Flow**:

1. **Onboarding**
   - Register → Verify Email → Login
   - Subscribe to Plan → Set Profile

2. **Content Creation**
   - Create Diary Entry → Submit → Review → Confirm
   - Start Essay Mission → Write → Submit
   - Begin Novel → Write Chapters → Submit

3. **Engagement**
   - Play Story Maker → Submit Story → Get Evaluated
   - Browse Shop → Purchase Items → Apply Skins
   - Chat with Tutors → Receive Feedback

4. **Achievement**
   - Earn Awards → View Hall of Fame
   - Check Progress → View Analytics

### Performance & Load Testing
**Critical Paths**:
1. Authentication flow under load
2. Concurrent diary submissions
3. Real-time chat performance
4. File upload/download stress tests

### Security Testing
**Security Scenarios**:
1. JWT token manipulation
2. Cross-user data access attempts
3. File access permission bypass
4. SQL injection attempts
5. XSS prevention validation

---

## Testing Environment Setup

### Prerequisites
- Valid test database
- File storage access (local/S3)
- Email service configuration
- WebSocket support

### Test Data Requirements
- Test users (students, tutors, admins)
- Sample content (diary entries, essays)
- Shop items and categories
- Notification templates

### Automation Considerations
- API response time monitoring
- Database state verification
- File cleanup procedures
- Notification delivery confirmation

---

## Minimum Test Cases Summary

### Critical Path Tests (Must Pass)
1. User registration and login
2. Diary entry creation and submission
3. Essay writing and submission
4. Shop item purchase
5. Chat message sending
6. File upload and access

### Integration Tests (High Priority)
1. Tutor assignment workflow
2. Award calculation system
3. Notification delivery
4. Payment processing
5. Real-time features

### Edge Case Tests (Medium Priority)
1. Concurrent user actions
2. Large file uploads
3. Network interruption recovery
4. Data validation boundaries
5. Permission edge cases

This comprehensive testing guide ensures complete coverage of all HEC backend functionality and user journeys.