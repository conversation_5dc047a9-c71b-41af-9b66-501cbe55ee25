import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  <PERSON><PERSON><PERSON><PERSON>,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { TimezoneService } from '../services/timezone.service';
import { JwtService } from '@nestjs/jwt';

@Injectable()
export class TimezoneInterceptor implements NestInterceptor {
  private readonly logger = new Logger(TimezoneInterceptor.name);

  constructor(
    private readonly timezoneService: TimezoneService,
    private readonly jwtService: JwtService,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    
    return next.handle().pipe(
      map(async (data) => {
        try {
          const userId = await this.extractUserIdFromRequest(request);
          if (!userId) {
            return data;
          }

          const userTimezone = await this.timezoneService.getUserTimezone(userId);
          return this.transformTimestamps(data, userTimezone);
        } catch (error) {
          this.logger.error('Error in timezone transformation:', error);
          return data;
        }
      }),
    );
  }

  private async extractUserIdFromRequest(request: any): Promise<string | null> {
    try {
      const authHeader = request.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return null;
      }

      const token = authHeader.substring(7);
      const payload = this.jwtService.verify(token);
      return payload.id || payload.sub || null;
    } catch (error) {
      return null;
    }
  }

  private transformTimestamps(obj: any, timezone: string): any {
    if (!obj || typeof obj !== 'object') {
      return obj;
    }

    if (obj instanceof Date) {
      return this.timezoneService.convertUTCToUserTimezone(obj, timezone);
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.transformTimestamps(item, timezone));
    }

    const transformed = { ...obj };
    
    // Transform common timestamp fields
    const timestampFields = [
      'createdAt', 'updatedAt', 'lastLoginAt', 'readAt', 'deliveredAt',
      'timestamp', 'scheduledAt', 'completedAt', 'startDate', 'endDate',
      'token_expires', 'refresh_token_expires', 'approvedAt', 'rejectedAt'
    ];

    for (const field of timestampFields) {
      if (transformed[field]) {
        if (transformed[field] instanceof Date) {
          transformed[field] = this.timezoneService.convertUTCToUserTimezone(
            transformed[field],
            timezone
          );
        } else if (typeof transformed[field] === 'string' && this.isISODateString(transformed[field])) {
          // Handle ISO date strings
          const date = new Date(transformed[field]);
          if (!isNaN(date.getTime())) {
            transformed[field] = this.timezoneService.convertUTCToUserTimezone(date, timezone);
          }
        }
      }
    }

    // Recursively transform nested objects
    for (const key in transformed) {
      if (transformed[key] && typeof transformed[key] === 'object') {
        transformed[key] = this.transformTimestamps(transformed[key], timezone);
      }
    }

    // Add timezone metadata
    if (obj && typeof obj === 'object' && !Array.isArray(obj)) {
      transformed._timezone = timezone;
    }

    return transformed;
  }

  private isISODateString(value: string): boolean {
    return /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/.test(value);
  }
}