import { ApiProperty } from '@nestjs/swagger';
import { WaterfallQuestionResponseDto } from './waterfall-question.dto';
import { WaterfallQuestionType } from '../../entities/tutor-waterfall-set.entity';

/**
 * DTO for waterfall set full response (including questions)
 */
export class WaterfallSetFullResponseDto {
  @ApiProperty({
    description: 'The ID of the waterfall set',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'The title of the waterfall set',
    example: 'Basic Grammar Set 1',
  })
  title: string;

  @ApiProperty({
    description: 'The total score for the waterfall set',
    example: 50,
  })
  total_score: number;

  @ApiProperty({
    description: 'The total number of questions in the set',
    example: 10,
  })
  total_questions: number;

  @ApiProperty({
    description: 'The question type for this set',
    enum: WaterfallQuestionType,
    required: false,
  })
  question_type?: WaterfallQuestionType;

  @ApiProperty({
    description: 'The date the set was created',
    example: '2023-07-25T12:34:56.789Z',
  })
  created_at: Date;

  @ApiProperty({
    description: 'The date the set was last updated',
    example: '2023-07-25T12:34:56.789Z',
  })
  updated_at: Date;

  @ApiProperty({
    description: 'Whether this set has already been played by students',
    example: true,
  })
  already_played: boolean;

  @ApiProperty({
    description: 'The questions in the set',
    type: [WaterfallQuestionResponseDto],
  })
  questions: WaterfallQuestionResponseDto[];
}

/**
 * DTO for waterfall game question
 */
export class WaterfallGameQuestionDto {
  @ApiProperty({
    description: 'The ID of the question',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'The question text in rich HTML format',
    example: '<p>The cat <span class="blank-highlight">___</span> on the mat.</p>',
  })
  question_text: string;

  @ApiProperty({
    description: 'The plain text version of the question with [[gap]] markers',
    example: 'The cat [[gap]] on the mat.',
  })
  question_text_plain: string;

  @ApiProperty({
    description: 'Array of options for the question',
    example: ['sits', 'standing', 'lying'],
  })
  options: string[];

  @ApiProperty({
    description: 'Array of correct answers for each gap',
    example: ['sat'],
  })
  correct_answers: string[];

  @ApiProperty({
    description: 'Time limit for answering this question in seconds',
    example: 30,
    required: false,
  })
  time_limit_in_seconds?: number;

  @ApiProperty({
    description: 'Difficulty level of the question as a number',
    example: 2,
    required: false,
  })
  level?: number;
}

/**
 * DTO for waterfall game response
 */
export class WaterfallGameResponseDto {
  @ApiProperty({
    description: 'The ID of the waterfall set',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'The title of the waterfall set',
    example: 'Basic Grammar Set 1',
  })
  title: string;

  @ApiProperty({
    description: 'The total score for the waterfall set',
    example: 50,
  })
  total_score: number;

  @ApiProperty({
    description: 'The total number of questions in the set',
    example: 10,
  })
  total_questions: number;

  @ApiProperty({
    description: 'The questions in the set',
    type: [WaterfallGameQuestionDto],
  })
  questions: WaterfallGameQuestionDto[];
}
