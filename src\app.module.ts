import { Module, NestModule, MiddlewareConsumer } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { UsersModule } from './modules/users/users.module';
import { AuthModule } from './modules/auth/auth.module';
import { PlansModule } from './modules/plans/plans.module';
import { DiaryModule } from './modules/diary/diary.module';
import { TutorApprovalModule } from './modules/tutor-approval/tutor-approval.module';
import { MediaModule } from './modules/media/media.module';
import { AwardsModule } from './modules/awards/awards.module';
import { PromotionsModule } from './modules/promotions/promotions.module';
import { ShopModule } from './modules/shop/shop.module';
import { NotificationModule } from './modules/notification/notification.module';
import { TutorMatchingModule } from './modules/tutor-matching/tutor-matching.module';
import { ChatModule } from './modules/chat/chat.module';
import { StudentModule } from './modules/student/student.module';
import { getTypeOrmConfig } from './config/database.config';
import { CommonModule } from './common/common.module';
import { SeedModule } from './config/seed.module';
import { DatabaseModule } from './database/database.module';
import { CurrentUserMiddleware } from './common/middleware/current-user.middleware';
import { TimezoneService } from './common/services/timezone.service';
import { TimezoneInterceptor } from './common/interceptors/timezone.interceptor';
import { SocketTimezoneTransformer } from './common/transformers/socket-timezone.transformer';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { User } from './database/entities/user.entity';
import { QAMissionModule } from './modules/qa-mission/qa-mission.module';
import { PlayModule } from './modules/play/play.module';
import { EssayModule } from './modules/essay/essay.module';
import { QAModule } from './modules/qa/qa.module';
import { PermissionsModule } from './modules/permissions/permissions.module';
import { PaymentModule } from './modules/payment/payment.module';
import { NovelModule } from './modules/novel/novel.module';
import { HallOfFameModule } from './modules/hall-of-fame/hall-of-fame.module';
import { CategoryModule } from './modules/category/category.module';
import { DashboardModule } from './modules/dashboard/dashboard.module';
import { ContactModule } from './modules/contact/contact.module';
import { TranscriptionModule } from './modules/transcription/transcription.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => getTypeOrmConfig(configService),
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([User]),
    ScheduleModule.forRoot(),
    DatabaseModule,
    DatabaseModule,
    AuthModule,
    CommonModule,
    UsersModule,
    PlansModule,
    DiaryModule,
    TutorApprovalModule,
    MediaModule,
    AwardsModule,
    PromotionsModule,
    ShopModule,
    NotificationModule,
    TutorMatchingModule,
    ChatModule,
    StudentModule,
    SeedModule,
    QAMissionModule,
    QAModule,
    EssayModule,
    NovelModule,
    PlayModule,
    PermissionsModule,
    PaymentModule,
    HallOfFameModule,
    DashboardModule,
    CategoryModule,
    ContactModule,
    TranscriptionModule,
  ],
  controllers: [],
  providers: [
    TimezoneService,
    SocketTimezoneTransformer,
    {
      provide: APP_INTERCEPTOR,
      useClass: TimezoneInterceptor,
    },
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    // Apply the CurrentUserMiddleware to all routes
    consumer.apply(CurrentUserMiddleware).forRoutes('*');
  }
}
