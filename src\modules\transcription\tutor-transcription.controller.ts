import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags, ApiOperation } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { ApiOkResponseWithArrayType } from '../../common/decorators/api-response.decorator';
import { TranscriptionSessionService } from './services/transcription-session.service';
import { TranscriptionAttemptService } from './services/transcription-attempt.service';
import { TranscriptionSessionResponseDto, TranscriptionAttemptResponseDto } from '../../database/models/transcription.dto';

@ApiTags('transcription-tutor')
@Controller('transcription/tutor')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class TutorTranscriptionController {
  constructor(
    private readonly sessionService: TranscriptionSessionService,
    private readonly attemptService: TranscriptionAttemptService,
  ) {}

  @Get('students/:id/sessions')
  @ApiOperation({ summary: 'Get student transcription sessions (Tutor only)' })
  @ApiOkResponseWithArrayType(TranscriptionSessionResponseDto, 'Student sessions retrieved successfully')
  async getStudentSessions(@Param('id') studentId: string): Promise<ApiResponse<TranscriptionSessionResponseDto[]>> {
    const result = await this.sessionService.findByStudentId(studentId);
    return ApiResponse.success(result, 'Student sessions retrieved successfully');
  }

  @Get('sessions/:id/attempts')
  @ApiOperation({ summary: 'Get session attempts for review (Tutor only)' })
  @ApiOkResponseWithArrayType(TranscriptionAttemptResponseDto, 'Session attempts retrieved successfully')
  async getSessionAttempts(@Param('id') sessionId: string): Promise<ApiResponse<TranscriptionAttemptResponseDto[]>> {
    const result = await this.attemptService.findBySessionId(sessionId);
    return ApiResponse.success(result, 'Session attempts retrieved successfully');
  }
}