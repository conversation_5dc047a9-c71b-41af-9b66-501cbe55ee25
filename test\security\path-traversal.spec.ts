import { SecureTestDataFactory } from '../fixtures/factories/secure-test-data.factory';

/**
 * Path traversal security tests to prevent file system attacks
 * Tests file path validation and access control mechanisms
 */
describe('Path Traversal Security Tests', () => {
  describe('File Path Validation', () => {
    it('should allow safe file paths', () => {
      const safePaths = SecureTestDataFactory.createSafeTestFilePaths();
      
      safePaths.forEach(path => {
        expect(() => validateSecureFilePath(path)).not.toThrow();
        expect(isPathSafe(path)).toBe(true);
      });
    });

    it('should reject dangerous file paths', () => {
      const unsafePaths = SecureTestDataFactory.createUnsafeTestFilePaths();
      
      unsafePaths.forEach(path => {
        expect(() => validateSecureFilePath(path)).toThrow();
        expect(isPathSafe(path)).toBe(false);
      });
    });

    it('should normalize paths before validation', () => {
      const pathsToNormalize = [
        './uploads/../../../etc/passwd',
        'uploads\\..\\..\\windows\\system32',
        'uploads/./../../etc/shadow',
      ];

      pathsToNormalize.forEach(path => {
        const normalized = normalizePath(path);
        expect(normalized).not.toContain('..');
        expect(() => validateSecureFilePath(normalized)).toThrow();
      });
    });
  });

  describe('Directory Traversal Prevention', () => {
    it('should prevent access to system directories', () => {
      const systemPaths = [
        '/etc/passwd',
        '/etc/shadow',
        'C:\\Windows\\System32',
        '/proc/self/environ',
        '/var/log/auth.log',
      ];

      systemPaths.forEach(path => {
        expect(isSystemPath(path)).toBe(true);
        expect(() => validateSecureFilePath(path)).toThrow('Access to system paths denied');
      });
    });

    it('should allow access only to whitelisted directories', () => {
      const allowedDirectories = [
        'uploads/',
        'profile-pictures/',
        'diary-skins/',
        'shop-items/',
      ];

      const testPaths = [
        'uploads/test-file.jpg',
        'profile-pictures/avatar.png',
        'diary-skins/skin1.jpg',
        'shop-items/item1.png',
      ];

      testPaths.forEach(path => {
        const isAllowed = allowedDirectories.some(dir => path.startsWith(dir));
        expect(isAllowed).toBe(true);
        expect(() => validateSecureFilePath(path)).not.toThrow();
      });
    });

    it('should reject paths outside allowed directories', () => {
      const disallowedPaths = [
        'config/database.config.ts',
        'src/modules/auth/auth.service.ts',
        '.env',
        'package.json',
      ];

      disallowedPaths.forEach(path => {
        expect(() => validateSecureFilePath(path)).toThrow('Path not in allowed directory');
      });
    });
  });

  describe('File Extension Validation', () => {
    it('should allow safe file extensions', () => {
      const safeExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.txt'];
      const safeFiles = safeExtensions.map(ext => `test-file${ext}`);

      safeFiles.forEach(file => {
        expect(hasAllowedExtension(file)).toBe(true);
      });
    });

    it('should reject dangerous file extensions', () => {
      const dangerousExtensions = ['.exe', '.bat', '.sh', '.php', '.js', '.html'];
      const dangerousFiles = dangerousExtensions.map(ext => `malicious${ext}`);

      dangerousFiles.forEach(file => {
        expect(hasAllowedExtension(file)).toBe(false);
        expect(() => validateSecureFilePath(file)).toThrow('File extension not allowed');
      });
    });
  });

  describe('Symbolic Link Detection', () => {
    it('should detect and reject symbolic links', () => {
      // Mock symbolic link detection
      const mockSymlinks = [
        'uploads/symlink-to-etc-passwd',
        'profile-pictures/link-to-system',
      ];

      mockSymlinks.forEach(path => {
        // Simulate symbolic link detection
        const isSymlink = mockIsSymbolicLink(path);
        if (isSymlink) {
          expect(() => validateSecureFilePath(path)).toThrow('Symbolic links not allowed');
        }
      });
    });
  });
});

// Mock validation functions for testing
function validateSecureFilePath(path: string): void {
  const normalized = normalizePath(path);
  
  if (!isPathSafe(normalized)) {
    throw new Error('Unsafe path detected');
  }
  
  if (isSystemPath(normalized)) {
    throw new Error('Access to system paths denied');
  }
  
  if (!isInAllowedDirectory(normalized)) {
    throw new Error('Path not in allowed directory');
  }
  
  if (!hasAllowedExtension(normalized)) {
    throw new Error('File extension not allowed');
  }
  
  if (mockIsSymbolicLink(normalized)) {
    throw new Error('Symbolic links not allowed');
  }
}

function isPathSafe(path: string): boolean {
  return !path.includes('..') && 
         !path.includes('/etc/') && 
         !path.includes('C:\\Windows\\') &&
         !path.startsWith('/') &&
         !path.match(/^[a-zA-Z]:\\/);
}

function normalizePath(path: string): string {
  return path.replace(/\\/g, '/').replace(/\/+/g, '/');
}

function isSystemPath(path: string): boolean {
  const systemPaths = ['/etc/', '/proc/', '/var/', 'C:\\Windows\\', '/usr/', '/bin/'];
  return systemPaths.some(sysPath => path.includes(sysPath));
}

function isInAllowedDirectory(path: string): boolean {
  const allowedDirs = ['uploads/', 'profile-pictures/', 'diary-skins/', 'shop-items/'];
  return allowedDirs.some(dir => path.startsWith(dir));
}

function hasAllowedExtension(path: string): boolean {
  const allowedExts = ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.txt'];
  return allowedExts.some(ext => path.toLowerCase().endsWith(ext));
}

function mockIsSymbolicLink(path: string): boolean {
  // Mock implementation - in real code, use fs.lstatSync
  return path.includes('symlink') || path.includes('link-to');
}