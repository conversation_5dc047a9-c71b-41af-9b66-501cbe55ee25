import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { GeminiAiService, GeminiEvaluationResult } from '../../../common/services/gemini-ai.service';
import { StoryMakerSubmission } from '../../../database/entities/story-maker-submission.entity';
import { StoryMakerEvaluation } from '../../../database/entities/story-maker-evaluation.entity';
import { StoryMakerParticipation } from '../../../database/entities/story-maker-participation.entity';
import { StoryMaker } from '../../../database/entities/story-maker.entity';
import { FileRegistryService } from '../../../common/services/file-registry.service';
import { FileEntityType } from '../../../common/enums/file-entity-type.enum';

export interface ComprehensiveStoryScore {
  // Part 1: Sentence-based scoring
  sentenceCount: number;
  sentenceScore: number; // = sentenceCount

  // Part 2: AI-evaluated components
  creativityScore: number; // 1-5
  sentencePowerScore: number; // 1-3
  participationScore: number; // 1-5 (word count based)
  accuracyScore: number; // 1-3 (grammar based)
  popularityScore: number; // 1-5 (likes based, initially 0)
  relevanceScore?: number; // 1-5 (image relevance, only when image context available)

  // Totals
  totalScore: number;
  maxPossibleScore: number; // For percentage calculation

  // Supporting data
  wordCount: number;
  grammarErrorCount: number;
  grammarErrors: string[];

  // AI feedback
  aiFeedback: string;
  creativityExplanation: string;
  sentencePowerExplanation: string;
  relevanceExplanation?: string;

  // Metadata
  evaluatedAt: Date;
  processingTime: number;
}

@Injectable()
export class StoryMakerScoringService {
  private readonly logger = new Logger(StoryMakerScoringService.name);

  constructor(
    private readonly geminiAiService: GeminiAiService,
    private readonly fileRegistryService: FileRegistryService,
    @InjectRepository(StoryMakerEvaluation)
    private readonly evaluationRepository: Repository<StoryMakerEvaluation>,
    @InjectRepository(StoryMakerParticipation)
    private readonly participationRepository: Repository<StoryMakerParticipation>,
    @InjectRepository(StoryMakerSubmission)
    private readonly submissionRepository: Repository<StoryMakerSubmission>,
    @InjectRepository(StoryMaker)
    private readonly storyMakerRepository: Repository<StoryMaker>,
  ) {}

  /**
   * Perform comprehensive scoring of a story submission
   * This is the main method called when a story is submitted
   */
  async scoreStorySubmission(submission: StoryMakerSubmission): Promise<ComprehensiveStoryScore> {
    const startTime = Date.now();

    try {
      this.logger.log(`Starting comprehensive scoring for submission ${submission.id}`);

      // Get the story maker with image analysis (if submission doesn't have participation loaded)
      let storyMaker = null;
      if (submission.participation?.storyMaker) {
        storyMaker = submission.participation.storyMaker;
      } else {
        // Load the participation and story maker if not already loaded
        const submissionWithRelations = await this.submissionRepository.findOne({
          where: { id: submission.id },
          relations: ['participation', 'participation.storyMaker']
        });
        storyMaker = submissionWithRelations?.participation?.storyMaker;
      }

      // Part 1: Count sentences for sentence-based scoring
      const sentenceResult = this.geminiAiService.countSentences(submission.content);
      const sentenceScore = sentenceResult.sentenceCount;

      this.logger.log(`Sentence analysis: ${sentenceResult.sentenceCount} sentences = ${sentenceScore} points`);

      // Part 2: Ensure image analysis is available, then perform AI evaluation
      let imageAnalysis = storyMaker?.imageAnalysis;
      const adminInstructions = storyMaker?.instruction;

      // If image analysis is missing, populate it automatically before evaluation
      if (storyMaker && !imageAnalysis) {
        this.logger.log(`Image analysis missing for story maker ${storyMaker.id}. Populating analysis before evaluation...`);

        try {
          imageAnalysis = await this.populateImageAnalysis(storyMaker);
          this.logger.log(`✅ Successfully populated image analysis for story maker ${storyMaker.id}`);
        } catch (error) {
          this.logger.error(`Failed to populate image analysis for story maker ${storyMaker.id}: ${error.message}`);
          // Continue without image analysis - relevance scoring will be skipped
          this.logger.log('Proceeding with evaluation without image analysis - relevance scoring will be skipped');
        }
      }

      if (imageAnalysis) {
        this.logger.log(`Using image analysis for relevance evaluation: ${imageAnalysis.scene}`);
        this.logger.debug(`Image analysis details:`, {
          objects: imageAnalysis.objects?.length || 0,
          themes: imageAnalysis.themes?.length || 0,
          relevanceKeywords: imageAnalysis.relevanceKeywords?.length || 0
        });
      } else {
        this.logger.log('No image analysis available - relevance scoring will be skipped');
      }

      if (adminInstructions) {
        this.logger.log(`Using admin instructions for evaluation guidance`);
      }

      const aiEvaluation = await this.geminiAiService.evaluateStory(submission.content, imageAnalysis, adminInstructions);

      // Calculate word count for metadata
      const wordCount = this.countWords(submission.content);
      
      // Calculate composite score from 9 criteria (0-100 scale)
      const totalScore = this.calculateCompositeScore(aiEvaluation);
      const popularityScore = 0; // Will be updated later by popularity service
      
      this.logger.log(`AI evaluation completed: ${totalScore}/100 composite score`);

      const processingTime = Date.now() - startTime;

      const comprehensiveScore: ComprehensiveStoryScore = {
        sentenceCount: sentenceResult.sentenceCount,
        sentenceScore,
        creativityScore: aiEvaluation.creativityScore || Math.round(aiEvaluation.creativity / 20),
        sentencePowerScore: aiEvaluation.sentencePowerScore || Math.round(aiEvaluation.sentenceFluencyStyle / 33),
        participationScore: Math.round(aiEvaluation.vocabularyLexical / 20),
        accuracyScore: aiEvaluation.accuracyScore || Math.round(aiEvaluation.grammarAccuracy / 33),
        popularityScore,
        totalScore,
        maxPossibleScore: 100,
        wordCount,
        grammarErrorCount: aiEvaluation.grammarErrorCount,
        grammarErrors: aiEvaluation.grammarErrors,
        aiFeedback: aiEvaluation.overallFeedback,
        creativityExplanation: aiEvaluation.creativityExplanation || 'Evaluated using 9-criteria system',
        sentencePowerExplanation: aiEvaluation.sentencePowerExplanation || 'Evaluated using 9-criteria system',
        evaluatedAt: new Date(),
        processingTime,
      };

      // Add relevance scoring if available
      if (aiEvaluation.relevanceScore !== undefined) {
        comprehensiveScore.relevanceScore = aiEvaluation.relevanceScore;
        comprehensiveScore.relevanceExplanation = aiEvaluation.relevanceExplanation;
        this.logger.log(`✅ Relevance analysis: ${aiEvaluation.relevanceScore}/5 - ${aiEvaluation.relevanceExplanation}`);
      } else if (imageAnalysis) {
        this.logger.warn(`⚠️ Image analysis was present but relevance score was not returned by Gemini AI`);
      }

      this.logger.log(`Comprehensive scoring completed in ${processingTime}ms. Total score: ${totalScore}/${comprehensiveScore.maxPossibleScore}`);
      
      return comprehensiveScore;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.logger.error(`Scoring failed after ${processingTime}ms: ${error.message}`, error.stack);
      throw error; // Don't provide fallback, let the error propagate
    }
  }

  /**
   * Save the comprehensive score to the database
   */
  async saveEvaluation(submissionId: string, score: ComprehensiveStoryScore): Promise<StoryMakerEvaluation> {
    try {
      const evaluation = this.evaluationRepository.create({
        submissionId,
        // Use comprehensive score results (contains AI evaluation)
        contentTaskFulfillment: score.creativityScore * 20, // Map legacy to new temporarily
        organizationCoherence: score.sentencePowerScore * 33,
        grammarAccuracy: score.accuracyScore * 33,
        vocabularyLexical: score.participationScore * 20,
        sentenceFluencyStyle: score.sentencePowerScore * 33,
        clarityCohesion: score.sentencePowerScore * 33,
        creativity: score.creativityScore * 20,
        criticalThinking: 70,
        expressiveness: score.creativityScore * 20,
        popularityScore: score.popularityScore,
        totalScore: score.totalScore,
        wordCount: score.wordCount,
        grammarErrorCount: score.grammarErrorCount,
        aiFeedback: score.aiFeedback,
        aiEvaluationData: {
          creativityExplanation: score.creativityExplanation,
          sentencePowerExplanation: score.sentencePowerExplanation,
          relevanceExplanation: score.relevanceExplanation,
          grammarErrors: score.grammarErrors,
          processingTime: score.processingTime,
        },
        evaluatedAt: score.evaluatedAt,
      });

      const savedEvaluation = await this.evaluationRepository.save(evaluation);

      // Update submission isEvaluated flag
      await this.updateSubmissionEvaluatedFlag(submissionId);

      // Update participation with the total score
      await this.updateParticipationScore(submissionId, score.totalScore, score.evaluatedAt);

      this.logger.log(`Evaluation saved for submission ${submissionId} with total score ${score.totalScore}`);

      return savedEvaluation;

    } catch (error) {
      this.logger.error(`Failed to save evaluation: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Calculate participation score based on word count
   * Requirements:
   * - 1 point: ≤30 words
   * - 2 points: 31-60 words  
   * - 3 points: 61-100 words
   * - 4 points: 101-150 words
   * - 5 points: 200+ words
   */
  private calculateParticipationScore(wordCount: number): number {
    if (wordCount <= 30) return 1;
    if (wordCount <= 60) return 2;
    if (wordCount <= 100) return 3;
    if (wordCount <= 150) return 4;
    if (wordCount >= 200) return 5;
    return 4; // 151-199 words
  }

  /**
   * Apply accuracy multiplier for stories with 100+ words
   * Requirements: If participation score ≥ 3 (100+ words), apply 2x multiplier to accuracy
   */
  private applyAccuracyMultiplier(accuracyScore: number, participationScore: number, wordCount: number): number {
    if (participationScore >= 3 && wordCount >= 100) {
      const multipliedScore = accuracyScore * 2;
      this.logger.log(`Accuracy multiplier applied: ${accuracyScore} x 2 = ${multipliedScore} (${wordCount} words)`);
      return multipliedScore;
    }
    return accuracyScore;
  }

  /**
   * Count words in text
   */
  private countWords(text: string): number {
    if (!text || text.trim().length === 0) {
      return 0;
    }
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * Update submission isEvaluated flag
   */
  private async updateSubmissionEvaluatedFlag(submissionId: string): Promise<void> {
    try {
      const submission = await this.submissionRepository.findOne({
        where: { id: submissionId }
      });

      if (submission) {
        submission.isEvaluated = true;
        await this.submissionRepository.save(submission);

        this.logger.log(`Submission ${submissionId} marked as evaluated`);
      }
    } catch (error) {
      this.logger.error(`Failed to update submission evaluated flag: ${error.message}`);
    }
  }

  /**
   * Update participation record with final score
   */
  private async updateParticipationScore(submissionId: string, totalScore: number, evaluatedAt: Date): Promise<void> {
    try {
      // Find participation through submission
      const result = await this.participationRepository
        .createQueryBuilder('participation')
        .innerJoin('participation.submissions', 'submission')
        .where('submission.id = :submissionId', { submissionId })
        .getOne();

      if (result) {
        result.score = totalScore;
        result.isEvaluated = true;
        result.evaluatedAt = evaluatedAt;
        await this.participationRepository.save(result);

        this.logger.log(`Participation ${result.id} updated with score ${totalScore}`);
      }
    } catch (error) {
      this.logger.error(`Failed to update participation score: ${error.message}`);
      // Don't throw - this is not critical for the main flow
    }
  }



  /**
   * Update popularity score for an existing evaluation
   * This will be called by the popularity service
   */
  async updatePopularityScore(submissionId: string, popularityScore: number): Promise<void> {
    try {
      const evaluation = await this.evaluationRepository.findOne({
        where: { submissionId },
      });

      if (evaluation) {
        const oldTotalScore = evaluation.totalScore;
        evaluation.popularityScore = popularityScore;
        evaluation.totalScore = oldTotalScore - evaluation.popularityScore + popularityScore;
        
        await this.evaluationRepository.save(evaluation);
        
        // Also update participation score
        await this.updateParticipationScore(submissionId, evaluation.totalScore, evaluation.evaluatedAt);
        
        this.logger.log(`Updated popularity score for submission ${submissionId}: ${popularityScore} points`);
      }
    } catch (error) {
      this.logger.error(`Failed to update popularity score: ${error.message}`);
    }
  }

  /**
   * Populate image analysis for a story maker that is missing analysis
   * This method automatically generates and saves image analysis
   */
  private async populateImageAnalysis(storyMaker: StoryMaker): Promise<any> {
    try {
      this.logger.log(`Starting automatic image analysis for story maker ${storyMaker.id} (${storyMaker.title})`);

      // Get the image file from the file registry
      const imageFileData = await this.fileRegistryService.getFileBuffer(FileEntityType.STORY_MAKER, storyMaker.id);

      if (!imageFileData) {
        throw new Error(`No image file found for story maker ${storyMaker.id} - cannot perform analysis`);
      }

      // Extract buffer and use the actual MIME type from the file
      const { buffer: imageBuffer, mimeType: actualMimeType } = imageFileData;
      const mimeType = actualMimeType || this.determineMimeType(storyMaker.picture) || 'image/jpeg';

      // Perform image analysis using Gemini AI
      const imageAnalysis = await this.geminiAiService.analyzeImage(imageBuffer, mimeType);

      // Prepare the analysis data
      const analysisData = {
        objects: imageAnalysis.objects,
        scene: imageAnalysis.scene,
        mood: imageAnalysis.mood,
        themes: imageAnalysis.themes,
        colors: imageAnalysis.colors,
        setting: imageAnalysis.setting,
        characters: imageAnalysis.characters,
        emotions: imageAnalysis.emotions,
        description: imageAnalysis.description,
        relevanceKeywords: imageAnalysis.relevanceKeywords,
      };

      // Update the story maker with the analysis results
      await this.storyMakerRepository.update(storyMaker.id, {
        imageAnalysis: analysisData
      });

      this.logger.log(`✅ Successfully populated image analysis for story maker ${storyMaker.id} (${storyMaker.title})`);
      this.logger.debug(`Analysis result: ${JSON.stringify(analysisData, null, 2)}`);

      return analysisData;

    } catch (error) {
      this.logger.error(`Failed to populate image analysis for story maker ${storyMaker.id}: ${error.message}`, error.stack);

      // Set a fallback analysis to prevent repeated failures
      const fallbackAnalysis = {
        objects: ['creative elements'],
        scene: 'A creative scene perfect for storytelling',
        mood: 'inspiring and imaginative',
        themes: ['creativity', 'imagination', 'storytelling'],
        colors: ['vibrant', 'inspiring'],
        setting: 'creative storytelling environment',
        characters: ['creative storyteller'],
        emotions: ['inspiration', 'wonder', 'creativity'],
        description: 'An inspiring image that encourages creative storytelling and imagination. Analysis was completed with fallback data due to technical limitations.',
        relevanceKeywords: ['creative', 'story', 'imagination', 'narrative', 'adventure', 'discovery'],
      };

      await this.storyMakerRepository.update(storyMaker.id, {
        imageAnalysis: fallbackAnalysis
      });

      this.logger.log(`Used fallback image analysis for story maker ${storyMaker.id}`);
      return fallbackAnalysis;
    }
  }

  /**
   * Calculate composite score from 9 criteria (0-100 scale)
   */
  private calculateCompositeScore(aiEvaluation: GeminiEvaluationResult): number {
    const scores = [
      aiEvaluation.contentTaskFulfillment,
      aiEvaluation.organizationCoherence,
      aiEvaluation.grammarAccuracy,
      aiEvaluation.vocabularyLexical,
      aiEvaluation.sentenceFluencyStyle,
      aiEvaluation.clarityCohesion,
      aiEvaluation.creativity,
      aiEvaluation.criticalThinking,
      aiEvaluation.expressiveness,
    ];
    
    // Calculate weighted average (all criteria equally weighted)
    const average = scores.reduce((sum, score) => sum + score, 0) / scores.length;
    return Math.round(average);
  }

  /**
   * Determine MIME type from file extension
   */
  private determineMimeType(filename: string): string | null {
    if (!filename) return null;

    const extension = filename.toLowerCase().split('.').pop();
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'webp':
        return 'image/webp';
      default:
        return null;
    }
  }
}
