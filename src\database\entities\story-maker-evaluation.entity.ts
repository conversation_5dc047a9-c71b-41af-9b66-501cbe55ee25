import { <PERSON><PERSON><PERSON>, <PERSON>um<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { StoryMakerSubmission } from './story-maker-submission.entity';

@Entity()
export class StoryMakerEvaluation extends AuditableBaseEntity {
  @Column({ name: 'submission_id', type: 'uuid' })
  submissionId: string;

  // AI-generated feedback instead of tutor corrections
  @Column({ name: 'ai_feedback', type: 'text', nullable: true })
  aiFeedback: string;

  // 9 Key Writing Evaluation Criteria (0-100 scale)
  @Column({ name: 'content_task_fulfillment', nullable: false, default: 0 })
  contentTaskFulfillment: number; // 0-100

  @Column({ name: 'organization_coherence', nullable: false, default: 0 })
  organizationCoherence: number; // 0-100

  @Column({ name: 'grammar_accuracy', nullable: false, default: 0 })
  grammarAccuracy: number; // 0-100

  @Column({ name: 'vocabulary_lexical', nullable: false, default: 0 })
  vocabularyLexical: number; // 0-100

  @Column({ name: 'sentence_fluency_style', nullable: false, default: 0 })
  sentenceFluencyStyle: number; // 0-100

  @Column({ name: 'clarity_cohesion', nullable: false, default: 0 })
  clarityCohesion: number; // 0-100

  @Column({ name: 'creativity', nullable: false, default: 0 })
  creativity: number; // 0-100

  @Column({ name: 'critical_thinking', nullable: false, default: 0 })
  criticalThinking: number; // 0-100

  @Column({ name: 'expressiveness', nullable: false, default: 0 })
  expressiveness: number; // 0-100

  // Legacy scores for backward compatibility
  @Column({ name: 'popularity_score', nullable: false, default: 0 })
  popularityScore: number; // 1-5 (updated by scheduled job)

  @Column({ name: 'total_score', nullable: false })
  totalScore: number; // Composite score from 9 criteria

  // Supporting data
  @Column({ name: 'word_count', nullable: false })
  wordCount: number;

  @Column({ name: 'grammar_error_count', nullable: false })
  grammarErrorCount: number;

  @Column({ name: 'ai_evaluation_data', type: 'jsonb', nullable: true })
  aiEvaluationData: any; // Full Gemini API response

  @Column({ name: 'evaluated_at', nullable: false })
  evaluatedAt: Date; // Immediate upon submission

  // Relationships
  @ManyToOne(() => StoryMakerSubmission, (submission) => submission.evaluations)
  @JoinColumn({ name: 'submission_id' })
  submission: StoryMakerSubmission;
}
