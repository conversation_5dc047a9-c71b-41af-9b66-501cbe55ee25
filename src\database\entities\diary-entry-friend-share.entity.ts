import { <PERSON><PERSON><PERSON>, <PERSON>um<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { DiaryEntry } from './diary-entry.entity';
import { User } from './user.entity';

/**
 * Entity representing a diary entry shared with a specific friend
 */
@Entity()
export class DiaryEntryFriendShare extends AuditableBaseEntity {
  @Column({ name: 'diary_entry_id' })
  diaryEntryId: string;

  @ManyToOne(() => DiaryEntry, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'diary_entry_id' })
  diaryEntry: DiaryEntry;

  @Column({ name: 'shared_by_id' })
  sharedById: string;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'shared_by_id' })
  sharedBy: User;

  @Column({ name: 'shared_with_id' })
  sharedWithId: string;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'shared_with_id' })
  sharedWith: User;

  @Column({ name: 'message', type: 'text', nullable: true })
  message: string;

  @Column({ name: 'share_date', type: 'timestamp' })
  shareDate: Date;

  @Column({ name: 'expiry_date', type: 'timestamp', nullable: true })
  expiryDate: Date;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'chat_message_id', nullable: true })
  chatMessageId: string;
}
