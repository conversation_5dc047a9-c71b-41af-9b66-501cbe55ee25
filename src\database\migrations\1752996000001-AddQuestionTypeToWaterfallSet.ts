import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddQuestionTypeToWaterfallSet1752996000001 implements MigrationInterface {
  name = 'AddQuestionTypeToWaterfallSet1752996000001';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TYPE "public"."waterfall_set_question_type_enum" AS ENUM(
        'fill_in_blank', 
        'true_false', 
        'multiple_choice_single', 
        'multiple_choice_multiple'
      )
    `);
    
    await queryRunner.query(`
      ALTER TABLE "waterfall_set" 
      ADD "question_type" "public"."waterfall_set_question_type_enum" DEFAULT 'fill_in_blank'
    `);
    
    await queryRunner.query(`
      UPDATE "waterfall_set" 
      SET "question_type" = 'fill_in_blank' 
      WHERE "question_type" IS NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "waterfall_set" 
      DROP COLUMN "question_type"
    `);
    
    await queryRunner.query(`
      DROP TYPE "public"."waterfall_set_question_type_enum"
    `);
  }
}