import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DiaryEntry, DiaryEntryStatus } from '../../database/entities/diary-entry.entity';
import { Diary } from '../../database/entities/diary.entity';
import { DiaryEntryHistory } from '../../database/entities/diary-entry-history.entity';
import { DiaryEntryResponseDto, DiaryDetailsDto, TutorDiaryDetailsDto, DiarySkinResponseDto, DiaryFeedbackResponseDto, TutorDiaryEntryResponseDto } from '../../database/models/diary.dto';
import { DeeplinkService, DeeplinkType } from '../../common/utils/deeplink.service';
import { FileRegistryService } from '../../common/services/file-registry.service';
import { FileEntityType } from '../../common/enums/file-entity-type.enum';
import { DiaryShare } from '../../database/entities/diary-share.entity';

@Injectable()
export class DiaryMapperService {
  private readonly logger = new Logger(DiaryMapperService.name);

  constructor(
    @InjectRepository(DiaryEntry)
    private readonly diaryEntryRepository: Repository<DiaryEntry>,
    @InjectRepository(DiaryEntryHistory)
    private readonly diaryEntryHistoryRepository: Repository<DiaryEntryHistory>,
    private readonly deeplinkService: DeeplinkService,
    private readonly fileRegistryService: FileRegistryService,
    @InjectRepository(DiaryShare)
    private readonly diaryShareRepository: Repository<DiaryShare>,
  ) {}

  // Helper method to map Diary entity to DiaryDetailsDto
  async mapDiaryToDetailsDto(diary: Diary): Promise<DiaryDetailsDto> {
    // Get total entries count
    const totalEntries = await this.diaryEntryRepository.count({
      where: { diaryId: diary.id },
    });

    // Get confirmed entries count
    const reviewedEntries = await this.diaryEntryRepository.count({
      where: { diaryId: diary.id, status: DiaryEntryStatus.REVIEWED },
    });

    // Get average score
    const scoreResult = await this.diaryEntryRepository
      .createQueryBuilder('entry')
      .select('AVG(entry.score)', 'averageScore')
      .where('entry.diaryId = :diaryId', { diaryId: diary.id })
      .andWhere('entry.score IS NOT NULL')
      .getRawOne();

    const averageScore = scoreResult?.averageScore ? parseFloat(scoreResult.averageScore) : null;

    // Get cover photo URL
    const coverPhotoUrl = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.DIARY_COVER, diary.id);

    return {
      id: diary.id,
      userId: diary.userId,
      userName: diary.user?.name,
      defaultSkinId: diary.defaultSkinId,
      totalEntries,
      reviewedEntries,
      averageScore,
      coverPhotoUrl,
      createdAt: diary.createdAt,
      updatedAt: diary.updatedAt,
    };
  }

  // Helper method to map Diary entity to TutorDiaryDetailsDto (excludes user details)
  async mapDiaryToTutorDetailsDto(diary: Diary): Promise<TutorDiaryDetailsDto> {
    // Get total entries count
    const totalEntries = await this.diaryEntryRepository.count({
      where: { diaryId: diary.id },
    });

    // Get confirmed entries count
    const reviewedEntries = await this.diaryEntryRepository.count({
      where: { diaryId: diary.id, status: DiaryEntryStatus.REVIEWED },
    });

    // Get average score
    const scoreResult = await this.diaryEntryRepository
      .createQueryBuilder('entry')
      .select('AVG(entry.score)', 'averageScore')
      .where('entry.diaryId = :diaryId', { diaryId: diary.id })
      .andWhere('entry.score IS NOT NULL')
      .getRawOne();

    const averageScore = scoreResult?.averageScore ? parseFloat(scoreResult.averageScore) : null;

    // Get cover photo URL
    const coverPhotoUrl = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.DIARY_COVER, diary.id);

    return {
      id: diary.id,
      defaultSkinId: diary.defaultSkinId,
      totalEntries,
      reviewedEntries,
      averageScore,
      coverPhotoUrl,
      createdAt: diary.createdAt,
      updatedAt: diary.updatedAt,
    };
  }

  private mapSkinToResponseDto(skin: any): DiarySkinResponseDto | undefined {
    if (!skin) return undefined;
    return {
      ...skin,
      isUsedIn: skin.isUsedIn ?? [],
    };
  }

  private mapFeedbackToResponseDto(feedback: any): DiaryFeedbackResponseDto {
    return {
      id: feedback.id,
      tutorId: feedback.tutorId,
      tutorName: feedback.tutor?.name ?? 'Unknown Tutor',
      feedback: feedback.feedback,
      award: feedback.award,
      createdAt: feedback.createdAt,
    };
  }

  /**
   * Calculate edit history count from actual history table records
   */
  private async calculateEditHistoryCount(entryId: string, type: 'diary' | 'novel' | 'mission'): Promise<number> {
    try {
      let count = 0;

      if (type === 'diary') {
        count = await this.diaryEntryHistoryRepository.count({
          where: { diaryEntryId: entryId },
        });
      }
      // Add other types when needed

      return count;
    } catch (error) {
      this.logger.warn(`Failed to calculate edit history count for ${type} entry ${entryId}: ${error.message}`);
      return 0;
    }
  }
  // Helper method to map DiaryEntry entity to DiaryEntryResponseDto
  // Made public so it can be used by other services
  async mapEntryToResponseDto(entry: DiaryEntry): Promise<DiaryEntryResponseDto> {
    // Explicitly fetch original reviewed version if originalReviewedVersionId exists
    let originalReviewedVersion = undefined;

    if (entry.originalReviewedVersionId) {
      try {
        const originalVersion = await this.diaryEntryHistoryRepository.findOne({
          where: { id: entry.originalReviewedVersionId },
        });
        if (originalVersion) {
          originalReviewedVersion = {
            title: originalVersion.title,
            content: originalVersion.content,
            versionNumber: originalVersion.versionNumber,
            createdAt: originalVersion.createdAt,
          };
        }
      } catch (error) {
        this.logger.warn(`Failed to load original reviewed version ${entry.originalReviewedVersionId}: ${error.message}`);
      }
    }

    // Handle skin loading - use entry skinId or diary defaultSkinId
    let skinDto = undefined;
    const skinIdToLoad = entry.skinId || entry.diary?.defaultSkinId;
    if (skinIdToLoad) {
      try {
        // First try to load as global skin
        const globalSkin = await this.diaryEntryRepository.manager.getRepository('DiarySkin').findOne({
          where: { id: skinIdToLoad }
        });
        if (globalSkin) {
          skinDto = this.mapSkinToResponseDto(globalSkin);
        } else {
          // If not found as global skin, try as student skin
          const studentSkin = await this.diaryEntryRepository.manager.getRepository('StudentDiarySkin').findOne({
            where: { id: skinIdToLoad }
          });
          if (studentSkin) {
            skinDto = this.mapSkinToResponseDto(studentSkin);
          }
        }
      } catch (error) {
        this.logger.warn(`Failed to load skin ${skinIdToLoad}: ${error.message}`);
      }
    }
    
    // If no skin found, provide placeholder
    if (!skinDto) {
      skinDto = {
        id: '',
        name: 'No Skin',
        description: 'No skin applied',
        previewImagePath: null,
        isActive: true,
        isGlobal: true,
        createdById: null,
        templateContent: '',
        isUsedIn: false
      };
    }

    return {
      id: entry.id,
      diary: entry.diary,
      entryDate: entry.entryDate,
      title: entry.title,
      content: entry.content,
      status: entry.status,
      skin: skinDto,
      backgroundColor: entry.backgroundColor,
      decoration: entry.decoration,
      isPrivate: entry.isPrivate,
      score: entry.score,
      evaluatedAt: entry.evaluatedAt,
      createdAt: entry.createdAt,
      updatedAt: entry.updatedAt,
      hasGreeting: (() => {
        const hasGreeting = !!entry.diary?.tutorGreeting;
        this.logger.log(`DiaryMapper - Entry ${entry.id}: diary exists: ${!!entry.diary}, tutorGreeting: ${entry.diary?.tutorGreeting}, hasGreeting: ${hasGreeting}`);
        return hasGreeting;
      })(),
      feedbacks: entry.feedbacks?.map((feedback) => this.mapFeedbackToResponseDto(feedback)) || [],
      likeCount: entry.likes ? entry.likes.length : 0,
      hasLiked: false, // This will be updated by the DiaryEntryService if a userId is provided
      settings: entry.settings,
      correction: entry.correction,
      thanksMessage: entry.thanksMessage,
      // Add share URLs only if entry is not private
      shareUrl: !entry.isPrivate && entry.shares?.length > 0 ? this.deeplinkService.getWebLink(DeeplinkType.DIARY_SHARE, { id: entry.id }) : undefined,
      // Add cache-busting timestamp to get the latest QR code if regenerated
      qrCodeUrl: !entry.isPrivate && entry.shares?.length > 0 ? `${this.fileRegistryService.getFileUrl(FileEntityType.DIARY_QR, entry.id)}?t=${Date.now()}` : undefined,
      // Version tracking fields
      currentVersion: entry.currentVersion?.versionNumber || entry.totalEditHistory || 0,
      totalEditHistory: await this.calculateEditHistoryCount(entry.id, 'diary'),
      // NEW REQUIREMENT: Explicitly populated original reviewed version
      originalReviewedVersion: originalReviewedVersion || null,
      versionCreatedAt: entry.currentVersion?.createdAt || entry.updatedAt,
      hasHistory: (await this.calculateEditHistoryCount(entry.id, 'diary')) > 0,

      // NEW: Unified submission/draft tracking fields
      isDraft: entry.isDraft || false,
      submittedVersionCount: entry.submittedVersionCount || 0,
      canSubmitNewVersion: entry.canSubmitNewVersion !== undefined ? entry.canSubmitNewVersion : true,
      lastSubmittedAt: entry.lastSubmittedAt || null,
      lastReviewedAt: entry.lastReviewedAt || null,

      // NEW: Resubmission tracking fields
      isResubmission: entry.isResubmission || false,
      resubmissionType: entry.resubmissionType || null,
      previousReviewCount: entry.previousReviewCount || 0,
      previousConfirmationCount: entry.previousConfirmationCount || 0,
    };
  }

  // Helper method to map DiaryEntry entity to TutorDiaryEntryResponseDto (excludes user details)
  async mapEntryToTutorResponseDto(entry: DiaryEntry): Promise<TutorDiaryEntryResponseDto> {
    // Explicitly fetch original reviewed version if originalReviewedVersionId exists
    let originalReviewedVersion = undefined;

    if (entry.originalReviewedVersionId) {
      try {
        const originalVersion = await this.diaryEntryHistoryRepository.findOne({
          where: { id: entry.originalReviewedVersionId },
        });
        if (originalVersion) {
          originalReviewedVersion = {
            title: originalVersion.title,
            content: originalVersion.content,
            versionNumber: originalVersion.versionNumber,
            createdAt: originalVersion.createdAt,
          };
        }
      } catch (error) {
        this.logger.warn(`Failed to load original reviewed version ${entry.originalReviewedVersionId}: ${error.message}`);
      }
    }

    // Handle skin loading for tutor response - use entry skinId or diary defaultSkinId
    let tutorSkinDto = undefined;
    const tutorSkinIdToLoad = entry.skinId || entry.diary?.defaultSkinId;
    if (tutorSkinIdToLoad) {
      try {
        // First try to load as global skin
        const globalSkin = await this.diaryEntryRepository.manager.getRepository('DiarySkin').findOne({ where: { id: tutorSkinIdToLoad } });
        if (globalSkin) {
          tutorSkinDto = this.mapSkinToResponseDto(globalSkin);
        } else {
          // If not found as global skin, try as student skin
          const studentSkin = await this.diaryEntryRepository.manager.getRepository('StudentDiarySkin').findOne({
            where: { id: tutorSkinIdToLoad }
          });
          if (studentSkin) {
            tutorSkinDto = this.mapSkinToResponseDto(studentSkin);
          }
        }
      } catch (error) {
        this.logger.warn(`Failed to load skin ${tutorSkinIdToLoad}: ${error.message}`);
      }
    }
    
    // If no skin found, provide placeholder
    if (!tutorSkinDto) {
      tutorSkinDto = {
        id: '',
        name: 'No Skin',
        description: 'No skin applied',
        previewImagePath: null,
        isActive: true,
        isGlobal: true,
        createdById: null,
        templateContent: '',
        isUsedIn: false
      };
    }

    // Map diary details without user information
    const tutorDiaryDetails = entry.diary ? await this.mapDiaryToTutorDetailsDto(entry.diary) : undefined;

    return {
      id: entry.id,
      diary: tutorDiaryDetails,
      entryDate: entry.entryDate,
      title: entry.title,
      content: entry.content,
      status: entry.status,
      skin: tutorSkinDto,
      backgroundColor: entry.backgroundColor,
      decoration: entry.decoration,
      isPrivate: entry.isPrivate,
      score: entry.score,
      evaluatedAt: entry.evaluatedAt,
      createdAt: entry.createdAt,
      updatedAt: entry.updatedAt,
      hasGreeting: !!entry.diary?.tutorGreeting,
      feedbacks: entry.feedbacks?.map((feedback) => this.mapFeedbackToResponseDto(feedback)) || [],
      likeCount: entry.likes ? entry.likes.length : 0,
      hasLiked: false, // This will be updated by the DiaryEntryService if a userId is provided
      settings: entry.settings,
      correction: entry.correction,
      thanksMessage: entry.thanksMessage,
      submittedByUserName: entry.diary?.user?.name,
      studentId: entry.diary?.userId || '',
      // Add share URLs only if entry is not private
      shareUrl: !entry.isPrivate && entry.shares?.length > 0 ? this.deeplinkService.getWebLink(DeeplinkType.DIARY_SHARE, { id: entry.id }) : undefined,
      // Add cache-busting timestamp to get the latest QR code if regenerated
      qrCodeUrl: !entry.isPrivate && entry.shares?.length > 0 ? `${this.fileRegistryService.getFileUrl(FileEntityType.DIARY_QR, entry.id)}?t=${Date.now()}` : undefined,
      // Version tracking fields
      currentVersion: entry.currentVersion?.versionNumber || entry.totalEditHistory || 0,
      totalEditHistory: await this.calculateEditHistoryCount(entry.id, 'diary'),
      // NEW REQUIREMENT: Explicitly populated original reviewed version
      originalReviewedVersion: originalReviewedVersion || null,
      versionCreatedAt: entry.currentVersion?.createdAt || entry.updatedAt,
      hasHistory: (await this.calculateEditHistoryCount(entry.id, 'diary')) > 0,

      // NEW: Unified submission/draft tracking fields
      isDraft: entry.isDraft || false,
      submittedVersionCount: entry.submittedVersionCount || 0,
      canSubmitNewVersion: entry.canSubmitNewVersion !== undefined ? entry.canSubmitNewVersion : true,
      lastSubmittedAt: entry.lastSubmittedAt || null,
      lastReviewedAt: entry.lastReviewedAt || null,

      // NEW: Resubmission tracking fields
      isResubmission: entry.isResubmission || false,
      resubmissionType: entry.resubmissionType || null,
      previousReviewCount: entry.previousReviewCount || 0,
      previousConfirmationCount: entry.previousConfirmationCount || 0,
    };
  }
}
