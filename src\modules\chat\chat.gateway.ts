import { WebSocketGateway, WebSocketServer, SubscribeMessage, OnGatewayConnection, OnGatewayDisconnect, WsException, ConnectedSocket, MessageBody } from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger, Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ChatService } from './chat.service';
import { AdminChatService } from './admin-chat.service';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User, UserType } from '../../database/entities/user.entity';
import { Message, MessageStatus } from '../../database/entities/message.entity';
import { AdminConversationParticipant } from '../../database/entities/admin-conversation-participant.entity';
import { CreateMessageDto, MessageDto } from '../../database/models/chat.dto';
import { getCurrentUTCDate } from '../../common/utils/date-utils';
import { JwtPayload } from '../../modules/auth/interfaces/jwt-payload.interface';
import { isAdminConversationId, stripAdminPrefix, addAdminPrefix } from './admin-conversation.utils';
import { ChatValidationUtil } from './chat-validation.util';
import { AdminConversationManagerService } from './admin-conversation-manager.service';
import { SocketTimezoneTransformer } from '../../common/transformers/socket-timezone.transformer';

interface SocketWithUser extends Socket {
  user: {
    id: string;
    email: string;
    name: string;
  };
}

@WebSocketGateway({
  namespace: 'chat',
  cors: {
    origin: '*',
    methods: ['GET', 'POST'],
  },
})
@Injectable()
export class ChatGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(ChatGateway.name);
  private readonly userConnections = new Map<string, Set<string>>();
  private readonly typingUsers = new Map<string, { userId: string; conversationId: string; timeout: NodeJS.Timeout }>();

  constructor(
    private readonly jwtService: JwtService,
    private readonly chatService: ChatService,
    private readonly adminChatService: AdminChatService,
    private readonly adminConversationManagerService: AdminConversationManagerService,
    private readonly socketTimezoneTransformer: SocketTimezoneTransformer,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Message)
    private readonly messageRepository: Repository<Message>,
    @InjectRepository(AdminConversationParticipant)
    private readonly adminConversationParticipantRepository: Repository<AdminConversationParticipant>,
  ) {}

  /**
   * Handle new WebSocket connections
   * @param client Socket client
   */
  async handleConnection(client: Socket): Promise<void> {
    try {
      // Log connection attempt
      this.logger.log(`New connection attempt: ${client.id}`);
      this.logger.log(
        `Handshake data: ${JSON.stringify({
          auth: client.handshake.auth,
          query: client.handshake.query,
          headers: client.handshake.headers,
        })}`,
      );

      // Try to get token from different sources
      let token = client.handshake.auth.token;

      // If not in auth, check query params
      if (!token && client.handshake.query.token) {
        token = client.handshake.query.token as string;
        this.logger.log('Using token from query params');
      }

      // If not in query, check authorization header
      if (!token && client.handshake.headers.authorization) {
        const authHeader = client.handshake.headers.authorization;
        if (authHeader.startsWith('Bearer ')) {
          token = authHeader.substring(7); // Remove 'Bearer ' prefix
          this.logger.log('Using token from authorization header');
        }
      }

      // If still no token, throw error
      if (!token) {
        this.logger.error('No token provided in any source (auth, query, headers)');
        throw new UnauthorizedException('No token provided');
      }

      this.logger.log(`Received token: ${token.substring(0, 20)}...`);

      let payload: JwtPayload;
      try {
        payload = this.jwtService.verify<JwtPayload>(token);
        this.logger.log(
          `Token verified successfully. Payload: ${JSON.stringify({
            id: payload.id,
            sub: payload.sub,
            username: payload.username,
            type: payload.type,
          })}`,
        );

        // Use either id or sub as the user ID
        const userId = payload.id || payload.sub;

        if (!userId) {
          throw new UnauthorizedException('Invalid token: missing user ID');
        }
      } catch (error) {
        this.logger.error(`Token verification failed: ${error.message}`);
        throw new UnauthorizedException(`Invalid token: ${error.message}`);
      }

      // Get the user ID from the payload
      const userId = payload.id || payload.sub;

      // Get user
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new UnauthorizedException('User not found');
      }

      // Store connection
      if (!this.userConnections.has(userId)) {
        this.userConnections.set(userId, new Set());
      }
      this.userConnections.get(userId).add(client.id);

      // Add user data to socket
      (client as SocketWithUser).user = {
        id: userId,
        email: user.email, // Use email from user object instead of payload
        name: user.name,
      };

      // Join user-specific room
      client.join(`user:${userId}`);

      this.logger.log(`Chat client connected: ${client.id} for user ${userId}`);

      // Send connection confirmation
      client.emit('connected', { userId, name: user.name });

      // Broadcast user online status
      this.broadcastUserStatus(userId, true);
    } catch (error) {
      this.logger.error(`Chat connection error: ${error.message}`, error.stack);
      client.disconnect(true);
    }
  }

  /**
   * Handle WebSocket disconnections
   * @param client Socket client
   */
  handleDisconnect(client: Socket): void {
    try {
      const socketWithUser = client as SocketWithUser;
      if (socketWithUser.user) {
        const userId = socketWithUser.user.id;

        // Remove connection from user's connections
        if (this.userConnections.has(userId)) {
          this.userConnections.get(userId).delete(client.id);

          // If no more connections for this user, remove user from connections map
          if (this.userConnections.get(userId).size === 0) {
            this.userConnections.delete(userId);

            // Broadcast user offline status
            this.broadcastUserStatus(userId, false);
          }
        }

        this.logger.log(`Chat client disconnected: ${client.id} for user ${userId}`);
      }
    } catch (error) {
      this.logger.error(`Chat disconnect error: ${error.message}`, error.stack);
    }
  }

  /**
   * Broadcast user status to all connected clients
   * @param userId User ID
   * @param isOnline Whether user is online
   */
  private broadcastUserStatus(userId: string, isOnline: boolean): void {
    this.server.emit('user_status', { userId, isOnline, timestamp: new Date() });
  }

  /**
   * Check if user is connected
   * @param userId User ID
   * @returns Whether user is connected
   */
  isUserConnected(userId: string): boolean {
    return this.userConnections.has(userId) && this.userConnections.get(userId).size > 0;
  }

  /**
   * Subscribe to a conversation
   * @param client Socket client
   * @param data Subscription data
   */
  @SubscribeMessage('subscribe_conversation')
  async handleSubscribeConversation(@ConnectedSocket() client: SocketWithUser, @MessageBody() data: { conversationId: string }): Promise<void> {
    try {
      // Check if user is defined
      if (!client.user) {
        this.logger.error('User not authenticated in subscribe_conversation');
        client.emit('error', { message: 'User not authenticated' });
        return;
      }

      let { conversationId } = data;
      const userId = ChatValidationUtil.validateUserId(client.user.id);
      let dbConversationId = stripAdminPrefix(ChatValidationUtil.validateConversationId(conversationId));

      if (isAdminConversationId(conversationId)) {
        // For admin conversations, we need to handle both user and admin access
        const user = await this.userRepository.findOne({ where: { id: userId } });
        
        if (user?.type === UserType.ADMIN) {
          // Admin accessing admin conversation - use the provided conversation ID
          dbConversationId = stripAdminPrefix(conversationId);
          
          // Ensure admin is enrolled as participant
          await this.adminConversationManagerService.ensureAdminParticipant(dbConversationId, userId);
          
          conversationId = addAdminPrefix(dbConversationId);
        } else {
          // Regular user accessing their admin conversation
          const adminConversation = await this.adminChatService.getOrCreateAdminConversationForUser(userId);
          dbConversationId = adminConversation.id;
          conversationId = addAdminPrefix(adminConversation.id);
        }
      }

      const conversation = await this.chatService.getConversation(dbConversationId, userId);
      if (!conversation) {
        throw new WsException('Conversation not found or user is not a participant');
      }

      // Join conversation room (use original for socket room)
      client.join(`conversation:${conversationId}`);

      // Mark messages as delivered
      await this.chatService.markMessagesAsDelivered(dbConversationId, userId);

      this.logger.log(`User ${userId} subscribed to conversation ${conversationId}`);
      client.emit('subscribed_conversation', { conversationId });

      // Notify other participants that messages were delivered
      this.server.to(`conversation:${conversationId}`).emit('messages_delivered', {
        conversationId,
        userId,
      });
    } catch (error) {
      this.logger.error(`Subscription error: ${error.message}`, error.stack);
      client.emit('error', { message: error.message });
    }
  }

  /**
   * Unsubscribe from a conversation
   * @param client Socket client
   * @param data Unsubscription data
   */
  @SubscribeMessage('unsubscribe_conversation')
  async handleUnsubscribeConversation(@ConnectedSocket() client: SocketWithUser, @MessageBody() data: { conversationId: string }): Promise<void> {
    try {
      // Check if user is defined
      if (!client.user) {
        this.logger.error('User not authenticated in unsubscribe_conversation');
        client.emit('error', { message: 'User not authenticated' });
        return;
      }

  let { conversationId } = data;
  const userId = client.user.id;
  // Always use admin conversation for user-to-admin chats
  if (isAdminConversationId(conversationId)) {
    const adminConversation = await this.adminChatService.getOrCreateAdminConversationForUser(userId);
    conversationId = addAdminPrefix(adminConversation.id);
  }
  // Leave conversation room
  client.leave(`conversation:${conversationId}`);

  this.logger.log(`User ${userId} unsubscribed from conversation ${conversationId}`);
  client.emit('unsubscribed_conversation', { conversationId });
    } catch (error) {
      this.logger.error(`Unsubscription error: ${error.message}`, error.stack);
      client.emit('error', { message: error.message });
    }
  }

  /**
   * Send a message
   * @param client Socket client
   * @param data Message data
   */
  @SubscribeMessage('send_message')
  async handleSendMessage(@ConnectedSocket() client: SocketWithUser, @MessageBody() data: CreateMessageDto): Promise<void> {
    try {
      // Log the client object to see what's available
      this.logger.log(
        `Client in send_message: ${JSON.stringify({
          id: client.id,
          connected: client.connected,
          rooms: Array.from(client.rooms || []),
          handshake: {
            auth: client.handshake?.auth,
            query: client.handshake?.query,
          },
        })}`,
      );

      // Check if user is defined
      if (!client.user) {
        this.logger.warn('User not authenticated in send_message, attempting to authenticate from token');

        // Try to get token from different sources
        let token = client.handshake.auth.token;

        // If not in auth, check query params
        if (!token && client.handshake.query.token) {
          token = client.handshake.query.token as string;
        }

        // If not in query, check authorization header
        if (!token && client.handshake.headers.authorization) {
          const authHeader = client.handshake.headers.authorization;
          if (authHeader.startsWith('Bearer ')) {
            token = authHeader.substring(7); // Remove 'Bearer ' prefix
          }
        }

        if (!token) {
          this.logger.error('No token available for authentication');
          client.emit('error', {
            message: 'User not authenticated. Please authenticate first by calling the authenticate event with your token.',
          });
          return;
        }

        try {
          // Verify the token
          const payload = this.jwtService.verify<JwtPayload>(token);
          const userId = payload.id || payload.sub;

          if (!userId) {
            this.logger.error('Invalid token: missing user ID');
            client.emit('error', { message: 'Invalid token: missing user ID' });
            return;
          }

          // Get user from database
          const user = await this.userRepository.findOne({ where: { id: userId } });
          if (!user) {
            this.logger.error(`User not found: ${userId}`);
            client.emit('error', { message: 'User not found' });
            return;
          }

          // Store connection
          if (!this.userConnections.has(userId)) {
            this.userConnections.set(userId, new Set());
          }
          this.userConnections.get(userId).add(client.id);

          // Attach user to socket
          client.user = {
            id: userId,
            email: user.email,
            name: user.name,
          };

          // Join user-specific room
          client.join(`user:${userId}`);

          this.logger.log(`User auto-authenticated in send_message: ${userId}`);
        } catch (error) {
          this.logger.error(`Token verification failed: ${error.message}`);
          client.emit('error', {
            message: 'Authentication failed. Please authenticate first by calling the authenticate event with your token.',
          });
          return;
        }
      }

      // At this point, client.user should be defined
      if (!client.user) {
        this.logger.error('User still not authenticated after auto-authentication attempt');
        client.emit('error', { message: 'User not authenticated' });
        return;
      }

      const userId = client.user.id;

      let message: MessageDto;
      let isAdminConversation = false;

      // Always use admin conversation for user-to-admin chats
      let adminConversationIdFromClient: string | undefined = undefined;
      if (isAdminConversationId(data.conversationId)) {
        isAdminConversation = true;
        // Use the admin conversationId provided by the client
        adminConversationIdFromClient = stripAdminPrefix(data.conversationId);
        // Validate that the admin conversation exists and user is a participant
        const adminConversation = await this.adminChatService.validateUserAdminConversation(userId, adminConversationIdFromClient);
        if (!adminConversation) {
          this.logger.error(`User ${userId} is not a participant of admin conversation ${adminConversationIdFromClient}`);
          client.emit('error', { message: 'Invalid or unauthorized admin conversation' });
          return;
        }
        // Get user to check if they're an admin
        const user = await this.userRepository.findOne({ where: { id: userId } });
        if (user && user.type === UserType.ADMIN) {
          message = await this.adminChatService.sendAdminMessage(userId, adminConversationIdFromClient, data);
        } else {
          message = await this.chatService.sendMessage(userId, data);
        }
      } else {
        // Regular conversation
        message = await this.chatService.sendMessage(userId, data);
      }

      // For admin conversations, always use the admin-<id> provided by the client for all emits and message.conversationId
      let roomConversationId = message.conversationId;
      if (isAdminConversation) {
        const normalizedAdminId = addAdminPrefix(adminConversationIdFromClient);
        message.conversationId = normalizedAdminId;
        roomConversationId = normalizedAdminId;
      }

      // Transform message with timezone and emit to conversation room
      const transformedMessage = await this.socketTimezoneTransformer.transform({
        ...message,
        conversationId: roomConversationId,
      }, userId);
      
      this.server.to(`conversation:${roomConversationId}`).emit('new_message', transformedMessage);

      // Also emit to recipient's user room with timezone transformation
      const recipientTransformedMessage = await this.socketTimezoneTransformer.transform({
        ...message,
        conversationId: roomConversationId,
      }, message.recipientId);
      
      this.server.to(`user:${message.recipientId}`).emit('new_message', recipientTransformedMessage);

      if (isAdminConversation && adminConversationIdFromClient) {
        const adminParticipants = await this.adminConversationManagerService.getActiveAdminParticipants(adminConversationIdFromClient);
        
        for (const participant of adminParticipants) {
          if (participant.adminId !== userId) {
            const adminTransformedMessage = await this.socketTimezoneTransformer.transform({
              ...message,
              conversationId: roomConversationId,
            }, participant.adminId);
            
            this.server.to(`user:${participant.adminId}`).emit('new_message', adminTransformedMessage);
          }
        }
        
        this.server.to(`conversation:${roomConversationId}`).emit('conversation_updated', {
          conversationId: roomConversationId,
          lastMessage: message.content,
          lastMessageAt: message.createdAt,
          senderId: message.senderId,
        });
      }

      // Emit delivered event to sender
      client.emit('on_delivered', {
        messageId: message.id,
        conversationId: roomConversationId,
        delivered: true,
        timestamp: new Date(),
      });

      // Clear typing indicator
      this.clearTypingIndicator(userId, roomConversationId);

      this.logger.log(`User ${userId} sent message to conversation ${roomConversationId}`);
    } catch (error) {
      this.logger.error(`Send message error: ${error.message}`, error.stack);
      client.emit('error', { message: error.message });
    }
  }

  /**
   * Mark messages as read
   * @param client Socket client
   * @param data Read data
   */
  @SubscribeMessage('mark_read')
  async handleMarkRead(@ConnectedSocket() client: SocketWithUser, @MessageBody() data: { conversationId: string }): Promise<void> {
    try {
      // Check if user is defined
      if (!client.user) {
        this.logger.error('User not authenticated in mark_read');
        client.emit('error', { message: 'User not authenticated' });
        return;
      }

      let { conversationId } = data;
      const userId = ChatValidationUtil.validateUserId(client.user.id);
      let dbConversationId = stripAdminPrefix(ChatValidationUtil.validateConversationId(conversationId));
      
      if (isAdminConversationId(conversationId)) {
        const user = await this.userRepository.findOne({ where: { id: userId } });
        
        if (user?.type === UserType.ADMIN) {
          // Admin marking admin conversation as read
          dbConversationId = stripAdminPrefix(conversationId);
          
          // Ensure admin is enrolled as participant first
          await this.adminConversationManagerService.ensureAdminParticipant(dbConversationId, userId);
          await this.adminConversationManagerService.markAdminParticipantAsRead(dbConversationId, userId);
          
          conversationId = addAdminPrefix(dbConversationId);
        } else {
          // Regular user marking their admin conversation as read
          const adminConversation = await this.adminChatService.getOrCreateAdminConversationForUser(userId);
          dbConversationId = adminConversation.id;
          conversationId = addAdminPrefix(adminConversation.id);
        }
      }
      
      await this.chatService.markMessagesAsRead(dbConversationId, userId);

      // Notify conversation participants
      this.server.to(`conversation:${conversationId}`).emit('messages_read', {
        conversationId,
        userId,
      });

      this.logger.log(`User ${userId} marked messages as read in conversation ${conversationId}`);
    } catch (error) {
      this.logger.error(`Mark read error: ${error.message}`, error.stack);
      client.emit('error', { message: error.message });
    }
  }

  /**
   * Handle typing indicator
   * @param client Socket client
   * @param data Typing data
   */
  @SubscribeMessage('typing')
  async handleTyping(@ConnectedSocket() client: SocketWithUser, @MessageBody() data: { conversationId: string; isTyping: boolean }): Promise<void> {
    try {
      // Check if user is defined
      if (!client.user) {
        this.logger.error('User not authenticated in typing');
        client.emit('error', { message: 'User not authenticated' });
        return;
      }

      const { conversationId, isTyping } = data;
      const userId = client.user.id;
      const userName = client.user.name;
      
      // Clear existing timeout if any
      const key = `${userId}:${conversationId}`;
      if (this.typingUsers.has(key)) {
        clearTimeout(this.typingUsers.get(key).timeout);
        this.typingUsers.delete(key);
      }
      // For admin conversations, always use the admin-<id> provided by the client for all emits and message.conversationId
      let roomConversationId = data.conversationId;
      if (isAdminConversationId(data.conversationId)) {
        const normalizedAdminId = stripAdminPrefix(roomConversationId);
        roomConversationId = normalizedAdminId;
      }
      if (isTyping) {
        // Set timeout to automatically clear typing indicator after 5 seconds
        const timeout = setTimeout(() => {
          this.clearTypingIndicator(userId, conversationId);
        }, 5000);

        this.typingUsers.set(key, { userId, conversationId:roomConversationId, timeout });

        // Emit typing indicator to conversation room

        this.server.to(`conversation:${conversationId}`).emit('typing_indicator', {
          conversationId,
          userId,
          userName,
          isTyping: true,
        });
      } else {
        this.clearTypingIndicator(userId, roomConversationId);
      }
    } catch (error) {
      this.logger.error(`Typing indicator error: ${error.message}`, error.stack);
      client.emit('error', { message: error.message });
    }
  }

  /**
   * Clear typing indicator
   * @param userId User ID
   * @param conversationId Conversation ID
   */
  private clearTypingIndicator(userId: string, conversationId: string): void {
    const key = `${userId}:${conversationId}`;

    if (this.typingUsers.has(key)) {
      clearTimeout(this.typingUsers.get(key).timeout);
      this.typingUsers.delete(key);

      // Emit typing stopped to conversation room
      this.server.to(`conversation:${conversationId}`).emit('typing_indicator', {
        conversationId,
        userId,
        isTyping: false,
      });
    }
  }

  /**
   * Authenticate a user from a message event
   * This is a workaround for when the user object is not properly attached during connection
   * @param client Socket client
   * @returns Promise<string> User ID if authenticated, null otherwise
   */
  @SubscribeMessage('authenticate')
  async handleAuthenticate(@ConnectedSocket() client: Socket, @MessageBody() data: { token: string }): Promise<void> {
    try {
      this.logger.log(`Authentication attempt from client ${client.id}`);

      if (!data || !data.token) {
        this.logger.error('No token provided in authenticate event');
        client.emit('auth_error', { message: 'No token provided' });
        return;
      }

      const token = data.token;
      this.logger.log(`Received token in authenticate event: ${token.substring(0, 20)}...`);

      try {
        // Verify the token
        const payload = this.jwtService.verify<JwtPayload>(token);
        const userId = payload.id || payload.sub;

        if (!userId) {
          this.logger.error('Invalid token: missing user ID');
          client.emit('auth_error', { message: 'Invalid token: missing user ID' });
          return;
        }

        // Get user from database
        const user = await this.userRepository.findOne({ where: { id: userId } });
        if (!user) {
          this.logger.error(`User not found: ${userId}`);
          client.emit('auth_error', { message: 'User not found' });
          return;
        }

        // Store connection
        if (!this.userConnections.has(userId)) {
          this.userConnections.set(userId, new Set());
        }
        this.userConnections.get(userId).add(client.id);

        // Attach user to socket
        (client as SocketWithUser).user = {
          id: userId,
          email: user.email,
          name: user.name,
        };

        // Join user-specific room
        client.join(`user:${userId}`);

        this.logger.log(`User authenticated via event: ${userId}`);
        client.emit('authenticated', {
          userId,
          name: user.name,
          success: true,
        });

        // Broadcast user online status
        this.broadcastUserStatus(userId, true);
      } catch (error) {
        this.logger.error(`Token verification failed: ${error.message}`);
        client.emit('auth_error', { message: `Invalid token: ${error.message}` });
      }
    } catch (error) {
      this.logger.error(`Authentication error: ${error.message}`, error.stack);
      client.emit('auth_error', { message: error.message });
    }
  }

  /**
   * Send a message to a user
   * @param userId User ID
   * @param message Message data
   * @returns Whether message was sent
   */
  async sendMessageToUser(userId: string, message: MessageDto): Promise<boolean> {
    try {
      // Check if user is connected
      if (!this.isUserConnected(userId)) {
        return false;
      }

      // Transform message with timezone and send to user's room
      const transformedMessage = await this.socketTimezoneTransformer.transform(message, userId);
      this.server.to(`user:${userId}`).emit('new_message', transformedMessage);
      this.logger.log(`Sent message ${message.id} to user ${userId}`);
      return true;
    } catch (error) {
      this.logger.error(`Error sending message to user ${userId}: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Ensure an admin is added as a participant in an admin conversation
   * @param conversationId The conversation ID
   * @param adminId The admin ID
   */

}
