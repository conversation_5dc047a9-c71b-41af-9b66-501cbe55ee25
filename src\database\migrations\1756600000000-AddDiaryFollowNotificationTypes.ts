import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddDiaryFollowNotificationTypes1756600000000 implements MigrationInterface {
  name = 'AddDiaryFollowNotificationTypes1756600000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add new notification types to the enum
    await queryRunner.query(`
      ALTER TYPE "notification_type_enum" 
      ADD VALUE IF NOT EXISTS 'diary_follow_accepted'
    `);
    
    await queryRunner.query(`
      ALTER TYPE "notification_type_enum" 
      ADD VALUE IF NOT EXISTS 'diary_follow_rejected'
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Note: PostgreSQL doesn't support removing enum values directly
    // This would require recreating the enum type, which is complex
    // For now, we'll leave the enum values in place
    console.log('Cannot remove enum values in PostgreSQL. Values will remain in the enum.');
  }
}