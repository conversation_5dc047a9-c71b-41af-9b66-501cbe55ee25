import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { TimezoneService } from './timezone.service';
import { User } from '../../database/entities/user.entity';

describe('TimezoneService', () => {
  let service: TimezoneService;
  let mockUserRepository: any;

  beforeEach(async () => {
    mockUserRepository = {
      findOne: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TimezoneService,
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
      ],
    }).compile();

    service = module.get<TimezoneService>(TimezoneService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should return default timezone for new user', async () => {
    mockUserRepository.findOne.mockResolvedValue({ timezone: 'Asia/Seoul' });
    
    const timezone = await service.getUserTimezone('test-user-id');
    expect(timezone).toBe('Asia/Seoul');
  });

  it('should validate supported timezones', () => {
    expect(service.isValidTimezone('Asia/Seoul')).toBe(true);
    expect(service.isValidTimezone('America/New_York')).toBe(true);
    expect(service.isValidTimezone('Invalid/Timezone')).toBe(false);
  });

  it('should convert UTC to user timezone', () => {
    const utcDate = new Date('2024-01-15T10:30:00.000Z');
    const converted = service.convertUTCToUserTimezone(utcDate, 'Asia/Seoul');
    
    // Seoul is UTC+9, so 10:30 UTC should become 19:30 Seoul time
    const convertedDate = new Date(converted);
    expect(convertedDate.getHours()).toBe(19);
    expect(convertedDate.getMinutes()).toBe(30);
  });

  it('should get date range for user timezone', () => {
    const range = service.getUserDateRange('2024-01-15', 'Asia/Seoul');
    
    expect(range.start).toBeInstanceOf(Date);
    expect(range.end).toBeInstanceOf(Date);
    expect(range.end.getTime()).toBeGreaterThan(range.start.getTime());
  });
});