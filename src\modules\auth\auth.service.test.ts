import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { JwtService } from '@nestjs/jwt';
import { DataSource } from 'typeorm';
import { BadRequestException, UnauthorizedException, ConflictException, NotFoundException } from '@nestjs/common';
import { AuthService } from './auth.service';
import { UsersService } from '../users/users.service';
import EmailService from '../../common/services/email.service';
import { ProfilePictureService } from '../../common/services/profile-picture.service';
import { DiaryService } from '../diary/diary.service';
import { AsyncNotificationHelperService } from '../notification/async-notification-helper.service';
import { DeeplinkService } from '../../common/utils/deeplink.service';
import { TokenBlacklistService } from '../../common/services/token-blacklist.service';
import { EmailTemplateService } from '../../common/services/email-template.service';
import { PlansService } from '../plans/plans.service';
import { Role } from '../../database/entities/role.entity';
import { User, UserType } from '../../database/entities/user.entity';
import { UserPlan } from '../../database/entities/user-plan.entity';
import { PasswordReset } from '../../database/entities/password-reset.entity';
import { EmailVerification } from '../../database/entities/email-verification.entity';
import { TutorApproval, TutorApprovalStatus } from '../../database/entities/tutor-approval.entity';
import { Diary } from '../../database/entities/diary.entity';
import { LoginUserDto, RegisterDto, ForgotPasswordDto, ResetPasswordDto, ChangePasswordDto } from '../../database/models/users.dto';

describe('AuthService', () => {
  let service: AuthService;
  let usersService: jest.Mocked<UsersService>;
  let jwtService: jest.Mocked<JwtService>;
  let emailService: jest.Mocked<EmailService>;
  let profilePictureService: jest.Mocked<ProfilePictureService>;
  let userRepository: any;
  let emailVerificationRepository: any;
  let passwordResetRepository: any;
  let tutorApprovalRepository: any;
  let dataSource: any;

  beforeEach(async () => {
    const mockUserRepository = {
      findOne: jest.fn(),
      find: jest.fn(),
      create: jest.fn(),
      save: jest.fn(),
    };

    const mockEmailVerificationRepository = {
      findOne: jest.fn(),
      save: jest.fn(),
      delete: jest.fn(),
    };

    const mockPasswordResetRepository = {
      findOne: jest.fn(),
      save: jest.fn(),
      delete: jest.fn(),
    };

    const mockTutorApprovalRepository = {
      findOne: jest.fn(),
      save: jest.fn(),
    };

    const mockDataSource = {
      query: jest.fn(),
      createQueryRunner: jest.fn().mockReturnValue({
        connect: jest.fn(),
        startTransaction: jest.fn(),
        commitTransaction: jest.fn(),
        rollbackTransaction: jest.fn(),
        release: jest.fn(),
        manager: {
          getRepository: jest.fn().mockImplementation((entity) => {
            if (entity === User) return mockUserRepository;
            if (entity === Role) return { findOne: jest.fn(), create: jest.fn(), save: jest.fn() };
            return { save: jest.fn(), create: jest.fn(), delete: jest.fn() };
          }),
        },
      }),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: UsersService,
          useValue: {
            findByUserId: jest.fn(),
            findByEmail: jest.fn(),
            getAllAdminUsers: jest.fn(),
          },
        },
        {
          provide: EmailService,
          useValue: {
            sendVerificationLink: jest.fn(),
            sendPasswordResetLink: jest.fn(),
            sendUserId: jest.fn(),
            sendPasswordChangeNotification: jest.fn(),
          },
        },
        {
          provide: JwtService,
          useValue: {
            sign: jest.fn(),
            decode: jest.fn(),
          },
        },
        {
          provide: ProfilePictureService,
          useValue: {
            hasProfilePicture: jest.fn(),
            getProfilePictureDirectUrl: jest.fn(),
          },
        },
        {
          provide: DataSource,
          useValue: mockDataSource,
        },
        {
          provide: DiaryService,
          useValue: {
            getOrCreateDiary: jest.fn(),
          },
        },
        {
          provide: AsyncNotificationHelperService,
          useValue: {
            notifyAsync: jest.fn(),
            notifyManyAsync: jest.fn(),
          },
        },
        {
          provide: DeeplinkService,
          useValue: {
            getLinkHtml: jest.fn(),
          },
        },
        {
          provide: TokenBlacklistService,
          useValue: {
            blacklistToken: jest.fn(),
          },
        },
        {
          provide: EmailTemplateService,
          useValue: {
            generateSubscriptionWelcomeTemplate: jest.fn(),
            generatePaymentConfirmationTemplate: jest.fn(),
          },
        },
        {
          provide: PlansService,
          useValue: {
            subscribeWithFreePayment: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(Role),
          useValue: { findOne: jest.fn(), find: jest.fn(), create: jest.fn(), save: jest.fn() },
        },
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
        {
          provide: getRepositoryToken(UserPlan),
          useValue: { findOne: jest.fn(), find: jest.fn(), create: jest.fn(), save: jest.fn() },
        },
        {
          provide: getRepositoryToken(PasswordReset),
          useValue: mockPasswordResetRepository,
        },
        {
          provide: getRepositoryToken(EmailVerification),
          useValue: mockEmailVerificationRepository,
        },
        {
          provide: getRepositoryToken(TutorApproval),
          useValue: mockTutorApprovalRepository,
        },
        {
          provide: getRepositoryToken(Diary),
          useValue: { findOne: jest.fn(), find: jest.fn(), create: jest.fn(), save: jest.fn() },
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    usersService = module.get(UsersService);
    jwtService = module.get(JwtService);
    emailService = module.get(EmailService);
    profilePictureService = module.get(ProfilePictureService);
    userRepository = module.get(getRepositoryToken(User));
    emailVerificationRepository = module.get(getRepositoryToken(EmailVerification));
    passwordResetRepository = module.get(getRepositoryToken(PasswordReset));
    tutorApprovalRepository = module.get(getRepositoryToken(TutorApproval));
    dataSource = module.get(DataSource);
  });

  describe('validateUser', () => {
    it('should validate user with correct credentials', async () => {
      const mockUser = {
        id: 'user-1',
        userId: 'TEST001',
        verifyPassword: jest.fn().mockReturnValue(true),
      };

      usersService.findByUserId.mockResolvedValue(mockUser as any);

      const result = await service.validateUser('TEST001', 'password123');

      expect(result).toEqual(mockUser);
      expect(usersService.findByUserId).toHaveBeenCalledWith('TEST001');
      expect(mockUser.verifyPassword).toHaveBeenCalledWith('password123');
    });

    it('should return null for invalid credentials', async () => {
      const mockUser = {
        id: 'user-1',
        userId: 'TEST001',
        verifyPassword: jest.fn().mockReturnValue(false),
      };

      usersService.findByUserId.mockResolvedValue(mockUser as any);

      const result = await service.validateUser('TEST001', 'wrongpassword');

      expect(result).toBeNull();
    });

    it('should return null for non-existent user', async () => {
      usersService.findByUserId.mockResolvedValue(null);

      const result = await service.validateUser('NONEXISTENT', 'password123');

      expect(result).toBeNull();
    });

    it('should handle password verification errors gracefully', async () => {
      const mockUser = {
        id: 'user-1',
        userId: 'TEST001',
        verifyPassword: jest.fn().mockImplementation(() => {
          throw new Error('Password verification failed');
        }),
      };

      usersService.findByUserId.mockResolvedValue(mockUser as any);

      const result = await service.validateUser('TEST001', 'password123');

      expect(result).toBeNull();
    });
  });

  describe('login', () => {
    const mockUser = {
      id: 'user-1',
      userId: 'TEST001',
      email: '<EMAIL>',
      name: 'Test User',
      type: UserType.STUDENT,
      isActive: true,
      isConfirmed: true,
      userRoles: [{ role: { name: 'student' } }],
      verifyPassword: jest.fn().mockReturnValue(true),
      toDto: jest.fn().mockReturnValue({
        id: 'user-1',
        userId: 'TEST001',
        email: '<EMAIL>',
        type: 'student',
      }),
      lastLoginAt: new Date(),
      refreshToken: '',
      refreshTokenExpiry: new Date(0),
    };

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should login student successfully', async () => {
      const loginDto: LoginUserDto = {
        userId: 'TEST001',
        password: 'password123',
      };

      usersService.findByUserId.mockResolvedValue(mockUser as any);
      userRepository.save.mockResolvedValue(mockUser);
      jwtService.sign.mockReturnValue('jwt-token');
      jwtService.decode.mockReturnValue({ exp: Math.floor(Date.now() / 1000) + 3600 });
      profilePictureService.hasProfilePicture.mockResolvedValue(false);
      dataSource.query.mockResolvedValue([]);

      const result = await service.login(loginDto);

      expect(result).toHaveProperty('access_token', 'jwt-token');
      expect(result).toHaveProperty('user');
      expect(userRepository.save).toHaveBeenCalled();
    });

    it('should login with remember me enabled', async () => {
      const loginDto: LoginUserDto = {
        userId: 'TEST001',
        password: 'password123',
        rememberMe: true,
      };

      usersService.findByUserId.mockResolvedValue(mockUser as any);
      userRepository.save.mockResolvedValue(mockUser);
      jwtService.sign.mockReturnValue('jwt-token');
      jwtService.decode.mockReturnValue({ exp: Math.floor(Date.now() / 1000) + 3600 });
      profilePictureService.hasProfilePicture.mockResolvedValue(false);
      dataSource.query.mockResolvedValue([]);

      const result = await service.login(loginDto);

      expect(result).toHaveProperty('refresh_token');
      expect(result).toHaveProperty('refresh_token_expires');
    });

    it('should throw UnauthorizedException for invalid user ID', async () => {
      const loginDto: LoginUserDto = {
        userId: 'INVALID',
        password: 'password123',
      };

      usersService.findByUserId.mockResolvedValue(null);

      await expect(service.login(loginDto)).rejects.toThrow(UnauthorizedException);
    });

    it('should throw UnauthorizedException for invalid password', async () => {
      const loginDto: LoginUserDto = {
        userId: 'TEST001',
        password: 'wrongpassword',
      };

      const mockUserWithWrongPassword = {
        ...mockUser,
        verifyPassword: jest.fn().mockReturnValue(false),
      };

      usersService.findByUserId.mockResolvedValue(mockUserWithWrongPassword as any);

      await expect(service.login(loginDto)).rejects.toThrow(UnauthorizedException);
    });

    it('should throw UnauthorizedException for unconfirmed user', async () => {
      const loginDto: LoginUserDto = {
        userId: 'TEST001',
        password: 'password123',
      };

      const unconfirmedUser = {
        ...mockUser,
        isConfirmed: false,
      };

      usersService.findByUserId.mockResolvedValue(unconfirmedUser as any);

      await expect(service.login(loginDto)).rejects.toThrow(UnauthorizedException);
    });

    it('should throw UnauthorizedException for inactive user', async () => {
      const loginDto: LoginUserDto = {
        userId: 'TEST001',
        password: 'password123',
      };

      const inactiveUser = {
        ...mockUser,
        isActive: false,
      };

      usersService.findByUserId.mockResolvedValue(inactiveUser as any);

      await expect(service.login(loginDto)).rejects.toThrow(UnauthorizedException);
    });

    it('should check tutor approval status for tutor users', async () => {
      const loginDto: LoginUserDto = {
        userId: 'TUTOR001',
        password: 'password123',
      };

      const tutorUser = {
        ...mockUser,
        type: UserType.TUTOR,
        userRoles: [{ role: { name: 'tutor' } }],
      };

      usersService.findByUserId.mockResolvedValue(tutorUser as any);
      tutorApprovalRepository.findOne.mockResolvedValue({
        status: TutorApprovalStatus.APPROVED,
      });

      userRepository.save.mockResolvedValue(tutorUser);
      jwtService.sign.mockReturnValue('jwt-token');
      jwtService.decode.mockReturnValue({ exp: Math.floor(Date.now() / 1000) + 3600 });
      profilePictureService.hasProfilePicture.mockResolvedValue(false);
      dataSource.query.mockResolvedValue([]);

      const result = await service.login(loginDto);

      expect(result).toHaveProperty('access_token');
      expect(tutorApprovalRepository.findOne).toHaveBeenCalled();
    });

    it('should throw UnauthorizedException for unapproved tutor', async () => {
      const loginDto: LoginUserDto = {
        userId: 'TUTOR001',
        password: 'password123',
      };

      const tutorUser = {
        ...mockUser,
        type: UserType.TUTOR,
      };

      usersService.findByUserId.mockResolvedValue(tutorUser as any);
      tutorApprovalRepository.findOne.mockResolvedValue({
        status: TutorApprovalStatus.PENDING,
      });

      await expect(service.login(loginDto)).rejects.toThrow(UnauthorizedException);
    });
  });

  describe('register', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should register student successfully', async () => {
      const registerDto: RegisterDto = {
        userId: 'STUDENT001',
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123',
        phoneNumber: '1234567890',
        gender: 'male',
        type: UserType.STUDENT,
        agreedToTerms: true,
      };

      usersService.findByEmail.mockResolvedValue(null);
      usersService.findByUserId.mockResolvedValue(null);
      emailService.sendVerificationLink.mockResolvedValue(true);

      const result = await service.register(registerDto);

      expect(result).toEqual({
        success: true,
        message: 'Registration successful. Please check your email for verification link.',
        userId: expect.any(String),
      });
    });

    it('should register tutor successfully', async () => {
      const registerDto: RegisterDto = {
        userId: 'TUTOR001',
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123',
        phoneNumber: '1234567890',
        gender: 'female',
        type: UserType.TUTOR,
        agreedToTerms: true,
        bio: 'Experienced tutor',
      };

      usersService.findByEmail.mockResolvedValue(null);
      usersService.findByUserId.mockResolvedValue(null);
      emailService.sendVerificationLink.mockResolvedValue(true);

      const result = await service.register(registerDto);

      expect(result).toEqual({
        success: true,
        message: 'Registration successful. Please check your email for verification link.',
        userId: expect.any(String),
      });
    });

    it('should throw ConflictException for existing email', async () => {
      const registerDto: RegisterDto = {
        userId: 'STUDENT001',
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123',
        phoneNumber: '1234567890',
        gender: 'male',
        type: UserType.STUDENT,
        agreedToTerms: true,
      };

      const existingUser = { id: 'existing-user', email: '<EMAIL>' };
      usersService.findByEmail.mockResolvedValue(existingUser as any);

      await expect(service.register(registerDto)).rejects.toThrow(ConflictException);
    });

    it('should throw ConflictException for existing userId', async () => {
      const registerDto: RegisterDto = {
        userId: 'EXISTING001',
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123',
        phoneNumber: '1234567890',
        gender: 'male',
        type: UserType.STUDENT,
        agreedToTerms: true,
      };

      const existingUser = { id: 'existing-user', userId: 'EXISTING001' };
      usersService.findByEmail.mockResolvedValue(null);
      usersService.findByUserId.mockResolvedValue(existingUser as any);

      await expect(service.register(registerDto)).rejects.toThrow(ConflictException);
    });

    it('should throw BadRequestException for password mismatch', async () => {
      const registerDto: RegisterDto = {
        userId: 'STUDENT001',
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'differentpassword',
        phoneNumber: '1234567890',
        gender: 'male',
        type: UserType.STUDENT,
        agreedToTerms: true,
      };

      usersService.findByEmail.mockResolvedValue(null);
      usersService.findByUserId.mockResolvedValue(null);

      await expect(service.register(registerDto)).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException for not agreeing to terms', async () => {
      const registerDto: RegisterDto = {
        userId: 'STUDENT001',
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123',
        phoneNumber: '1234567890',
        gender: 'male',
        type: UserType.STUDENT,
        agreedToTerms: false,
      };

      usersService.findByEmail.mockResolvedValue(null);
      usersService.findByUserId.mockResolvedValue(null);

      await expect(service.register(registerDto)).rejects.toThrow(BadRequestException);
    });
  });

  describe('verifyEmail', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should verify email successfully for student', async () => {
      const token = 'verification-token';
      const mockVerification = {
        token,
        userId: 'user-1',
        expirationTime: new Date(Date.now() + 300000),
        isUsed: false,
      };

      const mockUser = {
        id: 'user-1',
        type: UserType.STUDENT,
        isConfirmed: false,
        userRoles: [{ role: { name: 'student' } }],
        toDto: jest.fn().mockReturnValue({ id: 'user-1', type: 'student' }),
      };

      emailVerificationRepository.findOne.mockResolvedValue(mockVerification);
      userRepository.findOne.mockResolvedValue(mockUser);
      userRepository.save.mockResolvedValue({ ...mockUser, isConfirmed: true });
      emailVerificationRepository.save.mockResolvedValue({ ...mockVerification, isUsed: true });
      jwtService.sign.mockReturnValue('jwt-token');
      profilePictureService.hasProfilePicture.mockResolvedValue(false);

      const result = await service.verifyEmail(token);

      expect(result).toHaveProperty('success', true);
      expect(result).toHaveProperty('access_token', 'jwt-token');
      expect(userRepository.save).toHaveBeenCalled();
    });

    it('should verify email and create tutor approval for tutor', async () => {
      const token = 'verification-token';
      const mockVerification = {
        token,
        userId: 'tutor-1',
        expirationTime: new Date(Date.now() + 300000),
        isUsed: false,
      };

      const mockTutor = {
        id: 'tutor-1',
        type: UserType.TUTOR,
        isConfirmed: false,
        userRoles: [{ role: { name: 'tutor' } }],
        toDto: jest.fn().mockReturnValue({ id: 'tutor-1', type: 'tutor' }),
      };

      emailVerificationRepository.findOne.mockResolvedValue(mockVerification);
      userRepository.findOne.mockResolvedValue(mockTutor);
      userRepository.save.mockResolvedValue({ ...mockTutor, isConfirmed: true });
      emailVerificationRepository.save.mockResolvedValue({ ...mockVerification, isUsed: true });
      tutorApprovalRepository.save.mockResolvedValue({});
      jwtService.sign.mockReturnValue('jwt-token');
      profilePictureService.hasProfilePicture.mockResolvedValue(false);
      usersService.getAllAdminUsers.mockResolvedValue([]);

      const result = await service.verifyEmail(token);

      expect(result).toHaveProperty('success', true);
      expect(result).toHaveProperty('requiresApproval', true);
      expect(tutorApprovalRepository.save).toHaveBeenCalled();
    });

    it('should throw BadRequestException for invalid token', async () => {
      const token = 'invalid-token';

      emailVerificationRepository.findOne.mockResolvedValue(null);

      await expect(service.verifyEmail(token)).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException for expired token', async () => {
      const token = 'expired-token';
      const mockVerification = {
        token,
        userId: 'user-1',
        expirationTime: new Date(Date.now() - 300000),
        isUsed: false,
      };

      emailVerificationRepository.findOne.mockResolvedValue(mockVerification);

      await expect(service.verifyEmail(token)).rejects.toThrow(BadRequestException);
    });
  });

  describe('forgotPassword', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should send password reset email for valid email', async () => {
      const forgotPasswordDto: ForgotPasswordDto = {
        identifier: '<EMAIL>',
      };

      const mockUser = {
        id: 'user-1',
        email: '<EMAIL>',
      };

      userRepository.findOne.mockResolvedValue(mockUser);
      passwordResetRepository.delete.mockResolvedValue({ affected: 1 });
      passwordResetRepository.save.mockResolvedValue({ id: 'reset-1' });
      emailService.sendPasswordResetLink.mockResolvedValue(true);

      const result = await service.forgotPassword(forgotPasswordDto);

      expect(result).toEqual({
        success: true,
        message: 'If your email is registered, you will receive a password reset link.',
      });
      expect(emailService.sendPasswordResetLink).toHaveBeenCalled();
    });

    it('should send password reset email for valid userId', async () => {
      const forgotPasswordDto: ForgotPasswordDto = {
        identifier: 'TEST001',
      };

      const mockUser = {
        id: 'user-1',
        userId: 'TEST001',
        email: '<EMAIL>',
      };

      userRepository.findOne.mockResolvedValue(mockUser);
      passwordResetRepository.delete.mockResolvedValue({ affected: 1 });
      passwordResetRepository.save.mockResolvedValue({ id: 'reset-1' });
      emailService.sendPasswordResetLink.mockResolvedValue(true);

      const result = await service.forgotPassword(forgotPasswordDto);

      expect(result).toEqual({
        success: true,
        message: 'If your email is registered, you will receive a password reset link.',
      });
    });

    it('should return success message for non-existent identifier', async () => {
      const forgotPasswordDto: ForgotPasswordDto = {
        identifier: '<EMAIL>',
      };

      userRepository.findOne.mockResolvedValue(null);

      const result = await service.forgotPassword(forgotPasswordDto);

      expect(result).toEqual({
        success: true,
        message: 'If your email is registered, you will receive a password reset link.',
      });
    });
  });

  describe('resetPassword', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should reset password successfully', async () => {
      const resetPasswordDto: ResetPasswordDto = {
        token: 'reset-token',
        newPassword: 'newpassword123',
      };

      const mockReset = {
        token: 'reset-token',
        userId: 'user-1',
        expirationTime: new Date(Date.now() + 300000),
        isUsed: false,
      };

      const mockUser = {
        id: 'user-1',
        setPassword: jest.fn(),
      };

      passwordResetRepository.findOne.mockResolvedValue(mockReset);
      userRepository.findOne.mockResolvedValue(mockUser);
      userRepository.save.mockResolvedValue(mockUser);
      passwordResetRepository.save.mockResolvedValue({ ...mockReset, isUsed: true });

      const result = await service.resetPassword(resetPasswordDto);

      expect(result).toEqual({
        success: true,
        message: 'Password has been reset successfully. You can now login with your new password.',
      });
      expect(mockUser.setPassword).toHaveBeenCalledWith('newpassword123');
    });

    it('should throw BadRequestException for invalid token', async () => {
      const resetPasswordDto: ResetPasswordDto = {
        token: 'invalid-token',
        newPassword: 'newpassword123',
      };

      passwordResetRepository.findOne.mockResolvedValue(null);

      await expect(service.resetPassword(resetPasswordDto)).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException for expired token', async () => {
      const resetPasswordDto: ResetPasswordDto = {
        token: 'expired-token',
        newPassword: 'newpassword123',
      };

      const mockReset = {
        token: 'expired-token',
        userId: 'user-1',
        expirationTime: new Date(Date.now() - 300000),
        isUsed: false,
      };

      passwordResetRepository.findOne.mockResolvedValue(mockReset);

      await expect(service.resetPassword(resetPasswordDto)).rejects.toThrow(BadRequestException);
    });
  });

  describe('changePassword', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should change password successfully', async () => {
      const changePasswordDto: ChangePasswordDto = {
        currentPassword: 'oldpassword123',
        newPassword: 'newpassword123',
        confirmNewPassword: 'newpassword123',
      };

      const mockRequest = {
        user: { sub: 'user-1' },
        ip: '127.0.0.1',
        get: jest.fn().mockReturnValue('Mozilla/5.0'),
      };

      const mockUser = {
        id: 'user-1',
        userId: 'TEST001',
        name: 'Test User',
        type: UserType.STUDENT,
        userRoles: [{ role: { name: 'student' } }],
        verifyPassword: jest.fn()
          .mockReturnValueOnce(true)
          .mockReturnValueOnce(false),
        setPassword: jest.fn(),
        toDto: jest.fn().mockReturnValue({ id: 'user-1', type: 'student' }),
        refreshToken: '',
        refreshTokenExpiry: new Date(0),
        lastLoginAt: new Date(),
      };

      userRepository.findOne.mockResolvedValue(mockUser);
      userRepository.save.mockResolvedValue(mockUser);
      jwtService.sign.mockReturnValue('new-jwt-token');
      jwtService.decode.mockReturnValue({ exp: Math.floor(Date.now() / 1000) + 3600 });
      profilePictureService.hasProfilePicture.mockResolvedValue(false);
      emailService.sendPasswordChangeNotification.mockResolvedValue(true);
      dataSource.query.mockResolvedValue([]);

      const result = await service.changePassword(changePasswordDto, mockRequest as any);

      expect(result).toHaveProperty('access_token', 'new-jwt-token');
      expect(mockUser.setPassword).toHaveBeenCalledWith('newpassword123');
      expect(emailService.sendPasswordChangeNotification).toHaveBeenCalled();
    });

    it('should throw BadRequestException for password mismatch', async () => {
      const changePasswordDto: ChangePasswordDto = {
        currentPassword: 'oldpassword123',
        newPassword: 'newpassword123',
        confirmNewPassword: 'differentpassword',
      };

      const mockRequest = {
        user: { sub: 'user-1' },
      };

      await expect(service.changePassword(changePasswordDto, mockRequest as any))
        .rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException for incorrect current password', async () => {
      const changePasswordDto: ChangePasswordDto = {
        currentPassword: 'wrongpassword',
        newPassword: 'newpassword123',
        confirmNewPassword: 'newpassword123',
      };

      const mockRequest = {
        user: { sub: 'user-1' },
      };

      const mockUser = {
        id: 'user-1',
        verifyPassword: jest.fn().mockReturnValue(false),
      };

      userRepository.findOne.mockResolvedValue(mockUser);

      await expect(service.changePassword(changePasswordDto, mockRequest as any))
        .rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException for same password', async () => {
      const changePasswordDto: ChangePasswordDto = {
        currentPassword: 'password123',
        newPassword: 'password123',
        confirmNewPassword: 'password123',
      };

      const mockRequest = {
        user: { sub: 'user-1' },
      };

      const mockUser = {
        id: 'user-1',
        verifyPassword: jest.fn()
          .mockReturnValueOnce(true)
          .mockReturnValueOnce(true),
      };

      userRepository.findOne.mockResolvedValue(mockUser);

      await expect(service.changePassword(changePasswordDto, mockRequest as any))
        .rejects.toThrow(BadRequestException);
    });
  });

  describe('logout', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should logout user successfully', async () => {
      const userId = 'user-1';
      const accessToken = 'jwt-token';

      const mockUser = {
        id: userId,
        userId: 'TEST001',
        refreshToken: 'refresh-token',
        refreshTokenExpiry: new Date(),
      };

      userRepository.findOne.mockResolvedValue(mockUser);
      userRepository.save.mockResolvedValue({
        ...mockUser,
        refreshToken: '',
        refreshTokenExpiry: new Date(0),
      });

      await service.logout(userId, accessToken);

      expect(userRepository.save).toHaveBeenCalled();
    });

    it('should logout user without access token', async () => {
      const userId = 'user-1';

      const mockUser = {
        id: userId,
        userId: 'TEST001',
        refreshToken: 'refresh-token',
        refreshTokenExpiry: new Date(),
      };

      userRepository.findOne.mockResolvedValue(mockUser);
      userRepository.save.mockResolvedValue({
        ...mockUser,
        refreshToken: '',
        refreshTokenExpiry: new Date(0),
      });

      await service.logout(userId);

      expect(userRepository.save).toHaveBeenCalled();
    });

    it('should throw NotFoundException for non-existent user', async () => {
      const userId = 'non-existent';

      userRepository.findOne.mockResolvedValue(null);

      await expect(service.logout(userId)).rejects.toThrow(NotFoundException);
    });
  });

  describe('checkTutorApprovalStatus', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should return approved status for approved tutor', async () => {
      const userId = 'tutor-1';
      const mockApproval = {
        userId,
        status: TutorApprovalStatus.APPROVED,
        createdAt: new Date(),
      };

      tutorApprovalRepository.findOne.mockResolvedValue(mockApproval);

      const result = await service.checkTutorApprovalStatus(userId);

      expect(result).toEqual({
        isApproved: true,
        status: TutorApprovalStatus.APPROVED,
      });
    });

    it('should return rejected status for rejected tutor', async () => {
      const userId = 'tutor-1';
      const mockApproval = {
        userId,
        status: TutorApprovalStatus.REJECTED,
        rejectionReason: 'Insufficient qualifications',
        createdAt: new Date(),
      };

      tutorApprovalRepository.findOne.mockResolvedValue(mockApproval);

      const result = await service.checkTutorApprovalStatus(userId);

      expect(result).toEqual({
        isApproved: false,
        status: TutorApprovalStatus.REJECTED,
        message: 'Your tutor account has been rejected. Reason: Insufficient qualifications. Please contact an administrator for more information.',
      });
    });

    it('should return pending status for tutor without approval record', async () => {
      const userId = 'tutor-1';

      tutorApprovalRepository.findOne.mockResolvedValue(null);

      const result = await service.checkTutorApprovalStatus(userId);

      expect(result).toEqual({
        isApproved: false,
        status: TutorApprovalStatus.PENDING,
        message: 'Your tutor account is pending approval. Please wait for an administrator to approve your account.',
      });
    });
  });

  describe('refreshToken', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should refresh token successfully', async () => {
      const refreshTokenDto = { refreshToken: 'valid-refresh-token' };
      
      const mockUser = {
        id: 'user-1',
        userId: 'TEST001',
        name: 'Test User',
        type: UserType.STUDENT,
        isActive: true,
        isConfirmed: true,
        refreshToken: 'valid-refresh-token',
        refreshTokenExpiry: new Date(Date.now() + ********),
        userRoles: [{ role: { name: 'student' } }],
        toDto: jest.fn().mockReturnValue({ id: 'user-1', type: 'student' }),
        lastLoginAt: new Date(),
      };

      userRepository.findOne.mockResolvedValue(mockUser);
      userRepository.save.mockResolvedValue(mockUser);
      jwtService.sign.mockReturnValue('new-jwt-token');
      jwtService.decode.mockReturnValue({ exp: Math.floor(Date.now() / 1000) + 3600 });
      profilePictureService.hasProfilePicture.mockResolvedValue(false);
      dataSource.query.mockResolvedValue([]);

      const result = await service.refreshToken(refreshTokenDto);

      expect(result).toHaveProperty('access_token', 'new-jwt-token');
      expect(result).toHaveProperty('refresh_token');
    });

    it('should throw UnauthorizedException for invalid refresh token', async () => {
      const refreshTokenDto = { refreshToken: 'invalid-refresh-token' };

      userRepository.findOne.mockResolvedValue(null);

      await expect(service.refreshToken(refreshTokenDto)).rejects.toThrow(UnauthorizedException);
    });

    it('should throw UnauthorizedException for expired refresh token', async () => {
      const refreshTokenDto = { refreshToken: 'expired-refresh-token' };
      
      const mockUser = {
        id: 'user-1',
        refreshToken: 'expired-refresh-token',
        refreshTokenExpiry: new Date(Date.now() - ********),
      };

      userRepository.findOne.mockResolvedValue(mockUser);

      await expect(service.refreshToken(refreshTokenDto)).rejects.toThrow(UnauthorizedException);
    });
  });
});