import { Injectable, Logger } from '@nestjs/common';
import { NotificationOutboxService } from './notification-outbox.service';
import { NotificationType } from '../../database/entities/notification.entity';
import { OutboxNotificationType } from '../../database/entities/notification-outbox.entity';
import { NotificationService } from './notification.service';

/**
 * Async notification helper using the outbox pattern for reliable delivery
 * Guarantees notification delivery even across server restarts
 */
@Injectable()
export class AsyncNotificationHelperService {
  private readonly logger = new Logger(AsyncNotificationHelperService.name);

  constructor(
    private readonly outboxService: NotificationOutboxService,
    private readonly notificationService: NotificationService
  ) {}

  /**
   * Map NotificationType to OutboxNotificationType
   */
  private mapToOutboxType(type: NotificationType): OutboxNotificationType {
    const mapping: Record<NotificationType, OutboxNotificationType> = {
      // QA Notifications
      [NotificationType.QA_ASSIGNMENT]: OutboxNotificationType.QA_SUBMISSION,
      [NotificationType.QA_MISSION_SUBMISSION]: OutboxNotificationType.QA_SUBMISSION,
      [NotificationType.QA_MISSION_REVIEW]: OutboxNotificationType.QA_REVIEWED,
      [NotificationType.QA_MISSION_FEEDBACK]: OutboxNotificationType.QA_REVIEWED,
      // Diary Notifications
      [NotificationType.DIARY_SUBMISSION]: OutboxNotificationType.DIARY_SUBMISSION,
      [NotificationType.DIARY_UPDATE]: OutboxNotificationType.DIARY_SUBMISSION,
      [NotificationType.DIARY_REVIEW]: OutboxNotificationType.DIARY_REVIEWED,
      [NotificationType.DIARY_FEEDBACK]: OutboxNotificationType.DIARY_REVIEWED,
      [NotificationType.MISSION_CREATED]: OutboxNotificationType.MISSION_SUBMISSION,
      [NotificationType.MISSION_SUBMISSION]: OutboxNotificationType.MISSION_SUBMISSION,
      [NotificationType.MISSION_SUBMISSION_UPDATED]: OutboxNotificationType.MISSION_SUBMISSION,
      [NotificationType.MISSION_FEEDBACK]: OutboxNotificationType.MISSION_REVIEWED,
      [NotificationType.MISSION_CORRECTION]: OutboxNotificationType.MISSION_REVIEWED,
      [NotificationType.MISSION_REVIEW_COMPLETE]: OutboxNotificationType.MISSION_REVIEWED,
      [NotificationType.MISSION_CONFIRMED]: OutboxNotificationType.MISSION_CONFIRMED,
      [NotificationType.TUTOR_GREETING]: OutboxNotificationType.TUTOR_ASSIGNMENT,
      [NotificationType.TUTOR_ASSIGNMENT]: OutboxNotificationType.TUTOR_ASSIGNMENT,
      [NotificationType.TUTOR_VERIFICATION]: OutboxNotificationType.TUTOR_ASSIGNMENT,
      [NotificationType.CHAT_MESSAGE]: OutboxNotificationType.CHAT_MESSAGE,
      [NotificationType.QA_SUBMISSION]: OutboxNotificationType.QA_SUBMISSION,
      [NotificationType.QA_REVIEW]: OutboxNotificationType.QA_REVIEWED,
      [NotificationType.QA_FEEDBACK]: OutboxNotificationType.QA_REVIEWED,
      [NotificationType.ESSAY_SUBMISSION]: OutboxNotificationType.GENERAL,
      [NotificationType.ESSAY_REVIEW]: OutboxNotificationType.GENERAL,
      [NotificationType.ESSAY_FEEDBACK]: OutboxNotificationType.GENERAL,
      [NotificationType.STORY_SUBMISSION]: OutboxNotificationType.GENERAL,
      [NotificationType.STORY_REVIEW]: OutboxNotificationType.GENERAL,
      [NotificationType.NOVEL_SUBMISSION]: OutboxNotificationType.NOVEL_SUBMISSION,
      [NotificationType.NOVEL_UPDATE]: OutboxNotificationType.NOVEL_SUBMISSION,
      [NotificationType.NOVEL_REVIEW]: OutboxNotificationType.NOVEL_REVIEWED,
      [NotificationType.NOVEL_FEEDBACK]: OutboxNotificationType.NOVEL_REVIEWED,
      [NotificationType.AWARD_WINNER]: OutboxNotificationType.ACHIEVEMENT,
      [NotificationType.SYSTEM]: OutboxNotificationType.SYSTEM,
      [NotificationType.STORY_MAKER_SUBMITTED]: OutboxNotificationType.GENERAL,
      [NotificationType.STORY_MAKER_EVALUATED]: OutboxNotificationType.GENERAL,
      [NotificationType.STORY_MAKER_LIKED]: OutboxNotificationType.GENERAL,
      [NotificationType.STORY_MAKER_SHARED]: OutboxNotificationType.GENERAL,
      [NotificationType.STORY_MAKER_ACHIEVEMENT]: OutboxNotificationType.ACHIEVEMENT,
      [NotificationType.STORY_MAKER_POPULARITY_UPDATE]: OutboxNotificationType.GENERAL,
      [NotificationType.STORY_MAKER_HALL_OF_FAME]: OutboxNotificationType.ACHIEVEMENT,
      [NotificationType.DIARY_FRIEND_SHARE]: OutboxNotificationType.GENERAL,
      [NotificationType.DIARY_FOLLOW_REQUEST]: OutboxNotificationType.FRIEND_REQUEST,
      [NotificationType.DIARY_FOLLOW_ACCEPTED]: OutboxNotificationType.FRIEND_ACCEPTED,
      [NotificationType.DIARY_FOLLOW_REJECTED]: OutboxNotificationType.FRIEND_REQUEST,
    };

    return mapping[type] || OutboxNotificationType.GENERAL;
  }

  /**
   * Send a notification asynchronously using the outbox pattern
   * Guarantees delivery even across server restarts
   */
  async notifyAsync(
    userId: string,
    type: NotificationType,
    title: string,
    message: string,
    options: {
      relatedEntityId?: string;
      relatedEntityType?: string;
      htmlContent?: string;
      webLink?: string;
      deepLink?: string;
      sendEmail?: boolean;
      sendPush?: boolean;
      sendInApp?: boolean;
      sendRealtime?: boolean;
    } = {},
    metadata: {
      submissionId?: string;
      entryType?: string;
      priority?: number;
      batchId?: string;
    } = {},
  ): Promise<void> {
    try {
      // Add to outbox for reliable delivery
      await this.outboxService.addToOutbox(
        userId,
        this.mapToOutboxType(type),
        title,
        message,
        options,
        metadata
      );

      this.logger.log(`Added notification to outbox for user ${userId}: ${title}`);
    } catch (error) {
      this.logger.error(
        `Failed to add notification to outbox for user ${userId}: ${error?.message || 'Unknown error'}`,
        error?.stack
      );

      // Fallback: try immediate delivery if outbox fails
      try {
        this.logger.warn(`Attempting immediate delivery as fallback for user ${userId}`);
        await this.notificationService.notify({
          userId,
          type,
          title,
          message,
          relatedEntityId: options.relatedEntityId,
          relatedEntityType: options.relatedEntityType,
          htmlContent: options.htmlContent,
        });
        this.logger.log(`Fallback notification sent successfully for user ${userId}`);
      } catch (fallbackError) {
        this.logger.error(
          `Both outbox and fallback delivery failed for user ${userId}: ${fallbackError?.message}`,
          fallbackError?.stack
        );
        // Don't throw - this should not break the main flow
      }
    }
  }

  /**
   * Send multiple notifications asynchronously using outbox pattern
   */
  async notifyManyAsync(
    notifications: Array<{
      userId: string;
      type: NotificationType;
      title: string;
      message: string;
      options?: {
        relatedEntityId?: string;
        relatedEntityType?: string;
        htmlContent?: string;
        webLink?: string;
        deepLink?: string;
        sendEmail?: boolean;
        sendPush?: boolean;
        sendInApp?: boolean;
        sendMobile?: boolean;
        sendSms?: boolean;
        sendRealtime?: boolean;
      };
      metadata?: {
        submissionId?: string;
        entryType?: string;
        priority?: number;
      };
    }>
  ): Promise<void> {
    // Add all notifications to outbox
    const promises = notifications.map(notification =>
      this.notifyAsync(
        notification.userId,
        notification.type,
        notification.title,
        notification.message,
        notification.options || {},
        notification.metadata || {}
      )
    );

    await Promise.all(promises);
  }

  /**
   * Send notification to multiple users asynchronously using outbox pattern
   */
  async notifyUsersAsync(
    userIds: string[],
    type: NotificationType,
    title: string,
    message: string,
    options: {
      relatedEntityId?: string;
      relatedEntityType?: string;
      htmlContent?: string;
      webLink?: string;
      deepLink?: string;
      sendEmail?: boolean;
      sendPush?: boolean;
      sendInApp?: boolean;
      sendRealtime?: boolean;
    } = {},
    metadata: {
      submissionId?: string;
      entryType?: string;
      priority?: number;
      batchId?: string;
    } = {},
  ): Promise<void> {
    // Add batch ID for tracking
    const batchId = metadata.batchId || `batch_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    // Send to all users asynchronously
    const promises = userIds.map(userId =>
      this.notifyAsync(userId, type, title, message, options, { ...metadata, batchId })
    );

    await Promise.all(promises);
  }

  /**
   * Get notification system status (for monitoring)
   */
  async getStatus(): Promise<{
    type: string;
    description: string;
    outboxStats?: any;
  }> {
    try {
      const outboxStats = await this.outboxService.getOutboxStats();
      return {
        type: 'outbox-pattern',
        description: 'Reliable async notification system using outbox pattern with guaranteed delivery',
        outboxStats
      };
    } catch (error) {
      return {
        type: 'outbox-pattern',
        description: 'Reliable async notification system using outbox pattern (stats unavailable)',
      };
    }
  }

  /**
   * Manually retry failed notifications (for admin use)
   */
  async retryFailedNotifications(limit: number = 100): Promise<number> {
    return this.outboxService.retryFailedNotifications(limit);
  }
}
