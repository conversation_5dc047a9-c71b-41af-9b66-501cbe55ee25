// Utility functions for admin conversation ID handling

/**
 * Returns true if the conversationId is an admin conversation (starts with 'admin-')
 */
export function isAdminConversationId(conversationId: string): boolean {
  return typeof conversationId === 'string' && conversationId.startsWith('admin-');
}

/**
 * Strips the 'admin-' prefix from an admin conversationId
 */
export function stripAdminPrefix(conversationId: string): string {
  return isAdminConversationId(conversationId) ? conversationId.replace('admin-', '') : conversationId;
}

/**
 * Adds the 'admin-' prefix to a conversationId if not already present
 */
export function addAdminPrefix(conversationId: string): string {
  return isAdminConversationId(conversationId) ? conversationId : `admin-${conversationId}`;
}
