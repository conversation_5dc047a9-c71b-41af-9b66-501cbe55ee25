import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsArray, IsBoolean, IsNumber, IsOptional, IsEnum, IsUUID } from 'class-validator';
import { WaterfallQuestionType } from '../../entities/tutor-waterfall-set.entity';

export class WaterfallTrueFalseQuestionDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  statement: string;

  @ApiProperty()
  correct_answer: boolean;

  @ApiProperty()
  time_limit_in_seconds?: number;

  @ApiProperty()
  level?: number;
}

export class WaterfallMultipleChoiceQuestionDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  question_text: string;

  @ApiProperty({ type: [String] })
  options: string[];

  @ApiProperty()
  allow_multiple_selection: boolean;

  @ApiProperty()
  min_selections?: number;

  @ApiProperty()
  max_selections?: number;

  @ApiProperty({ type: [Number] })
  correct_option_indices: number[];

  @ApiProperty()
  time_limit_in_seconds?: number;

  @ApiProperty()
  level?: number;
}

export class EnhancedWaterfallGameResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  title: string;

  @ApiProperty()
  total_score: number;

  @ApiProperty()
  total_questions: number;

  @ApiProperty({ enum: WaterfallQuestionType })
  question_type: WaterfallQuestionType;

  @ApiProperty()
  source: 'admin' | 'tutor';

  @ApiProperty({ type: [Object] })
  questions: (WaterfallTrueFalseQuestionDto | WaterfallMultipleChoiceQuestionDto | any)[];
}

export class TrueFalseAnswerDto {
  @ApiProperty()
  @IsUUID()
  question_id: string;

  @ApiProperty()
  @IsBoolean()
  answer: boolean;
}

export class MultipleChoiceAnswerDto {
  @ApiProperty()
  @IsUUID()
  question_id: string;

  @ApiProperty({ type: [Number] })
  @IsArray()
  @IsNumber({}, { each: true })
  selected_indices: number[];
}

export class EnhancedSubmitWaterfallGameDto {
  @ApiProperty()
  @IsUUID()
  set_id: string;

  @ApiProperty({ enum: WaterfallQuestionType })
  @IsEnum(WaterfallQuestionType)
  question_type: WaterfallQuestionType;

  @ApiProperty({ type: [Object] })
  @IsArray()
  answers: (TrueFalseAnswerDto | MultipleChoiceAnswerDto | any)[];
}