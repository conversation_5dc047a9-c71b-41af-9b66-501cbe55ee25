import { Test, TestingModule } from '@nestjs/testing';
import { PlansService } from '../src/modules/plans/plans.service';
import { AsyncNotificationHelperService } from '../src/modules/notification/async-notification-helper.service';
import { NotificationOutboxService } from '../src/modules/notification/notification-outbox.service';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Plan } from '../src/database/entities/plan.entity';
import { User } from '../src/database/entities/user.entity';
import { UserPlan } from '../src/database/entities/user-plan.entity';
import { NotificationOutbox, OutboxStatus } from '../src/database/entities/notification-outbox.entity';

describe('Notification Fixes', () => {
  let plansService: PlansService;
  let notificationOutboxService: NotificationOutboxService;
  let asyncNotificationHelper: AsyncNotificationHelperService;
  let outboxRepository: Repository<NotificationOutbox>;

  const mockUser = {
    id: 'test-user-id',
    name: 'Test Student',
    email: '<EMAIL>',
    type: 'STUDENT'
  };

  const mockPlan = {
    id: 'test-plan-id',
    name: 'Ultimate Monthly',
    type: 'ULTIMATE',
    subscriptionType: 'MONTHLY',
    planFeatures: [
      { id: 'feature-1', name: 'Feature 1' },
      { id: 'feature-2', name: 'Feature 2' }
    ]
  };

  const mockTutorAssignments = [
    {
      tutor: { id: 'tutor-1', name: 'Test Tutor' },
      module: { id: 'feature-1', name: 'Feature 1' },
      mapping: { id: 'mapping-1' }
    },
    {
      tutor: { id: 'tutor-1', name: 'Test Tutor' },
      module: { id: 'feature-2', name: 'Feature 2' },
      mapping: { id: 'mapping-2' }
    }
  ];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PlansService,
        NotificationOutboxService,
        AsyncNotificationHelperService,
        {
          provide: getRepositoryToken(Plan),
          useValue: {
            findOne: jest.fn(),
            find: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(User),
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(UserPlan),
          useValue: {
            findOne: jest.fn(),
            find: jest.fn(),
            create: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(NotificationOutbox),
          useValue: {
            findOne: jest.fn(),
            create: jest.fn(),
            save: jest.fn(),
          },
        },
      ],
    }).compile();

    plansService = module.get<PlansService>(PlansService);
    notificationOutboxService = module.get<NotificationOutboxService>(NotificationOutboxService);
    asyncNotificationHelper = module.get<AsyncNotificationHelperService>(AsyncNotificationHelperService);
    outboxRepository = module.get<Repository<NotificationOutbox>>(getRepositoryToken(NotificationOutbox));
  });

  describe('Notification Deduplication', () => {
    it('should prevent duplicate tutor assignment notifications', async () => {
      // Mock existing notification
      const existingNotification = {
        id: 'existing-notification',
        userId: 'tutor-1',
        type: 'TUTOR_ASSIGNMENT',
        createdAt: new Date(Date.now() - 2 * 60 * 1000), // 2 minutes ago
        status: OutboxStatus.COMPLETED,
        options: { relatedEntityId: 'mapping-1' }
      };

      jest.spyOn(outboxRepository, 'findOne').mockResolvedValue(existingNotification as any);

      const result = await notificationOutboxService.addToOutbox(
        'tutor-1',
        'TUTOR_ASSIGNMENT' as any,
        'New Student Assignment',
        'Test message',
        { relatedEntityId: 'mapping-1' }
      );

      expect(result).toBe(existingNotification);
      expect(outboxRepository.findOne).toHaveBeenCalled();
    });

    it('should allow new notifications when no duplicates exist', async () => {
      jest.spyOn(outboxRepository, 'findOne').mockResolvedValue(null);
      jest.spyOn(outboxRepository, 'create').mockReturnValue({} as any);
      jest.spyOn(outboxRepository, 'save').mockResolvedValue({ id: 'new-notification' } as any);

      const result = await notificationOutboxService.addToOutbox(
        'tutor-1',
        'TUTOR_ASSIGNMENT' as any,
        'New Student Assignment',
        'Test message',
        { relatedEntityId: 'mapping-1' }
      );

      expect(result.id).toBe('new-notification');
      expect(outboxRepository.create).toHaveBeenCalled();
      expect(outboxRepository.save).toHaveBeenCalled();
    });
  });

  describe('Batch ID Generation', () => {
    it('should generate unique batch IDs for different subscription events', () => {
      const batchId1 = `subscription_user1_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      const batchId2 = `subscription_user2_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      expect(batchId1).not.toBe(batchId2);
      expect(batchId1).toMatch(/^subscription_user1_\d+_[a-z0-9]+$/);
      expect(batchId2).toMatch(/^subscription_user2_\d+_[a-z0-9]+$/);
    });
  });
});
