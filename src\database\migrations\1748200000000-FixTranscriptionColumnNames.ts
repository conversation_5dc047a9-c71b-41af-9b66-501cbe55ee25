import { MigrationInterface, QueryRunner } from 'typeorm';

export class FixTranscriptionColumnNames1748200000000 implements MigrationInterface {
  name = 'FixTranscriptionColumnNames1748200000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Fix books table column names - check if columns exist first
    const booksColumns = await queryRunner.query(`
      SELECT column_name FROM information_schema.columns 
      WHERE table_name = 'books' AND table_schema = 'public'
    `);
    
    const columnNames = booksColumns.map(row => row.column_name);
    
    if (columnNames.includes('publicationYear')) {
      await queryRunner.query(`
        ALTER TABLE "books" 
        RENAME COLUMN "publicationYear" TO "publication_year"
      `);
    }

    if (columnNames.includes('isActive')) {
      await queryRunner.query(`
        ALTER TABLE "books" 
        RENAME COLUMN "isActive" TO "is_active"
      `);
    }

    // Fix sentences table column names - check if columns exist first
    const sentencesColumns = await queryRunner.query(`
      SELECT column_name FROM information_schema.columns 
      WHERE table_name = 'sentences' AND table_schema = 'public'
    `);
    
    const sentenceColumnNames = sentencesColumns.map(row => row.column_name);
    
    if (sentenceColumnNames.includes('orderIndex')) {
      await queryRunner.query(`
        ALTER TABLE "sentences" 
        RENAME COLUMN "orderIndex" TO "order_index"
      `);
    }

    if (sentenceColumnNames.includes('difficultyLevel')) {
      await queryRunner.query(`
        ALTER TABLE "sentences" 
        RENAME COLUMN "difficultyLevel" TO "difficulty_level"
      `);
    }

    if (sentenceColumnNames.includes('grammarPattern')) {
      await queryRunner.query(`
        ALTER TABLE "sentences" 
        RENAME COLUMN "grammarPattern" TO "grammar_pattern"
      `);
    }

    if (sentenceColumnNames.includes('bookId')) {
      await queryRunner.query(`
        ALTER TABLE "sentences" 
        RENAME COLUMN "bookId" TO "book_id"
      `);
    }

    // Fix transcription_sessions table column names - check if columns exist first
    const sessionsColumns = await queryRunner.query(`
      SELECT column_name FROM information_schema.columns 
      WHERE table_name = 'transcription_sessions' AND table_schema = 'public'
    `);
    
    const sessionColumnNames = sessionsColumns.map(row => row.column_name);
    
    if (sessionColumnNames.includes('studentId')) {
      await queryRunner.query(`
        ALTER TABLE "transcription_sessions" 
        RENAME COLUMN "studentId" TO "student_id"
      `);
    }

    if (sessionColumnNames.includes('startedAt')) {
      await queryRunner.query(`
        ALTER TABLE "transcription_sessions" 
        RENAME COLUMN "startedAt" TO "started_at"
      `);
    }

    if (sessionColumnNames.includes('completedAt')) {
      await queryRunner.query(`
        ALTER TABLE "transcription_sessions" 
        RENAME COLUMN "completedAt" TO "completed_at"
      `);
    }

    if (sessionColumnNames.includes('totalAttempts')) {
      await queryRunner.query(`
        ALTER TABLE "transcription_sessions" 
        RENAME COLUMN "totalAttempts" TO "total_attempts"
      `);
    }

    if (sessionColumnNames.includes('correctAttempts')) {
      await queryRunner.query(`
        ALTER TABLE "transcription_sessions" 
        RENAME COLUMN "correctAttempts" TO "correct_attempts"
      `);
    }

    if (sessionColumnNames.includes('isCompleted')) {
      await queryRunner.query(`
        ALTER TABLE "transcription_sessions" 
        RENAME COLUMN "isCompleted" TO "is_completed"
      `);
    }

    // Fix transcription_attempts table column names - check if columns exist first
    const attemptsColumns = await queryRunner.query(`
      SELECT column_name FROM information_schema.columns 
      WHERE table_name = 'transcription_attempts' AND table_schema = 'public'
    `);
    
    const attemptColumnNames = attemptsColumns.map(row => row.column_name);
    
    if (attemptColumnNames.includes('sessionId')) {
      await queryRunner.query(`
        ALTER TABLE "transcription_attempts" 
        RENAME COLUMN "sessionId" TO "session_id"
      `);
    }

    if (attemptColumnNames.includes('sentenceId')) {
      await queryRunner.query(`
        ALTER TABLE "transcription_attempts" 
        RENAME COLUMN "sentenceId" TO "sentence_id"
      `);
    }

    if (attemptColumnNames.includes('userInput')) {
      await queryRunner.query(`
        ALTER TABLE "transcription_attempts" 
        RENAME COLUMN "userInput" TO "user_input"
      `);
    }

    if (attemptColumnNames.includes('isCorrect')) {
      await queryRunner.query(`
        ALTER TABLE "transcription_attempts" 
        RENAME COLUMN "isCorrect" TO "is_correct"
      `);
    }

    if (attemptColumnNames.includes('timeSpentSeconds')) {
      await queryRunner.query(`
        ALTER TABLE "transcription_attempts" 
        RENAME COLUMN "timeSpentSeconds" TO "time_spent_seconds"
      `);
    }

    if (attemptColumnNames.includes('attemptedAt')) {
      await queryRunner.query(`
        ALTER TABLE "transcription_attempts" 
        RENAME COLUMN "attemptedAt" TO "attempted_at"
      `);
    }

    // Drop and recreate foreign key constraints with correct column names - check if constraints exist first
    try {
      await queryRunner.query(`
        ALTER TABLE "sentences" 
        DROP CONSTRAINT IF EXISTS "FK_sentences_bookId"
      `);
    } catch (error) {
      // Constraint might not exist, continue
    }

    // Only add constraint if book_id column exists
    if (sentenceColumnNames.includes('book_id')) {
      await queryRunner.query(`
        ALTER TABLE "sentences" 
        ADD CONSTRAINT "FK_sentences_book_id" 
        FOREIGN KEY ("book_id") REFERENCES "books"("id") ON DELETE CASCADE ON UPDATE NO ACTION
      `);
    }

    try {
      await queryRunner.query(`
        ALTER TABLE "transcription_sessions" 
        DROP CONSTRAINT IF EXISTS "FK_transcription_sessions_studentId"
      `);
    } catch (error) {
      // Constraint might not exist, continue
    }

    // Only add constraint if student_id column exists
    if (sessionColumnNames.includes('student_id')) {
      await queryRunner.query(`
        ALTER TABLE "transcription_sessions" 
        ADD CONSTRAINT "FK_transcription_sessions_student_id" 
        FOREIGN KEY ("student_id") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION
      `);
    }

    try {
      await queryRunner.query(`
        ALTER TABLE "transcription_attempts" 
        DROP CONSTRAINT IF EXISTS "FK_transcription_attempts_sessionId"
      `);
    } catch (error) {
      // Constraint might not exist, continue
    }

    // Only add constraint if session_id column exists
    if (attemptColumnNames.includes('session_id')) {
      await queryRunner.query(`
        ALTER TABLE "transcription_attempts" 
        ADD CONSTRAINT "FK_transcription_attempts_session_id" 
        FOREIGN KEY ("session_id") REFERENCES "transcription_sessions"("id") ON DELETE CASCADE ON UPDATE NO ACTION
      `);
    }

    try {
      await queryRunner.query(`
        ALTER TABLE "transcription_attempts" 
        DROP CONSTRAINT IF EXISTS "FK_transcription_attempts_sentenceId"
      `);
    } catch (error) {
      // Constraint might not exist, continue
    }

    // Only add constraint if sentence_id column exists
    if (attemptColumnNames.includes('sentence_id')) {
      await queryRunner.query(`
        ALTER TABLE "transcription_attempts" 
        ADD CONSTRAINT "FK_transcription_attempts_sentence_id" 
        FOREIGN KEY ("sentence_id") REFERENCES "sentences"("id") ON DELETE CASCADE ON UPDATE NO ACTION
      `);
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Reverse the foreign key constraints
    await queryRunner.query(`ALTER TABLE "transcription_attempts" DROP CONSTRAINT "FK_transcription_attempts_sentence_id"`);
    await queryRunner.query(`ALTER TABLE "transcription_attempts" DROP CONSTRAINT "FK_transcription_attempts_session_id"`);
    await queryRunner.query(`ALTER TABLE "transcription_sessions" DROP CONSTRAINT "FK_transcription_sessions_student_id"`);
    await queryRunner.query(`ALTER TABLE "sentences" DROP CONSTRAINT "FK_sentences_book_id"`);

    // Recreate original foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "sentences" 
      ADD CONSTRAINT "FK_sentences_bookId" 
      FOREIGN KEY ("bookId") REFERENCES "books"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "transcription_sessions" 
      ADD CONSTRAINT "FK_transcription_sessions_studentId" 
      FOREIGN KEY ("studentId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "transcription_attempts" 
      ADD CONSTRAINT "FK_transcription_attempts_sessionId" 
      FOREIGN KEY ("sessionId") REFERENCES "transcription_sessions"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "transcription_attempts" 
      ADD CONSTRAINT "FK_transcription_attempts_sentenceId" 
      FOREIGN KEY ("sentenceId") REFERENCES "sentences"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    // Reverse column renames for transcription_attempts
    await queryRunner.query(`ALTER TABLE "transcription_attempts" RENAME COLUMN "attempted_at" TO "attemptedAt"`);
    await queryRunner.query(`ALTER TABLE "transcription_attempts" RENAME COLUMN "time_spent_seconds" TO "timeSpentSeconds"`);
    await queryRunner.query(`ALTER TABLE "transcription_attempts" RENAME COLUMN "is_correct" TO "isCorrect"`);
    await queryRunner.query(`ALTER TABLE "transcription_attempts" RENAME COLUMN "user_input" TO "userInput"`);
    await queryRunner.query(`ALTER TABLE "transcription_attempts" RENAME COLUMN "sentence_id" TO "sentenceId"`);
    await queryRunner.query(`ALTER TABLE "transcription_attempts" RENAME COLUMN "session_id" TO "sessionId"`);

    // Reverse column renames for transcription_sessions
    await queryRunner.query(`ALTER TABLE "transcription_sessions" RENAME COLUMN "is_completed" TO "isCompleted"`);
    await queryRunner.query(`ALTER TABLE "transcription_sessions" RENAME COLUMN "correct_attempts" TO "correctAttempts"`);
    await queryRunner.query(`ALTER TABLE "transcription_sessions" RENAME COLUMN "total_attempts" TO "totalAttempts"`);
    await queryRunner.query(`ALTER TABLE "transcription_sessions" RENAME COLUMN "completed_at" TO "completedAt"`);
    await queryRunner.query(`ALTER TABLE "transcription_sessions" RENAME COLUMN "started_at" TO "startedAt"`);
    await queryRunner.query(`ALTER TABLE "transcription_sessions" RENAME COLUMN "student_id" TO "studentId"`);

    // Reverse column renames for sentences
    await queryRunner.query(`ALTER TABLE "sentences" RENAME COLUMN "book_id" TO "bookId"`);
    await queryRunner.query(`ALTER TABLE "sentences" RENAME COLUMN "grammar_pattern" TO "grammarPattern"`);
    await queryRunner.query(`ALTER TABLE "sentences" RENAME COLUMN "difficulty_level" TO "difficultyLevel"`);
    await queryRunner.query(`ALTER TABLE "sentences" RENAME COLUMN "order_index" TO "orderIndex"`);

    // Reverse column renames for books
    await queryRunner.query(`ALTER TABLE "books" RENAME COLUMN "is_active" TO "isActive"`);
    await queryRunner.query(`ALTER TABLE "books" RENAME COLUMN "publication_year" TO "publicationYear"`);
  }
}