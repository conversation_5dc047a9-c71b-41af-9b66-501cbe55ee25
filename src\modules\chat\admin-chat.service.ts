import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { Conversation, ConversationStatus, ConversationType } from '../../database/entities/conversation.entity';
import { Message, MessageStatus, MessageType } from '../../database/entities/message.entity';
import { AdminConversationParticipant } from '../../database/entities/admin-conversation-participant.entity';
import { User, UserType } from '../../database/entities/user.entity';
import { AsyncNotificationHelperService } from '../notification/async-notification-helper.service';
import { NotificationType } from '../../database/entities/notification.entity';
import {
  ConversationDto,
  MessageDto,
  CreateMessageDto,
  ConversationFilterDto,
  ConversationParticipantDto,
  PagedConversationListDto,
} from '../../database/models/chat.dto';
import { getCurrentUTCDate } from '../../common/utils/date-utils';
import { VirtualAdminService } from './virtual-admin.service';
import { AdminConversationService } from './admin-conversation.service';
import { AdminConversationManagerService } from './admin-conversation-manager.service';
import { ChatValidationUtil } from './chat-validation.util';
import { DeeplinkService } from '../../common/utils/deeplink.service';
import { FileRegistryService } from '../../common/services/file-registry.service';
import { FileEntityType } from '../../common/enums/file-entity-type.enum';
import { RelatedEntityType } from '../../common/enums/related-entity-type.enum';

/**
 * Service for handling admin chat functionality
 * Manages shared admin conversations where any admin can participate
 */
@Injectable()
export class AdminChatService {
  /**
   * Public wrapper to get or create admin conversation for a user
   */
  public async getOrCreateAdminConversationForUser(userId: string) {
    return this.adminConversationService.getOrCreateAdminConversation(userId);
  }
  private readonly logger = new Logger(AdminChatService.name);

  constructor(
    @InjectRepository(Conversation)
    private readonly conversationRepository: Repository<Conversation>,
    @InjectRepository(Message)
    private readonly messageRepository: Repository<Message>,
    @InjectRepository(AdminConversationParticipant)
    private readonly adminConversationParticipantRepository: Repository<AdminConversationParticipant>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly virtualAdminService: VirtualAdminService,
    private readonly adminConversationService: AdminConversationService,
    private readonly adminConversationManagerService: AdminConversationManagerService,
    private readonly asyncNotificationHelper: AsyncNotificationHelperService,
    private readonly dataSource: DataSource,
    private readonly deeplinkService: DeeplinkService,
    private readonly fileRegistryService: FileRegistryService,
  ) {}

  /**
   * Get or create a shared admin conversation with a user
   * @param adminId The admin initiating the conversation
   * @param targetUserId The user to chat with (tutor or student)
   * @returns The admin conversation
   */
  async getOrCreateAdminConversation(adminId: string, targetUserId: string): Promise<any> {
    try {
      const sanitizedAdminId = ChatValidationUtil.validateUserId(adminId);
      const sanitizedTargetUserId = ChatValidationUtil.validateUserId(targetUserId);

      const { conversation } = await this.adminConversationManagerService.getOrCreateAdminConversationWithParticipant(
        sanitizedAdminId,
        sanitizedTargetUserId
      );
      
      this.logger.log(ChatValidationUtil.sanitizeLogMessage(`Got/created admin conversation ${conversation.id} for user ${sanitizedTargetUserId}`));
      return conversation;
    } catch (error) {
      this.logger.error(ChatValidationUtil.sanitizeLogMessage(`Error getting or creating admin conversation: ${error.message}`), error.stack);
      throw error;
    }
  }





  /**
   * Send a message in an admin conversation
   * @param adminId The admin sending the message
   * @param adminConversationId The admin conversation ID (without 'admin-' prefix)
   * @param createMessageDto Message data
   * @returns The created message
   */
  async sendAdminMessage(adminId: string, adminConversationId: string, createMessageDto: CreateMessageDto): Promise<MessageDto> {
    try {
      const sanitizedAdminId = ChatValidationUtil.validateUserId(adminId);
      const sanitizedConversationId = ChatValidationUtil.validateConversationId(adminConversationId);
      const sanitizedContent = ChatValidationUtil.validateMessageContent(createMessageDto.content);

      // Validate admin access
      const hasAccess = await this.adminConversationManagerService.validateAdminAccess(sanitizedAdminId, sanitizedConversationId);
      if (!hasAccess) {
        throw new NotFoundException('Admin conversation not found or access denied');
      }

      const adminConversation = await this.adminConversationService.getAdminConversation(sanitizedConversationId);
      const admin = await this.userRepository.findOne({ where: { id: sanitizedAdminId } });
      const virtualAdminUserId = await this.virtualAdminService.getVirtualAdminUserId();
      
      const message = this.messageRepository.create({
        adminConversationId: sanitizedConversationId,
        senderId: virtualAdminUserId,
        recipientId: adminConversation.userId,
        actualSenderId: sanitizedAdminId,
        type: createMessageDto.type || MessageType.TEXT,
        content: sanitizedContent,
        metadata: createMessageDto.metadata,
        status: MessageStatus.SENT,
      });

      const savedMessage = await this.messageRepository.save(message);

      await this.adminConversationService.updateLastMessage(
        sanitizedConversationId,
        sanitizedContent,
        savedMessage.createdAt,
        savedMessage.senderId
      );
      
      await this.adminConversationService.incrementUnreadCount(sanitizedConversationId, true);
      await this.adminConversationManagerService.incrementUnreadCountForAdminParticipants(sanitizedConversationId, sanitizedAdminId);

      await this.asyncNotificationHelper.notifyAsync(
        adminConversation.userId,
        NotificationType.CHAT_MESSAGE,
        'New message from Admin',
        'You have a new message from admin',
        {
          relatedEntityId: savedMessage.id,
          relatedEntityType: RelatedEntityType.CHAT_MESSAGE,
          sendInApp: true,
          sendPush: true,
          sendRealtime: true,
        },
      );

      const messageDto = await this.mapMessageToDto(savedMessage, admin);
      messageDto.conversationId = `admin-${sanitizedConversationId}`;
      return messageDto;
    } catch (error) {
      this.logger.error(ChatValidationUtil.sanitizeLogMessage(`Error sending admin message: ${error.message}`), error.stack);
      throw error;
    }
  }





  /**
   * Process message attachments to get proper file URLs
   * @param attachments Message attachments
   * @returns Processed attachment DTOs
   */
  private async processAttachments(attachments: any[], messageId: string): Promise<any[]> {
    const attachmentDtos = [];
    
    // Get MessageRegistry entries for this message to map attachments to registry IDs
    const messageRegistries = await this.dataSource.getRepository('MessageRegistry').find({
      where: { messageId: messageId, isTemporary: false },
    });
    
    for (const attachment of attachments) {
      try {
        // Find the corresponding MessageRegistry entry by matching file path
        const registry = messageRegistries.find(r => r.filePath === attachment.filePath);
        const registryId = registry?.id;
        
        let fileUrl = attachment.filePath; // fallback
        let thumbnailUrl = attachment.thumbnailPath; // fallback
        
        if (registryId) {
          // Get proper file URL from file registry service using registry ID
          fileUrl = await this.fileRegistryService.getFileUrl(FileEntityType.MESSAGE_ATTACHMENT, registryId);
          if (attachment.thumbnailPath) {
            thumbnailUrl = await this.fileRegistryService.getFileUrl(FileEntityType.MESSAGE_ATTACHMENT, registryId);
          }
        }

        attachmentDtos.push({
          id: attachment.id,
          filePath: attachment.filePath,
          fileName: attachment.fileName,
          mimeType: attachment.mimeType,
          fileSize: attachment.fileSize,
          thumbnailPath: attachment.thumbnailPath,
          fileUrl: fileUrl,
          thumbnailUrl: thumbnailUrl,
        });
      } catch (error) {
        this.logger.error(`Error getting file URL for attachment ${attachment.id}: ${error.message}`);
        // Fallback to file path if URL generation fails
        attachmentDtos.push({
          id: attachment.id,
          filePath: attachment.filePath,
          fileName: attachment.fileName,
          mimeType: attachment.mimeType,
          fileSize: attachment.fileSize,
          thumbnailPath: attachment.thumbnailPath,
          fileUrl: attachment.filePath,
          thumbnailUrl: attachment.thumbnailPath,
        });
      }
    }
    
    return attachmentDtos;
  }

  /**
   * Map message entity to DTO (optimized for admin messages)
   * @param message Message entity
   * @param actualSender The actual admin who sent the message
   * @returns Message DTO
   */
  private async mapMessageToDto(message: Message, actualSender?: User): Promise<MessageDto> {
    // If the sender is an admin, use virtual admin details for unified identity
    let senderId = message.senderId;
    let senderName = 'Admin';
    let senderProfilePicture: string | null = null;
    let isSenderVirtualAdmin = false;

    if (actualSender && actualSender.type === UserType.ADMIN) {
      try {
        // Use the optimized method for better performance
        const virtualAdminData = await this.virtualAdminService.getVirtualAdminForMessage();
        senderId = virtualAdminData.userId;
        isSenderVirtualAdmin = virtualAdminData.isSenderVirtualAdmin;
        senderName = virtualAdminData.displayName;
        senderProfilePicture = virtualAdminData.profilePicture;
      } catch (error) {
        this.logger.warn(`Failed to get virtual admin details, using fallback: ${error.message}`);
        // Fallback to default admin details
        senderName = 'HEC Admin';
        senderProfilePicture = null;
      }
    } else if (actualSender) {
      senderName = actualSender.name;
      // Generate the profile picture URL using DeeplinkService
      senderProfilePicture = this.deeplinkService.getProfilePictureUrl(actualSender.id);
    }

    return {
      id: message.id,
      conversationId: message.conversationId,
      senderId: senderId,
      senderName: senderName,
      senderProfilePicture: senderProfilePicture,
      recipientId: message.recipientId,
      type: message.type,
      content: message.content,
      status: message.status,
      readAt: message.readAt,
      deliveredAt: message.deliveredAt,
      createdAt: message.createdAt,
      updatedAt: message.updatedAt,
      metadata: message.metadata,
      // For admin messages, include actual sender info
      actualSender: actualSender ? {
        id: actualSender.id,
        name: actualSender.name,
        userId: actualSender.userId,
        email: actualSender.email,
        type: actualSender.type,
        profilePictureUrl: this.deeplinkService.getProfilePictureUrl(actualSender.id),
      } : undefined,
      attachments: await this.processAttachments(message.attachments || [], message.id),
      isAdminMessage: true, // Admin chat service always handles admin messages
      isSenderVirtualAdmin: isSenderVirtualAdmin, // Admin chat service always handles admin messages
    };
  }

  /**
   * Validates that the admin conversation exists and the user is a participant (owner) or admin participant
   * @param userId string
   * @param adminConversationId string
   * @returns the admin conversation entity if valid, otherwise null
   */
  async validateUserAdminConversation(userId: string, adminConversationId: string): Promise<any | null> {
    try {
      const sanitizedUserId = ChatValidationUtil.validateUserId(userId);
      const sanitizedConversationId = ChatValidationUtil.validateConversationId(adminConversationId);

      const user = await this.userRepository.findOne({ where: { id: sanitizedUserId } });
      if (!user) return null;

      const adminConversation = await this.adminConversationService.getAdminConversation(sanitizedConversationId);
      if (!adminConversation) return null;

      if (user.type !== UserType.ADMIN) {
        return adminConversation.userId === sanitizedUserId ? adminConversation : null;
      }

      const hasAccess = await this.adminConversationManagerService.validateAdminAccess(sanitizedUserId, sanitizedConversationId);
      return hasAccess ? adminConversation : null;
    } catch (error) {
      this.logger.error(ChatValidationUtil.sanitizeLogMessage(`Error validating admin conversation: ${error.message}`));
      return null;
    }
  }
}
