import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddBlockGameDistractorFields1756000000000 implements MigrationInterface {
  name = 'AddBlockGameDistractorFields1756000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add distractor fields to block_game_sentence table
    await queryRunner.query(`
      ALTER TABLE "block_game_sentence" 
      ADD COLUMN "starting_part_distractors" text[] DEFAULT '{}'
    `);

    await queryRunner.query(`
      ALTER TABLE "block_game_sentence" 
      ADD COLUMN "expanding_part_distractors" text[] DEFAULT '{}'
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove distractor fields
    await queryRunner.query(`
      ALTER TABLE "block_game_sentence" 
      DROP COLUMN "expanding_part_distractors"
    `);

    await queryRunner.query(`
      ALTER TABLE "block_game_sentence" 
      DROP COLUMN "starting_part_distractors"
    `);
  }
}