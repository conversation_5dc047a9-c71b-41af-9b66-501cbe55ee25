import { BadRequestException, UnauthorizedException, ConflictException, NotFoundException } from '@nestjs/common';
import { UserType } from '../../database/entities/user.entity';
import { TutorApprovalStatus } from '../../database/entities/tutor-approval.entity';
import { SecureTestDataFactory } from '../../../test/fixtures/factories/secure-test-data.factory';
import { AuthTestSetup, AuthTestMocks } from '../../../test/fixtures/auth-mock-setup';
import { AuthTestDataFactory } from '../../../test/fixtures/auth-test-data';

describe('AuthService - Secure Tests', () => {
  let mocks: AuthTestMocks;

  beforeEach(async () => {
    const module = await AuthTestSetup.createTestingModule();
    mocks = AuthTestSetup.extractMocks(module);
    AuthTestSetup.setupCommonMocks(mocks);
  });

  describe('validateUser', () => {
    it('should validate user with correct credentials', async () => {
      const mockUser = SecureTestDataFactory.createMockUser();
      mocks.usersService.findByUserId.mockResolvedValue(mockUser);

      const result = await mocks.service.validateUser(mockUser.userId, '<test-password>');

      expect(result).toEqual(mockUser);
      expect(mocks.usersService.findByUserId).toHaveBeenCalledWith(mockUser.userId);
      expect(mockUser.verifyPassword).toHaveBeenCalledWith('<test-password>');
    });

    it('should return null for invalid credentials', async () => {
      const mockUser = SecureTestDataFactory.createMockUser({
        verifyPassword: jest.fn().mockReturnValue(false),
      });
      mocks.usersService.findByUserId.mockResolvedValue(mockUser);

      const result = await mocks.service.validateUser(mockUser.userId, '<wrong-password>');

      expect(result).toBeNull();
    });

    it('should return null for non-existent user', async () => {
      mocks.usersService.findByUserId.mockResolvedValue(null);

      const result = await mocks.service.validateUser('<nonexistent>', '<password>');

      expect(result).toBeNull();
    });
  });

  describe('login', () => {
    it('should login user successfully', async () => {
      const mockUser = SecureTestDataFactory.createMockUser();
      const loginDto = AuthTestDataFactory.createLoginDto();

      mocks.usersService.findByUserId.mockResolvedValue(mockUser);
      mocks.userRepository.save.mockResolvedValue(mockUser);

      const result = await mocks.service.login(loginDto);

      expect(result).toHaveProperty('access_token', '<jwt-token>');
      expect(result).toHaveProperty('user');
      expect(mocks.userRepository.save).toHaveBeenCalled();
    });

    it('should throw UnauthorizedException for invalid user ID', async () => {
      const loginDto = AuthTestDataFactory.createLoginDto({ userId: '<invalid>' });
      mocks.usersService.findByUserId.mockResolvedValue(null);

      await expect(mocks.service.login(loginDto)).rejects.toThrow(UnauthorizedException);
    });

    it('should throw UnauthorizedException for invalid password', async () => {
      const mockUser = SecureTestDataFactory.createMockUser({
        verifyPassword: jest.fn().mockReturnValue(false),
      });
      const loginDto = AuthTestDataFactory.createLoginDto();

      mocks.usersService.findByUserId.mockResolvedValue(mockUser);

      await expect(mocks.service.login(loginDto)).rejects.toThrow(UnauthorizedException);
    });

    it('should throw UnauthorizedException for unconfirmed user', async () => {
      const mockUser = SecureTestDataFactory.createMockUser({ isConfirmed: false });
      const loginDto = AuthTestDataFactory.createLoginDto();

      mocks.usersService.findByUserId.mockResolvedValue(mockUser);

      await expect(mocks.service.login(loginDto)).rejects.toThrow(UnauthorizedException);
    });

    it('should throw UnauthorizedException for inactive user', async () => {
      const mockUser = SecureTestDataFactory.createMockUser({ isActive: false });
      const loginDto = AuthTestDataFactory.createLoginDto();

      mocks.usersService.findByUserId.mockResolvedValue(mockUser);

      await expect(mocks.service.login(loginDto)).rejects.toThrow(UnauthorizedException);
    });

    it('should check tutor approval status for tutor users', async () => {
      const mockTutor = SecureTestDataFactory.createMockUser({ type: UserType.TUTOR });
      const loginDto = AuthTestDataFactory.createLoginDto();
      const mockApproval = AuthTestDataFactory.createMockTutorApproval();

      mocks.usersService.findByUserId.mockResolvedValue(mockTutor);
      mocks.tutorApprovalRepository.findOne.mockResolvedValue(mockApproval);
      mocks.userRepository.save.mockResolvedValue(mockTutor);

      const result = await mocks.service.login(loginDto);

      expect(result).toHaveProperty('access_token');
      expect(mocks.tutorApprovalRepository.findOne).toHaveBeenCalled();
    });

    it('should throw UnauthorizedException for unapproved tutor', async () => {
      const mockTutor = SecureTestDataFactory.createMockUser({ type: UserType.TUTOR });
      const loginDto = AuthTestDataFactory.createLoginDto();

      mocks.usersService.findByUserId.mockResolvedValue(mockTutor);
      mocks.tutorApprovalRepository.findOne.mockResolvedValue(null);

      await expect(mocks.service.login(loginDto)).rejects.toThrow(UnauthorizedException);
    });
  });

  describe('register', () => {
    it('should register student successfully', async () => {
      const registerDto = AuthTestDataFactory.createRegisterDto();

      mocks.usersService.findByEmail.mockResolvedValue(null);
      mocks.usersService.findByUserId.mockResolvedValue(null);

      const result = await mocks.service.register(registerDto);

      expect(result).toEqual({
        success: true,
        message: 'Registration successful. Please check your email for verification link.',
        userId: expect.any(String),
      });
    });

    it('should throw ConflictException for existing email', async () => {
      const registerDto = AuthTestDataFactory.createRegisterDto();
      const existingUser = SecureTestDataFactory.createMockUser();

      mocks.usersService.findByEmail.mockResolvedValue(existingUser);

      await expect(mocks.service.register(registerDto)).rejects.toThrow(ConflictException);
    });

    it('should throw BadRequestException for password mismatch', async () => {
      const registerDto = AuthTestDataFactory.createRegisterDto({
        confirmPassword: '<different-password>',
      });

      mocks.usersService.findByEmail.mockResolvedValue(null);
      mocks.usersService.findByUserId.mockResolvedValue(null);

      await expect(mocks.service.register(registerDto)).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException for missing terms agreement', async () => {
      const registerDto = AuthTestDataFactory.createRegisterDto({ agreedToTerms: false });

      mocks.usersService.findByEmail.mockResolvedValue(null);
      mocks.usersService.findByUserId.mockResolvedValue(null);

      await expect(mocks.service.register(registerDto)).rejects.toThrow(BadRequestException);
    });
  });

  describe('verifyEmail', () => {
    it('should verify email successfully', async () => {
      const mockVerification = AuthTestDataFactory.createMockVerification();
      const mockUser = SecureTestDataFactory.createMockUser();

      mocks.emailVerificationRepository.findOne.mockResolvedValue(mockVerification);
      mocks.userRepository.findOne.mockResolvedValue(mockUser);
      mocks.userRepository.save.mockResolvedValue({ ...mockUser, isConfirmed: true });
      mocks.emailVerificationRepository.save.mockResolvedValue({ ...mockVerification, isUsed: true });

      const result = await mocks.service.verifyEmail('<verification-token>');

      expect(result).toHaveProperty('success', true);
      expect(result).toHaveProperty('access_token', '<jwt-token>');
      expect(mocks.userRepository.save).toHaveBeenCalled();
    });

    it('should throw BadRequestException for invalid token', async () => {
      mocks.emailVerificationRepository.findOne.mockResolvedValue(null);

      await expect(mocks.service.verifyEmail('<invalid-token>')).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException for expired token', async () => {
      const expiredVerification = AuthTestDataFactory.createMockVerification({
        expirationTime: new Date(Date.now() - 300000),
      });

      mocks.emailVerificationRepository.findOne.mockResolvedValue(expiredVerification);

      await expect(mocks.service.verifyEmail('<expired-token>')).rejects.toThrow(BadRequestException);
    });

    it('should create tutor approval for tutor verification', async () => {
      const mockVerification = AuthTestDataFactory.createMockVerification();
      const mockTutor = SecureTestDataFactory.createMockUser({ type: UserType.TUTOR });

      mocks.emailVerificationRepository.findOne.mockResolvedValue(mockVerification);
      mocks.userRepository.findOne.mockResolvedValue(mockTutor);
      mocks.userRepository.save.mockResolvedValue({ ...mockTutor, isConfirmed: true });
      mocks.emailVerificationRepository.save.mockResolvedValue({ ...mockVerification, isUsed: true });
      mocks.tutorApprovalRepository.save.mockResolvedValue({});
      mocks.usersService.getAllAdminUsers.mockResolvedValue([]);

      const result = await mocks.service.verifyEmail('<verification-token>');

      expect(result).toHaveProperty('requiresApproval', true);
      expect(mocks.tutorApprovalRepository.save).toHaveBeenCalled();
    });
  });

  describe('forgotPassword', () => {
    it('should send password reset email for valid email', async () => {
      const forgotPasswordDto = AuthTestDataFactory.createForgotPasswordDto();
      const mockUser = SecureTestDataFactory.createMockUser();

      mocks.userRepository.findOne.mockResolvedValue(mockUser);
      mocks.passwordResetRepository.delete.mockResolvedValue({ affected: 1 });
      mocks.passwordResetRepository.save.mockResolvedValue({ id: '<reset-id>' });

      const result = await mocks.service.forgotPassword(forgotPasswordDto);

      expect(result).toEqual({
        success: true,
        message: 'If your email is registered, you will receive a password reset link.',
      });
      expect(mocks.emailService.sendPasswordResetLink).toHaveBeenCalled();
    });

    it('should return success message for non-existent email', async () => {
      const forgotPasswordDto = AuthTestDataFactory.createForgotPasswordDto();

      mocks.userRepository.findOne.mockResolvedValue(null);

      const result = await mocks.service.forgotPassword(forgotPasswordDto);

      expect(result).toEqual({
        success: true,
        message: 'If your email is registered, you will receive a password reset link.',
      });
    });

    it('should handle userId identifier', async () => {
      const forgotPasswordDto = AuthTestDataFactory.createForgotPasswordDto({
        identifier: '<test-user-001>',
      });
      const mockUser = SecureTestDataFactory.createMockUser();

      mocks.userRepository.findOne.mockResolvedValue(mockUser);
      mocks.passwordResetRepository.delete.mockResolvedValue({ affected: 1 });
      mocks.passwordResetRepository.save.mockResolvedValue({ id: '<reset-id>' });

      const result = await mocks.service.forgotPassword(forgotPasswordDto);

      expect(result).toEqual({
        success: true,
        message: 'If your email is registered, you will receive a password reset link.',
      });
    });
  });

  describe('resetPassword', () => {
    it('should reset password successfully', async () => {
      const resetPasswordDto = AuthTestDataFactory.createResetPasswordDto();
      const mockReset = AuthTestDataFactory.createMockPasswordReset();
      const mockUser = SecureTestDataFactory.createMockUser();

      mocks.passwordResetRepository.findOne.mockResolvedValue(mockReset);
      mocks.userRepository.findOne.mockResolvedValue(mockUser);
      mocks.userRepository.save.mockResolvedValue(mockUser);
      mocks.passwordResetRepository.save.mockResolvedValue({ ...mockReset, isUsed: true });

      const result = await mocks.service.resetPassword(resetPasswordDto);

      expect(result).toEqual({
        success: true,
        message: 'Password has been reset successfully. You can now login with your new password.',
      });
      expect(mockUser.setPassword).toHaveBeenCalledWith('<new-password>');
    });

    it('should throw BadRequestException for invalid token', async () => {
      const resetPasswordDto = AuthTestDataFactory.createResetPasswordDto();

      mocks.passwordResetRepository.findOne.mockResolvedValue(null);

      await expect(mocks.service.resetPassword(resetPasswordDto)).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException for expired token', async () => {
      const resetPasswordDto = AuthTestDataFactory.createResetPasswordDto();
      const expiredReset = AuthTestDataFactory.createMockPasswordReset({
        expirationTime: new Date(Date.now() - 300000),
      });

      mocks.passwordResetRepository.findOne.mockResolvedValue(expiredReset);

      await expect(mocks.service.resetPassword(resetPasswordDto)).rejects.toThrow(BadRequestException);
    });
  });

  describe('changePassword', () => {
    it('should change password successfully', async () => {
      const changePasswordDto = AuthTestDataFactory.createChangePasswordDto();
      const mockRequest = AuthTestDataFactory.createMockRequest();
      const mockUser = SecureTestDataFactory.createMockUser({
        verifyPassword: jest.fn()
          .mockReturnValueOnce(true)
          .mockReturnValueOnce(false),
      });

      mocks.userRepository.findOne.mockResolvedValue(mockUser);
      mocks.userRepository.save.mockResolvedValue(mockUser);

      const result = await mocks.service.changePassword(changePasswordDto, mockRequest);

      expect(result).toHaveProperty('access_token', '<jwt-token>');
      expect(mockUser.setPassword).toHaveBeenCalledWith('<new-password>');
    });

    it('should throw BadRequestException for incorrect current password', async () => {
      const changePasswordDto = AuthTestDataFactory.createChangePasswordDto();
      const mockRequest = AuthTestDataFactory.createMockRequest();
      const mockUser = SecureTestDataFactory.createMockUser({
        verifyPassword: jest.fn().mockReturnValue(false),
      });

      mocks.userRepository.findOne.mockResolvedValue(mockUser);

      await expect(mocks.service.changePassword(changePasswordDto, mockRequest))
        .rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException for password mismatch', async () => {
      const changePasswordDto = AuthTestDataFactory.createChangePasswordDto({
        confirmNewPassword: '<different-password>',
      });
      const mockRequest = AuthTestDataFactory.createMockRequest();

      await expect(mocks.service.changePassword(changePasswordDto, mockRequest))
        .rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException for same password', async () => {
      const changePasswordDto = AuthTestDataFactory.createChangePasswordDto();
      const mockRequest = AuthTestDataFactory.createMockRequest();
      const mockUser = SecureTestDataFactory.createMockUser({
        verifyPassword: jest.fn().mockReturnValue(true),
      });

      mocks.userRepository.findOne.mockResolvedValue(mockUser);

      await expect(mocks.service.changePassword(changePasswordDto, mockRequest))
        .rejects.toThrow(BadRequestException);
    });
  });

  describe('logout', () => {
    it('should logout user successfully', async () => {
      const mockUser = SecureTestDataFactory.createMockUser();

      mocks.userRepository.findOne.mockResolvedValue(mockUser);
      mocks.userRepository.save.mockResolvedValue({
        ...mockUser,
        refreshToken: '',
        refreshTokenExpiry: new Date(0),
      });

      await mocks.service.logout(mockUser.id, '<jwt-token>');

      expect(mocks.userRepository.save).toHaveBeenCalled();
    });

    it('should throw NotFoundException for non-existent user', async () => {
      mocks.userRepository.findOne.mockResolvedValue(null);

      await expect(mocks.service.logout('<non-existent>')).rejects.toThrow(NotFoundException);
    });
  });

  describe('checkTutorApprovalStatus', () => {
    it('should return approved status for approved tutor', async () => {
      const mockApproval = AuthTestDataFactory.createMockTutorApproval();

      mocks.tutorApprovalRepository.findOne.mockResolvedValue(mockApproval);

      const result = await mocks.service.checkTutorApprovalStatus('<tutor-id>');

      expect(result).toEqual({
        isApproved: true,
        status: TutorApprovalStatus.APPROVED,
      });
    });

    it('should return pending status for tutor without approval record', async () => {
      mocks.tutorApprovalRepository.findOne.mockResolvedValue(null);

      const result = await mocks.service.checkTutorApprovalStatus('<tutor-id>');

      expect(result).toEqual({
        isApproved: false,
        status: TutorApprovalStatus.PENDING,
        message: 'Your tutor account is pending approval. Please wait for an administrator to approve your account.',
      });
    });

    it('should return rejected status for rejected tutor', async () => {
      const mockApproval = AuthTestDataFactory.createMockTutorApproval({
        status: TutorApprovalStatus.REJECTED,
        rejectionReason: '<test-rejection-reason>',
      });

      mocks.tutorApprovalRepository.findOne.mockResolvedValue(mockApproval);

      const result = await mocks.service.checkTutorApprovalStatus('<tutor-id>');

      expect(result).toEqual({
        isApproved: false,
        status: TutorApprovalStatus.REJECTED,
        message: 'Your tutor account has been rejected. Reason: <test-rejection-reason>. Please contact an administrator for more information.',
      });
    });
  });

  describe('refreshToken', () => {
    it('should refresh token successfully', async () => {
      const mockUser = SecureTestDataFactory.createMockUser({
        refreshToken: '<valid-refresh-token>',
        refreshTokenExpiry: new Date(Date.now() + ********),
      });

      mocks.userRepository.findOne.mockResolvedValue(mockUser);
      mocks.userRepository.save.mockResolvedValue(mockUser);

      const result = await mocks.service.refreshToken({ refreshToken: '<valid-refresh-token>' });

      expect(result).toHaveProperty('access_token', '<jwt-token>');
      expect(result).toHaveProperty('refresh_token');
      expect(mocks.userRepository.save).toHaveBeenCalled();
    });

    it('should throw UnauthorizedException for invalid refresh token', async () => {
      mocks.userRepository.findOne.mockResolvedValue(null);

      await expect(mocks.service.refreshToken({ refreshToken: '<invalid-token>' }))
        .rejects.toThrow(UnauthorizedException);
    });

    it('should throw UnauthorizedException for expired refresh token', async () => {
      const mockUser = SecureTestDataFactory.createMockUser({
        refreshToken: '<expired-token>',
        refreshTokenExpiry: new Date(Date.now() - ********),
      });

      mocks.userRepository.findOne.mockResolvedValue(mockUser);

      await expect(mocks.service.refreshToken({ refreshToken: '<expired-token>' }))
        .rejects.toThrow(UnauthorizedException);
    });
  });

  describe('Security Tests', () => {
    it('should handle malicious input in login', async () => {
      const maliciousInputs = SecureTestDataFactory.getMaliciousInputs();
      
      for (const maliciousInput of maliciousInputs) {
        const loginDto = AuthTestDataFactory.createLoginDto({
          userId: maliciousInput,
          password: maliciousInput,
        });

        mocks.usersService.findByUserId.mockResolvedValue(null);

        await expect(mocks.service.login(loginDto)).rejects.toThrow(UnauthorizedException);
      }
    });

    it('should handle malicious input in registration', async () => {
      const maliciousInputs = SecureTestDataFactory.getMaliciousInputs();
      
      for (const maliciousInput of maliciousInputs) {
        const registerDto = AuthTestDataFactory.createRegisterDto({
          userId: maliciousInput,
          email: `${maliciousInput}@example.com`,
        });

        mocks.usersService.findByEmail.mockResolvedValue(null);
        mocks.usersService.findByUserId.mockResolvedValue(null);

        // Should either succeed with sanitized input or fail with validation error
        try {
          await mocks.service.register(registerDto);
        } catch (error) {
          expect(error).toBeInstanceOf(BadRequestException);
        }
      }
    });

    it('should not expose sensitive information in error messages', async () => {
      const loginDto = AuthTestDataFactory.createLoginDto();
      
      mocks.usersService.findByUserId.mockRejectedValue(new Error('<database-connection-string>'));

      try {
        await mocks.service.login(loginDto);
      } catch (error) {
        expect(error.message).not.toContain('<database-connection-string>');
        expect(error.message).not.toContain('password');
        expect(error.message).not.toContain('hash');
      }
    });
  });
});