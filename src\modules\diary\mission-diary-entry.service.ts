import { Injectable, Logger, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { MissionDiaryEntry, MissionEntryStatus } from '../../database/entities/mission-diary-entry.entity';
import { MissionDiaryEntryHistory } from '../../database/entities/mission-diary-entry-history.entity';
import { DiaryMission } from '../../database/entities/diary-mission.entity';
import { MissionDiaryEntryFeedback } from '../../database/entities/mission-diary-entry-feedback.entity';
import { User, UserType } from '../../database/entities/user.entity';
import { FileRegistryService } from '../../common/services/file-registry.service';
import { FileEntityType } from '../../common/enums/file-entity-type.enum';
import { DiarySkinService } from './diary-skin.service';
import { MissionDiaryEntryHistoryService } from './mission-diary-entry-history.service';
import {
  CreateMissionDiaryEntryDto,
  UpdateMissionDiaryEntryDto,
  MissionDiaryEntryResponseDto,
  MissionEntryFilterDto,
  AddMissionFeedbackDto,
  AddMissionCorrectionDto,
  AssignMissionScoreDto,
  AddMissionCorrectionWithScoreDto,
  MissionFeedbackResponseDto,
  MissionDiaryEntryHistoryResponseDto,
  MissionDiaryEntryVersionDto,
} from '../../database/models/mission-diary-entry.dto';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';
import { AsyncNotificationHelperService } from '../notification/async-notification-helper.service';
import { NotificationType } from '../../database/entities/notification.entity';
import { TutorMatchingService } from '../tutor-matching/tutor-matching.service';
import { getCurrentUTCDate } from '../../common/utils/date-utils';
import { DiaryMissionService } from './diary-mission.service';
import { TransactionHelper } from '../../common/utils/transaction-helper';
import { DeeplinkService, DeeplinkType } from '../../common/utils/deeplink.service';
import { RelatedEntityType } from '../../common/enums/related-entity-type.enum';

@Injectable()
export class MissionDiaryEntryService {
  private readonly logger = new Logger(MissionDiaryEntryService.name);

  /**
   * Retry utility for database operations with exponential backoff
   */
  private async retryOperation<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    baseDelay: number = 1000
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;

        // Don't retry on validation errors or not found errors
        if (error instanceof BadRequestException ||
            error instanceof NotFoundException ||
            error instanceof ForbiddenException) {
          throw error;
        }

        if (attempt === maxRetries) {
          this.logger.error(`Operation failed after ${maxRetries} attempts: ${error.message}`, error.stack);
          throw error;
        }

        const delay = baseDelay * Math.pow(2, attempt - 1);
        this.logger.warn(`Operation failed on attempt ${attempt}/${maxRetries}, retrying in ${delay}ms: ${error.message}`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError;
  }

  constructor(
    @InjectRepository(MissionDiaryEntry)
    private readonly missionDiaryEntryRepository: Repository<MissionDiaryEntry>,
    @InjectRepository(MissionDiaryEntryHistory)
    private readonly missionDiaryEntryHistoryRepository: Repository<MissionDiaryEntryHistory>,
    @InjectRepository(DiaryMission)
    private readonly diaryMissionRepository: Repository<DiaryMission>,
    @InjectRepository(MissionDiaryEntryFeedback)
    private readonly feedbackRepository: Repository<MissionDiaryEntryFeedback>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly dataSource: DataSource,
    private readonly asyncNotificationHelper: AsyncNotificationHelperService,
    private readonly tutorMatchingService: TutorMatchingService,
    private readonly diaryMissionService: DiaryMissionService,
    private readonly deeplinkService: DeeplinkService,
    private readonly fileRegistryService: FileRegistryService,
    private readonly diarySkinService: DiarySkinService,
    private readonly missionDiaryEntryHistoryService: MissionDiaryEntryHistoryService,
  ) {}

  /**
   * Create a new mission diary entry
   * @param studentId ID of the student creating the entry
   * @param createDto Entry creation data
   * @returns The created entry
   */
  async createMissionEntry(studentId: string, createDto: CreateMissionDiaryEntryDto): Promise<MissionDiaryEntryResponseDto> {
    try {
      // Verify the student exists
      const student = await this.userRepository.findOne({ where: { id: studentId } });
      if (!student) {
        throw new NotFoundException(`Student with ID ${studentId} not found`);
      }

      if (student.type !== UserType.STUDENT) {
        throw new BadRequestException('Only students can create mission entries');
      }

      // Verify the mission exists and is active
      const mission = await this.diaryMissionRepository.findOne({
        where: { id: createDto.missionId },
        relations: ['tutor'],
      });

      if (!mission) {
        throw new NotFoundException(`Mission with ID ${createDto.missionId} not found`);
      }

      if (!mission.isActive) {
        throw new BadRequestException('This mission is not active');
      }

      // Check if the mission has expired
      const now = getCurrentUTCDate();
      if (mission.expiryDate && mission.expiryDate < now) {
        throw new BadRequestException('This mission has expired');
      }

      // Check if the student already has an entry for this mission
      const existingEntry = await this.missionDiaryEntryRepository.findOne({
        where: {
          missionId: createDto.missionId,
          studentId,
        },
      });

      if (existingEntry) {
        throw new BadRequestException('You already have an entry for this mission');
      }

      // Validate word count - only check max word count for create
      const wordCount = this.calculateWordCount(createDto.content);
      if (mission.targetMaxWordCount && wordCount > mission.targetMaxWordCount) {
        throw new BadRequestException(`Entry cannot exceed ${mission.targetMaxWordCount} words`);
      }

      const progress = this.calculateProgress(wordCount, mission.targetWordCount);

      // Create the entry
      const entry = this.missionDiaryEntryRepository.create({
        missionId: createDto.missionId,
        studentId,
        content: createDto.content,
        wordCount,
        progress,
        status: MissionEntryStatus.NEW,
      });

      // Save the entry
      const savedEntry = await this.missionDiaryEntryRepository.save(entry);
      this.logger.log(`Created mission entry with ID ${savedEntry.id} by student ${studentId}`);

      return await this.mapToResponseDto(savedEntry, mission, student);
    } catch (error) {
      this.logger.error(`Error creating mission entry: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update an existing mission diary entry
   * @param id Entry ID
   * @param studentId ID of the student updating the entry
   * @param updateDto Entry update data
   * @returns The updated entry
   */
  async updateMissionEntry(id: string, studentId: string, updateDto: UpdateMissionDiaryEntryDto): Promise<MissionDiaryEntryResponseDto> {
    try {
      // Find the entry
      const entry = await this.missionDiaryEntryRepository.findOne({
        where: { id },
        relations: ['mission', 'mission.tutor', 'mission.category', 'student'],
      });

      if (!entry) {
        throw new NotFoundException(`Mission entry with ID ${id} not found`);
      }

      // Check if the student is the creator of the entry
      if (entry.studentId !== studentId) {
        throw new ForbiddenException('You can only update your own entries');
      }

      // Allow unlimited updates - students can update entries in any status
      // Tutors will be notified of updates after submission

      // Update the entry
      if (updateDto.content !== undefined) {
        // Validate word count using the submitted content - only check max word count for update
        const wordCount = this.calculateWordCount(updateDto.content);

        if (entry.mission.targetMaxWordCount && wordCount > entry.mission.targetMaxWordCount) {
          throw new BadRequestException(`Entry cannot exceed ${entry.mission.targetMaxWordCount} words`);
        }

        // NEW REQUIREMENT: Update API should NOT create versions - this is "save as draft"
        this.logger.log(`Updating mission entry ${entry.id} as draft (no version created)`);

        // Update content, word count and progress
        const previousWordCount = entry.wordCount;
        entry.content = updateDto.content;
        entry.wordCount = wordCount;
        entry.progress = this.calculateProgress(wordCount, entry.mission.targetWordCount);

        // NEW REQUIREMENT: Mark as draft
        entry.isDraft = true;

        // Check if the entry now meets the target word count while previously it didn't
        const meetsTargetNow = wordCount >= entry.mission.targetWordCount;
        const metTargetBefore = previousWordCount >= entry.mission.targetWordCount;

        if (meetsTargetNow && !metTargetBefore) {
          // Send notification when target word count is met for the first time
          // Get the tutor assigned to this student
          const tutorMappingsResult = await this.tutorMatchingService.getStudentTutors(studentId);

          if (tutorMappingsResult?.items?.length > 0) {
            const tutorId = tutorMappingsResult.items[0].id;
            const tutor = await this.userRepository.findOne({ where: { id: tutorId } });

            if (tutor) {
              // Send notification asynchronously to avoid blocking update
              this.sendMissionEntryNotificationAsync(
                studentId,
                entry.id,
                entry,
                tutor,
                NotificationType.MISSION_SUBMISSION_UPDATED,
                'Mission Entry Update - Target Word Count Met',
                `${entry.student.name} has met the target word count for mission: "${entry.mission.title}"`,
                'View Entry',
              );
            }
          }
        }
      }

      // Save the updated entry
      const updatedEntry = await this.missionDiaryEntryRepository.save(entry);
      this.logger.log(`Updated mission entry with ID ${updatedEntry.id} by student ${studentId}`);

      // If entry was already submitted/reviewed/confirmed, notify tutor of the update
      if (
        entry.status === MissionEntryStatus.SUBMITTED ||
        entry.status === MissionEntryStatus.REVIEWED
      ) {
        try {
          // Get the tutor assigned to this student
          const tutorMappingsResult = await this.tutorMatchingService.getStudentTutors(studentId);

          if (tutorMappingsResult?.items?.length > 0) {
            const tutorId = tutorMappingsResult.items[0].id;
            const tutor = await this.userRepository.findOne({ where: { id: tutorId } });

            if (tutor) {
              // Send notification asynchronously to avoid blocking update
              this.sendMissionEntryNotificationAsync(
                studentId,
                entry.id,
                entry,
                tutor,
                NotificationType.MISSION_SUBMISSION_UPDATED,
                'Mission Entry Updated After Submission',
                `${entry.student.name} has updated their mission entry after submission: "${entry.mission.title}"`,
                'Review Updated Entry',
              );
            }
          }
        } catch (error) {
          this.logger.warn(`Failed to send update notification: ${error.message}`);
        }
      }

      return await this.mapToResponseDto(updatedEntry, entry.mission, entry.student);
    } catch (error) {
      this.logger.error(`Error updating mission entry: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get or create a mission diary entry by mission ID
   * @param missionId Mission ID
   * @param studentId Student ID
   * @returns The entry (existing or new)
   */
  async getOrCreateMissionEntry(missionId: string, studentId: string): Promise<MissionDiaryEntryResponseDto> {
    try {
      // Verify the student exists
      const student = await this.userRepository.findOne({ where: { id: studentId } });
      if (!student) {
        throw new NotFoundException(`Student with ID ${studentId} not found`);
      }

      if (student.type !== UserType.STUDENT) {
        throw new BadRequestException('Only students can access mission entries');
      }

      // Verify the mission exists and is active
      const mission = await this.diaryMissionRepository.findOne({
        where: { id: missionId },
        relations: ['tutor', 'category'],
      });

      if (!mission) {
        throw new NotFoundException(`Mission with ID ${missionId} not found`);
      }

      if (!mission.isActive) {
        throw new BadRequestException('This mission is not active');
      }

      // Check if the mission has expired
      const now = getCurrentUTCDate();
      if (mission.expiryDate && mission.expiryDate < now) {
        throw new BadRequestException('This mission has expired');
      }

      // Handle missing category for the mission
      if (!mission.category && mission.categoryId) {
        try {
          const categoryRepository = this.dataSource.getRepository('category');
          const category = await categoryRepository.findOne({
            where: { id: mission.categoryId }
          });
          if (category) {
            mission.category = category as any;
            this.logger.log(`Loaded missing category ${category.name} for mission ${mission.id}`);
          }
        } catch (error) {
          this.logger.warn(`Failed to load category ${mission.categoryId} for mission ${mission.id}: ${error.message}`);
        }
      }

      // If still no category, assign a default one
      if (!mission.category) {
        try {
          const categoryRepository = this.dataSource.getRepository('category');
          const defaultCategory = await categoryRepository.findOne({
            where: { name: 'General' }
          });
          if (defaultCategory) {
            mission.category = defaultCategory as any;
            this.logger.log(`Assigned default category 'General' to mission ${mission.id}`);
          }
        } catch (error) {
          this.logger.warn(`Failed to load default category for mission ${mission.id}: ${error.message}`);
        }
      }

      // Check if the student already has an entry for this mission
      let entry = await this.missionDiaryEntryRepository.findOne({
        where: {
          missionId,
          studentId,
        },
        relations: ['mission', 'mission.tutor', 'mission.category', 'student', 'reviewer', 'feedbacks', 'feedbacks.tutor', 'skin', 'originalReviewedVersion', 'correctionProvider'],
      });

      if (entry) {
        // Handle missing category for this entry as well
        if (entry.mission && !entry.mission.category && entry.mission.categoryId) {
          try {
            const categoryRepository = this.dataSource.getRepository('category');
            const category = await categoryRepository.findOne({
              where: { id: entry.mission.categoryId }
            });
            if (category) {
              entry.mission.category = category as any;
              this.logger.log(`Loaded missing category ${category.name} for mission ${entry.mission.id}`);
            }
          } catch (error) {
            this.logger.warn(`Failed to load category ${entry.mission.categoryId} for mission ${entry.mission.id}: ${error.message}`);
          }
        }

        // If still no category, assign a default one
        if (entry.mission && !entry.mission.category) {
          try {
            const categoryRepository = this.dataSource.getRepository('category');
            const defaultCategory = await categoryRepository.findOne({
              where: { name: 'General' }
            });
            if (defaultCategory) {
              entry.mission.category = defaultCategory as any;
              this.logger.log(`Assigned default category 'General' to mission ${entry.mission.id}`);
            }
          } catch (error) {
            this.logger.warn(`Failed to load default category for mission ${entry.mission.id}: ${error.message}`);
          }
        }

        return await this.mapToResponseDto(entry, mission, student);
      }

      // Create a new entry with empty content
      entry = this.missionDiaryEntryRepository.create({
        missionId,
        studentId,
        content: '',
        wordCount: 0,
        progress: 0,
        status: MissionEntryStatus.NEW,
      });

      // Save the entry
      const savedEntry = await this.missionDiaryEntryRepository.save(entry);
      this.logger.log(`Created empty mission entry with ID ${savedEntry.id} by student ${studentId}`);

      return await this.mapToResponseDto(savedEntry, mission, student);
    } catch (error) {
      this.logger.error(`Error getting or creating mission entry: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get a specific mission diary entry by ID
   * @param id Entry ID
   * @returns The entry
   */
  async getMissionEntry(id: string): Promise<MissionDiaryEntryResponseDto> {
    try {
      const entry = await this.missionDiaryEntryRepository.findOne({
        where: { id },
        relations: ['mission', 'mission.tutor', 'mission.category', 'student', 'reviewer', 'feedbacks', 'feedbacks.tutor', 'skin', 'originalReviewedVersion', 'correctionProvider'],
      });

      if (!entry) {
        throw new NotFoundException(`Mission entry with ID ${id} not found`);
      }

      // Handle missing category for this entry as well
      if (entry.mission && !entry.mission.category && entry.mission.categoryId) {
        try {
          const categoryRepository = this.dataSource.getRepository('category');
          const category = await categoryRepository.findOne({
            where: { id: entry.mission.categoryId }
          });
          if (category) {
            entry.mission.category = category as any;
            this.logger.log(`Loaded missing category ${category.name} for mission ${entry.mission.id}`);
          }
        } catch (error) {
          this.logger.warn(`Failed to load category ${entry.mission.categoryId} for mission ${entry.mission.id}: ${error.message}`);
        }
      }

      // If still no category, assign a default one
      if (entry.mission && !entry.mission.category) {
        try {
          const categoryRepository = this.dataSource.getRepository('category');
          const defaultCategory = await categoryRepository.findOne({
            where: { name: 'General' }
          });
          if (defaultCategory) {
            entry.mission.category = defaultCategory as any;
            this.logger.log(`Assigned default category 'General' to mission ${entry.mission.id}`);
          }
        } catch (error) {
          this.logger.warn(`Failed to load default category for mission ${entry.mission.id}: ${error.message}`);
        }
      }

      return await this.mapToResponseDto(entry, entry.mission, entry.student, entry.reviewer);
    } catch (error) {
      this.logger.error(`Error getting mission entry: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get mission entries for a specific student with filtering and pagination
   * @param studentId Student ID
   * @param filterDto Filter criteria
   * @param paginationDto Pagination options
   * @returns Paged list of entries
   */
  async getStudentMissionEntries(studentId: string, filterDto: MissionEntryFilterDto = {}, paginationDto: PaginationDto = { page: 1, limit: 10 }): Promise<PagedListDto<MissionDiaryEntryResponseDto>> {
    try {
      const { page = 1, limit = 10 } = paginationDto;
      const skip = (page - 1) * limit;

      // Build query
      const queryBuilder = this.missionDiaryEntryRepository
        .createQueryBuilder('entry')
        .leftJoinAndSelect('entry.mission', 'mission')
        .leftJoinAndSelect('mission.tutor', 'tutor')
        .leftJoinAndSelect('mission.category', 'category')
        .leftJoinAndSelect('entry.student', 'student')
        .leftJoinAndSelect('entry.reviewer', 'reviewer')
        .leftJoinAndSelect('entry.feedbacks', 'feedbacks')
        .leftJoinAndSelect('feedbacks.tutor', 'feedbackTutor')
        .leftJoinAndSelect('entry.correctionProvider', 'correctionProvider')
        .where('entry.studentId = :studentId', { studentId })
        .orderBy('entry.createdAt', 'DESC');

      // Apply filters
      if (filterDto.missionId) {
        queryBuilder.andWhere('entry.missionId = :missionId', { missionId: filterDto.missionId });
      }

      if (filterDto.status) {
        queryBuilder.andWhere('entry.status = :status', { status: filterDto.status });
      }

      if (filterDto.createdAtFrom) {
        queryBuilder.andWhere('entry.createdAt >= :createdAtFrom', { createdAtFrom: new Date(filterDto.createdAtFrom) });
      }

      if (filterDto.createdAtTo) {
        queryBuilder.andWhere('entry.createdAt <= :createdAtTo', { createdAtTo: new Date(filterDto.createdAtTo) });
      }

      // Get total count
      const totalItems = await queryBuilder.getCount();

      // Get paginated results
      const entries = await queryBuilder.skip(skip).take(limit).getMany();

      // Map to response DTOs with category handling
      const items = await Promise.all(entries.map(async (entry) => {
        // Handle missions without categories by loading a default category
        if (entry.mission && !entry.mission.category && entry.mission.categoryId) {
          // Try to load the category separately if it wasn't loaded by the join
          try {
            const categoryRepository = this.dataSource.getRepository('category');
            const category = await categoryRepository.findOne({
              where: { id: entry.mission.categoryId }
            });
            if (category) {
              entry.mission.category = category as any;
              this.logger.log(`Loaded missing category ${category.name} for mission ${entry.mission.id}`);
            }
          } catch (error) {
            this.logger.warn(`Failed to load category ${entry.mission.categoryId} for mission ${entry.mission.id}: ${error.message}`);
          }
        }

        // If still no category, assign a default one
        if (entry.mission && !entry.mission.category) {
          try {
            const categoryRepository = this.dataSource.getRepository('category');
            const defaultCategory = await categoryRepository.findOne({
              where: { name: 'General' }
            });
            if (defaultCategory) {
              entry.mission.category = defaultCategory as any;
              this.logger.log(`Assigned default category 'General' to mission ${entry.mission.id}`);
            }
          } catch (error) {
            this.logger.warn(`Failed to load default category for mission ${entry.mission.id}: ${error.message}`);
          }
        }

        return this.mapToResponseDto(entry, entry.mission, entry.student, entry.reviewer);
      }));

      return new PagedListDto(items, totalItems, page, limit);
    } catch (error) {
      this.logger.error(`Error getting student mission entries: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get mission entries for a specific tutor with filtering and pagination
   * @param tutorId Tutor ID
   * @param filterDto Filter criteria
   * @param paginationDto Pagination options
   * @returns Paged list of entries
   */
  async getTutorMissionEntries(tutorId: string, filterDto: MissionEntryFilterDto = {}, paginationDto: PaginationDto = { page: 1, limit: 10 }): Promise<PagedListDto<MissionDiaryEntryResponseDto>> {
    try {
      const { page = 1, limit = 10 } = paginationDto;
      const skip = (page - 1) * limit;

      // Get the tutor's assigned students
      const studentMappings = await this.tutorMatchingService.getTutorStudents(tutorId);
      const studentIds = studentMappings.map((mapping: any) => mapping.id);

      if (studentIds.length === 0) {
        // Return empty result if no students assigned
        return new PagedListDto([], 0, page, limit);
      }

      // Build query - exclude NEW and REVIEWED entries, exclude resubmissions, and get only latest submissions
      const queryBuilder = this.missionDiaryEntryRepository
        .createQueryBuilder('entry')
        .leftJoinAndSelect('entry.mission', 'mission')
        .leftJoinAndSelect('mission.tutor', 'tutor')
        .leftJoinAndSelect('mission.category', 'category')
        .leftJoinAndSelect('entry.student', 'student')
        .leftJoinAndSelect('entry.reviewer', 'reviewer')
        .leftJoinAndSelect('entry.feedbacks', 'feedbacks')
        .leftJoinAndSelect('feedbacks.tutor', 'feedbackTutor')
        .leftJoinAndSelect('entry.correctionProvider', 'correctionProvider')
        .where('entry.studentId IN (:...studentIds)', { studentIds })
        .andWhere('entry.status NOT IN (:...excludedStatuses)', {
          excludedStatuses: [MissionEntryStatus.NEW, MissionEntryStatus.REVIEWED],
        })
        .andWhere('entry.isResubmission != :isResubmission', { isResubmission: true })
        .andWhere('entry.updatedAt = (' +
          'SELECT MAX(e2.updated_at) FROM mission_diary_entry e2 ' +
          'WHERE e2.student_id = entry.student_id ' +
          'AND e2.mission_id = entry.mission_id ' +
          'AND e2.status NOT IN (:...excludedStatuses) ' +
          'AND e2.is_resubmission != :isResubmission' +
        ')')
        .orderBy('entry.createdAt', 'DESC');

      // Apply filters
      if (filterDto.missionId) {
        queryBuilder.andWhere('entry.missionId = :missionId', { missionId: filterDto.missionId });
      }

      if (filterDto.studentId) {
        queryBuilder.andWhere('entry.studentId = :studentId', { studentId: filterDto.studentId });
      }

      if (filterDto.status) {
        queryBuilder.andWhere('entry.status = :status', { status: filterDto.status });
      }

      if (filterDto.createdAtFrom) {
        queryBuilder.andWhere('entry.createdAt >= :createdAtFrom', { createdAtFrom: new Date(filterDto.createdAtFrom) });
      }

      if (filterDto.createdAtTo) {
        queryBuilder.andWhere('entry.createdAt <= :createdAtTo', { createdAtTo: new Date(filterDto.createdAtTo) });
      }

      // Get total count
      const totalItems = await queryBuilder.getCount();

      this.logger.log(`[getTutorMissionEntries] Found ${totalItems} mission entries available for review (excluding NEW and CONFIRMED entries)`);

      // Get paginated results
      const entries = await queryBuilder.skip(skip).take(limit).getMany();

      // Map to response DTOs
      const items = await Promise.all(entries.map((entry) => this.mapToResponseDto(entry, entry.mission, entry.student, entry.reviewer)));

      return new PagedListDto(items, totalItems, page, limit);
    } catch (error) {
      this.logger.error(`Error getting tutor mission entries: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Submit a mission diary entry for review
   * @param id Entry ID
   * @param studentId ID of the student submitting the entry
   * @returns The submitted entry
   */
  async submitMissionEntry(id: string, studentId: string, submitDto?: UpdateMissionDiaryEntryDto): Promise<MissionDiaryEntryResponseDto> {
    try {
      // Optimized: Find the entry with minimal relations and validate ownership
      const entry = await this.missionDiaryEntryRepository.findOne({
        where: { id, studentId }, // Validate ownership in the query
        relations: ['mission', 'mission.category', 'student'],
        select: {
          id: true,
          content: true,
          wordCount: true,
          status: true,
          studentId: true,
          isDraft: true,
          canSubmitNewVersion: true,
          submittedVersionCount: true,
          isResubmission: true,
          resubmissionType: true,
          previousReviewCount: true,
          mission: {
            id: true,
            title: true,
            targetWordCount: true,
          },
          student: {
            id: true,
            name: true,
          },
        },
      });

      if (!entry) {
        throw new NotFoundException(`Mission entry with ID ${id} not found or you don't have permission to access it`);
      }

      // NEW REQUIREMENT: Check if user can submit a new version
      // Allow submissions after review or if canSubmitNewVersion is true
      // Also allow resubmissions (entries that have been reviewed before)
      const hasBeenReviewed = entry.lastReviewedAt || entry.previousReviewCount > 0;
      const canSubmit =
        entry.canSubmitNewVersion ||
        entry.status === MissionEntryStatus.REVIEWED || // Reviewed entries
        hasBeenReviewed; // Allow resubmissions after any previous review

      if (!canSubmit) {
        // Provide more specific error message based on current status
        let errorMessage = 'Cannot submit new version.';

        if (entry.status === MissionEntryStatus.SUBMITTED) {
          errorMessage += ' Previous submission is still pending review by tutor.';
        } else {
          errorMessage += ' Previous submission must be reviewed by tutor first.';
        }

        throw new BadRequestException(errorMessage);
      }

      // Check word count requirements using submitted content
      const wordCount = this.calculateWordCount(submitDto.content);
      if (wordCount < entry.mission.targetWordCount) {
        throw new BadRequestException(`Entry must contain at least ${entry.mission.targetWordCount} words before submission`);
      }

      if (entry.mission.targetMaxWordCount && wordCount > entry.mission.targetMaxWordCount) {
        throw new BadRequestException(`Entry cannot exceed ${entry.mission.targetMaxWordCount} words`);
      }

      // NEW REQUIREMENT: Create a submitted version in history with resubmission tracking
      const submissionNumber = entry.submittedVersionCount + 1;

      // Determine if this is a resubmission and what type
      const isResubmission = entry.submittedVersionCount > 0;
      let resubmissionType: 'after_review' | null = null;
      let previousStatus: string | null = null;

      if (isResubmission) {
        previousStatus = entry.status;
        if (entry.status === MissionEntryStatus.REVIEWED) {
          resubmissionType = 'after_review';
        }
      }

      // Mark all existing versions as not latest before creating new submission
      await this.missionDiaryEntryHistoryRepository.update({ missionEntryId: id }, { isLatest: false });

      // Create submitted version with resubmission tracking
      const submittedVersion = await this.missionDiaryEntryHistoryRepository.save(
        this.missionDiaryEntryHistoryRepository.create({
          missionEntryId: id,
          content: submitDto.content,
          versionNumber: submissionNumber,
          isLatest: true,
          isSubmittedVersion: true,
          submissionNumber: submissionNumber,
          submittedAt: new Date(),
          wordCount: wordCount,
          isResubmission: isResubmission,
          resubmissionType: resubmissionType,
          previousStatus: previousStatus,
          metaData: {
            submissionType: isResubmission ? 'resubmission' : 'initial_submit',
            resubmissionType: resubmissionType,
            previousStatus: previousStatus,
            browserInfo: 'N/A',
            ipAddress: 'N/A',
          },
        }),
      );

      // Update the entry content and word count
      entry.content = submitDto.content;
      entry.wordCount = wordCount;
      entry.progress = this.calculateProgress(wordCount, entry.mission.targetWordCount);

      // Update the entry status
      entry.status = MissionEntryStatus.SUBMITTED;

      // NEW REQUIREMENT: Update submission tracking fields
      entry.isDraft = false;
      entry.lastSubmittedAt = new Date();
      entry.canSubmitNewVersion = false; // Will be set to true when reviewed
      entry.submittedVersionCount = submissionNumber;
      entry.currentSubmittedVersionId = submittedVersion.id;

      // Update resubmission tracking fields
      entry.isResubmission = isResubmission;
      entry.resubmissionType = resubmissionType;
      if (resubmissionType === 'after_review') {
        entry.previousReviewCount = (entry.previousReviewCount || 0) + 1;
      }
      // Note: after_confirmation type removed from lifecycle

      // Save the updated entry
      const submittedEntry = await this.missionDiaryEntryRepository.save(entry);

      // NEW REQUIREMENT: Always send notification for submissions (unlimited submissions allowed)
      try {
        this.logger.log(`Sending submission notification for mission entry: ${submittedEntry.id} (submission #${submissionNumber})`);

        // Get the tutor assigned to this student
        const tutorMappingsResult = await this.tutorMatchingService.getStudentTutors(studentId);

        if (tutorMappingsResult?.items?.length > 0) {
          const tutorId = tutorMappingsResult.items[0].id;
          const tutor = await this.userRepository.findOne({ where: { id: tutorId } });

          if (tutor) {
            // Send notification asynchronously to avoid blocking submission
            this.sendMissionEntryNotificationAsync(
              studentId,
              entry.id,
              entry,
              tutor,
              NotificationType.MISSION_SUBMISSION,
              'New Mission Entry Submission',
              `${entry.student.name} has submitted an entry for mission: "${entry.mission.title}"`,
              'Review Submission',
            );
          }
        }
      } catch (error) {
        this.logger.error(`Failed to send notification to tutor: ${error.message}`, error.stack);
      }

      return await this.mapToResponseDto(submittedEntry, entry.mission, entry.student);
    } catch (error) {
      this.logger.error(`Error submitting mission entry: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Add feedback to a mission diary entry
   * @param entryId Entry ID
   * @param tutorId ID of the tutor adding the feedback
   * @param feedbackDto Feedback data
   * @returns The added feedback
   */
  async addFeedback(entryId: string, tutorId: string, feedbackDto: AddMissionFeedbackDto): Promise<MissionFeedbackResponseDto> {
    try {
      // Verify the tutor exists
      const tutor = await this.userRepository.findOne({ where: { id: tutorId } });
      if (!tutor) {
        throw new NotFoundException(`Tutor with ID ${tutorId} not found`);
      }

      if (tutor.type !== UserType.TUTOR) {
        throw new BadRequestException('Only tutors can add feedback');
      }

      // Find the entry
      const entry = await this.missionDiaryEntryRepository.findOne({
        where: { id: entryId },
        relations: ['mission', 'mission.tutor', 'mission.category', 'student'],
      });

      if (!entry) {
        throw new NotFoundException(`Mission entry with ID ${entryId} not found`);
      }

      // Check if the entry is in a state that can receive feedback
      if (entry.status === MissionEntryStatus.NEW) {
        throw new BadRequestException('Cannot add feedback to an entry that has not been submitted');
      }

      // Create the feedback
      const feedback = this.feedbackRepository.create({
        missionEntryId: entryId,
        tutorId,
        feedback: feedbackDto.feedback,
        rating: feedbackDto.rating,
      });

      // Save the feedback
      const savedFeedback = await this.feedbackRepository.save(feedback);
      this.logger.log(`Added feedback with ID ${savedFeedback.id} to mission entry ${entryId} by tutor ${tutorId}`);

      // Send notification to student
      try {
        // Generate deeplinks for the mission entry
        const webLink = this.deeplinkService.getWebLink(DeeplinkType.MISSION_DIARY_ENTRY, { id: entry.missionId });
        const deepLink = this.deeplinkService.getDeepLink(DeeplinkType.MISSION_DIARY_ENTRY, { id: entry.missionId });
        const entryButton = this.deeplinkService.getLinkHtml(DeeplinkType.MISSION_DIARY_ENTRY, {
          id: entry.missionId,
          linkText: 'View Feedback',
          buttonStyle: true,
        });

        // Create HTML content for email notification
        const htmlContent = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
            <div style="text-align: center; margin-bottom: 20px;">
              <h2 style="color: #333;">New Feedback on Your Mission Entry</h2>
            </div>
            <div style="margin-bottom: 20px;">
              <p>Hello ${entry.student.name || 'there'},</p>
              <p>Your tutor ${tutor.name} has provided feedback on your mission entry:</p>
              <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 15px 0;">
                <p><strong>Mission:</strong> ${entry.mission.title}</p>
                <p><strong>Feedback:</strong> ${feedbackDto.feedback}</p>
                ${feedbackDto.rating ? `<p><strong>Rating:</strong> ${feedbackDto.rating}/5</p>` : ''}
              </div>
              <p>Click the button below to view the complete feedback:</p>
              <div style="text-align: center; margin: 25px 0;">
                ${entryButton}
              </div>
            </div>
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
              <p>This is an automated message from the HEC system.</p>
              <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
            </div>
          </div>
        `;

        await this.asyncNotificationHelper.notifyAsync(
          entry.studentId,
          NotificationType.MISSION_FEEDBACK,
          'New Feedback on Your Mission Entry',
          `${tutor.name} has provided feedback on your mission entry: "${entry.mission.title}"`,
          {
            relatedEntityId: entry.missionId,
            relatedEntityType: RelatedEntityType.MISSION_DIARY_ENTRY,
            htmlContent: htmlContent,
            webLink: webLink,
            deepLink: deepLink,
            sendEmail: true,
            sendPush: true,
            sendInApp: true,
            sendRealtime: false,
          },
        );
      } catch (error) {
        this.logger.warn(`Failed to send notification: ${error.message}`);
      }

      return {
        id: savedFeedback.id,
        missionEntryId: savedFeedback.missionEntryId,
        tutorId: savedFeedback.tutorId,
        tutorName: tutor.name,
        feedback: savedFeedback.feedback,
        rating: savedFeedback.rating,
        createdAt: savedFeedback.createdAt,
      };
    } catch (error) {
      this.logger.error(`Error adding feedback: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Add correction to a mission diary entry
   * @param entryId Entry ID
   * @param tutorId ID of the tutor adding the correction
   * @param correctionDto Correction data
   * @returns The updated entry
   */
  async addCorrection(entryId: string, tutorId: string, correctionDto: AddMissionCorrectionDto): Promise<MissionDiaryEntryResponseDto> {
    return this.retryOperation(async () => {
      const queryRunner = this.dataSource.createQueryRunner();
      await queryRunner.connect();

      try {
        await queryRunner.startTransaction();
      // Optimized combined tutor validation and entry loading with access check
      const entry = await this.missionDiaryEntryRepository
        .createQueryBuilder('entry')
        .leftJoinAndSelect('entry.mission', 'mission')
        .leftJoinAndSelect('mission.tutor', 'missionTutor')
        .leftJoinAndSelect('mission.category', 'category')
        .leftJoinAndSelect('entry.student', 'student')
        .leftJoinAndSelect('entry.feedbacks', 'feedbacks')
        .leftJoinAndSelect('feedbacks.tutor', 'feedbackTutor')
        .innerJoin('student_tutor_mapping', 'stm', 'stm.studentId = entry.studentId AND stm.tutorId = :tutorId AND stm.status = :status')
        .innerJoin('user', 'tutor', 'tutor.id = :tutorId AND tutor.type = :tutorType')
        .where('entry.id = :entryId', { entryId })
        .setParameters({ tutorId, status: 'active', tutorType: UserType.TUTOR })
        .getOne();

      if (!entry) {
        throw new ForbiddenException('Mission entry not found or you do not have access to add corrections to this entry');
      }

      // Get tutor details for notifications
      const tutor = await this.userRepository.findOne({
        where: { id: tutorId },
        select: ['id', 'name', 'type']
      });

      if (!entry) {
        throw new NotFoundException(`Mission entry with ID ${entryId} not found`);
      }

      // Check if the entry is in a state that can receive corrections
      if (entry.status === MissionEntryStatus.NEW) {
        throw new BadRequestException('Cannot add correction to an entry that has not been submitted');
      }

      // Check if a score has already been assigned - if so, prevent correction updates
      if (entry.gainedScore !== null && entry.gainedScore !== undefined) {
        throw new BadRequestException('Cannot update correction after a score has been assigned. Correction and score can only be given once per entry.');
      }

      // NEW REQUIREMENT: Set original reviewed version if not already set
      if (!entry.originalReviewedVersionId) {
        // Create a version history entry for the current state before review
        const originalVersion = new MissionDiaryEntryHistory();
        originalVersion.missionEntryId = entry.id;
        originalVersion.content = entry.content;
        // Calculate next version number based on existing history records
        const existingVersionsCount = await this.missionDiaryEntryHistoryRepository.count({
          where: { missionEntryId: entry.id },
        });
        originalVersion.versionNumber = existingVersionsCount + 1;
        originalVersion.isLatest = false; // This is the original reviewed version, not the latest
        originalVersion.wordCount = this.calculateWordCount(entry.content);
        originalVersion.metaData = {
          updateTrigger: 'submit' as const,
          significantChange: true,
        };
        originalVersion.createdBy = entry.studentId;
        originalVersion.updatedBy = tutorId;

        const savedOriginalVersion = await this.missionDiaryEntryHistoryRepository.save(originalVersion);

        // Update entry to reference this as the original reviewed version
        entry.originalReviewedVersionId = savedOriginalVersion.id;
        // totalEditHistory is now calculated from actual history records, no need to update counter

        this.logger.log(`Set original reviewed version ${savedOriginalVersion.id} for mission entry ${entry.id}`);
      }

      // Update the entry with the correction
      entry.correction = correctionDto.correction;
      entry.correctionProvidedAt = getCurrentUTCDate();
      entry.correctionProvidedBy = tutorId;
      entry.status = MissionEntryStatus.REVIEWED;

      // Save the updated entry
      const updatedEntry = await this.missionDiaryEntryRepository.save(entry);
      this.logger.log(`Added correction to mission entry ${entryId} by tutor ${tutorId}`);

      // Send notification to student
      try {
        // Generate deeplinks for the mission entry
        const webLink = this.deeplinkService.getWebLink(DeeplinkType.MISSION_DIARY_ENTRY, { id: entryId });
        const deepLink = this.deeplinkService.getDeepLink(DeeplinkType.MISSION_DIARY_ENTRY, { id: entryId });
        const entryButton = this.deeplinkService.getLinkHtml(DeeplinkType.MISSION_DIARY_ENTRY, {
          id: entryId,
          linkText: 'View Corrections',
          buttonStyle: true,
        });

        // Create HTML content for email notification
        const htmlContent = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
            <div style="text-align: center; margin-bottom: 20px;">
              <h2 style="color: #333;">Corrections on Your Mission Entry</h2>
            </div>
            <div style="margin-bottom: 20px;">
              <p>Hello ${entry.student.name || 'there'},</p>
              <p>Your tutor ${tutor.name} has provided corrections on your mission entry:</p>
              <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 15px 0;">
                <p><strong>Mission:</strong> ${entry.mission.title}</p>
                <p><strong>Corrections:</strong> ${correctionDto.correction}</p>
              </div>
              <p>Click the button below to view the complete corrections:</p>
              <div style="text-align: center; margin: 25px 0;">
                ${entryButton}
              </div>
            </div>
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
              <p>This is an automated message from the HEC system.</p>
              <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
            </div>
          </div>
        `;

        // Send notification asynchronously to avoid blocking the response
        this.asyncNotificationHelper.notifyAsync(
          entry.studentId,
          NotificationType.MISSION_CORRECTION,
          'Corrections on Your Mission Entry',
          `${tutor.name} has provided corrections on your mission entry: "${entry.mission.title}"`,
          {
            relatedEntityId: entryId,
            relatedEntityType: RelatedEntityType.MISSION_DIARY_ENTRY,
            htmlContent: htmlContent,
            webLink: webLink,
            deepLink: deepLink,
            sendEmail: true,
            sendPush: true,
            sendInApp: true,
            sendRealtime: false,
          },
          {
            submissionId: entryId,
            entryType: 'mission_diary_entry',
            priority: 2, // Medium priority for corrections
          }
        ).catch(error => {
          this.logger.error(`Failed to send async notification: ${error?.message || 'Unknown error'}`, error?.stack);
        });
      } catch (error) {
        this.logger.warn(`Failed to prepare notification: ${error.message}`);
      }

        return await this.mapToResponseDto(updatedEntry, entry.mission, entry.student, tutor);
      } catch (error) {
        await queryRunner.rollbackTransaction();
        this.logger.error(`Error adding correction: ${error.message}`, error.stack);
        throw error;
      } finally {
        await queryRunner.release();
      }
    });
  }

  /**
   * Submit review for mission diary entry - NEW UNIFIED LOGIC
   * Score is required, correction is optional, can only review once
   * @param entryId The ID of the mission diary entry
   * @param tutorId The ID of the tutor submitting the review
   * @param score Required score for the entry
   * @param correction Optional correction text
   * @returns The updated mission diary entry
   */
  async submitMissionReview(entryId: string, tutorId: string, score: number, correction?: string): Promise<MissionDiaryEntry> {
    // Check if the user is a tutor
    const user = await this.userRepository.findOne({
      where: { id: tutorId },
      relations: ['userRoles', 'userRoles.role'],
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${tutorId} not found`);
    }

    const isTutor = user.userRoles.some((userRole) => userRole.role.name === 'tutor');
    if (!isTutor) {
      throw new ForbiddenException('Only tutors can submit reviews');
    }

    // Get the mission diary entry
    const entry = await this.missionDiaryEntryRepository.findOne({
      where: { id: entryId },
      relations: ['mission', 'mission.category', 'student'],
    });

    if (!entry) {
      throw new NotFoundException(`Mission diary entry with ID ${entryId} not found`);
    }

    if (entry.status !== MissionEntryStatus.SUBMITTED) {
      throw new BadRequestException('Entry must be in SUBMITTED status to be reviewed');
    }

    // NEW REQUIREMENT: Check if already reviewed (can only review once)
    if (entry.reviewedBy && entry.gainedScore !== null && entry.gainedScore !== undefined) {
      throw new BadRequestException('Entry has already been reviewed and scored. Only feedback can be added after review.');
    }

    // Validate score (default max score is 100)
    const maxScore = 100;
    if (score < 0 || score > maxScore) {
      throw new BadRequestException(`Score must be between 0 and ${maxScore}`);
    }

    // Store resubmission tracking fields before update to preserve them
    const resubmissionType = entry.resubmissionType;
    const isResubmission = entry.isResubmission;
    const previousReviewCount = entry.previousReviewCount;
    const previousConfirmationCount = entry.previousConfirmationCount;
    const submittedVersionCount = entry.submittedVersionCount;

    // Update the entry with the score
    entry.gainedScore = score;
    entry.reviewedBy = tutorId;
    entry.reviewedAt = getCurrentUTCDate();
    entry.status = MissionEntryStatus.REVIEWED;

    // NEW REQUIREMENT: Enable subsequent submissions after review
    entry.lastReviewedAt = getCurrentUTCDate();
    entry.canSubmitNewVersion = true;

    // PRESERVE resubmission tracking fields
    entry.resubmissionType = resubmissionType;
    entry.isResubmission = isResubmission;
    entry.previousReviewCount = previousReviewCount;
    entry.previousConfirmationCount = previousConfirmationCount;
    entry.submittedVersionCount = submittedVersionCount;

    // Add correction if provided
    if (correction) {
      entry.correction = correction;
      entry.correctionProvidedAt = getCurrentUTCDate();
      entry.correctionProvidedBy = tutorId;
    }

    // Save the updated entry
    const updatedEntry = await this.missionDiaryEntryRepository.save(entry);

    // Send review notification
    try {
      await this.sendMissionReviewNotification(updatedEntry, score, correction);
    } catch (notificationError) {
      this.logger.error(`Failed to send review notification: ${notificationError.message}`, notificationError.stack);
    }

    this.logger.log(`Submitted review for mission entry ${entryId} by tutor ${tutorId} with score ${score}`);
    return updatedEntry;
  }

  /**
   * Add unlimited feedback to mission diary entry - NEW UNIFIED LOGIC
   * @param entryId The ID of the mission diary entry
   * @param tutorId The ID of the tutor adding feedback
   * @param feedbackText The feedback text
   * @returns The created feedback
   */
  async addMissionFeedback(entryId: string, tutorId: string, feedbackText: string): Promise<MissionDiaryEntryFeedback> {
    return this.retryOperation(async () => {
    // Check if the user is a tutor
    const user = await this.userRepository.findOne({
      where: { id: tutorId },
      relations: ['userRoles', 'userRoles.role'],
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${tutorId} not found`);
    }

    const isTutor = user.userRoles.some((userRole) => userRole.role.name === 'tutor');
    if (!isTutor) {
      throw new ForbiddenException('Only tutors can add feedback');
    }

    // Get the mission diary entry
    const entry = await this.missionDiaryEntryRepository.findOne({
      where: { id: entryId },
      relations: ['mission', 'mission.category', 'student'],
    });

    if (!entry) {
      throw new NotFoundException(`Mission diary entry with ID ${entryId} not found`);
    }

    if (entry.status === MissionEntryStatus.NEW) {
      throw new BadRequestException('Entry must be submitted to receive feedback');
    }

    // Create feedback
    const feedback = this.feedbackRepository.create({
      missionEntryId: entryId,
      tutorId: tutorId,
      feedback: feedbackText,
      createdBy: tutorId,
      updatedBy: tutorId,
    });

    const savedFeedback = await this.feedbackRepository.save(feedback);

    // Send feedback notification
    try {
      await this.sendMissionFeedbackNotification(entry, savedFeedback);
    } catch (notificationError) {
      this.logger.error(`Failed to send feedback notification: ${notificationError.message}`, notificationError.stack);
    }

      return savedFeedback;
    });
  }

  /**
   * Send review notification to student
   */
  private async sendMissionReviewNotification(entry: MissionDiaryEntry, score: number, correction?: string): Promise<void> {
    try {
      // Get tutor details
      const tutor = await this.userRepository.findOne({
        where: { id: entry.reviewedBy },
      });

      if (!tutor || !entry.student) {
        this.logger.warn('Missing tutor or student information for review notification');
        return;
      }

      const message = correction ? `Your mission diary entry has been reviewed with score ${score} and correction provided.` : `Your mission diary entry has been reviewed with score ${score}.`;

      await this.asyncNotificationHelper.notifyAsync(entry.studentId, NotificationType.MISSION_REVIEW_COMPLETE, 'Mission Diary Entry Reviewed', message, {
        relatedEntityId: entry.id,
        relatedEntityType: RelatedEntityType.MISSION_DIARY_ENTRY,
        sendEmail: true,
        sendPush: true,
        sendInApp: true,
        sendRealtime: false,
      });

      this.logger.log(`Sent review notification to student ${entry.studentId} for mission entry ${entry.id}`);
    } catch (error) {
      this.logger.error(`Failed to send review notification: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Send feedback notification to student
   */
  private async sendMissionFeedbackNotification(entry: MissionDiaryEntry, feedback: MissionDiaryEntryFeedback): Promise<void> {
    try {
      // Get tutor details
      const tutor = await this.userRepository.findOne({
        where: { id: feedback.tutorId },
      });

      if (!tutor || !entry.student) {
        this.logger.warn('Missing tutor or student information for feedback notification');
        return;
      }

      // Send notification asynchronously to avoid blocking the response
      this.asyncNotificationHelper.notifyAsync(
        entry.studentId,
        NotificationType.MISSION_FEEDBACK,
        'New Feedback on Mission Diary Entry',
        `${tutor.name} has provided feedback on your mission diary entry.`,
        {
          relatedEntityId: entry.id,
          relatedEntityType: RelatedEntityType.MISSION_DIARY_ENTRY,
          sendEmail: true,
          sendPush: true,
          sendInApp: true,
          sendRealtime: false,
        },
        {
          submissionId: entry.id,
          entryType: 'mission_diary_entry',
          priority: 2, // Medium priority for feedback
        }
      ).catch(error => {
        this.logger.error(`Failed to send async notification: ${error?.message || 'Unknown error'}`, error?.stack);
      });

      this.logger.log(`Queued feedback notification to student ${entry.studentId} for mission entry ${entry.id}`);
    } catch (error) {
      this.logger.error(`Failed to prepare feedback notification: ${error.message}`, error.stack);
      // Don't throw error to avoid breaking the feedback submission
    }
  }

  /**
   * Assign a score to a mission diary entry (LEGACY METHOD - kept for backward compatibility)
   * @param entryId Entry ID
   * @param tutorId ID of the tutor assigning the score
   * @param scoreDto Score data
   * @returns The updated entry
   */
  async assignScore(entryId: string, tutorId: string, scoreDto: AssignMissionScoreDto): Promise<MissionDiaryEntryResponseDto> {
    try {
      // Verify the tutor exists
      const tutor = await this.userRepository.findOne({ where: { id: tutorId } });
      if (!tutor) {
        throw new NotFoundException(`Tutor with ID ${tutorId} not found`);
      }

      if (tutor.type !== UserType.TUTOR) {
        throw new BadRequestException('Only tutors can assign scores');
      }

      // Find the entry
      const entry = await this.missionDiaryEntryRepository.findOne({
        where: { id: entryId },
        relations: ['mission', 'mission.tutor', 'mission.category', 'student', 'feedbacks', 'feedbacks.tutor'],
      });

      if (!entry) {
        throw new NotFoundException(`Mission entry with ID ${entryId} not found`);
      }

      // Check if the entry is in a state that can receive a score
      if (entry.status === MissionEntryStatus.NEW) {
        throw new BadRequestException('Cannot assign a score to an entry that has not been submitted');
      }

      // Check if a score has already been assigned
      if (entry.gainedScore !== null && entry.gainedScore !== undefined) {
        throw new BadRequestException('A score has already been assigned to this entry');
      }

      // Validate the score
      if (scoreDto.score < 0 || scoreDto.score > entry.mission.score) {
        throw new BadRequestException(`Score must be between 0 and ${entry.mission.score}`);
      }

      // Store resubmission tracking fields before update to preserve them
      const resubmissionType = entry.resubmissionType;
      const isResubmission = entry.isResubmission;
      const previousReviewCount = entry.previousReviewCount;
      const previousConfirmationCount = entry.previousConfirmationCount;
      const submittedVersionCount = entry.submittedVersionCount;

      // Update the entry with the score
      entry.gainedScore = scoreDto.score;
      entry.reviewedBy = tutorId;
      entry.reviewedAt = getCurrentUTCDate();
      entry.status = MissionEntryStatus.REVIEWED;

      // NEW REQUIREMENT: Enable subsequent submissions after review
      entry.lastReviewedAt = getCurrentUTCDate();
      entry.canSubmitNewVersion = true;

      // PRESERVE resubmission tracking fields
      entry.resubmissionType = resubmissionType;
      entry.isResubmission = isResubmission;
      entry.previousReviewCount = previousReviewCount;
      entry.previousConfirmationCount = previousConfirmationCount;
      entry.submittedVersionCount = submittedVersionCount;

      // Save the updated entry
      const updatedEntry = await this.missionDiaryEntryRepository.save(entry);
      this.logger.log(`Assigned score ${scoreDto.score} to mission entry ${entryId} by tutor ${tutorId}`);

      // Send notification to student
      try {
        // Generate deeplinks for the mission entry
        const webLink = this.deeplinkService.getWebLink(DeeplinkType.MISSION_DIARY_ENTRY, { id: entryId });
        const deepLink = this.deeplinkService.getDeepLink(DeeplinkType.MISSION_DIARY_ENTRY, { id: entryId });
        const entryButton = this.deeplinkService.getLinkHtml(DeeplinkType.MISSION_DIARY_ENTRY, {
          id: entryId,
          linkText: 'View Review',
          buttonStyle: true,
        });

        // Create HTML content for email notification
        const htmlContent = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
            <div style="text-align: center; margin-bottom: 20px;">
              <h2 style="color: #333;">Mission Entry Review Completed</h2>
            </div>
            <div style="margin-bottom: 20px;">
              <p>Hello ${entry.student.name || 'there'},</p>
              <p>Your tutor ${tutor.name} has completed the review of your mission entry:</p>
              <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 15px 0;">
                <p><strong>Mission:</strong> ${entry.mission.title}</p>
                <p><strong>Score:</strong> ${scoreDto.score}/${entry.mission.score}</p>
                <p><strong>Percentage:</strong> ${Math.round((scoreDto.score / entry.mission.score) * 100)}%</p>
              </div>
              <p>Click the button below to view the complete review:</p>
              <div style="text-align: center; margin: 25px 0;">
                ${entryButton}
              </div>
            </div>
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
              <p>This is an automated message from the HEC system.</p>
              <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
            </div>
          </div>
        `;

        await this.asyncNotificationHelper.notifyAsync(
          entry.studentId,
          NotificationType.MISSION_REVIEW_COMPLETE,
          'Mission Entry Review Completed',
          `${tutor.name} has completed the review of your mission entry: "${entry.mission.title}"`,
          {
            relatedEntityId: entryId,
            relatedEntityType: RelatedEntityType.MISSION_DIARY_ENTRY,
            htmlContent: htmlContent,
            webLink: webLink,
            deepLink: deepLink,
            sendEmail: true,
            sendPush: true,
            sendInApp: true,
            sendRealtime: false,
          },
        );
      } catch (error) {
        this.logger.warn(`Failed to send notification: ${error.message}`);
      }

      return await this.mapToResponseDto(updatedEntry, entry.mission, entry.student, tutor);
    } catch (error) {
      this.logger.error(`Error assigning score: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Add correction with score to a mission diary entry in a single operation
   * @param entryId Entry ID
   * @param tutorId ID of the tutor adding the correction and score
   * @param correctionWithScoreDto Correction and score data
   * @returns The updated entry
   */
  async addCorrectionWithScore(entryId: string, tutorId: string, correctionWithScoreDto: AddMissionCorrectionWithScoreDto): Promise<MissionDiaryEntryResponseDto> {
    try {
      // Verify the tutor exists
      const tutor = await this.userRepository.findOne({ where: { id: tutorId } });
      if (!tutor) {
        throw new NotFoundException(`Tutor with ID ${tutorId} not found`);
      }

      if (tutor.type !== UserType.TUTOR) {
        throw new BadRequestException('Only tutors can add corrections and scores');
      }

      // Find the entry
      const entry = await this.missionDiaryEntryRepository.findOne({
        where: { id: entryId },
        relations: ['mission', 'mission.tutor', 'mission.category', 'student', 'feedbacks', 'feedbacks.tutor'],
      });

      if (!entry) {
        throw new NotFoundException(`Mission entry with ID ${entryId} not found`);
      }

      // Check if the entry is in a state that can receive corrections and scores
      if (entry.status === MissionEntryStatus.NEW) {
        throw new BadRequestException('Cannot add correction and score to an entry that has not been submitted');
      }

      // Check if a score has already been assigned
      if (entry.gainedScore !== null && entry.gainedScore !== undefined) {
        throw new BadRequestException('A score has already been assigned to this entry');
      }

      // Validate the score
      if (correctionWithScoreDto.score < 0 || correctionWithScoreDto.score > entry.mission.score) {
        throw new BadRequestException(`Score must be between 0 and ${entry.mission.score}`);
      }

      // Store resubmission tracking fields before update to preserve them
      const resubmissionType = entry.resubmissionType;
      const isResubmission = entry.isResubmission;
      const previousReviewCount = entry.previousReviewCount;
      const previousConfirmationCount = entry.previousConfirmationCount;
      const submittedVersionCount = entry.submittedVersionCount;

      // Update the entry with both correction and score
      entry.correction = correctionWithScoreDto.correction;
      entry.correctionProvidedAt = getCurrentUTCDate();
      entry.correctionProvidedBy = tutorId;
      entry.gainedScore = correctionWithScoreDto.score;
      entry.reviewedBy = tutorId;
      entry.reviewedAt = getCurrentUTCDate();
      entry.status = MissionEntryStatus.REVIEWED;

      // NEW REQUIREMENT: Enable subsequent submissions after review
      entry.lastReviewedAt = getCurrentUTCDate();
      entry.canSubmitNewVersion = true;

      // PRESERVE resubmission tracking fields
      entry.resubmissionType = resubmissionType;
      entry.isResubmission = isResubmission;
      entry.previousReviewCount = previousReviewCount;
      entry.previousConfirmationCount = previousConfirmationCount;
      entry.submittedVersionCount = submittedVersionCount;

      // Set original reviewed version if not already set
      if (!entry.originalReviewedVersionId) {
        // Create a version history entry for the current state before review
        const originalVersion = new MissionDiaryEntryHistory();
        originalVersion.missionEntryId = entryId;
        originalVersion.content = entry.content;
        // Calculate next version number based on existing history records
        const existingVersionsCount = await this.missionDiaryEntryHistoryRepository.count({
          where: { missionEntryId: entryId },
        });
        originalVersion.versionNumber = existingVersionsCount + 1;
        originalVersion.isLatest = false; // This is the original reviewed version, not the latest
        originalVersion.wordCount = this.calculateWordCount(entry.content);
        originalVersion.metaData = {
          updateTrigger: 'submit' as const,
          significantChange: true,
        };
        originalVersion.createdBy = entry.studentId;
        originalVersion.updatedBy = tutorId;

        const savedOriginalVersion = await this.missionDiaryEntryHistoryRepository.save(originalVersion);

        // Update entry to reference this as the original reviewed version
        entry.originalReviewedVersionId = savedOriginalVersion.id;

        this.logger.log(`Set original reviewed version ${savedOriginalVersion.id} for mission entry ${entry.id}`);
      }

      // Save the updated entry
      const updatedEntry = await this.missionDiaryEntryRepository.save(entry);
      this.logger.log(`Added correction and assigned score ${correctionWithScoreDto.score} to mission entry ${entryId} by tutor ${tutorId}`);

      // Send notification to student about the correction and score
      try {
        const webLink = this.deeplinkService.getWebLink(DeeplinkType.MISSION_DIARY_ENTRY, { id: entry.missionId });
        const deepLink = this.deeplinkService.getDeepLink(DeeplinkType.MISSION_DIARY_ENTRY, { id: entry.missionId });
        const entryButton = this.deeplinkService.getLinkHtml(DeeplinkType.MISSION_DIARY_ENTRY, {
          id: entry.missionId,
          linkText: 'View Your Entry',
          buttonStyle: true,
        });

        const htmlContent = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
            <div style="text-align: center; margin-bottom: 20px;">
              <h2 style="color: #333;">Mission Entry Review Completed</h2>
            </div>
            <div style="margin-bottom: 20px;">
              <p>Hello ${entry.student?.name || 'there'},</p>
              <p>${tutor.name} has completed the review of your mission entry: "${entry.mission.title}"</p>
              <p><strong>Score:</strong> ${correctionWithScoreDto.score}/${entry.mission.score}</p>
              <p><strong>Corrections:</strong> ${correctionWithScoreDto.correction}</p>
              <p>Click the button below to view your entry:</p>
              <div style="text-align: center; margin: 25px 0;">
                ${entryButton}
              </div>
            </div>
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
              <p>This is an automated message from the HEC system.</p>
              <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
            </div>
          </div>
        `;

        // Send notification asynchronously to avoid blocking the response
        this.asyncNotificationHelper.notifyAsync(
          entry.studentId,
          NotificationType.MISSION_REVIEW_COMPLETE,
          'Mission Entry Review Completed',
          `${tutor.name} has completed the review of your mission entry: "${entry.mission.title}" with score ${correctionWithScoreDto.score}/${entry.mission.score}`,
          {
            relatedEntityId: entry.missionId,
            relatedEntityType: RelatedEntityType.MISSION_DIARY_ENTRY,
            htmlContent: htmlContent,
            webLink: webLink,
            deepLink: deepLink,
            sendEmail: true,
            sendPush: true,
            sendInApp: true,
            sendRealtime: false,
          },
          {
            submissionId: entryId,
            entryType: 'mission_diary_entry',
            priority: 1, // High priority for review completion
          }
        ).catch(error => {
          this.logger.error(`Failed to send async notification: ${error?.message || 'Unknown error'}`, error?.stack);
        });
      } catch (error) {
        this.logger.warn(`Failed to prepare notification: ${error.message}`);
      }

      return await this.mapToResponseDto(updatedEntry, entry.mission, entry.student, tutor);
    } catch (error) {
      this.logger.error(`Error adding correction with score: ${error.message}`, error.stack);
      throw error;
    }
  }

  // REMOVED: confirmMissionEntry method - confirm stage removed from lifecycle
  // Review submission is now the final state, no additional confirmation needed
  /*
  async confirmMissionEntry(entryId: string, tutorId: string): Promise<MissionDiaryEntryResponseDto> {
    try {
      // Verify the tutor exists
      const tutor = await this.userRepository.findOne({ where: { id: tutorId } });
      if (!tutor) {
        throw new NotFoundException(`Tutor with ID ${tutorId} not found`);
      }

      if (tutor.type !== UserType.TUTOR) {
        throw new BadRequestException('Only tutors can confirm mission entries');
      }

      // Find the entry
      const entry = await this.missionDiaryEntryRepository.findOne({
        where: { id: entryId },
        relations: ['mission', 'mission.tutor', 'mission.category', 'student', 'feedbacks', 'feedbacks.tutor']
      });

      if (!entry) {
        throw new NotFoundException(`Mission entry with ID ${entryId} not found`);
      }

      // Check if the entry is in a state that can be confirmed
      // Updated logic: Allow confirmation based on status and resubmission context
      const canConfirm = entry.status === MissionEntryStatus.REVIEWED ||
                        entry.status === MissionEntryStatus.REVIEWED ||
                        // Allow confirmation of resubmissions after confirmation that have been reviewed/scored
                        (entry.resubmissionType === 'after_review' &&
                         entry.gainedScore !== null && entry.gainedScore !== undefined);

      if (!canConfirm) {
        throw new BadRequestException('Can only confirm entries that have been reviewed or resubmissions after review with scores assigned');
      }

      // Check if a score has been assigned
      if (entry.gainedScore === null || entry.gainedScore === undefined) {
        throw new BadRequestException('Cannot confirm an entry without a score');
      }

      // Update the entry status to confirmed
      entry.status = MissionEntryStatus.REVIEWED;

      // NEW REQUIREMENT: Ensure subsequent submissions are still enabled after confirmation
      entry.canSubmitNewVersion = true;

      // Save the updated entry
      const updatedEntry = await this.missionDiaryEntryRepository.save(entry);
      this.logger.log(`Confirmed mission entry ${entryId} by tutor ${tutorId}`);

      // Send notification to student about confirmation
      try {
        const webLink = this.deeplinkService.getWebLink(DeeplinkType.MISSION_DIARY_ENTRY, { id: entryId });
        const deepLink = this.deeplinkService.getDeepLink(DeeplinkType.MISSION_DIARY_ENTRY, { id: entryId });
        const entryButton = this.deeplinkService.getLinkHtml(DeeplinkType.MISSION_DIARY_ENTRY, {
          id: entryId,
          linkText: 'View Confirmed Entry',
          buttonStyle: true
        });

        const htmlContent = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
            <div style="text-align: center; margin-bottom: 20px;">
              <h2 style="color: #333;">Mission Entry Confirmed</h2>
            </div>
            <div style="margin-bottom: 20px;">
              <p>Hello ${entry.student?.name || 'there'},</p>
              <p>${tutor.name} has confirmed your mission entry: "${entry.mission.title}"</p>
              <p><strong>Final Score:</strong> ${entry.gainedScore}/${entry.mission.score}</p>
              <p>Your entry has been successfully completed and confirmed.</p>
              <div style="text-align: center; margin: 25px 0;">
                ${entryButton}
              </div>
            </div>
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
              <p>This is an automated message from the HEC system.</p>
              <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
            </div>
          </div>
        `;

        await this.asyncNotificationHelper.notifyAsync(
          entry.studentId,
          NotificationType.MISSION_CONFIRMED,
          'Mission Entry Confirmed',
          `${tutor.name} has confirmed your mission entry: "${entry.mission.title}"`,
          {
            relatedEntityId: entryId,
            relatedEntityType: RelatedEntityType.MISSION_DIARY_ENTRY,
            htmlContent: htmlContent,
            webLink: webLink,
            deepLink: deepLink,
            sendEmail: true,
            sendPush: true,
            sendInApp: true,
            sendMobile: true,
            sendSms: false,
            sendRealtime: false
          }
        );
      } catch (error) {
        this.logger.warn(`Failed to send confirmation notification: ${error.message}`);
      }

      return await this.mapToResponseDto(updatedEntry, entry.mission, entry.student, tutor);
    } catch (error) {
      this.logger.error(`Error confirming mission entry: ${error.message}`, error.stack);
      throw error;
    }
  }
  */

  /**
   * Get mission entry version history
   */
  async getMissionEntryHistory(entryId: string, userId: string): Promise<MissionDiaryEntryHistoryResponseDto> {
    return this.missionDiaryEntryHistoryService.getVersionHistory(entryId, userId);
  }

  /**
   * Get a specific mission entry version
   */
  async getMissionEntryVersion(versionId: string, userId: string): Promise<MissionDiaryEntryVersionDto> {
    return this.missionDiaryEntryHistoryService.getVersion(versionId, userId);
  }

  /**
   * Restore a previous version as the current version
   */
  async restoreMissionEntryVersion(entryId: string, versionId: string, userId: string): Promise<MissionDiaryEntryResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Get the version to restore
      const version = await this.missionDiaryEntryHistoryService.getVersion(versionId, userId);

      // Get the current entry
      const entry = await this.missionDiaryEntryRepository.findOne({
        where: { id: entryId, studentId: userId },
        relations: ['mission', 'mission.category'],
      });

      if (!entry) {
        throw new NotFoundException(`Mission entry with ID ${entryId} not found`);
      }

      // Store old content for version tracking
      const oldContent = entry.content;

      // Update entry with restored content
      entry.content = version.content;
      entry.wordCount = this.calculateWordCount(version.content);
      entry.progress = this.calculateProgress(entry.wordCount, entry.mission?.targetWordCount || 50);
      entry.updatedAt = new Date();

      // Save the updated entry
      await queryRunner.manager.save(entry);

      // Create a new version for the restore operation
      await this.missionDiaryEntryHistoryService.createVersionFromUpdate(entryId, { content: oldContent }, { content: version.content }, userId);

      await queryRunner.commitTransaction();

      // Return the updated entry
      return this.mapToResponseDto(entry, entry.mission);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Error restoring mission entry version ${versionId} for entry ${entryId}: ${error.message}`, error.stack);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Calculate the word count of a text
   * @param content The text content
   * @returns The word count
   */
  private calculateWordCount(content: string): number {
    // Handle empty or null content
    if (!content) return 0;

    // Normalize content: trim, replace multiple spaces with single space
    const normalizedContent = content.trim().replace(/\s+/g, ' ');

    // Handle case where content is just whitespace
    if (normalizedContent === '') return 0;

    // Split by spaces and count words
    return normalizedContent.split(' ').length;
  }

  /**
   * Calculate the progress percentage based on word count
   * @param wordCount The current word count
   * @param targetWordCount The target word count
   * @returns The progress percentage (0-100)
   */
  calculateProgress(wordCount: number, targetWordCount: number): number {
    // Handle invalid inputs
    if (targetWordCount <= 0) return 0;
    if (wordCount < 0) return 0;

    // Calculate progress percentage
    const progress = (wordCount / targetWordCount) * 100;

    // Ensure progress is between 0 and 100
    return Math.max(0, Math.min(progress, 100));
  }

  /**
   * Map a MissionDiaryEntry entity to a MissionDiaryEntryResponseDto
   * @param entry The entry entity
   * @param mission The mission entity (optional)
   * @param student The student user entity (optional)
   * @param reviewer The reviewer user entity (optional)
   * @returns The mapped DTO
   */
  private async mapToResponseDto(entry: MissionDiaryEntry, mission?: DiaryMission, student?: User, reviewer?: User): Promise<MissionDiaryEntryResponseDto> {
    // Map feedbacks if available
    const feedbacks =
      entry.feedbacks?.map((feedback) => ({
        id: feedback.id,
        missionEntryId: feedback.missionEntryId,
        tutorId: feedback.tutorId,
        tutorName: feedback.tutor?.name,
        feedback: feedback.feedback,
        rating: feedback.rating,
        createdAt: feedback.createdAt,
      })) || [];



    // Map mission if available
    const missionDto = mission
      ? {
          id: mission.id,
          title: mission.title,
          description: mission.description,
          category: mission.category ? {
            id: mission.category.id,
            name: mission.category.name,
            description: mission.category.description,
            color: mission.category.color,
            isActive: mission.category.isActive,
            sortOrder: mission.category.sortOrder,
            createdAt: mission.category.createdAt,
            updatedAt: mission.category.updatedAt,
          } : null,
          targetWordCount: mission.targetWordCount,
          targetMaxWordCount: mission.targetMaxWordCount,
          publishDate: mission.publishDate,
          expiryDate: mission.expiryDate,
          adminId: mission.adminId,
          createdBy: mission.adminId,
          isActive: mission.isActive,
          score: mission.score,
          createdAt: mission.createdAt,
          updatedAt: mission.updatedAt,
        }
      : undefined;

    // Map skin - handle both global and student skins with fallback logic
    let skinDto = undefined;
    let effectiveSkinId = entry.skinId;

    if (entry.skin) {
      // Entry has a global skin relation loaded
      const skinUrl = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.DIARY_SKIN, entry.skin.id);
      skinDto = {
        id: entry.skin.id,
        name: entry.skin.name,
        description: entry.skin.description,
        previewImagePath: skinUrl,
        isActive: entry.skin.isActive,
        isGlobal: entry.skin.isGlobal,
        createdById: entry.skin.createdById,
        templateContent: entry.skin.templateContent,
        isUsedIn: true,
      };
    } else if (entry.skinId) {
      // Entry has skinId but skin relation not loaded - load it manually
      try {
        const skinData = await this.diarySkinService.getDiarySkinById(entry.skinId);
        if (skinData) {
          skinDto = skinData;
        }
      } catch (error) {
        this.logger.warn(`Failed to load skin ${entry.skinId}: ${error.message}`);
      }
    } else if (entry.studentId) {
      // No specific skin assigned, try to get user's default diary skin
      try {
        const defaultSkin = await this.diarySkinService.getDefaultDiarySkin(entry.studentId);
        if (defaultSkin) {
          skinDto = defaultSkin;
          effectiveSkinId = defaultSkin.id;
        }
      } catch (error) {
        this.logger.warn(`Failed to load default diary skin for student ${entry.studentId}: ${error.message}`);
      }
    }

    // Provide placeholder if no skin found
    if (!skinDto) {
      skinDto = {
        id: '',
        name: 'No Skin',
        description: 'No skin applied',
        previewImagePath: null,
        isActive: true,
        isGlobal: true,
        createdById: null,
        templateContent: '',
        isUsedIn: false,
      };
    }

    return {
      id: entry.id,
      missionId: entry.missionId,
      mission: missionDto,
      studentId: entry.studentId,
      studentName: student?.name,
      content: entry.content,
      wordCount: entry.wordCount,
      progress: entry.progress,
      status: entry.status,
      // Include feedback and correction information
      feedbacks: feedbacks,
      correction: entry.correction
        ? {
            id: entry.id, // Use mission entry ID as correction ID since there's no separate correction entity
            missionEntryId: entry.id,
            tutorId: entry.correctionProvidedBy,
            tutorName: entry.correctionProvider?.name,
            correctionText: entry.correction,
            score: entry.gainedScore,
            comments: null, // Mission diary doesn't have separate comments field
            createdAt: entry.correctionProvidedAt,
            updatedAt: entry.correctionProvidedAt, // Use same timestamp for both created and updated
          }
        : undefined,
      skinId: effectiveSkinId, // Use effective skin ID (entry's skin or default skin)
      skin: skinDto,
      gainedScore: entry.gainedScore,
      reviewedBy: entry.reviewedBy || reviewer?.id,
      reviewerName: reviewer?.name,
      reviewedAt: entry.reviewedAt,
      createdAt: entry.createdAt,
      updatedAt: entry.updatedAt,
      // NEW REQUIREMENT: Include original reviewed version
      originalReviewedVersion: entry.originalReviewedVersion
        ? {
            content: entry.originalReviewedVersion.content,
            versionNumber: entry.originalReviewedVersion.versionNumber,
            createdAt: entry.originalReviewedVersion.createdAt,
          }
        : undefined,
      totalEditHistory: await this.calculateEditHistoryCount(entry.id),

      // NEW: Submission tracking fields
      submittedVersionCount: entry.submittedVersionCount || 0,
      canSubmitNewVersion: entry.canSubmitNewVersion ?? true,
      lastSubmittedAt: entry.lastSubmittedAt,
      lastReviewedAt: entry.lastReviewedAt,

      // NEW: Resubmission tracking fields
      isResubmission: entry.isResubmission || false,
      resubmissionType: entry.resubmissionType,
      previousReviewCount: entry.previousReviewCount || 0,
      previousConfirmationCount: entry.previousConfirmationCount || 0,
    };
  }

  /**
   * Send notification about a mission diary entry
   * @param studentId Student ID
   * @param entryId Entry ID
   * @param entry The mission diary entry
   * @param tutor The tutor user
   * @param type The type of notification (submission, update, etc.)
   * @param title The notification title
   * @param description The notification description
   * @param linkText The text to show on the action button
   * @returns Promise<void>
   */
  private async sendMissionEntryNotification(
    studentId: string,
    entryId: string,
    entry: MissionDiaryEntry,
    tutor: User,
    notificationType: NotificationType,
    title: string,
    description: string,
    linkText: string,
  ): Promise<void> {
    try {
      // Generate deeplinks for the mission entry
      const webLink = this.deeplinkService.getWebLink(DeeplinkType.MISSION_DIARY_ENTRY, { id: entryId });
      const deepLink = this.deeplinkService.getDeepLink(DeeplinkType.MISSION_DIARY_ENTRY, { id: entryId });
      const entryButton = this.deeplinkService.getLinkHtml(DeeplinkType.MISSION_DIARY_ENTRY, {
        id: entryId,
        linkText,
        buttonStyle: true,
      });

      // Create HTML content for email notification
      const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
          <div style="text-align: center; margin-bottom: 20px;">
            <h2 style="color: #333;">${title}</h2>
          </div>
          <div style="margin-bottom: 20px;">
            <p>Hello ${tutor.name || 'there'},</p>
            <p>${description}</p>
            <p><strong>Word Count:</strong> ${entry.wordCount} (${entry.progress.toFixed(0)}% of target)</p>
            <p>Click the button below to view the entry:</p>
            <div style="text-align: center; margin: 25px 0;">
              ${entryButton}
            </div>
          </div>
          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
            <p>This is an automated message from the HEC system.</p>
            <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
          </div>
        </div>
      `;

      await this.asyncNotificationHelper.notifyAsync(tutor.id, notificationType, title, description, {
        relatedEntityId: entryId,
        relatedEntityType: RelatedEntityType.MISSION_DIARY_ENTRY,
        htmlContent: htmlContent,
        webLink: webLink,
        deepLink: deepLink,
        sendEmail: true,
        sendPush: true,
        sendInApp: true,
        sendRealtime: false,
      });
    } catch (error) {
      this.logger.warn(`Failed to send notification: ${error.message}`);
    }
  }

  /**
   * Check if tutor should be notified for mission submission
   * NEW REQUIREMENT: Only notify on first qualifying submission
   */
  private async shouldNotifyTutorForMissionSubmission(entry: MissionDiaryEntry, userId: string): Promise<boolean> {
    try {
      // Check if already notified
      if (entry.firstSubmissionNotified) {
        this.logger.log(`Mission entry ${entry.id} already notified, skipping notification`);
        return false;
      }

      // Check word count meets target (minimum requirement)
      const wordCount = this.calculateWordCount(entry.content);
      const targetWordCount = entry.mission?.targetWordCount || 50;

      if (wordCount < targetWordCount) {
        this.logger.log(`Mission entry ${entry.id} word count ${wordCount} below target ${targetWordCount}, skipping notification`);
        return false;
      }

      this.logger.log(`Mission entry ${entry.id} qualifies for tutor notification - first submission with ${wordCount} words (target: ${targetWordCount})`);
      return true;
    } catch (error) {
      this.logger.error(`Error checking notification eligibility for mission entry ${entry.id}: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Calculate edit history count from actual history table records
   */
  private async calculateEditHistoryCount(entryId: string): Promise<number> {
    try {
      const count = await this.missionDiaryEntryHistoryRepository.count({
        where: { missionEntryId: entryId },
      });
      return count;
    } catch (error) {
      this.logger.warn(`Failed to calculate edit history count for mission entry ${entryId}: ${error.message}`);
      return 0;
    }
  }

  /**
   * Get all feedbacks for a mission diary entry
   * @param entryId The ID of the mission diary entry
   * @param tutorId The ID of the tutor requesting the feedbacks
   * @returns Array of feedback response DTOs
   */
  async getFeedbacks(entryId: string, tutorId: string): Promise<MissionFeedbackResponseDto[]> {
    try {
      // Verify the tutor exists and has tutor role
      const tutor = await this.userRepository.findOne({
        where: { id: tutorId },
        relations: ['userRoles', 'userRoles.role'],
      });

      if (!tutor) {
        throw new NotFoundException(`Tutor with ID ${tutorId} not found`);
      }

      const isTutor = tutor.userRoles.some((userRole) => userRole.role.name === 'tutor');
      if (!isTutor) {
        throw new ForbiddenException('Only tutors can view feedbacks on mission diary entries');
      }

      // Find the mission diary entry
      const entry = await this.missionDiaryEntryRepository.findOne({
        where: { id: entryId },
        relations: ['mission', 'mission.category', 'student', 'feedbacks', 'feedbacks.tutor'],
      });

      if (!entry) {
        throw new NotFoundException(`Mission diary entry with ID ${entryId} not found`);
      }

      // Check if the tutor has access to this student
      const studentId = entry.studentId;
      const hasAccess = await this.tutorMatchingService.hasTutorStudentAccess(tutorId, studentId);
      if (!hasAccess) {
        throw new ForbiddenException("You do not have access to this student's mission diary entries");
      }

      // Map feedbacks to response DTOs
      const feedbacks =
        entry.feedbacks?.map((feedback) => ({
          id: feedback.id,
          missionEntryId: feedback.missionEntryId,
          tutorId: feedback.tutorId,
          tutorName: feedback.tutor?.name || 'Unknown Tutor',
          feedback: feedback.feedback,
          rating: feedback.rating,
          createdAt: feedback.createdAt,
          updatedAt: feedback.updatedAt,
        })) || [];

      this.logger.log(`Retrieved ${feedbacks.length} feedbacks for mission diary entry ${entryId}`);
      return feedbacks;
    } catch (error) {
      this.logger.error(`Error getting feedbacks for mission diary entry ${entryId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Send notification about a mission diary entry asynchronously (non-blocking)
   * @param _studentId Student ID (unused but kept for interface consistency)
   * @param entryId Entry ID
   * @param entry The mission diary entry
   * @param tutor The tutor user
   * @param type The type of notification (submission, update, etc.)
   * @param title The notification title
   * @param description The notification description
   * @param linkText The text to show on the action button
   * @returns Promise<void>
   */
  private sendMissionEntryNotificationAsync(
    _studentId: string,
    entryId: string,
    entry: MissionDiaryEntry,
    tutor: User,
    notificationType: NotificationType,
    title: string,
    description: string,
    linkText: string,
  ): void {
    // Fire and forget - don't await this to avoid blocking submission
    this.sendMissionEntryNotificationAsyncInternal(
      entryId,
      entry,
      tutor,
      notificationType,
      title,
      description,
      linkText,
    ).catch(error => {
      this.logger.error(`Failed to send async mission entry notification: ${error.message}`, error.stack);
    });
  }

  /**
   * Internal async notification method
   */
  private async sendMissionEntryNotificationAsyncInternal(
    entryId: string,
    entry: MissionDiaryEntry,
    tutor: User,
    notificationType: NotificationType,
    title: string,
    description: string,
    linkText: string,
  ): Promise<void> {
    try {
      // Generate deeplinks for the mission entry
      const webLink = this.deeplinkService.getWebLink(DeeplinkType.DIARY_ENTRY, {
        id: entryId,
      });

      const deepLink = this.deeplinkService.getDeepLink(DeeplinkType.DIARY_ENTRY, {
        id: entryId,
      });

      // Create HTML content for the notification
      const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
          <div style="text-align: center; margin-bottom: 20px;">
            <h2 style="color: #333;">${title}</h2>
          </div>
          <div style="margin-bottom: 20px;">
            <p>Hello ${tutor.name},</p>
            <p>${description}</p>
            <p><strong>Mission:</strong> ${entry.mission.title}</p>
            <p><strong>Student:</strong> ${entry.student.name}</p>
            <p><strong>Word Count:</strong> ${entry.wordCount || 0} words</p>
          </div>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${webLink}" style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">${linkText}</a>
          </div>
          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
            <p>This is an automated message from the HEC system.</p>
            <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
          </div>
        </div>
      `;

      // Send notification asynchronously
      await this.asyncNotificationHelper.notifyAsync(
        tutor.id,
        notificationType,
        title,
        description,
        {
          relatedEntityId: entryId,
          relatedEntityType: RelatedEntityType.MISSION_DIARY_ENTRY,
          htmlContent: htmlContent,
          webLink: webLink,
          deepLink: deepLink,
          sendEmail: true,
          sendPush: true,
          sendInApp: true,
          sendRealtime: false,
        },
      );

      this.logger.log(`Queued mission entry notification for tutor ${tutor.id}: ${title}`);
    } catch (error) {
      this.logger.error(`Error sending async mission entry notification: ${error.message}`, error.stack);
      throw error;
    }
  }
}
