import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';
import LoggerService from './logger.service';

@Injectable()
export class FileUtilService {
  private readonly uploadDir: string;
  private readonly maxFileSize: number; // in bytes
  private readonly allowedMimeTypes: string[];

  constructor(
    private readonly configService: ConfigService,
    private readonly logger: LoggerService,
  ) {
    this.uploadDir = this.configService.get<string>('UPLOAD_DIR') || 'uploads';
    this.maxFileSize = parseInt(this.configService.get<string>('MAX_FILE_SIZE') || '5242880', 10); // 5MB default
    this.allowedMimeTypes = (this.configService.get<string>('ALLOWED_MIME_TYPES') || 'image/jpeg,image/png,image/gif,image/svg+xml').split(',');

    // Create upload directory if it doesn't exist
    this.ensureUploadDirExists();
  }

  private ensureUploadDirExists(): void {
    if (!fs.existsSync(this.uploadDir)) {
      fs.mkdirSync(this.uploadDir, { recursive: true });
    }

    // Create subdirectories for different file types
    const subdirs = ['profile-pictures', 'shop-items', 'diary-skins', 'student-diary-skins'];

    for (const subdir of subdirs) {
      const fullPath = path.join(this.uploadDir, subdir);
      if (!fs.existsSync(fullPath)) {
        fs.mkdirSync(fullPath, { recursive: true });
      }
    }
  }

  /**
   * Validate a file against size and type constraints
   * @param file The file to validate
   * @param options Optional validation options
   */
  validateFile(file: any, options?: { maxSizeInMB?: number; allowedMimeTypes?: string[] }): void {
    // Check if file exists
    if (!file) {
      throw new Error('No file provided');
    }

    // Check if file has buffer
    if (!file.buffer) {
      throw new Error('File buffer is missing');
    }

    const maxSize = options?.maxSizeInMB ? options.maxSizeInMB * 1024 * 1024 : this.maxFileSize;
    const allowedTypes = options?.allowedMimeTypes || this.allowedMimeTypes;

    // Check file size
    if (file.size && file.size > maxSize) {
      throw new Error(`File size exceeds the limit of ${maxSize / 1024 / 1024}MB`);
    }

    // Check file type if mimetype is provided
    if (file.mimetype && allowedTypes.length > 0 && !allowedTypes.includes(file.mimetype)) {
      throw new Error(`File type ${file.mimetype} is not allowed. Allowed types: ${allowedTypes.join(', ')}`);
    }

    // Log validation success
    this.logger.info(`File validated successfully: ${file.originalname || 'unnamed file'}`);
  }

  /**
   * Write a file to disk
   * @param file The file to write
   * @param subDirectory The subdirectory within the upload directory
   * @param filename The filename to use
   * @returns The relative path to the file
   */
  writeFileToDisk(file: any, subDirectory: string, filename: string): string {
    try {
      // Log input parameters
      this.logger.info(`Writing file to disk - subDirectory: ${subDirectory}, filename: ${filename}`);
      this.logger.info(`Upload directory: ${this.uploadDir}`);

      // Define the file path - handle absolute paths correctly
      let targetDir;
      if (this.uploadDir.startsWith('/')) {
        // If uploadDir is already an absolute path (in Docker)
        targetDir = path.join(this.uploadDir, subDirectory);
      } else {
        // If uploadDir is a relative path (in development)
        targetDir = path.join(process.cwd(), this.uploadDir, subDirectory);
      }

      this.logger.info(`Target directory: ${targetDir}`);

      // Create directory if it doesn't exist
      if (!fs.existsSync(targetDir)) {
        this.logger.info(`Creating directory: ${targetDir}`);
        fs.mkdirSync(targetDir, { recursive: true });
      }

      const filePath = path.join(targetDir, filename);
      this.logger.info(`Full file path: ${filePath}`);

      // Check if file has buffer
      if (!file.buffer) {
        throw new Error('File buffer is missing');
      }

      // Write the file to disk
      fs.writeFileSync(filePath, file.buffer);

      // Log success
      this.logger.info(`File written to disk: ${filePath}`);

      // Always use forward slashes for the relative path to ensure cross-platform compatibility
      const relativePath = path.join(subDirectory, filename).replace(/\\/g, '/');
      this.logger.info(`Returning relative path: ${relativePath}`);

      return relativePath;
    } catch (error) {
      this.logger.error(`Failed to write file to disk: ${error.message}`);
      if (error.stack) {
        this.logger.error(`Stack trace: ${error.stack}`);
      }
      throw new Error(`Failed to write file to disk: ${error.message}`);
    }
  }

  /**
   * Generate a unique filename
   * @param originalFilename Original filename
   * @param prefix Optional prefix for the filename
   * @returns A unique filename
   */
  generateUniqueFilename(originalFilename: string, prefix?: string): string {
    const fileExtension = path.extname(originalFilename).toLowerCase();
    const timestamp = Date.now();
    const randomString = crypto.randomBytes(8).toString('hex');
    return `${prefix ? prefix + '-' : ''}${timestamp}-${randomString}${fileExtension}`;
  }

  /**
   * Delete a file from disk
   * @param relativePath Relative path to the file
   */
  deleteFile(relativePath: string): void {
    try {
      // Normalize the path to handle both Windows and Linux path separators
      let normalizedPath = relativePath;
      if (normalizedPath && normalizedPath.includes('\\')) {
        normalizedPath = normalizedPath.replace(/\\/g, '/');
      }

      // If uploadDir is already an absolute path (starts with / in Linux), don't prepend process.cwd()
      let filePath;
      if (this.uploadDir.startsWith('/')) {
        filePath = path.join(this.uploadDir, normalizedPath);
      } else {
        filePath = path.join(process.cwd(), this.uploadDir, normalizedPath);
      }

      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        this.logger.info(`File deleted: ${normalizedPath}`);
      }
    } catch (error) {
      this.logger.error(`Failed to delete file ${relativePath}: ${error.message}`);
    }
  }

  /**
   * Get the absolute file path
   * @param relativePath Relative path to the file
   * @returns The absolute file path
   */
  getAbsoluteFilePath(relativePath: string): string {
    // Normalize the path to handle both Windows and Linux path separators
    let normalizedPath = relativePath;
    if (normalizedPath && normalizedPath.includes('\\')) {
      normalizedPath = normalizedPath.replace(/\\/g, '/');
    }

    // If uploadDir is already an absolute path (starts with / in Linux), don't prepend process.cwd()
    let absolutePath;
    if (this.uploadDir.startsWith('/')) {
      absolutePath = path.join(this.uploadDir, normalizedPath || '');
    } else {
      absolutePath = path.join(process.cwd(), this.uploadDir, normalizedPath || '');
    }

    // Log the absolute path for debugging
    this.logger.info(`Generated absolute path: ${absolutePath} for relative path: ${relativePath}`);

    return absolutePath;
  }

  /**
   * Get the base URL for API endpoints
   * @returns The base URL
   */
  getBaseUrl(): string {
    return this.configService.get<string>('API_URL') || 'http://localhost:3012';
  }

  /**
   * Get the upload directory path
   * @returns The upload directory path
   */
  getUploadDir(): string {
    return this.uploadDir;
  }
}
