import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { WaterfallAdminService } from './waterfall-admin.service';
import { WaterfallSet } from '../../../database/entities/waterfall-set.entity';
import { WaterfallQuestion } from '../../../database/entities/waterfall-question.entity';
import { WaterfallTrueFalseQuestion } from '../../../database/entities/waterfall-true-false-question.entity';
import { WaterfallMultipleChoiceQuestion } from '../../../database/entities/waterfall-multiple-choice-question.entity';
import { WaterfallParticipation } from '../../../database/entities/waterfall-participation.entity';
import { WaterfallAnswer } from '../../../database/entities/waterfall-answer.entity';
import { CreateWaterfallQuestionsDto } from '../../../database/models/waterfall/waterfall-question.dto';

describe('WaterfallAdminService', () => {
  let service: WaterfallAdminService;

  const mockRepository = {
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
    find: jest.fn(),
    remove: jest.fn(),
  };

  const mockDataSource = {
    createQueryRunner: jest.fn(() => ({
      connect: jest.fn(),
      startTransaction: jest.fn(),
      commitTransaction: jest.fn(),
      rollbackTransaction: jest.fn(),
      release: jest.fn(),
      manager: {
        save: jest.fn(),
        remove: jest.fn(),
      },
    })),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WaterfallAdminService,
        {
          provide: getRepositoryToken(WaterfallSet),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(WaterfallQuestion),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(WaterfallTrueFalseQuestion),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(WaterfallMultipleChoiceQuestion),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(WaterfallParticipation),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(WaterfallAnswer),
          useValue: mockRepository,
        },
        {
          provide: DataSource,
          useValue: mockDataSource,
        },
      ],
    }).compile();

    service = module.get<WaterfallAdminService>(WaterfallAdminService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('validateQuestion', () => {
    it('should validate question with correct answers separate from options', () => {
      const question: CreateWaterfallQuestionsDto = {
        type: 'fill_in_blank',
        question_text: '<p>The cat <span class="blank-highlight">___</span> on the mat.</p>',
        question_text_plain: 'The cat [[gap]] on the mat.',
        correct_answers: ['sat'],
        options: ['sits', 'standing', 'lying'], // correct answer "sat" is NOT in options
        time_limit_in_seconds: 30,
        level: 2,
      };

      // Access the private method using bracket notation for testing
      const errors = (service as any).validateQuestion(question, 0);
      
      // Should pass validation - no errors expected
      expect(errors).toEqual([]);
    });

    it('should validate question with correct answers included in options', () => {
      const question: CreateWaterfallQuestionsDto = {
        type: 'fill_in_blank',
        question_text: '<p>The cat <span class="blank-highlight">___</span> on the mat.</p>',
        question_text_plain: 'The cat [[gap]] on the mat.',
        correct_answers: ['sat'],
        options: ['sits', 'sat', 'standing', 'lying'], // correct answer "sat" IS in options
        time_limit_in_seconds: 30,
        level: 2,
      };

      // Access the private method using bracket notation for testing
      const errors = (service as any).validateQuestion(question, 0);
      
      // Should also pass validation - no errors expected
      expect(errors).toEqual([]);
    });

    it('should fail validation when no gap markers are present', () => {
      const question: CreateWaterfallQuestionsDto = {
        type: 'fill_in_blank',
        question_text: '<p>The cat sat on the mat.</p>',
        question_text_plain: 'The cat sat on the mat.', // No [[gap]] markers
        correct_answers: ['sat'],
        options: ['sits', 'standing', 'lying'],
        time_limit_in_seconds: 30,
        level: 2,
      };

      const errors = (service as any).validateQuestion(question, 0);
      
      expect(errors).toContain('Question must contain at least one [[gap]] marker');
    });

    it('should fail validation when gap count does not match correct answers count', () => {
      const question: CreateWaterfallQuestionsDto = {
        type: 'fill_in_blank',
        question_text: '<p>The cat <span class="blank-highlight">___</span> <span class="blank-highlight">___</span> on the mat.</p>',
        question_text_plain: 'The cat [[gap]] [[gap]] on the mat.', // 2 gaps
        correct_answers: ['sat'], // Only 1 correct answer
        options: ['sits', 'standing', 'lying'],
        time_limit_in_seconds: 30,
        level: 2,
      };

      const errors = (service as any).validateQuestion(question, 0);
      
      expect(errors).toContain('Question has 2 [[gap]] marker(s) but 1 correct answer(s)');
    });

    it('should validate multi-gap question correctly', () => {
      const question: CreateWaterfallQuestionsDto = {
        type: 'fill_in_blank',
        question_text: '<p>I <span class="blank-highlight">___</span> to <span class="blank-highlight">___</span> to the store.</p>',
        question_text_plain: 'I [[gap]] to [[gap]] to the store.',
        correct_answers: ['want', 'go'],
        options: ['fly', 'walk', 'run'], // correct answers are NOT in options
        time_limit_in_seconds: 45,
        level: 3,
      };

      const errors = (service as any).validateQuestion(question, 0);
      
      // Should pass validation - no errors expected
      expect(errors).toEqual([]);
    });
  });
});
