import { MigrationInterface, QueryRunner } from 'typeorm';

/**
 * Migration to remove foreign key constraint for default novel skin
 * Novel module uses explicit inclusion logic like diary module for skin access validation
 * This makes novel consistent with diary's approach while maintaining diary's foreign key
 */
export class RemoveNovelSkinForeignKey1756900000000 implements MigrationInterface {
  name = 'RemoveNovelSkinForeignKey1756900000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if the foreign key constraint exists first
    const foreignKeys = await queryRunner.query(`
      SELECT constraint_name 
      FROM information_schema.table_constraints 
      WHERE table_name = 'user' 
      AND constraint_type = 'FOREIGN KEY' 
      AND constraint_name LIKE '%novel_skin%'
    `);

    // Drop the foreign key constraint for default_novel_skin_id if it exists
    if (foreignKeys.length > 0) {
      for (const fk of foreignKeys) {
        await queryRunner.query(`
          ALTER TABLE "user" 
          DROP CONSTRAINT "${fk.constraint_name}"
        `);
      }
    }

    // Also try the expected constraint name
    await queryRunner.query(`
      ALTER TABLE "user" 
      DROP CONSTRAINT IF EXISTS "FK_user_default_novel_skin"
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Restore the foreign key constraint
    await queryRunner.query(`
      ALTER TABLE "user" 
      ADD CONSTRAINT "FK_user_default_novel_skin" 
      FOREIGN KEY ("default_novel_skin_id") 
      REFERENCES "diary_skin"("id") 
      ON DELETE SET NULL
    `);
  }
}