import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveWaterfallParticipationForeignKey1756400000000 implements MigrationInterface {
  name = 'RemoveWaterfallParticipationForeignKey1756400000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "waterfall_participation" 
      DROP CONSTRAINT IF EXISTS "FK_b54f4c72fadee4feb2ca98da56d"
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "waterfall_participation" 
      ADD CONSTRAINT "FK_b54f4c72fadee4feb2ca98da56d" 
      FOREIGN KEY ("setId") REFERENCES "waterfall_set"("id") ON DELETE CASCADE
    `);
  }
}