import { Repository } from 'typeorm';
import { JwtService } from '@nestjs/jwt';
import { faker } from '@faker-js/faker';
import { AuthTestUtils } from './auth-test.utils';

/**
 * Factory for creating consistent, secure mock implementations
 */
export class MockFactory {
  /**
   * Creates a mock repository with common methods
   */
  static createMockRepository<T = any>(): jest.Mocked<Partial<Repository<T>>> {
    return {
      find: jest.fn().mockResolvedValue([]),
      findOne: jest.fn().mockResolvedValue(null),
      findOneBy: jest.fn().mockResolvedValue(null),
      create: jest.fn().mockImplementation((data) => ({ ...data, id: faker.string.uuid() })),
      save: jest.fn().mockImplementation((entity) => Promise.resolve(entity)),
      update: jest.fn().mockResolvedValue({ affected: 1 }),
      delete: jest.fn().mockResolvedValue({ affected: 1 }),
      remove: jest.fn().mockImplementation((entity) => Promise.resolve(entity)),
      createQueryBuilder: jest.fn().mockReturnValue({
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        innerJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([]),
        getManyAndCount: jest.fn().mockResolvedValue([[], 0]),
        getOne: jest.fn().mockResolvedValue(null),
        getRawOne: jest.fn().mockResolvedValue({}),
        getRawMany: jest.fn().mockResolvedValue([]),
      }),
    };
  }

  /**
   * Creates a secure JWT service mock
   */
  static createMockJwtService(): jest.Mocked<JwtService> {
    const tokens = AuthTestUtils.generateTestTokens();
    
    return {
      sign: jest.fn().mockImplementation((payload: any) => {
        // Validate payload doesn't contain sensitive data
        const payloadStr = JSON.stringify(payload);
        if (payloadStr.toLowerCase().includes('password')) {
          throw new Error('Sensitive data detected in JWT payload');
        }
        return tokens.accessToken;
      }),
      
      verify: jest.fn().mockImplementation((token: string) => {
        if (!token.startsWith('eyJ')) {
          throw new Error('Invalid token format');
        }
        return AuthTestUtils.createTestJwtPayload();
      }),
      
      decode: jest.fn().mockImplementation((token: string) => {
        return {
          exp: Math.floor(Date.now() / 1000) + 3600,
          iat: Math.floor(Date.now() / 1000),
        };
      }),
      
      signAsync: jest.fn().mockImplementation(async (payload: any) => {
        return tokens.accessToken;
      }),
      
      verifyAsync: jest.fn().mockImplementation(async (token: string) => {
        return AuthTestUtils.createTestJwtPayload();
      }),
    } as any;
  }

  /**
   * Creates a secure email service mock
   */
  static createMockEmailService() {
    return {
      sendEmail: jest.fn().mockImplementation(async (to: string, subject: string, content: string) => {
        // Validate email format
        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;
        if (!emailRegex.test(to)) {
          throw new Error('Invalid email format');
        }
        
        // Check for malicious content
        if (content.includes('<script>') || content.includes('javascript:')) {
          throw new Error('Malicious content detected');
        }
        
        return { messageId: `msg_${faker.string.alphanumeric(16)}` };
      }),
      
      sendVerificationLink: jest.fn().mockResolvedValue(true),
      sendPasswordResetLink: jest.fn().mockResolvedValue(true),
      sendUserId: jest.fn().mockResolvedValue(true),
      sendPasswordChangeNotification: jest.fn().mockResolvedValue(true),
      sendWelcomeEmail: jest.fn().mockResolvedValue(true),
      sendNotificationEmail: jest.fn().mockResolvedValue(true),
    };
  }

  /**
   * Creates a mock users service
   */
  static createMockUsersService() {
    return {
      findByUserId: jest.fn().mockResolvedValue(null),
      findByEmail: jest.fn().mockResolvedValue(null),
      findById: jest.fn().mockResolvedValue(null),
      create: jest.fn().mockImplementation((userData) => ({
        ...userData,
        id: faker.string.uuid(),
      })),
      update: jest.fn().mockResolvedValue({ affected: 1 }),
      delete: jest.fn().mockResolvedValue({ affected: 1 }),
      getAllAdminUsers: jest.fn().mockResolvedValue([]),
      validateUserData: jest.fn().mockReturnValue(true),
    };
  }

  /**
   * Creates a mock profile picture service
   */
  static createMockProfilePictureService() {
    return {
      hasProfilePicture: jest.fn().mockResolvedValue(false),
      getProfilePictureUrl: jest.fn().mockResolvedValue(null),
      getProfilePictureDirectUrl: jest.fn().mockResolvedValue(null),
      updateProfilePicture: jest.fn().mockResolvedValue(true),
      deleteProfilePicture: jest.fn().mockResolvedValue(true),
      validateImageFile: jest.fn().mockReturnValue(true),
    };
  }

  /**
   * Creates a mock data source
   */
  static createMockDataSource() {
    return {
      query: jest.fn().mockImplementation((sql: string, params?: any[]) => {
        // Basic SQL injection protection for tests
        const upperSql = sql.toUpperCase();
        const dangerousPatterns = ['DROP TABLE', 'DELETE FROM', 'TRUNCATE'];
        
        if (dangerousPatterns.some(pattern => upperSql.includes(pattern))) {
          throw new Error('Dangerous SQL operation detected in test');
        }
        
        return Promise.resolve([]);
      }),
      
      createQueryRunner: jest.fn().mockReturnValue({
        connect: jest.fn(),
        startTransaction: jest.fn(),
        commitTransaction: jest.fn(),
        rollbackTransaction: jest.fn(),
        release: jest.fn(),
        manager: {
          save: jest.fn().mockImplementation((entity) => Promise.resolve(entity)),
          create: jest.fn().mockImplementation((EntityClass, data) => ({ ...data, id: faker.string.uuid() })),
          getRepository: jest.fn().mockReturnValue(MockFactory.createMockRepository()),
          find: jest.fn().mockResolvedValue([]),
          findOne: jest.fn().mockResolvedValue(null),
        },
      }),
      
      manager: {
        save: jest.fn().mockImplementation((entity) => Promise.resolve(entity)),
        create: jest.fn().mockImplementation((EntityClass, data) => ({ ...data, id: faker.string.uuid() })),
        getRepository: jest.fn().mockReturnValue(MockFactory.createMockRepository()),
      },
    };
  }

  /**
   * Creates a mock notification service
   */
  static createMockNotificationService() {
    return {
      notifyAsync: jest.fn().mockResolvedValue(true),
      notifyManyAsync: jest.fn().mockResolvedValue(true),
      sendNotification: jest.fn().mockResolvedValue(true),
      createNotification: jest.fn().mockResolvedValue({ id: faker.string.uuid() }),
    };
  }

  /**
   * Creates a mock deeplink service
   */
  static createMockDeeplinkService() {
    return {
      getLinkHtml: jest.fn().mockImplementation((link: string) => {
        // Validate URL to prevent malicious links
        if (!link.startsWith('https://') && !link.startsWith('http://localhost')) {
          throw new Error('Invalid URL scheme');
        }
        return `<a href="${link}">Click here</a>`;
      }),
      generateDeeplink: jest.fn().mockReturnValue('https://app.example.com/verify/token'),
      validateDeeplink: jest.fn().mockReturnValue(true),
    };
  }

  /**
   * Creates a mock token blacklist service
   */
  static createMockTokenBlacklistService() {
    return {
      blacklistToken: jest.fn().mockResolvedValue(true),
      isTokenBlacklisted: jest.fn().mockResolvedValue(false),
      cleanupExpiredTokens: jest.fn().mockResolvedValue(true),
    };
  }

  /**
   * Creates a mock email template service
   */
  static createMockEmailTemplateService() {
    return {
      generateSubscriptionWelcomeTemplate: jest.fn().mockReturnValue('<html>Welcome</html>'),
      generatePaymentConfirmationTemplate: jest.fn().mockReturnValue('<html>Payment Confirmed</html>'),
      generateVerificationTemplate: jest.fn().mockReturnValue('<html>Verify Email</html>'),
      generatePasswordResetTemplate: jest.fn().mockReturnValue('<html>Reset Password</html>'),
      validateTemplate: jest.fn().mockReturnValue(true),
    };
  }

  /**
   * Creates a mock plans service
   */
  static createMockPlansService() {
    return {
      subscribeWithFreePayment: jest.fn().mockResolvedValue({ success: true }),
      findPlanById: jest.fn().mockResolvedValue(null),
      getActivePlans: jest.fn().mockResolvedValue([]),
      validatePlan: jest.fn().mockReturnValue(true),
    };
  }

  /**
   * Creates a mock diary service
   */
  static createMockDiaryService() {
    return {
      createDiary: jest.fn().mockResolvedValue({ id: faker.string.uuid() }),
      getOrCreateDiary: jest.fn().mockResolvedValue({ id: faker.string.uuid() }),
      findDiaryByUserId: jest.fn().mockResolvedValue(null),
    };
  }

  /**
   * Creates all common mocks in one call
   */
  static createAllCommonMocks() {
    return {
      jwtService: this.createMockJwtService(),
      emailService: this.createMockEmailService(),
      usersService: this.createMockUsersService(),
      profilePictureService: this.createMockProfilePictureService(),
      dataSource: this.createMockDataSource(),
      notificationService: this.createMockNotificationService(),
      deeplinkService: this.createMockDeeplinkService(),
      tokenBlacklistService: this.createMockTokenBlacklistService(),
      emailTemplateService: this.createMockEmailTemplateService(),
      plansService: this.createMockPlansService(),
      diaryService: this.createMockDiaryService(),
    };
  }
}