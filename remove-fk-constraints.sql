-- Remove all waterfall foreign key constraints
ALTER TABLE "waterfall_participation" DROP CONSTRAINT IF EXISTS "FK_b54f4c72fadee4feb2ca98da56d";
ALTER TABLE "waterfall_answer" DROP CONSTRAINT IF EXISTS "FK_d9104da9519ec2a769ba473f498";
ALTER TABLE "waterfall_question" DROP CONSTRAINT IF EXISTS "FK_c19ed78ab6eb5bbd64702f2a456";
ALTER TABLE "waterfall_true_false_question" DROP CONSTRAINT IF EXISTS "FK_48780dba8d9da4250e52f5d2821";
ALTER TABLE "waterfall_multiple_choice_question" DROP CONSTRAINT IF EXISTS "FK_waterfall_multiple_choice_question_set_id";

-- Find and remove any remaining foreign key constraints on set_id columns
DO $$
DECLARE
    constraint_record RECORD;
BEGIN
    FOR constraint_record IN
        SELECT 
            tc.constraint_name,
            tc.table_name
        FROM information_schema.table_constraints tc
        JOIN information_schema.key_column_usage kcu 
            ON tc.constraint_name = kcu.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY'
            AND kcu.column_name IN ('set_id', 'setId', 'question_id', 'questionId')
            AND tc.table_name LIKE '%waterfall%'
    LOOP
        EXECUTE 'ALTER TABLE "' || constraint_record.table_name || '" DROP CONSTRAINT IF EXISTS "' || constraint_record.constraint_name || '"';
    END LOOP;
END $$;