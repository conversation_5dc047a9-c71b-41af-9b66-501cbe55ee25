// src/common/common.module.ts
import { Global, Module } from '@nestjs/common';
import { TypeOrmModule, getRepositoryToken } from '@nestjs/typeorm';
import { APP_FILTER, APP_GUARD, APP_INTERCEPTOR } from '@nestjs/core';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { DeeplinkModule } from './utils/deeplink.module';
import { UtilsModule } from './utils/utils.module';

// Services
import LoggerService from './services/logger.service';
import EmailService from './services/email.service';
import { EmailTemplateService } from './services/email-template.service';
import { AuditLogService } from './services/audit-log.service';
import { FileUtilService } from './services/file-util.service';
import { ProfilePictureService } from './services/profile-picture.service';
import { PaginationService } from './services/pagination.service';
import { CurrentUserService } from './services/current-user.service';
import { QrCodeService } from './services/qr-code.service';
import { StorageConfigService } from './services/storage-config.service';
import { S3StorageProvider } from './providers/s3-storage.provider';
import { SubscriptionService } from './services/subscription.service';
import { TokenBlacklistService } from './services/token-blacklist.service';
import { GeminiAiService } from './services/gemini-ai.service';
// Removed SignedUrlService and CompactSignedUrlService as they're no longer used

// Filters
import { HttpExceptionFilter } from './filters/http-exception.filter';
import { GlobalExceptionFilter } from './filters/global-exception.filter';

// Interceptors
import { AuditLogInterceptor } from './interceptors/audit-log.interceptor';
import { OperationLogInterceptor } from './interceptors/operation-log.interceptor';

// Guards
import { JwtAuthGuard } from './guards/jwt.guard';
import { RolesGuard } from './guards/roles.guard';
import { StudentGuard } from './guards/student.guard';

// Entities
import { AuditLog } from '../database/entities/audit-log.entity';
import { ProfilePicture } from '../database/entities/profile-picture.entity';
import { ProfilePictureRegistry } from '../database/entities/profile-picture-registry.entity';
import { ShopItemRegistry } from '../database/entities/shop-item-registry.entity';
import { DiarySkinRegistry } from '../database/entities/diary-skin-registry.entity';
import { StudentDiarySkinRegistry } from '../database/entities/student-diary-skin-registry.entity';
import { DiaryCoverRegistry } from '../database/entities/diary-cover-registry.entity';
import { Diary } from '../database/entities/diary.entity';
import { DiaryQrRegistry } from '../database/entities/diary-qr-registry.entity';
import { ShopItem } from '../database/entities/shop-item.entity';
import { DiarySkin } from '../database/entities/diary-skin.entity';
import { StudentDiarySkin } from '../database/entities/student-diary-skin.entity';
import { DiaryEntry } from '../database/entities/diary-entry.entity';
import { ShopCategory } from '../database/entities/shop-category.entity';
import { StoryMakerRegistry } from '../database/entities/story-maker-registry.entity';
import { StoryMaker } from '../database/entities/story-maker.entity';
import { MessageRegistry } from '../database/entities/message-registry.entity';
import { TokenBlacklist } from '../database/entities/token-blacklist.entity';
import { UserPlan } from '../database/entities/user-plan.entity';
import { ContactUs } from '../database/entities/contact-us.entity';
// Old registry services removed
import { FileRegistryService } from './services/file-registry.service';
@Global()
@Module({
  imports: [
    TypeOrmModule.forFeature([
      AuditLog,
      ProfilePicture,
      ProfilePictureRegistry,
      ShopItemRegistry,
      DiarySkinRegistry,
      StudentDiarySkinRegistry,
      DiaryCoverRegistry,
      Diary,
      DiaryQrRegistry,
      MessageRegistry,
      ShopItem,
      DiarySkin,
      StudentDiarySkin,
      DiaryEntry,
      ShopCategory,
      StoryMakerRegistry,
      StoryMaker,
      UserPlan,
      TokenBlacklist,
      ContactUs,
    ]),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        const secret = configService.get<string>('JWT_SECRET');
        if (!secret) {
          console.error('JWT_SECRET is not defined in environment variables');
          throw new Error('JWT_SECRET is not defined');
        }
        return {
          secret,
          signOptions: {
            expiresIn: '60m',
          },
        };
      },
      inject: [ConfigService],
    }),
    DeeplinkModule,
    UtilsModule,
  ],
  providers: [
    // Services
    LoggerService,
    EmailService,
    AuditLogService,
    {
      provide: FileUtilService,
      useFactory: (configService, logger) => {
        return new FileUtilService(configService, logger);
      },
      inject: [ConfigService, LoggerService],
    },
    // Storage services
    StorageConfigService,
    S3StorageProvider,

    // File registry service with S3 support
    {
      provide: FileRegistryService,
      useFactory: (
        profilePictureRegistryRepo,
        profilePictureRepo,
        shopItemRegistryRepo,
        shopItemRepo,
        diarySkinRegistryRepo,
        diarySkinRepo,
        studentDiarySkinRegistryRepo,
        studentDiarySkinRepo,
        diaryCoverRegistryRepo,
        diaryRepo,
        diaryQrRegistryRepo,
        diaryEntryRepo,
        shopCategoryRepo,
        storyMakerRegistryRepo,
        storyMakerRepo,
        messageRegistryRepo,
        fileUtil,
        storageConfigService,
        s3StorageProvider,
      ) => {
        return new FileRegistryService(
          profilePictureRegistryRepo,
          profilePictureRepo,
          shopItemRegistryRepo,
          shopItemRepo,
          diarySkinRegistryRepo,
          diarySkinRepo,
          studentDiarySkinRegistryRepo,
          studentDiarySkinRepo,
          diaryCoverRegistryRepo,
          diaryRepo,
          diaryQrRegistryRepo,
          diaryEntryRepo,
          shopCategoryRepo,
          storyMakerRegistryRepo,
          storyMakerRepo,
          messageRegistryRepo,
          fileUtil,
          storageConfigService,
          s3StorageProvider,
        );
      },
      inject: [
        getRepositoryToken(ProfilePictureRegistry),
        getRepositoryToken(ProfilePicture),
        getRepositoryToken(ShopItemRegistry),
        getRepositoryToken(ShopItem),
        getRepositoryToken(DiarySkinRegistry),
        getRepositoryToken(DiarySkin),
        getRepositoryToken(StudentDiarySkinRegistry),
        getRepositoryToken(StudentDiarySkin),
        getRepositoryToken(DiaryCoverRegistry),
        getRepositoryToken(Diary),
        getRepositoryToken(DiaryQrRegistry),
        getRepositoryToken(DiaryEntry),
        getRepositoryToken(ShopCategory),
        getRepositoryToken(StoryMakerRegistry),
        getRepositoryToken(StoryMaker),
        getRepositoryToken(MessageRegistry),
        FileUtilService,
        StorageConfigService,
        S3StorageProvider,
      ],
    },
    {
      provide: ProfilePictureService,
      useFactory: (profilePictureRepo, configService, logger, fileRegistryService, fileUtil) => {
        return new ProfilePictureService(profilePictureRepo, configService, logger, fileRegistryService, fileUtil);
      },
      inject: [getRepositoryToken(ProfilePicture), ConfigService, LoggerService, FileRegistryService, FileUtilService],
    },
    PaginationService,
    CurrentUserService,
    QrCodeService,
    SubscriptionService,
    TokenBlacklistService,
    GeminiAiService,
    EmailTemplateService,

    // Global exception filters
    {
      provide: APP_FILTER,
      useClass: HttpExceptionFilter,
    },
    {
      provide: APP_FILTER,
      useClass: GlobalExceptionFilter,
    },

    // Global interceptors
    {
      provide: APP_INTERCEPTOR,
      useClass: AuditLogInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: OperationLogInterceptor,
    },

    // Global guards
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard,
    },
    {
      provide: APP_GUARD,
      useClass: RolesGuard,
    },
    StudentGuard,
  ],
  exports: [
    LoggerService,
    EmailService,
    AuditLogService,
    FileUtilService,
    FileRegistryService,
    ProfilePictureService,
    PaginationService,
    CurrentUserService,
    QrCodeService,
    StorageConfigService,
    S3StorageProvider,
    SubscriptionService,
    TokenBlacklistService,
    GeminiAiService,
    EmailTemplateService,
    JwtModule,
    StudentGuard,
    UtilsModule,
  ],
})
export class CommonModule {}
