import { Injectable, Logger, BadRequestException, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, ILike, Repository } from 'typeorm';
import { QAMissionGoal } from '../../database/entities/qa-mission-goal.entity';
import { QATaskMissions } from '../../database/entities/qa-task-missions.entity';
import {
  CreateQAMissionDto,
  QAMissionResponseDto,
  QAMissionPaginationDto,
  UpdateQAMissionDto,
  CreateAllQAMissionDto,
  AllQAMissionResponseDto,
  NewQAMissionResponseDto,
  UpdateQATaskDto,
  QAMissionListResponseDto,
} from '../../database/models/qa-mission.dto';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { QATaskSubmissions } from 'src/database/entities/qa-task-submissions.entity';
import { QAMonthlyMissionTasks } from 'src/database/entities/qa-monthly-mission-tasks.entity';
import { QAMission } from 'src/database/entities/qa-mission.entity';
import { QAWeeklyMissionTasks } from 'src/database/entities/qa-weekly-mission-tasks.entity';
import { QAMissionMonth } from 'src/database/entities/qa-mission-month.entity';
import { QAMissionWeek } from 'src/database/entities/qa-mission-week.entity';
import { QAMissionTasks } from 'src/database/entities/qa-mission-tasks.entity';

@Injectable()
export class QAMissionService {
  private readonly logger = new Logger(QAMissionService.name);

  constructor(
    @InjectRepository(QAMissionGoal)
    private readonly qaMissionGoalRepository: Repository<QAMissionGoal>,
    @InjectRepository(QAMission)
    private readonly qaMissionRepository: Repository<QAMission>,
    // @InjectRepository(QATaskMissions)
    // private readonly qaTaskMissionsRepository: Repository<QATaskMissions>,
    @InjectRepository(QAMissionTasks)
    private readonly qaMissionTasksRepository: Repository<QAMissionTasks>,
    @InjectRepository(QAMission)
    private readonly qaAllMissionRepository: Repository<QAMission>,
    @InjectRepository(QAWeeklyMissionTasks)
    private readonly qaWeeklyMissionTasksRepository: Repository<QAWeeklyMissionTasks>,
    @InjectRepository(QAMonthlyMissionTasks)
    private readonly qaMonthlyMissionTasksRepository: Repository<QAMonthlyMissionTasks>,
    @InjectRepository(QAMissionMonth)
    private readonly qaMissionMonthRepository: Repository<QAMissionMonth>,
    @InjectRepository(QAMissionWeek)
    private readonly qaMissionWeekRepository: Repository<QAMissionWeek>,
    private readonly dataSource: DataSource,
  ) {}

  async create(missionData: CreateQAMissionDto): Promise<QAMissionResponseDto> {
    try {
      const { timeFrequency, tasks } = missionData;

      const lastMission = await this.qaMissionGoalRepository.findOne({
        order: { sequenceNumber: 'DESC' },
        where: { isActive: true },
      });

      const lastSequenceNumber = lastMission ? lastMission.sequenceNumber + 1 : 1;

      const mission = this.qaMissionRepository.create({
        timeFrequency,
        isActive: true,
        sequenceNumber: lastSequenceNumber,
      });

      const savedMission = await this.qaMissionRepository.save(mission);

      const missionTasks = tasks.map((task) =>
        this.qaMissionTasksRepository.create({
          ...task,
          mission: savedMission,
          missionId: savedMission.id,
          isActive: true,
        }),
      );

      // Save all tasks in a single operation
      const savedTasks = await this.qaMissionTasksRepository.save(missionTasks);

      // Return the response DTO
      return this.toQAMissionResponseDto(savedMission, savedTasks);
    } catch (error) {
      this.logger.error(`Failed to create QA mission: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to create QA mission. Please check your input data.');
    }
  }

  async createMission(dto: CreateAllQAMissionDto): Promise<AllQAMissionResponseDto> {
    if (dto.timeFrequency === 'weekly' && !dto.weekId) {
      throw new BadRequestException('weekId is required when timeFrequency is weekly');
    }

    if (dto.timeFrequency === 'monthly' && !dto.monthId) {
      throw new BadRequestException('monthId is required when timeFrequency is monthly');
    }

    let mission = await this.qaAllMissionRepository.findOne({
      where: dto.timeFrequency === 'weekly' && dto.weekId ? { weekId: dto.weekId } : dto.timeFrequency === 'monthly' && dto.monthId ? { monthId: dto.monthId } : {},
    });

    if (!mission) {
      const sequenceNumber =
        dto.timeFrequency === 'weekly' ? (await this.qaMissionWeekRepository.findOneBy({ id: dto.weekId }))?.sequence : (await this.qaMissionMonthRepository.findOneBy({ id: dto.monthId }))?.sequence;

      mission = this.qaAllMissionRepository.create({
        timeFrequency: dto.timeFrequency,
        isActive: true,
        weekId: dto.weekId,
        monthId: dto.monthId,
        sequenceNumber,
      });

      mission = await this.qaAllMissionRepository.save(mission);
    }

    const existingTasks = await this.qaMissionTasksRepository.find({
      where: { missionId: mission.id },
      order: { sequence: 'DESC' },
    });

    const taskTitles = existingTasks.map((task) => task.title);

    const taskTitleSet = new Set(existingTasks.map((task) => task.title));

    for (const task of dto.tasks) {
      if (taskTitleSet.has(task.title)) {
        throw new BadRequestException(`Duplicate task title "${task.title}" found in the same mission`);
      }
      taskTitleSet.add(task.title);
    }

    const lastSequence = existingTasks.length > 0 ? existingTasks[0].sequence : 0;

    const tasksToCreate = dto.tasks.map((task, index) =>
      this.qaMissionTasksRepository.create({
        ...task,
        missionId: mission.id,
        sequence: lastSequence + index + 1,
      }),
    );

    await this.qaMissionTasksRepository.save(tasksToCreate);

    return {
      id: mission.id,
      timeFrequency: mission.timeFrequency,
      sequenceNumber: mission.sequenceNumber,
      isActive: mission.isActive,
      weekId: mission.weekId,
      monthId: mission.monthId,
      tasks: tasksToCreate.map((task) => ({
        id: task.id,
        title: task.title,
        description: task.description,
        wordLimitMinimum: task.wordLimitMinimum,
        wordLimitMaximum: task.wordLimitMaximum,
        deadline: task.deadline,
        instructions: task.instructions,
        isActive: task.isActive,
        sequence: task.sequence,
        totalScore: task.totalScore,
        missionId: task.missionId,
        createdAt: task.createdAt,
        updatedAt: task.updatedAt,
      })),
    };
  }

  async findAll(paginationDto?: QAMissionPaginationDto): Promise<PagedListDto<QAMissionResponseDto>> {
    try {
      const { page = 1, limit = 10, sortBy, sortDirection, timeFrequency, weekOrMonth, title } = paginationDto || {};

      // Create query builder
      const queryBuilder = this.qaMissionRepository
        .createQueryBuilder('mission')
        .leftJoinAndSelect('mission.tasks', 'tasks')
        .leftJoinAndSelect('tasks.submissions', 'submissions')
        .where('mission.isActive = :isActive', { isActive: true });

      // Apply filters
      if (timeFrequency) {
        queryBuilder.andWhere('mission.timeFrequency = :timeFrequency', { timeFrequency });
      }

      if (weekOrMonth && (sortBy === 'week' || sortBy === 'month')) {
        queryBuilder.andWhere('mission.sequenceNumber = :sequenceNumber', { sequenceNumber: weekOrMonth });
      }

      // Apply title search
      if (title) {
        queryBuilder.andWhere('tasks.title ILIKE :title', { title: `%${title}%` });
      }

      // Apply sorting
      if (sortBy && sortDirection) {
        if (sortBy === 'createdAt' || sortBy === 'updatedAt') {
          queryBuilder.orderBy(`mission.${sortBy}`, sortDirection.toUpperCase() as 'ASC' | 'DESC');
        }
      } else {
        // Default sorting
        queryBuilder.orderBy('mission.createdAt', 'DESC');
      }

      // Apply pagination
      queryBuilder.skip((page - 1) * limit).take(limit);

      // Execute query
      const [missions, totalCount] = await queryBuilder.getManyAndCount();

      // Map to DTOs
      const missionDtos = missions.map((mission) => this.toQAMissionResponseDto(mission, mission.tasks));

      return new PagedListDto(missionDtos, totalCount, page, limit);
    } catch (error) {
      this.logger.error(`Failed to fetch QA missions: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to fetch QA missions');
    }
  }

  async findList(paginationDto?: QAMissionPaginationDto): Promise<PagedListDto<QAMissionResponseDto>> {
    try {
      const { page = 1, limit = 10, sortBy, sortDirection, timeFrequency, weekOrMonth, title } = paginationDto || {};

      // Create query builder
      const queryBuilder = this.qaMissionRepository
        .createQueryBuilder('mission')
        .leftJoinAndSelect('mission.tasks', 'tasks', 'tasks.isActive = :isActive', { isActive: true })
        .leftJoinAndSelect('tasks.submissions', 'submissions')
        .where('mission.isActive = :isActive', { isActive: true });

      // Apply filters
      if (timeFrequency) {
        queryBuilder.andWhere('mission.timeFrequency = :timeFrequency', { timeFrequency });
      }

      if (weekOrMonth && (sortBy === 'week' || sortBy === 'month')) {
        queryBuilder.andWhere('mission.sequenceNumber = :sequenceNumber', { sequenceNumber: weekOrMonth });
      }

      // Apply title search
      if (title) {
        queryBuilder.andWhere('tasks.title ILIKE :title', { title: `%${title}%` });
      }

      // Apply sorting
      if (sortBy && sortDirection) {
        if (sortBy === 'createdAt' || sortBy === 'updatedAt') {
          queryBuilder.orderBy(`mission.${sortBy}`, sortDirection.toUpperCase() as 'ASC' | 'DESC');
        }
      } else {
        // Default sorting
        // queryBuilder.orderBy('mission.createdAt', 'DESC');
        queryBuilder.orderBy('mission.sequenceNumber', 'ASC').addOrderBy('tasks.sequence', 'ASC');
      }

      // Apply pagination
      queryBuilder.skip((page - 1) * limit).take(limit);

      // Execute query
      const [missions, totalCount] = await queryBuilder.getManyAndCount();

      // Map to DTOs
      const missionDtos = missions.map((mission) => this.toQAMissionListResponseDto(mission, mission.tasks));

      return new PagedListDto(missionDtos, totalCount);
    } catch (error) {
      this.logger.error(`Failed to fetch QA missions: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to fetch QA missions');
    }
  }

  async findMissionList(paginationDto?: QAMissionPaginationDto): Promise<PagedListDto<QAMissionListResponseDto>> {
    try {
      const { page = 1, limit = 10, sortBy, sortDirection, timeFrequency, weekOrMonth, title } = paginationDto || {};

      // Create query builder
      const queryBuilder = this.qaMissionRepository
        .createQueryBuilder('mission')
        .leftJoinAndSelect('mission.tasks', 'tasks', 'tasks.isActive = :isActive', { isActive: true })
        .leftJoinAndSelect('tasks.submissions', 'submissions')
        .leftJoinAndSelect('mission.week', 'week')
        .leftJoinAndSelect('mission.month', 'month')
        .where('mission.isActive = :isActive', { isActive: true });

      // Apply filters
      if (timeFrequency) {
        queryBuilder.andWhere('mission.timeFrequency = :timeFrequency', { timeFrequency });
      }

      if (weekOrMonth && (sortBy === 'week' || sortBy === 'month')) {
        queryBuilder.andWhere('mission.sequenceNumber = :sequenceNumber', { sequenceNumber: weekOrMonth });
      }

      // Apply title search
      if (title) {
        queryBuilder.andWhere('tasks.title ILIKE :title', { title: `%${title}%` });
      }

      // Apply sorting
      if (sortBy && sortDirection) {
        if (sortBy === 'createdAt' || sortBy === 'updatedAt') {
          queryBuilder.orderBy(`mission.${sortBy}`, sortDirection.toUpperCase() as 'ASC' | 'DESC');
        }
      } else {
        // Default sorting
        // queryBuilder.orderBy('mission.createdAt', 'DESC');
        queryBuilder.orderBy('mission.sequenceNumber', 'ASC').addOrderBy('tasks.sequence', 'ASC');
      }

      // Apply pagination
      queryBuilder.skip((page - 1) * limit).take(limit);

      // Execute query
      const [missions, totalCount] = await queryBuilder.getManyAndCount();

      // Map to DTOs
      // const missionDtos = missions.map(mission =>
      //   this.toQAMissionListResponseDto(mission, mission.tasks)
      // );

      const missionDtos = missions.map((mission) => ({
        id: mission.id,
        timeFrequency: mission.timeFrequency,
        isActive: mission.isActive,
        weekId: mission.weekId,
        monthId: mission.monthId,
        title: mission.weekId ? mission.week.title : mission.month.display,
        sequenceNumber: mission.sequenceNumber,
        totalTasks: mission.tasks.length,
      }));

      return new PagedListDto(missionDtos, totalCount);
    } catch (error) {
      this.logger.error(`Failed to fetch QA missions: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to fetch QA missions');
    }
  }

  async findAllForStudents(paginationDto?: QAMissionPaginationDto): Promise<PagedListDto<QAMissionResponseDto>> {
    const options: any = {
      relations: ['tasks', 'tasks.submissions'],
      where: { isActive: true },
    };

    if (paginationDto) {
      const { page = 1, limit = 10, sortBy, sortDirection, timeFrequency, weekOrMonth, title } = paginationDto;
      if (timeFrequency) {
        options.where.timeFrequency = timeFrequency;
      }
      options.skip = (page - 1) * limit;
      options.take = limit;

      if (sortBy && sortDirection) {
        if (sortBy == 'week' || sortBy == 'month') {
          options.where.sequenceNumber = weekOrMonth;
        } else if (sortBy == 'title') {
          options.where.tasks = { title: ILike(`%${title}%`) };
        }
        if (sortBy == 'createdAt' || sortBy == 'updatedAt') options.order = { [sortBy]: sortDirection.toUpperCase() };
      }
    }

    const [missions, totalCount] = await this.qaMissionGoalRepository.findAndCount(options);

    const missionDtos = missions.map((mission) => this.toStudentMissionResponseDto(mission, mission.tasks));

    return new PagedListDto(missionDtos, totalCount);
  }

  async findListForStudents(userId: string, paginationDto?: QAMissionPaginationDto): Promise<PagedListDto<QAMissionResponseDto>> {
    const options: any = {
      relations: ['tasks', 'tasks.submissions', 'tasks.submissions.submissionHistory', 'week', 'month'],
      where: { isActive: true },
    };

    if (paginationDto) {
      const { page = 1, limit = 10, sortBy, sortDirection, timeFrequency, weekOrMonth, title } = paginationDto;
      if (timeFrequency) {
        options.where.timeFrequency = timeFrequency;
      }
      options.skip = (page - 1) * limit;
      options.take = limit;

      if (sortBy && sortDirection) {
        if (sortBy == 'week' || sortBy == 'month') {
          options.where.sequenceNumber = weekOrMonth;
        } else if (sortBy == 'title') {
          options.where.tasks = { title: ILike(`%${title}%`) };
        }
        if (sortBy == 'createdAt' || sortBy == 'updatedAt') options.order = { [sortBy]: sortDirection.toUpperCase() };
      }
    }

    const [missions, totalCount] = await this.qaMissionRepository.findAndCount(options);
    let userSubmissions = null;
    const missionDtos = missions.map((mission) => ({
      id: mission.id,
      timeFrequency: mission.timeFrequency,
      isActive: mission.isActive,
      sequenceNumber: mission.sequenceNumber,
      weekId: mission.weekId,
      monthId: mission.monthId,
      missionTitle: mission.weekId ? mission.week.title : mission.month.display,
      tasks: mission.tasks.map((task) => {
        userSubmissions = (task.submissions || []).filter((submission) => submission.createdBy === userId);

        const hasSubmissionFlag = (task.submissions || []).filter((submission) => submission.createdBy === userId).length > 0;

        const firstRevisionSubmission = task.submissions?.find((submission) => submission.currentRevision === 1);
        
        // Calculate progress based on wordLimitMinimum and submitted word count
        let progress = 0;
        if (firstRevisionSubmission && task.wordLimitMinimum > 0) {
          // Get word count from the latest submission history
          const latestHistory = firstRevisionSubmission.submissionHistory?.find(h => h.id === firstRevisionSubmission.latestSubmissionId);
          const submittedWordCount = latestHistory?.wordCount || 0;
          progress = Math.min(100, Math.round((submittedWordCount / task.wordLimitMinimum) * 100));
        }

        return {
          id: task.id,
          title: task.title,
          description: task.description,
          totalScore: task.totalScore,
          wordLimitMinimum: task.wordLimitMinimum,
          wordLimitMaximum: task.wordLimitMaximum,
          isActive: task.isActive,
          sequence: task.sequence,
          deadline: task.deadline,
          instructions: task.instructions,
          isTaskStarted: hasSubmissionFlag,
          progress: progress,
          createdBySub: userSubmissions.createdBy,
          submissions: task.submissions.map((submission) => ({
            id: submission.id,
            createdBy: submission.createdBy,
            firstRevisionProgress: submission.firstRevisionProgress,
            currentRevision: submission.currentRevision,
            isFirstRevision: submission.currentRevision === 1,
          })),
        };
      }),
    }));

    return new PagedListDto(missionDtos, totalCount);
  }

  async findById(id: string): Promise<QAMissionResponseDto> {
    try {
      const mission = await this.qaMissionRepository.findOne({
        where: { id },
        relations: ['tasks'],
      });

      if (!mission) {
        throw new NotFoundException(`QA mission with ID ${id} not found`);
      }

      return this.toQAMissionUpResponseDto(mission, mission.tasks);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to fetch QA mission: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to fetch QA mission');
    }
  }

  /**
   * Update an existing QA mission
   * @param id Mission ID
   * @param missionData Update data
   * @returns Updated QA mission
   */
  async update(id: string, missionData: UpdateQAMissionDto): Promise<QAMissionResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const mission = await queryRunner.manager.findOne(QAMission, {
        where: { id },
        relations: ['tasks'],
      });

      if (!mission) {
        throw new NotFoundException(`QA mission with ID ${id} not found`);
      }

      // Update mission properties
      if (missionData.timeFrequency !== undefined) {
        mission.timeFrequency = missionData.timeFrequency;
      }

      if (missionData.isActive !== undefined) {
        mission.isActive = missionData.isActive;
      }

      // Save updated mission
      const updatedMission = await queryRunner.manager.save(mission);

      // Handle tasks updates
      if (missionData.tasks && missionData.tasks.length > 0) {
        for (const taskData of missionData.tasks) {
          if (taskData.id) {
            // Update existing task
            const existingTask = mission.tasks.find((t) => t.id === taskData.id);
            if (existingTask) {
              // Update task properties
              Object.assign(existingTask, taskData);
              await queryRunner.manager.save(existingTask);
            } else {
              this.logger.warn(`Task with ID ${taskData.id} not found in mission ${id}`);
            }
          } else {
            // Create new task
            const newTask = this.qaMissionTasksRepository.create({
              ...taskData,
              mission: updatedMission,
              missionId: updatedMission.id,
              isActive: true,
            });
            await queryRunner.manager.save(newTask);
          }
        }
      }

      // Fetch updated mission with tasks
      const refreshedMission = await queryRunner.manager.findOne(QAMission, {
        where: { id },
        relations: ['tasks'],
      });

      await queryRunner.commitTransaction();

      return this.toQAMissionResponseDto(refreshedMission, refreshedMission.tasks);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Failed to update QA mission: ${error.message}`, error.stack);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('Failed to update QA mission. Please check your input data.');
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Soft delete a QA mission and its tasks
   * @param id Mission ID
   */
  async softDelete(id: string): Promise<void> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const mission = await queryRunner.manager.findOne(QAMission, {
        where: { id },
        relations: ['tasks'],
      });

      if (!mission) {
        throw new NotFoundException(`QA mission with ID ${id} not found`);
      }

      mission.isActive = false;
      await queryRunner.manager.save(QAMission, mission);

      if (mission.tasks && mission.tasks.length > 0) {
        await queryRunner.manager.update(QAMissionTasks, { missionId: mission.id }, { isActive: false });
      }

      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Failed to soft delete QA mission ${id}: ${error.message}`, error.stack);

      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to soft delete QA mission: ${error.message}`);
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Permanently delete a QA mission and its tasks
   * @param id Mission ID
   */
  async hardDelete(id: string): Promise<void> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const mission = await queryRunner.manager.findOne(QAMission, {
        where: { id },
        relations: ['tasks'],
      });

      if (!mission) {
        throw new NotFoundException(`QA mission not found`);
      }

      // Check for submissions for each task
      if (mission.tasks && mission.tasks.length > 0) {
        for (const task of mission.tasks) {
          const submissionCount = await queryRunner.manager.count(QATaskSubmissions, {
            where: { taskId: task.id },
          });
          if (submissionCount > 0) {
            // throw new ConflictException(
            //   `Cannot delete mission ${id} because task ${task.id} has related submissions.`
            // );
            throw new ConflictException(`Cannot delete this mission because task has related submissions.`);
          }
        }
        // If no submissions, delete tasks
        await queryRunner.manager.delete(
          QAMissionTasks,
          mission.tasks.map((task) => task.id),
        );
      }

      // if (mission.tasks && mission.tasks.length > 0) {
      //   await queryRunner.manager.delete(QAMissionTasks,
      //     mission.tasks.map(task => task.id)
      //   );
      // }

      await queryRunner.manager.delete(QAMission, id);

      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Failed to delete QA mission ${id}: ${error.message}`, error.stack);

      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to delete QA mission: ${error.message}`);
    } finally {
      await queryRunner.release();
    }
  }

  async hardDeleteTask(id: string): Promise<void> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const task = await queryRunner.manager.findOne(QAMissionTasks, {
        where: { id },
        relations: ['submissions'],
      });

      if (!task) {
        throw new NotFoundException(`QA task not found`);
      }

      // Delete related submissions if they exist
      if (task.submissions && task.submissions.length > 0) {
        // await queryRunner.manager.delete(QATaskSubmissions,
        //   task.submissions.map(submission => submission.id)
        // );
        throw new ConflictException(`Cannot delete task because it has related submissions.`);
      }

      // Delete the task
      await queryRunner.manager.delete(QAMissionTasks, id);

      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Failed to delete QA task ${id}: ${error.message}`, error.stack);

      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to delete QA task: ${error.message}`);
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Soft delete a QA task mission
   * @param id Task ID
   */
  async softDeleteTask(id: string): Promise<void> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const task = await queryRunner.manager.findOne(QAMissionTasks, {
        where: { id },
        relations: ['submissions'],
      });

      if (!task) {
        throw new NotFoundException(`QA task with ID ${id} not found`);
      }

      // Mark task as inactive
      task.isActive = false;
      await queryRunner.manager.save(QAMissionTasks, task);

      // Mark related submissions as inactive if they exist
      if (task.submissions && task.submissions.length > 0) {
        await queryRunner.manager.update(QATaskSubmissions, { taskId: task.id }, { isActive: false });
      }

      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Failed to soft delete QA task ${id}: ${error.message}`, error.stack);

      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to soft delete QA task: ${error.message}`);
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Find a QA task mission by ID
   * @param id Task ID
   * @returns QA task mission
   */
  async findTaskById(id: string): Promise<any> {
    try {
      const task = await this.qaMissionTasksRepository.findOne({
        where: { id, isActive: true },
        relations: ['mission', 'submissions'],
      });

      if (!task) {
        throw new NotFoundException(`QA task with ID ${id} not found`);
      }

      return this.toQATaskResponseDto(task);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to fetch QA task: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to fetch QA task');
    }
  }

  private toStudentMissionResponseDto(mission: QAMissionGoal, tasks: QATaskMissions[]): QAMissionResponseDto {
    const taskProgress = tasks.flatMap((task) => {
      const firstRevisionSubmission = task.submissions?.find((submission) => submission.currentRevision === 1);

      if (firstRevisionSubmission) {
        return [
          {
            id: task.id,
            progress: firstRevisionSubmission.firstRevisionProgress || 0,
          },
        ];
      }
      return [
        {
          id: task.id,
          progress: 0,
        },
      ];
    });

    return {
      id: mission.id,
      timeFrequency: mission.timeFrequency,
      isActive: mission.isActive,
      sequenceNumber: mission.sequenceNumber,
      tasks: tasks.map((task) => ({
        id: task.id,
        title: task.title,
        description: task.description,
        wordLimitMinimum: task.wordLimitMinimum,
        wordLimitMaximum: task.wordLimitMaximum,
        isActive: task.isActive,
        timePeriodUnit: task.timePeriodUnit,
        deadline: task.deadline,
        instructions: task.instructions,
        metaData: task.metaData,
      })),
      taskProgress,
    };
  }

  // private toStudentMissionListResponseDto(mission: QAMission, tasks: QAMissionTasks[], week: QAMissionWeek, month: QAMissionMonth): QAMissionResponseDto {
  //   const taskProgress = tasks.flatMap(task => {
  //     const firstRevisionSubmission = task.submissions?.find(submission =>


  //     if (firstRevisionSubmission) {
  //       return [{
  //         id: task.id,
  //         progress: firstRevisionSubmission.firstRevisionProgress || 0,
  //       }];
  //     }
  //     return [{
  //       id: task.id,
  //       progress: 0,
  //     }];
  //   });

  //   return {
  //     id: mission.id,
  //     timeFrequency: mission.timeFrequency,
  //     isActive: mission.isActive,
  //     sequenceNumber: mission.sequenceNumber,
  //     tasks: tasks.map(task => ({
  //       id: task.id,
  //       title: task.title,
  //       description: task.description,
  //       totalScore: task.totalScore,
  //       wordLimitMinimum: task.wordLimitMinimum,
  //       wordLimitMaximum: task.wordLimitMaximum,
  //       isActive: task.isActive,
  //       sequence: task.sequence,
  //       deadline: task.deadline,
  //       instructions: task.instructions,
  //     })),
  //     taskProgress
  //   };
  // }

  private toStudentMissionListResponseDto(mission: QAMission, tasks: QAMissionTasks[], week: QAMissionWeek, month: QAMissionMonth): QAMissionResponseDto {
    // const taskProgress = tasks.flatMap(task => {
    //   const firstRevisionSubmission = task.submissions?.find(submission =>
    //     submission.isFirstRevision || submission.currentRevision === 1
    //   );

    //   if (firstRevisionSubmission) {
    //     return [{
    //       id: task.id,
    //       progress: firstRevisionSubmission.firstRevisionProgress || 0,
    //     }];
    //   }
    //   return [{
    //     id: task.id,
    //     progress: 0,
    //   }];
    // });

    return {
      id: mission.id,
      timeFrequency: mission.timeFrequency,
      isActive: mission.isActive,
      sequenceNumber: mission.sequenceNumber,
      // tasks: tasks.map(task => ({
      //   id: task.id,
      //   title: task.title,
      //   description: task.description,
      //   totalScore: task.totalScore,
      //   wordLimitMinimum: task.wordLimitMinimum,
      //   wordLimitMaximum: task.wordLimitMaximum,
      //   isActive: task.isActive,
      //   sequence: task.sequence,
      //   deadline: task.deadline,
      //   instructions: task.instructions,
      // })),
      // taskProgress
      tasks: tasks.map((task) => {
        const firstRevisionSubmission = task.submissions?.find((submission) => submission.currentRevision === 1);
        return {
          id: task.id,
          title: task.title,
          description: task.description,
          totalScore: task.totalScore,
          wordLimitMinimum: task.wordLimitMinimum,
          wordLimitMaximum: task.wordLimitMaximum,
          isActive: task.isActive,
          sequence: task.sequence,
          deadline: task.deadline,
          instructions: task.instructions,
          progress: firstRevisionSubmission ? firstRevisionSubmission.firstRevisionProgress || 0 : 0,
        };
      }),
    };
  }

  private toQAMissionResponseDto(mission: QAMission, tasks: QAMissionTasks[]): QAMissionResponseDto {
    return {
      id: mission.id,
      timeFrequency: mission.timeFrequency,
      isActive: mission.isActive,
      sequenceNumber: mission.sequenceNumber,
      tasks: tasks.map((task) => ({
        id: task.id,
        title: task.title,
        description: task.description,
        wordLimitMinimum: task.wordLimitMinimum,
        wordLimitMaximum: task.wordLimitMaximum,
        deadline: task.deadline,
        instructions: task.instructions,
        isActive: task.isActive,
        totalScore: task.totalScore,
        sequence: task.sequence,
        createdAt: task.createdAt,
        updatedAt: task.updatedAt,
      })),
    };
  }

  private toQAMissionUpResponseDto(mission: QAMission, tasks: QAMissionTasks[]): QAMissionResponseDto {
    return {
      id: mission.id,
      timeFrequency: mission.timeFrequency,
      isActive: mission.isActive,
      weekId: mission.weekId,
      monthId: mission.monthId,
      sequenceNumber: mission.sequenceNumber,
      tasks: tasks.map((task) => ({
        id: task.id,
        title: task.title,
        description: task.description,
        wordLimitMinimum: task.wordLimitMinimum,
        wordLimitMaximum: task.wordLimitMaximum,
        deadline: task.deadline,
        instructions: task.instructions,
        isActive: task.isActive,
        totalScore: task.totalScore,
        sequence: task.sequence,
        createdAt: task.createdAt,
        updatedAt: task.updatedAt,
      })),
    };
  }

  private toQAMissionListResponseDto(mission: QAMission, tasks: QAMissionTasks[]): NewQAMissionResponseDto {
    return {
      id: mission.id,
      timeFrequency: mission.timeFrequency,
      isActive: mission.isActive,
      weekId: mission.weekId,
      monthId: mission.monthId,
      sequenceNumber: mission.sequenceNumber,
      tasks: tasks.map((task) => ({
        id: task.id,
        title: task.title,
        description: task.description,
        wordLimitMinimum: task.wordLimitMinimum,
        wordLimitMaximum: task.wordLimitMaximum,
        deadline: task.deadline,
        sequence: task.sequence,
        totalScore: task.totalScore,
        instructions: task.instructions,
        isActive: task.isActive,
        createdAt: task.createdAt,
        updatedAt: task.updatedAt,
      })),
    };
  }

  private toQAWeeklyMissionResponseDto(mission: QAMissionGoal, tasks: QATaskMissions[]): QAMissionResponseDto {
    return {
      id: mission.id,
      timeFrequency: mission.timeFrequency,
      isActive: mission.isActive,
      sequenceNumber: mission.sequenceNumber,
      tasks: tasks.map((task) => ({
        id: task.id,
        title: task.title,
        description: task.description,
        wordLimitMinimum: task.wordLimitMinimum,
        wordLimitMaximum: task.wordLimitMaximum,
        deadline: task.deadline,
        instructions: task.instructions,
        isActive: task.isActive,
        metaData: task.metaData,
        createdAt: task.createdAt,
        updatedAt: task.updatedAt,
      })),
    };
  }

  private toQAMonthlyMissionResponseDto(mission: QAMissionGoal, tasks: QATaskMissions[]): QAMissionResponseDto {
    return {
      id: mission.id,
      timeFrequency: mission.timeFrequency,
      isActive: mission.isActive,
      sequenceNumber: mission.sequenceNumber,
      tasks: tasks.map((task) => ({
        id: task.id,
        title: task.title,
        description: task.description,
        wordLimitMinimum: task.wordLimitMinimum,
        wordLimitMaximum: task.wordLimitMaximum,
        deadline: task.deadline,
        instructions: task.instructions,
        isActive: task.isActive,
        metaData: task.metaData,
        createdAt: task.createdAt,
        updatedAt: task.updatedAt,
      })),
    };
  }

  private toQATaskResponseDto(task: QAMissionTasks): any {
    return {
      id: task.id,
      title: task.title,
      description: task.description,
      wordLimitMinimum: task.wordLimitMinimum,
      wordLimitMaximum: task.wordLimitMaximum,
      deadline: task.deadline,
      sequence: task.sequence,
      totalScore: task.totalScore,
      instructions: task.instructions,
      isActive: task.isActive,
      missionId: task.missionId,
      createdAt: task.createdAt,
      updatedAt: task.updatedAt,
    };
  }

  private toAllQATaskResponseDto(task: QATaskMissions): any {
    return {
      id: task.id,
      title: task.title,
      description: task.description,
      wordLimitMinimum: task.wordLimitMinimum,
      wordLimitMaximum: task.wordLimitMaximum,
      deadline: task.deadline,
      instructions: task.instructions,
      isActive: task.isActive,
      missionId: task.missionId,
      createdAt: task.createdAt,
      updatedAt: task.updatedAt,
    };
  }

  async updateTaskById(id: string, dto: UpdateQATaskDto): Promise<QAMissionTasks> {
    const task = await this.qaMissionTasksRepository.findOne({ where: { id } });

    if (!task) {
      throw new NotFoundException(`QA task with ID ${id} not found`);
    }

    // Merge the existing task with the updated fields
    const updatedTask = this.qaMissionTasksRepository.merge(task, dto);

    try {
      return await this.qaMissionTasksRepository.save(updatedTask);
    } catch (error) {
      throw new BadRequestException('Failed to update QA task');
    }
  }
}
