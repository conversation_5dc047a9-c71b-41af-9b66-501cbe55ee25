import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ContactUs, ContactStatus } from '../../database/entities/contact-us.entity';
import { CreateContactUsDto, AdminResponseDto, ContactUsResponseDto } from '../../database/models/contact-us.dto';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';
import { getCurrentUTCDate } from '../../common/utils/date-utils';
import EmailService from '../../common/services/email.service';

@Injectable()
export class ContactService {
  private readonly logger = new Logger(ContactService.name);

  constructor(
    @InjectRepository(ContactUs)
    private readonly contactRepository: Repository<ContactUs>,
    private readonly emailService: EmailService,
  ) {}

  async create(createDto: CreateContactUsDto): Promise<ContactUsResponseDto> {
    const contact = this.contactRepository.create(createDto);
    const saved = await this.contactRepository.save(contact);
    
    this.logger.log(`New contact submission: ${saved.email} - ${saved.subject}`);
    return this.toResponseDto(saved);
  }

  async findAll(paginationDto?: PaginationDto): Promise<PagedListDto<ContactUsResponseDto>> {
    const { page = 1, limit = 10, sortBy = 'createdAt', sortDirection = 'DESC' } = paginationDto || {};
    
    const [contacts, total] = await this.contactRepository.findAndCount({
      skip: (page - 1) * limit,
      take: limit,
      order: { [sortBy]: sortDirection },
    });

    return new PagedListDto(
      contacts.map(contact => this.toResponseDto(contact)),
      total,
      page,
      limit,
    );
  }

  async findById(id: string): Promise<ContactUsResponseDto> {
    const contact = await this.contactRepository.findOne({ where: { id } });
    if (!contact) {
      throw new NotFoundException('Contact not found');
    }
    return this.toResponseDto(contact);
  }

  async respond(id: string, responseDto: AdminResponseDto, adminId: string): Promise<ContactUsResponseDto> {
    const contact = await this.contactRepository.findOne({ where: { id } });
    if (!contact) {
      throw new NotFoundException('Contact not found');
    }

    contact.adminResponse = responseDto.response;
    contact.status = responseDto.status;
    contact.respondedBy = adminId;
    contact.respondedAt = getCurrentUTCDate();

    const updated = await this.contactRepository.save(contact);
    this.logger.log(`Admin ${adminId} responded to contact ${id}`);
    
    // Send email response to the guest
    try {
      await this.sendResponseEmail(updated);
      this.logger.log(`Response email sent to ${updated.email} for contact ${id}`);
    } catch (error) {
      this.logger.error(`Failed to send response email to ${updated.email}: ${error.message}`, error.stack);
      // Don't throw error - response was saved successfully
    }
    
    return this.toResponseDto(updated);
  }

  private async sendResponseEmail(contact: ContactUs): Promise<void> {
    const subject = `Re: ${contact.subject}`;
    const text = `Dear ${contact.name},\n\nThank you for contacting us. Here is our response to your inquiry:\n\n${contact.adminResponse}\n\nIf you have any further questions, please don't hesitate to contact us.\n\nBest regards,\nHEC Support Team`;
    
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
        <div style="text-align: center; margin-bottom: 20px;">
          <h2 style="color: #333;">HEC Support Response</h2>
        </div>
        <div style="margin-bottom: 20px;">
          <p>Dear <strong>${contact.name}</strong>,</p>
          <p>Thank you for contacting us. Here is our response to your inquiry:</p>
          
          <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h4 style="margin: 0 0 10px 0; color: #333;">Your Original Message:</h4>
            <p style="margin: 0; color: #666; font-style: italic;">${contact.message}</p>
          </div>
          
          <div style="background-color: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #4CAF50;">
            <h4 style="margin: 0 0 10px 0; color: #333;">Our Response:</h4>
            <p style="margin: 0; color: #333; white-space: pre-line;">${contact.adminResponse}</p>
          </div>
          
          <p>If you have any further questions, please don't hesitate to contact us.</p>
          <p>Best regards,<br><strong>HEC Support Team</strong></p>
        </div>
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
          <p>This is an automated response to your contact form submission.</p>
          <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
        </div>
      </div>
    `;
    
    await this.emailService.sendEmail(contact.email, subject, text, html);
  }

  private toResponseDto(contact: ContactUs): ContactUsResponseDto {
    return {
      id: contact.id,
      name: contact.name,
      email: contact.email,
      phone: contact.phone,
      subject: contact.subject,
      message: contact.message,
      status: contact.status,
      adminResponse: contact.adminResponse,
      respondedBy: contact.respondedBy,
      respondedAt: contact.respondedAt,
      createdAt: contact.createdAt,
    };
  }
}