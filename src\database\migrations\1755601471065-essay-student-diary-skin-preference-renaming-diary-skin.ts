import { MigrationInterface, QueryRunner } from "typeorm";

export class EssayStudentDiarySkinPreferenceRenamingDiarySkin1755601471065 implements MigrationInterface {
    name = 'EssayStudentDiarySkinPreferenceRenamingDiarySkin1755601471065'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "essay_module_skin_preference" ADD "diary_skin_id" uuid`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "essay_module_skin_preference" DROP COLUMN "diary_skin_id"`);
    }

}
