import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateWaterfallTrueFalseQuestions1734567890001 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'waterfall_true_false_question',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'gen_random_uuid()',
          },
          {
            name: 'statement',
            type: 'text',
          },
          {
            name: 'correct_answer',
            type: 'boolean',
          },
          {
            name: 'set_id',
            type: 'uuid',
          },
          {
            name: 'time_limit_in_seconds',
            type: 'integer',
            isNullable: true,
          },
          {
            name: 'level',
            type: 'integer',
            isNullable: true,
          },
          {
            name: 'is_active',
            type: 'boolean',
            default: true,
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'created_by',
            type: 'uuid',
            isNullable: true,
          },
          {
            name: 'updated_by',
            type: 'uuid',
            isNullable: true,
          },
        ],
        foreignKeys: [
          {
            columnNames: ['set_id'],
            referencedTableName: 'waterfall_set',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
          },
        ],
      }),
      true,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('waterfall_true_false_question');
  }
}