import { Logger } from '@nestjs/common';
import { Repository, In } from 'typeorm';
import { Plan, PlanType, SubscriptionType } from '../database/entities/plan.entity';
import { PlanFeature, FeatureType } from '../database/entities/plan-feature.entity';

export async function seedPlanFeatures(logger: Logger, planFeatureRepository: Repository<PlanFeature>): Promise<void> {
  logger.log('Seeding plan features...');

  // Define the features to seed
  const featuresToSeed = [
    {
      type: FeatureType.HEC_USER_DIARY,
      name: 'Hec User Diary',
      description: 'Access to the HEC User Diary platform',
    },
    {
      type: FeatureType.HEC_PLAY,
      name: 'HEC Play',
      description: 'Access to the HEC Play platform',
    },
    {
      type: FeatureType.ENGLISH_QA_WRITING,
      name: 'English Q&A Writing Platform',
      description: 'Access to the English Q&A Writing Platform',
    },
    {
      type: FeatureType.ENGLISH_ESSAY,
      name: 'English Essay Platform',
      description: 'Access to the English Essay Platform',
    },
    {
      type: FeatureType.ENGLISH_NOVEL,
      name: 'English Novel Platform',
      description: 'Access to the English Novel Platform',
    },
    {
      type: FeatureType.HEC_SC,
      name: 'HEC SC(Sentences Copying)',
      description: 'Access to the HEC Sentences Copying platform',
    }
  ];

  // Create or update each feature
  for (const featureData of featuresToSeed) {
    const existingFeature = await planFeatureRepository.findOne({ where: { type: featureData.type } });

    if (existingFeature) {
      // Check if update is needed
      const needsUpdate = existingFeature.name !== featureData.name || 
                         existingFeature.description !== featureData.description;
      
      if (needsUpdate) {
        existingFeature.name = featureData.name;
        existingFeature.description = featureData.description;
        existingFeature.updatedAt = new Date();
        await planFeatureRepository.save(existingFeature);
        logger.log(`Updated feature: ${featureData.name}`);
      } else {
        logger.log(`Feature ${featureData.name} is up to date`);
      }
    } else {
      // Create new feature
      const feature = new PlanFeature();
      feature.type = featureData.type;
      feature.name = featureData.name;
      feature.description = featureData.description;
      feature.createdAt = new Date();
      feature.updatedAt = new Date();
      await planFeatureRepository.save(feature);
      logger.log(`Created feature: ${featureData.name}`);
    }
  }

  logger.log('Plan features seeding completed.');
}

export async function seedPlans(logger: Logger, planRepository: Repository<Plan>, planFeatureRepository: Repository<PlanFeature>): Promise<void> {
  logger.log('Seeding plans...');

  // Get all features
  const hecUserDiary = await planFeatureRepository.findOne({ where: { type: FeatureType.HEC_USER_DIARY } });
  const hecPlay = await planFeatureRepository.findOne({ where: { type: FeatureType.HEC_PLAY } });
  const englishQA = await planFeatureRepository.findOne({ where: { type: FeatureType.ENGLISH_QA_WRITING } });
  const englishEssay = await planFeatureRepository.findOne({ where: { type: FeatureType.ENGLISH_ESSAY } });
  const englishNovel = await planFeatureRepository.findOne({ where: { type: FeatureType.ENGLISH_NOVEL } });
  const hecSC = await planFeatureRepository.findOne({ where: { type: FeatureType.HEC_SC } });

  if (!hecUserDiary || !hecPlay || !englishQA || !englishEssay || !englishNovel) {
    throw new Error('One or more required features not found. Please seed features first.');
  }

  // Define the plans to seed
  const plansToSeed = [
    // Starter Plans
    {
      name: 'Starter Monthly',
      type: PlanType.STARTER,
      subscriptionType: SubscriptionType.MONTHLY,
      description: 'Starter Plan - ₩ 27,500/month - Includes HEC Diary, HEC Play, and HEC SC(Sentences Copying)',
      price: 27500,
      durationDays: 30,
      autoRenew: true,
      featureIds: [hecUserDiary.id, hecPlay.id, hecSC.id],
      isActive: true,
    },
    {
      name: 'Starter Yearly',
      type: PlanType.STARTER,
      subscriptionType: SubscriptionType.YEARLY,
      description: 'Starter Plan - ₩ 275,000/year - Includes HEC Diary, HEC Play, and HEC SC(Sentences Copying)',
      price: 275000,
      durationDays: 365,
      autoRenew: true,    
      featureIds: [hecUserDiary.id, hecPlay.id, hecSC.id],
      isActive: true,
    },

    // Standard Plans
    {
      name: 'Standard Monthly',
      type: PlanType.STANDARD,
      subscriptionType: SubscriptionType.MONTHLY,
      description: 'Standard Plan - ₩ 38,500/month - Includes HEC Q&A, HEC Diary, HEC Play and HEC SC(Sentences Copying)',
      price: 38500,
      durationDays: 30,
      autoRenew: true,
      featureIds: [hecUserDiary.id, hecPlay.id, englishQA.id, hecSC.id],
      isActive: true,
    },
    {
      name: 'Standard Yearly',
      type: PlanType.STANDARD,
      subscriptionType: SubscriptionType.YEARLY,
      description: 'Standard Plan - ₩ 385,000/year - Includes HEC Q&A, HEC Diary, HEC Play and HEC SC(Sentences Copying)',
      price: 385000,
      durationDays: 365,
      autoRenew: true,
      featureIds: [hecUserDiary.id, hecPlay.id, englishQA.id, hecSC.id],
      isActive: true,
    },

    // Pro Plans
    {
      name: 'Pro Monthly',
      type: PlanType.PRO,
      subscriptionType: SubscriptionType.MONTHLY,
      description: 'Pro Plan - ₩ 55,000/month – Includes HEC Essay, HEC Q&A, HEC Diary, HEC Play and HEC SC(Sentences Copying)',
      price: 55000,
      durationDays: 30,
      autoRenew: true,
      featureIds: [hecUserDiary.id, hecPlay.id, englishQA.id, englishEssay.id, hecSC.id],
      isActive: true,
    },
    {
      name: 'Pro Yearly',
      type: PlanType.PRO,
      subscriptionType: SubscriptionType.YEARLY,
      description: 'Pro Plan - ₩ 550,000/year – Includes HEC Essay, HEC Q&A, HEC Diary, HEC Play and HEC SC(Sentences Copying) ',
      price: 550000,
      durationDays: 365,
      autoRenew: true,
      featureIds: [hecUserDiary.id, hecPlay.id, englishQA.id, englishEssay.id, hecSC.id],
      isActive: true,
    },

    // Ultimate Plans
    {
      name: 'Ultimate Monthly',
      type: PlanType.ULTIMATE,
      subscriptionType: SubscriptionType.MONTHLY,
      description: 'Ultimate Plan - ₩ 99,000/month - Includes all features: HEC Novel, HEC Essay, HEC Q&A, HEC Diary, HEC Play and HEC SC(Sentences Copying)',
      price: 99000,
      durationDays: 30,
      autoRenew: true,
      featureIds: [hecUserDiary.id, hecPlay.id, englishQA.id, englishEssay.id, englishNovel.id, hecSC.id],
      isActive: true,
    },
    {
      name: 'Ultimate Yearly',
      type: PlanType.ULTIMATE,
      subscriptionType: SubscriptionType.YEARLY,
      description: 'Ultimate Plan - ₩ 990,000/year - Includes all features: HEC Novel, HEC Essay, HEC Q&A, HEC Diary, HEC Play and HEC SC(Sentences Copying) ',
      price: 990000,
      durationDays: 365,
      autoRenew: true,
      featureIds: [hecUserDiary.id, hecPlay.id, englishQA.id, englishEssay.id, englishNovel.id, hecSC.id],
      isActive: true,
    },
  ];

  // Create or update each plan
  for (const planData of plansToSeed) {
    const existingPlan = await planRepository.findOne({ 
      where: { name: planData.name },
      relations: ['planFeatures']
    });

    // Get features for comparison
    const features = planData.featureIds && planData.featureIds.length > 0
      ? await planFeatureRepository.find({ where: { id: In(planData.featureIds) } })
      : [];

    if (existingPlan) {
      // Check if update is needed
      const currentFeatureIds = existingPlan.planFeatures?.map(f => f.id).sort() || [];
      const newFeatureIds = features.map(f => f.id).sort();
      
      const needsUpdate = 
        existingPlan.type !== planData.type ||
        existingPlan.subscriptionType !== planData.subscriptionType ||
        existingPlan.description !== planData.description ||
        existingPlan.price !== planData.price ||
        existingPlan.durationDays !== planData.durationDays ||
        existingPlan.autoRenew !== planData.autoRenew ||
        existingPlan.isActive !== planData.isActive ||
        JSON.stringify(currentFeatureIds) !== JSON.stringify(newFeatureIds);

      if (needsUpdate) {
        existingPlan.type = planData.type;
        existingPlan.subscriptionType = planData.subscriptionType;
        existingPlan.description = planData.description;
        existingPlan.price = planData.price;
        existingPlan.durationDays = planData.durationDays;
        existingPlan.autoRenew = planData.autoRenew;
        existingPlan.isActive = planData.isActive;
        existingPlan.planFeatures = features;
        existingPlan.updatedAt = new Date();
        await planRepository.save(existingPlan);
        logger.log(`Updated plan: ${planData.name}`);
      } else {
        logger.log(`Plan ${planData.name} is up to date`);
      }
    } else {
      // Create new plan
      const plan = new Plan();
      plan.name = planData.name;
      plan.type = planData.type;
      plan.subscriptionType = planData.subscriptionType;
      plan.description = planData.description;
      plan.price = planData.price;
      plan.durationDays = planData.durationDays;
      plan.autoRenew = planData.autoRenew;
      plan.isActive = planData.isActive;
      plan.planFeatures = features;
      plan.createdAt = new Date();
      plan.updatedAt = new Date();
      await planRepository.save(plan);
      logger.log(`Created plan: ${planData.name}`);
    }
  }

  logger.log('Plans seeding completed.');
}
