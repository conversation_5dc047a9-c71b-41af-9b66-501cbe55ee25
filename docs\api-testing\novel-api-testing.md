# Novel Module API Testing Flow

This document outlines the testing flow for the Novel Module API endpoints.

## Prerequisites

Before testing the Novel API:

1. Ensure the HEC backend is running
2. Have valid authentication tokens for students
3. Ensure student has subscription with ENGLISH_NOVEL feature
4. Set up your API testing tool (<PERSON><PERSON> recommended)

## Novel Topic Management Testing Flow

### Test Case 1: Get Active Topics

1. Authenticate with a student token
2. Send a GET request to `/api/student/novel/topics`
3. Verify HTTP status code is 200 OK
4. Verify response contains array of active topics
5. Verify topics include title, description, category, deadline

### Test Case 2: Filter Topics by Category

1. Send a GET request to `/api/student/novel/topics?category=monthly`
2. Verify only monthly topics are returned
3. Send a GET request to `/api/student/novel/topics?category=quarterly`
4. Verify only quarterly topics are returned
5. Test with invalid category and verify appropriate response

### Test Case 3: Get Specific Topic

1. Send a GET request to `/api/student/novel/topics/{id}`
2. Verify HTTP status code is 200 OK
3. Verify response contains complete topic details
4. Test with non-existent topic ID and verify 404 response

## Novel Entry Creation Testing Flow

### Test Case 1: Get or Create Entry by Topic

1. Authenticate with a student token
2. Send a GET request to `/api/student/novel/entries/topic/{topicId}`
3. Verify HTTP status code is 200 OK
4. If first time: verify new entry is created with default skin
5. If existing: verify existing entry is returned
6. Verify entry is associated with the topic and student

### Test Case 2: Entry Auto-Creation Validation

1. Test with inactive topic ID
2. Test with non-existent topic ID
3. Test without default skin configured
4. Verify appropriate error responses for each case

## Novel Writing Process Testing Flow

### Test Case 1: Update Novel Entry

1. Authenticate with a student token
2. Get or create a novel entry
3. Send a PUT request to `/api/student/novel/entries/{id}`:
   ```json
   {
     "content": "Once upon a time, in a magical kingdom...",
     "skinId": "123e4567-e89b-12d3-a456-426614174002",
     "backgroundColor": "#f5f5f5"
   }
   ```
4. Verify HTTP status code is 200 OK
5. Verify content is saved
6. Verify version history is created

### Test Case 2: Content Validation

1. Test with empty content
2. Test with extremely long content (100,000+ characters)
3. Test with invalid skinId
4. Test with invalid backgroundColor format
5. Verify appropriate validation errors

### Test Case 3: Entry Update Restrictions

1. Test updating entry that doesn't belong to student
2. Test updating entry that's already submitted
3. Test updating entry that's being reviewed
4. Verify appropriate authorization and status errors

## Novel Submission Testing Flow

### Test Case 1: Submit Novel Entry

1. Authenticate with a student token
2. Update novel entry with content
3. Send a POST request to `/api/student/novel/entries/submit`:
   ```json
   {
     "entryId": "123e4567-e89b-12d3-a456-426614174000",
     "content": "Final novel content...",
     "skinId": "123e4567-e89b-12d3-a456-426614174001",
     "backgroundColor": "#e8f4fd"
   }
   ```
4. Verify HTTP status code is 200 OK
5. Verify entry status changes to "SUBMITTED"
6. Verify final version is saved
7. Verify tutor assignment notification is triggered

### Test Case 2: Submit Without Updates

1. Send a POST request with only entryId:
   ```json
   {
     "entryId": "123e4567-e89b-12d3-a456-426614174000"
   }
   ```
2. Verify submission uses existing content
3. Verify submission is successful

### Test Case 3: Submission Validation

1. Test submitting non-existent entry
2. Test submitting entry that doesn't belong to student
3. Test submitting already reviewed entry
4. Test submitting with invalid updates
5. Verify appropriate error responses

## Novel Entry Retrieval Testing Flow

### Test Case 1: Get Student Entries

1. Authenticate with a student token
2. Send a GET request to `/api/student/novel/entries`
3. Verify HTTP status code is 200 OK
4. Verify response contains array of student's entries
5. Verify only student's own entries are returned

### Test Case 2: Filter Entries by Category

1. Send a GET request to `/api/student/novel/entries?category=monthly`
2. Verify only entries for monthly topics are returned
3. Test with quarterly category
4. Verify filtering works correctly

### Test Case 3: Get Specific Entry

1. Send a GET request to `/api/student/novel/entries/{id}`
2. Verify HTTP status code is 200 OK
3. Verify response contains complete entry details
4. Verify tutor feedback is included if available
5. Test with non-existent entry and verify 404 response

## Novel Entry Search Testing Flow

### Test Case 1: Search by Date

1. Send a GET request to `/api/student/novel/entries/search?date=2024-01-15`
2. Verify entries from specified date are returned
3. Test with date range
4. Test with invalid date format

### Test Case 2: Search by Sequence

1. Send a GET request to `/api/student/novel/entries/search?sequence=1`
2. Verify entries with specified sequence are returned
3. Test with different sequence numbers

### Test Case 3: Search by Title

1. Send a GET request to `/api/student/novel/entries/search?title=adventure`
2. Verify entries with matching titles are returned
3. Test partial title matching
4. Test case-insensitive search

### Test Case 4: Combined Search

1. Test search with multiple parameters
2. Verify AND logic is applied correctly
3. Test with no matching results
4. Verify pagination works with search results

## Version History Testing Flow

### Test Case 1: Get Entry History

1. Authenticate with a student token
2. Update a novel entry multiple times
3. Send a GET request to `/api/student/novel/entries/{id}/history`
4. Verify HTTP status code is 200 OK
5. Verify response contains all versions
6. Verify versions are ordered by creation date

### Test Case 2: Get Specific Version

1. Send a GET request to `/api/student/novel/entries/{id}/versions/{versionId}`
2. Verify HTTP status code is 200 OK
3. Verify response contains version content
4. Test with non-existent version and verify 404 response

### Test Case 3: Restore Previous Version

1. Send a PUT request to `/api/student/novel/entries/{id}/versions/{versionId}/restore`
2. Verify HTTP status code is 200 OK
3. Verify entry content is updated to restored version
4. Verify new version is created for the restoration
5. Test restoring non-existent version and verify error

### Test Case 4: Version History Permissions

1. Test accessing version history of other student's entry
2. Test restoring version of other student's entry
3. Verify proper authorization checks
4. Verify appropriate error responses

## Novel Skin Management Testing Flow

### Test Case 1: Get Available Skins

1. Send a GET request to `/api/student/novel/skins` with pagination
2. Verify HTTP status code is 200 OK
3. Verify response contains available skins
4. Verify student's purchased skins are included
5. Verify skin accessibility based on ownership

### Test Case 2: Set Default Novel Skin

1. Send a PUT request to `/api/student/novel/skins/{id}/set-as-default`
2. Verify HTTP status code is 200 OK
3. Verify default skin preference is saved
4. Verify new entries use the default skin
5. Test with inaccessible skin and verify error

### Test Case 3: Get Default Novel Skin

1. Send a GET request to `/api/student/novel/skins/my-default`
2. Verify HTTP status code is 200 OK
3. Verify response contains current default skin
4. Test when no default is set

## Novel Suggestions Testing Flow

### Test Case 1: Create Novel Suggestion

1. Send a POST request to `/api/student/novel/suggestions`:
   ```json
   {
     "description": "A story about magical creatures and their adventures in an enchanted forest"
   }
   ```
2. Verify HTTP status code is 201 Created
3. Verify suggestion is saved with student association
4. Verify suggestion status is set to "PENDING"

### Test Case 2: Get Student Suggestions

1. Send a GET request to `/api/student/novel/suggestions`
2. Verify HTTP status code is 200 OK
3. Verify response contains student's suggestions
4. Verify only student's own suggestions are returned
5. Verify suggestions include status and timestamps

### Test Case 3: Suggestion Validation

1. Test creating suggestion with empty description
2. Test creating suggestion with extremely long description
3. Test creating duplicate suggestions
4. Verify appropriate validation errors

## Tutor Greeting Testing Flow

### Test Case 1: Set Tutor Greeting

1. Send a POST request to `/api/student/novel/greeting`:
   ```json
   {
     "greeting": "Hello, please review my novel and provide detailed feedback!"
   }
   ```
2. Verify HTTP status code is 200 OK
3. Verify greeting is saved for the student
4. Verify greeting appears in tutor interface

### Test Case 2: Greeting Validation

1. Test with empty greeting
2. Test with extremely long greeting
3. Test with special characters
4. Verify appropriate validation rules

## Integration Testing Flow

### Test Case 1: Complete Novel Writing Workflow

1. **Topic Selection**
   - Browse available topics
   - Select monthly or quarterly topic

2. **Entry Creation**
   - Get or create entry for selected topic
   - Verify entry initialization

3. **Writing Process**
   - Update entry content multiple times
   - Apply different skins and backgrounds
   - Verify version history creation

4. **Submission**
   - Submit final novel entry
   - Verify submission confirmation
   - Check tutor assignment

5. **Review Process**
   - Wait for tutor review
   - Check feedback notifications
   - View reviewed entry

### Test Case 2: Multiple Novel Management

1. Work on multiple novel topics simultaneously
2. Verify each entry maintains separate state
3. Submit novels in different order
4. Verify all novels are tracked correctly

### Test Case 3: Version Management Workflow

1. Create novel entry
2. Make multiple updates
3. View version history
4. Restore previous version
5. Continue editing from restored version
6. Verify version chain integrity

## Performance Testing Flow

### Test Case 1: Large Content Handling

1. Test with novel content over 50,000 words
2. Verify update performance remains acceptable
3. Test version history with large content
4. Verify search performance with large entries

### Test Case 2: Version History Performance

1. Create entry with 100+ versions
2. Test history retrieval performance
3. Test version restoration performance
4. Verify pagination in version history

## Security Testing Flow

### Test Case 1: Access Control

1. Attempt to access other students' novels
2. Verify proper authorization checks
3. Test cross-student version access
4. Verify skin access permissions

### Test Case 2: Content Security

1. Test XSS prevention in novel content
2. Test script injection in content
3. Verify content sanitization
4. Test malicious file uploads (if applicable)

## Edge Cases Testing Flow

### Test Case 1: Deadline Scenarios

1. Submit novel exactly at topic deadline
2. Test access after deadline expiry
3. Verify deadline enforcement
4. Test timezone handling for deadlines

### Test Case 2: Concurrent Editing

1. Edit same novel from multiple devices
2. Test simultaneous updates
3. Verify conflict resolution
4. Test version creation during conflicts

### Test Case 3: Data Consistency

1. Test entry updates during submission
2. Verify data integrity during version restoration
3. Test skin changes during active editing
4. Verify consistent state across operations

This comprehensive testing flow ensures the Novel Module functions correctly across all user scenarios and maintains data integrity throughout the writing process.