import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { RelatedEntityType } from '../../common/enums/related-entity-type.enum';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { QATaskSubmissionMarking } from 'src/database/entities/qa-task-submission-marking.entity';
import { QATaskSubmissions, QASubmissionStatus } from 'src/database/entities/qa-task-submissions.entity';
import { QATaskSubmissionHistory } from 'src/database/entities/qa-task-submission-history.entity';
import { AsyncNotificationHelperService } from '../notification/async-notification-helper.service';
import { NotificationType } from '../../database/entities/notification.entity';
import { DeeplinkService, DeeplinkType } from '../../common/utils/deeplink.service';
import { QATaskSubmissionMarkingDto, CreateQATaskSubmissionMarkingDto } from 'src/database/models/qa-mission.dto';
import { TutorStudentDto } from 'src/database/models/tutor-matching.dto';
import { FeatureType } from 'src/database/entities/plan-feature.entity';
import { User } from 'src/database/entities/user.entity';
import { StudentTutorMapping } from 'src/database/entities/student-tutor-mapping.entity';
import { PaginationDto } from 'src/common/models/pagination.dto';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { PaginationService } from '../../common/services/pagination.service';
import { QASubmissionWithDetailsDto } from 'src/database/models/qa.dto';
import { QAMissionTasks } from 'src/database/entities/qa-mission-tasks.entity';

@Injectable()
export class TutorQAMissionService {
  private readonly logger = new Logger(TutorQAMissionService.name);

  constructor(
    private readonly paginationService: PaginationService,
    @InjectRepository(QATaskSubmissions)
    private readonly qaTaskSubmissionsRepository: Repository<QATaskSubmissions>,
    @InjectRepository(QATaskSubmissionHistory)
    private readonly qaTaskSubmissionHistoryRepository: Repository<QATaskSubmissionHistory>,
    @InjectRepository(QAMissionTasks)
    private readonly qaMissionTasksRepository: Repository<QAMissionTasks>,
    @InjectRepository(QATaskSubmissionMarking)
    private readonly qaTaskSubmissionMarkingRepository: Repository<QATaskSubmissionMarking>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(StudentTutorMapping)
    private readonly studentTutorMappingRepository: Repository<StudentTutorMapping>,
    private dataSource: DataSource,
    private readonly asyncNotificationHelper: AsyncNotificationHelperService,
    private readonly deeplinkService: DeeplinkService,
  ) {}

  private toQATaskSubmissionMarkingDto(taskSubmissionMark: QATaskSubmissionMarking): QATaskSubmissionMarkingDto {
    return {
      id: taskSubmissionMark.id,
      submissionId: taskSubmissionMark.submissionId,
      submissionHistoryId: taskSubmissionMark.submissionHistoryId,
      submissionFeedback: taskSubmissionMark.submissionFeedback,
      score: taskSubmissionMark.score,
      taskRemarks: taskSubmissionMark.taskRemarks,
    };
  }

  async getTutorStudents(tutorId: string): Promise<TutorStudentDto[]> {
    try {
      // Check if tutor exists
      const tutor = await this.userRepository.findOne({ where: { id: tutorId } });
      if (!tutor) {
        throw new NotFoundException(`Tutor with ID ${tutorId} not found`);
      }

      // Get assignments
      const assignments = await this.studentTutorMappingRepository.find({
        where: {
          tutorId,
          planFeature: {
            type: FeatureType.ENGLISH_QA_WRITING,
          },
        },
        relations: ['student', 'planFeature'],
      });

      // Map to DTOs
      return assignments.map((assignment) => ({
        id: assignment.studentId,
        name: assignment.student.name,
        email: assignment.student.email,
        profilePicture: assignment.student.profilePicture,
        planFeatureId: assignment.planFeatureId,
        moduleName: assignment.planFeature.name,
        moduleType: assignment.planFeature.type,
        status: assignment.status,
        assignedDate: assignment.assignedDate,
        lastActivityDate: assignment.lastActivityDate,
      }));
    } catch (error) {
      this.logger.error(`Error getting tutor students: ${error.message}`, error.stack);
      throw error;
    }
  }

  async markQATaskSubmission(qaSubmissionMarkDto: CreateQATaskSubmissionMarkingDto): Promise<QATaskSubmissionMarkingDto> {
    const { submissionId } = qaSubmissionMarkDto;

    const queryRunner = this.dataSource.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const submissionRepo = queryRunner.manager.getRepository(QATaskSubmissions);
      const historyRepo = queryRunner.manager.getRepository(QATaskSubmissionHistory);
      const markingRepo = queryRunner.manager.getRepository(QATaskSubmissionMarking);

      const taskSubmission = await submissionRepo.findOne({
        where: {
          id: submissionId,
          //status: QASubmissionStatus.SUBMITTED || QASubmissionStatus.REVIEWED,
        },
      });

      if (!taskSubmission) {
        throw new NotFoundException('Task submission not found');
      }

      const taskSubmissionHistory = await historyRepo.findOne({
        where: {
          id: taskSubmission.latestSubmissionId,
          submissionId: submissionId,
        },
      });

      if (!taskSubmissionHistory) {
        throw new NotFoundException('Task submission history not found');
      }

      // Update submission status to MARKED
      taskSubmission.status = QASubmissionStatus.REVIEWED;
      await submissionRepo.save(taskSubmission);

      let savedMark: QATaskSubmissionMarking;

      const existingMark = await markingRepo.findOne({
        where: { submissionId: submissionId, submissionHistoryId: taskSubmissionHistory.id },
      });

      if (existingMark) {
        await queryRunner.manager.update(
          QATaskSubmissionMarking,
          { id: existingMark.id },
          {
            score: existingMark.score,
            submissionFeedback: qaSubmissionMarkDto.submissionFeedback,
            taskRemarks: qaSubmissionMarkDto.taskRemarks,
          },
        );

        savedMark = await markingRepo.findOne({ where: { id: existingMark.id } });
      } else {
        // Create marking record
        const taskSubmissionMark = markingRepo.create({
          submissionId: submissionId,
          submissionHistoryId: taskSubmissionHistory.id,
          submissionFeedback: qaSubmissionMarkDto.submissionFeedback,
          score: qaSubmissionMarkDto.score,
          taskRemarks: qaSubmissionMarkDto.taskRemarks,
        });

        savedMark = await markingRepo.save(taskSubmissionMark);
      }

      // await queryRunner.commitTransaction();

      // Send notification to the student
      try {
        // Get the student ID from the submission
        const studentId = taskSubmission.createdBy;

        if (studentId) {
          // Generate deeplinks
          const webLink = this.deeplinkService.getWebLink(DeeplinkType.QA_MISSION_REVIEW, {
            id: taskSubmission.taskId,
          });

          const deepLink = this.deeplinkService.getDeepLink(DeeplinkType.QA_MISSION_REVIEW, {
            id: taskSubmission.taskId,
          });

          // Create HTML content for rich notifications
          const htmlContent = `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #333;">QA Mission Submission Reviewed</h2>
              <p>Your QA Mission submission has been reviewed by your tutor.</p>
              <p><strong>Score:</strong> ${qaSubmissionMarkDto.score}</p>
              <div style="margin: 20px 0;">
                <a href="${webLink}" style="background-color: #4CAF50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; display: inline-block;">View Review</a>
              </div>
            </div>
          `;

          // Send notification
          await this.asyncNotificationHelper.notifyAsync(studentId, NotificationType.SYSTEM, 'QA Mission Submission Reviewed', `Your QA Mission submission has been reviewed by your tutor.`, {
            relatedEntityId: taskSubmission.taskId,
            relatedEntityType: RelatedEntityType.QA_MISSION_SUBMISSION,
            htmlContent: htmlContent,
            webLink: webLink,
            deepLink: deepLink,
            sendEmail: true,
            sendPush: true,
            sendInApp: true,
            sendRealtime: false,
          });

          this.logger.log(`Sent notification to student ${studentId} for submission ${submissionId}`);
        }
      } catch (notificationError) {
        // Log the error but don't fail the marking process
        this.logger.error(`Error sending notification: ${notificationError.message}`, notificationError.stack);
      }

      return this.toQATaskSubmissionMarkingDto(savedMark);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to mark QA submission: ${error.message}`);
    } finally {
      await queryRunner.release();
    }
  }

  // async getStudentSubmissionsForTutor(
  //       tutorId: string,
  //       paginationDto: PaginationDto
  //     ): Promise<ApiResponse<PagedListDto<QASubmissionListResponseDto>>> {
  //       const { skip, take } = this.paginationService.getPaginationParameters(paginationDto);
  //   // Step 1: Get assigned students
  //   const students = await this.getTutorStudents(tutorId);
  //   const studentUserIds = students.map(s => s.id);

  //   //if (!studentUserIds.length) return [];
  //   if (studentUserIds.length === 0) {
  //     return ApiResponse.success(
  //       this.paginationService.createPagedList([], 0, paginationDto),
  //       'No pending submissions found for your students'
  //     );
  //   }

  //   // Step 2: Get submissions with task and submission history where createdBy is in studentUserIds
  //   const submissions = await this.qaTaskSubmissionsRepository
  //     .createQueryBuilder("submission")
  //     .leftJoinAndSelect("submission.task", "task")
  //     .leftJoinAndSelect("submission.submissionHistory", "submissionHistory")
  //     .where("submission.createdBy IN (:...studentUserIds)", { studentUserIds })
  //     .getMany();

  //   return submissions;
  // }

  async getStudentSubmissionsForTutor(tutorId: string, paginationDto: PaginationDto, { timeFrequency }): Promise<ApiResponse<PagedListDto<QASubmissionWithDetailsDto>>> {
    try {
      const students = await this.getTutorStudents(tutorId);
      const studentUserIds = students.map((s) => s.id);

      if (studentUserIds.length === 0) {
        return ApiResponse.success(this.paginationService.createPagedList([], 0, paginationDto), 'No pending submissions found for your students');
      }

      const queryBuilder = this.qaTaskSubmissionsRepository
        .createQueryBuilder('submission')
        .leftJoinAndSelect('submission.task', 'task')
        .leftJoin('task.mission', 'mission')
        .leftJoin(User, 'student', 'student.id = submission.created_by::uuid')
        .addSelect([
          'student.id AS student_id',
          'student.name AS student_name',
          'student.email AS student_email',
          'mission.id AS mission_id',
          'mission.time_frequency AS mission_time_frequency',
          'mission.sequence_number AS mission_sequence_number',
          'mission.week_id AS mission_week_id',
          'mission.month_id AS mission_month_id',
          'submission.status AS status',
          'submission.currentRevision AS currentRevision',
          'submission.createdBy AS createdBy',
          'submission.createdAt AS createdAt',
        ])
        //.where('submission.createdBy IN (:...studentUserIds)', { studentUserIds });
        .where('submission.createdBy IN (:...studentUserIds) AND submission.status = :status', { studentUserIds, status: QASubmissionStatus.SUBMITTED });
      if (timeFrequency) {
        queryBuilder.andWhere('mission.time_frequency = :timeFrequency', { timeFrequency });
      }

      const submissions = await queryBuilder.getRawMany();

      const totalCount = submissions.length;

      const result: QASubmissionWithDetailsDto[] = [];

      for (const submission of submissions) {
        const history = await this.qaTaskSubmissionHistoryRepository.findOne({
          where: {
            submissionId: submission.submission_id,
            createdBy: submission.createdBy,
          },
          order: {
            submissionDate: 'DESC',
            sequenceNumber: 'DESC',
          },
        });

        result.push({
          id: submission.id,
          status: submission.status,
          currentRevision: submission.currentRevision,
          createdBy: submission.createdBy,
          createdAt: submission.createdAt,
          student_email: submission.student_email,
          student_id: submission.student_id,
          student_name: submission.student_name,
          mission_id: submission.mission_id,
          mission_week_id: submission.mission_week_id,
          mission_month_id: submission.mission_month_id,
          mission_sequence_number: submission.mission_sequence_number,
          mission_time_frequency: submission.mission_time_frequency,
          task: {
            id: submission.task?.id,
            title: submission.task?.title,
            description: submission.task?.description,
          },
          // submissionHistory: history.map(h => ({
          //   id: h.id,
          //   content: h.content,
          //   wordCount: h.wordCount,
          //   submissionDate: h.submissionDate
          // }))
          submissionHistory: history
            ? [
                {
                  id: history.id,
                  content: history.content,
                  wordCount: history.wordCount,
                  submissionDate: history.submissionDate,
                },
              ]
            : [],
        });
      }
      return ApiResponse.success(this.paginationService.createPagedList(result, totalCount, paginationDto), 'Submissions retrieved successfully');

      //return ApiResponse.success(result, 'Submissions retrieved successfully');
    } catch (error) {
      this.logger.error(`Error getting student submissions for tutor: ${error.message}`, error.stack);
      throw error;
    }
  }

  async findTaskSubmissionById(id: string): Promise<any> {
    try {
      const task = await this.qaMissionTasksRepository.findOne({
        where: { id, isActive: true },
        relations: ['mission', 'submissions'],
      });

      if (!task) {
        throw new NotFoundException(`QA task with ID ${id} not found`);
      }

      const history = await this.qaTaskSubmissionHistoryRepository.findOne({
        where: {
          submissionId: task.submissions[0].id,
          createdBy: task.submissions[0].createdBy,
        },
        order: {
          submissionDate: 'DESC',
          sequenceNumber: 'DESC',
        },
      });

      return this.toQATaskResponseDto(task);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to fetch QA task: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to fetch QA task');
    }
  }

  // async getSubmissionHistoryById(id: string): Promise<QATaskSubmissionHistory> {
  //   const submissionHistory = await this.qaTaskSubmissionHistoryRepository.findOne({
  //     where: { id },
  //     relations: ['submission', 'submission.task']
  //     // relations: {
  //     //   submission: {
  //     //     task: true,
  //     //   },
  //     // },
  //   });

  //   const submission = await this.qaTaskSubmissionsRepository.findOne({
  //     where: { id: submissionHistory.submissionId },
  //     relations: ['task']
  //     // relations: {
  //     //   submission: {
  //     //     task: true,
  //     //   },
  //     // },
  //   });

  //   if (!submissionHistory) {
  //     throw new NotFoundException(`Submission history with id ${id} not found.`);
  //   }

  //   //return submissionHistory;
  //   return {
  //   submissionHistory: {
  //     id: submissionHistory.id,
  //     content: submissionHistory.content,
  //     wordCount: submissionHistory.wordCount,
  //     submissionDate: submissionHistory.submissionDate,
  //     sequenceNumber: submissionHistory.sequenceNumber,
  //     metaData: submissionHistory.metaData,
  //     createdAt: submissionHistory.createdAt,
  //     updatedAt: submissionHistory.updatedAt,
  //     // ...other fields you want to expose
  //   },
  //   submission: submission ? {
  //     id: submission.id,
  //     status: submission.status,
  //     currentRevision: submission.currentRevision,
  //     isActive: submission.isActive,
  //     createdBy: submission.createdBy,
  //     createdAt: submission.createdAt,
  //     updatedAt: submission.updatedAt,
  //     latestSubmissionId: submission.latestSubmissionId,
  //     taskId: submission.taskId,
  //     submissionHistory: null,
  //     task: submission?.task ? {
  //       id: submission.task.id,
  //       title: submission.task.title,
  //       description: submission.task.description,
  //       isActive: submission.task.isActive,
  //       sequence: submission.task.sequence,
  //       wordLimitMinimum: submission.task.wordLimitMinimum,
  //       wordLimitMaximum: submission.task.wordLimitMaximum,
  //       totalScore: submission.task.totalScore,
  //       deadline: submission.task.deadline,
  //       instructions: submission.task.instructions,
  //       // ...other task fields you want
  //       createdAt: submission.task.createdAt,
  //       updatedAt: submission.task.updatedAt,
  //     } : null,
  //     totalRevisions: submission.totalRevisions,
  //     firstRevisionProgress: submission.firstRevisionProgress,
  //     isFirstRevision: submission.currentRevision === 1,
  //   } : null,
  //   task: submission?.task ? {
  //     id: submission.task.id,
  //     title: submission.task.title,
  //     description: submission.task.description,
  //     isActive: submission.task.isActive,
  //     sequence: submission.task.sequence,
  //     wordLimitMinimum: submission.task.wordLimitMinimum,
  //     wordLimitMaximum: submission.task.wordLimitMaximum,
  //     totalScore: submission.task.totalScore,
  //     deadline: submission.task.deadline,
  //     instructions: submission.task.instructions,
  //     // ...other task fields you want
  //     createdAt: submission.task.createdAt,
  //     updatedAt: submission.task.updatedAt,
  //   } : null,
  // };
  // }

  async getSubmissionHistoryById(id: string): Promise<any> {
    // <-- Changed return type from Promise<QATaskSubmissionHistory> to Promise<any>
    const submissionHistory = await this.qaTaskSubmissionHistoryRepository.findOne({
      where: { id },
      relations: ['submission', 'submission.task'],
    });

    if (!submissionHistory) {
      throw new NotFoundException(`Submission history with id ${id} not found.`);
    }

    const submission = await this.qaTaskSubmissionsRepository.findOne({
      where: { id: submissionHistory.submissionId },
      relations: ['task'],
    });

    const marking = await this.qaTaskSubmissionMarkingRepository.findOne({
      where: { submissionId: submission.id, submissionHistoryId: submissionHistory.id },
    });

    // Return the custom shaped object exactly as you wanted,
    // but the method return type is relaxed to `any` to avoid TS errors
    return {
      submissionHistory: {
        id: submissionHistory.id,
        content: submissionHistory.content,
        wordCount: submissionHistory.wordCount,
        submissionDate: submissionHistory.submissionDate,
        sequenceNumber: submissionHistory.sequenceNumber,
        metaData: submissionHistory.metaData,
        createdAt: submissionHistory.createdAt,
        updatedAt: submissionHistory.updatedAt,
        // ...other fields you want to expose
      },
      submission: submission
        ? {
            id: submission.id,
            status: submission.status,
            currentRevision: submission.currentRevision,
            isActive: submission.isActive,
            createdBy: submission.createdBy,
            createdAt: submission.createdAt,
            updatedAt: submission.updatedAt,
            latestSubmissionId: submission.latestSubmissionId,
            taskId: submission.taskId,
            submissionHistory: null,
            task: submission?.task
              ? {
                  id: submission.task.id,
                  title: submission.task.title,
                  description: submission.task.description,
                  isActive: submission.task.isActive,
                  sequence: submission.task.sequence,
                  wordLimitMinimum: submission.task.wordLimitMinimum,
                  wordLimitMaximum: submission.task.wordLimitMaximum,
                  totalScore: submission.task.totalScore,
                  deadline: submission.task.deadline,
                  instructions: submission.task.instructions,
                  // ...other task fields you want
                  createdAt: submission.task.createdAt,
                  updatedAt: submission.task.updatedAt,
                }
              : null,
            totalRevisions: submission.totalRevisions,
            firstRevisionProgress: submission.firstRevisionProgress,
            isFirstSubmission: submission.currentRevision === 1,
          }
        : null,
      marking: marking
        ? {
            id: marking.id,
            submissionId: marking.submissionId,
            submissionHistoryId: marking.submissionHistoryId,
            submissionFeedback: marking.submissionFeedback,
            score: marking.score,
            taskRemarks: marking.taskRemarks,
          }
        : null,
      task: submission?.task
        ? {
            id: submission.task.id,
            title: submission.task.title,
            description: submission.task.description,
            isActive: submission.task.isActive,
            sequence: submission.task.sequence,
            wordLimitMinimum: submission.task.wordLimitMinimum,
            wordLimitMaximum: submission.task.wordLimitMaximum,
            totalScore: submission.task.totalScore,
            deadline: submission.task.deadline,
            instructions: submission.task.instructions,
            createdAt: submission.task.createdAt,
            updatedAt: submission.task.updatedAt,
          }
        : null,
    };
  }

  private toQATaskResponseDto(task: QAMissionTasks): any {
    return {
      id: task.id,
      title: task.title,
      description: task.description,
      wordLimitMinimum: task.wordLimitMinimum,
      wordLimitMaximum: task.wordLimitMaximum,
      deadline: task.deadline,
      sequence: task.sequence,
      totalScore: task.totalScore,
      instructions: task.instructions,
      isActive: task.isActive,
      missionId: task.missionId,
      createdAt: task.createdAt,
      updatedAt: task.updatedAt,
    };
  }
}
