import { Controller, Get, Post, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBody, ApiParam, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';
import { ContactService } from './contact.service';
import { CreateContactUsDto, AdminResponseDto, ContactUsResponseDto } from '../../database/models/contact-us.dto';
import { Public } from '../../common/decorators/public-api.decorator';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { AdminGuard } from '../../common/guards/admin.guard';
import { GetUser } from '../../common/decorators/get-user.decorator';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiOkResponseWithPagedListType, ApiErrorResponse } from '../../common/decorators/api-response.decorator';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';

@ApiTags('Contact Us')
@Controller('contact')
export class ContactController {
  constructor(private readonly contactService: ContactService) {}

  @Post()
  @Public()
  @ApiOperation({
    summary: 'Submit contact form (Public)',
    description: 'Submit a contact us form. This endpoint is publicly accessible.',
  })
  @ApiBody({ type: CreateContactUsDto })
  @ApiOkResponseWithType(ContactUsResponseDto, 'Contact form submitted successfully')
  @ApiErrorResponse(400, 'Invalid input data')
  async create(@Body() createDto: CreateContactUsDto): Promise<ApiResponse<ContactUsResponseDto>> {
    const result = await this.contactService.create(createDto);
    return ApiResponse.success(result, 'Contact form submitted successfully', 201);
  }

  @Get()
  @UseGuards(JwtAuthGuard, AdminGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: 'Get all contact submissions (Admin only)',
    description: 'Retrieve all contact form submissions with pagination.',
  })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'sortBy', required: false, type: String })
  @ApiQuery({ name: 'sortDirection', required: false, enum: ['ASC', 'DESC'] })
  @ApiOkResponseWithPagedListType(ContactUsResponseDto, 'Contact submissions retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  async findAll(@Query() paginationDto: PaginationDto): Promise<ApiResponse<PagedListDto<ContactUsResponseDto>>> {
    const result = await this.contactService.findAll(paginationDto);
    return ApiResponse.success(result, 'Contact submissions retrieved successfully');
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard, AdminGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: 'Get contact submission by ID (Admin only)',
    description: 'Retrieve a specific contact form submission.',
  })
  @ApiParam({ name: 'id', description: 'Contact submission ID' })
  @ApiOkResponseWithType(ContactUsResponseDto, 'Contact submission retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Contact not found')
  async findOne(@Param('id') id: string): Promise<ApiResponse<ContactUsResponseDto>> {
    const result = await this.contactService.findById(id);
    return ApiResponse.success(result, 'Contact submission retrieved successfully');
  }

  @Post(':id/respond')
  @UseGuards(JwtAuthGuard, AdminGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: 'Respond to contact submission (Admin only)',
    description: 'Add admin response to a contact form submission.',
  })
  @ApiParam({ name: 'id', description: 'Contact submission ID' })
  @ApiBody({ type: AdminResponseDto })
  @ApiOkResponseWithType(ContactUsResponseDto, 'Response added successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Contact not found')
  async respond(
    @Param('id') id: string,
    @Body() responseDto: AdminResponseDto,
    @GetUser() user: any,
  ): Promise<ApiResponse<ContactUsResponseDto>> {
    const result = await this.contactService.respond(id, responseDto, user.id);
    return ApiResponse.success(result, 'Response added successfully');
  }
}