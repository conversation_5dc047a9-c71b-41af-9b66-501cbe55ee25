import { Injectable, Logger, NotFoundException, Inject, forwardRef } from '@nestjs/common';
import { RelatedEntityType } from '../../../common/enums/related-entity-type.enum';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Novel } from '../../../database/entities/novel.entity';
import { User } from '../../../database/entities/user.entity';
import { DiarySkin } from '../../../database/entities/diary-skin.entity';
import { StudentTutorMapping } from '../../../database/entities/student-tutor-mapping.entity';
import { AsyncNotificationHelperService } from '../../notification/async-notification-helper.service';
import { NotificationType } from '../../../database/entities/notification.entity';
import { TutorMatchingService } from '../../tutor-matching/tutor-matching.service';
import { NovelDefaultSkinRequiredException } from '../../../common/exceptions/novel.exceptions';

@Injectable()
export class NovelService {
  private readonly logger = new Logger(NovelService.name);

  constructor(
    @InjectRepository(Novel)
    private readonly novelRepository: Repository<Novel>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(DiarySkin)
    private readonly diarySkinRepository: Repository<DiarySkin>,
    @InjectRepository(StudentTutorMapping)
    private readonly studentTutorMappingRepository: Repository<StudentTutorMapping>,
    @Inject(forwardRef(() => AsyncNotificationHelperService))
    private readonly asyncNotificationHelper: AsyncNotificationHelperService,
    @Inject(forwardRef(() => TutorMatchingService))
    private readonly tutorMatchingService: TutorMatchingService,
  ) {}

  /**
   * Get or create a novel for a student
   * @param userId Student user ID
   * @returns Novel
   */
  async getOrCreateNovel(userId: string): Promise<Novel> {
    try {
      // Check if novel already exists for this user
      let novel = await this.novelRepository.findOne({
        where: { userId },
        relations: ['defaultSkin'],
      });

      if (novel) {
        return novel;
      }

      // Novel doesn't exist, create a new one with default skin
      const defaultSkin = await this.diarySkinRepository.findOne({
        where: { isActive: true, isGlobal: true },
      });

      if (!defaultSkin) {
        throw new NovelDefaultSkinRequiredException('No active global skin found for Novel module');
      }

      novel = this.novelRepository.create({
        userId,
        defaultSkinId: defaultSkin.id,
      });

      const savedNovel = await this.novelRepository.save(novel);
      savedNovel.defaultSkin = defaultSkin;

      this.logger.log(`Created new novel for user ${userId}`);
      return savedNovel;
    } catch (error) {
      this.logger.error(`Error getting or creating novel: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update the tutor greeting message for a student's novel
   * @param userId Student user ID
   * @param greeting Greeting message for the tutor
   * @returns Updated novel
   */
  async updateTutorGreeting(userId: string, greeting: string): Promise<Novel> {
    try {
      // Get or create the novel
      const novel = await this.getOrCreateNovel(userId);

      // Update the greeting
      novel.tutorGreeting = greeting;

      // Save the updated novel
      const updatedNovel = await this.novelRepository.save(novel);

      // Get the student information for the notification
      const student = await this.userRepository.findOne({ where: { id: userId } });
      if (!student) {
        this.logger.warn(`Student with ID ${userId} not found when sending greeting notification`);
        return updatedNovel;
      }

      // Get the novel module feature ID
      const novelModuleId = await this.getNovelModuleFeatureId();
      if (!novelModuleId) {
        this.logger.warn('Could not find novel module feature ID for notification');
        return updatedNovel;
      }

      // Send notification to the assigned tutor
      try {
        const studentTutorMapping = await this.tutorMatchingService.getStudentTutorForModule(userId, novelModuleId);

        if (studentTutorMapping) {
          // Create HTML content for email notification
          const htmlContent = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
            <div style="text-align: center; margin-bottom: 20px;">
              <h2 style="color: #333;">New Student Greeting - Novel Module</h2>
            </div>
            <div style="margin-bottom: 20px;">
              <p>Hello,</p>
              <p>${student.name} has set a greeting message for you in the Novel module:</p>
              <div style="background-color: #f9f9f9; padding: 15px; border-left: 4px solid #4CAF50; margin: 15px 0;">
                <p style="font-style: italic;">"${greeting}"</p>
              </div>
              <p>You can view their profile and start interacting with them.</p>
              <div style="text-align: center; margin: 30px 0;">
                <a href="${process.env.FRONTEND_URL || 'http://localhost:3011'}/tutor/novel" style="background-color: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">View Novel Module</a>
              </div>
            </div>
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
              <p>This is an automated message from the HEC system.</p>
              <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
            </div>
          </div>`;

          // Send notification to the tutor
          await this.asyncNotificationHelper.notifyAsync(
            studentTutorMapping.tutorId,
            NotificationType.TUTOR_GREETING,
            'New Student Greeting - Novel Module',
            `${student.name} has set a greeting message for you in the Novel module.`,
            {
              relatedEntityId: novel.id,
              relatedEntityType: RelatedEntityType.NOVEL,
              htmlContent: htmlContent,
              // Use all notification channels
              sendEmail: true,
              sendPush: true,
              sendInApp: true,
              sendRealtime: false,
            },
          );

          this.logger.log(`Sent novel greeting notification to tutor ${studentTutorMapping.tutorId} from student ${userId}`);
        } else {
          this.logger.warn(`No tutor assigned for student ${userId} in novel module`);
        }
      } catch (notificationError) {
        this.logger.error(`Failed to send greeting notification: ${notificationError.message}`, notificationError.stack);
        // Continue execution even if notification fails
      }

      return updatedNovel;
    } catch (error) {
      this.logger.error(`Error updating tutor greeting: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get the novel module feature ID
   * @returns Novel module feature ID or null if not found
   */
  private async getNovelModuleFeatureId(): Promise<string | null> {
    try {
      // This should match the feature ID used in the subscription system
      // You may need to adjust this based on your actual feature configuration
      const result = await this.novelRepository.query(`
        SELECT id FROM plan_feature WHERE name = 'Novel Module' OR name LIKE '%novel%' LIMIT 1
      `);

      return result.length > 0 ? result[0].id : null;
    } catch (error) {
      this.logger.error(`Error getting novel module feature ID: ${error.message}`);
      return null;
    }
  }

  /**
   * Check if student has set tutor greeting
   * @param userId Student user ID
   * @returns boolean indicating if greeting is set
   */
  async hasSetTutorGreeting(userId: string): Promise<boolean> {
    try {
      const novel = await this.novelRepository.findOne({
        where: { userId },
        select: ['tutorGreeting'],
      });

      return novel && novel.tutorGreeting && novel.tutorGreeting.trim().length > 0;
    } catch (error) {
      this.logger.error(`Error checking tutor greeting: ${error.message}`, error.stack);
      return false;
    }
  }
}
