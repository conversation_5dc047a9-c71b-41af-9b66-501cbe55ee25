import { Injectable, Logger, NotFoundException, BadRequestException, UnauthorizedException, ForbiddenException } from '@nestjs/common';
import { RelatedEntityType } from '../../../common/enums/related-entity-type.enum';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { StoryMaker } from '../../../database/entities/story-maker.entity';
import { StoryMakerParticipation } from '../../../database/entities/story-maker-participation.entity';
import { StoryMakerSubmission } from '../../../database/entities/story-maker-submission.entity';
import { StoryMakerEvaluation } from '../../../database/entities/story-maker-evaluation.entity';
import { StoryMakerGameListResponseDto, StoryMakerGameListItemDto, StoryMakerGameDetailDto } from '../../../database/models/story-maker/story-maker-student.dto';
import { StoryMakerSubmissionDto } from '../../../database/models/story-maker/story-maker-student.dto';
import { PaginationDto } from '../../../common/models/pagination.dto';
import { FileRegistryService } from '../../../common/services/file-registry.service';
import { FileEntityType } from '../../../common/enums/file-entity-type.enum';
import { CurrentUserService } from '../../../common/services/current-user.service';
import { AsyncNotificationHelperService } from '../../../modules/notification/async-notification-helper.service';
import { NotificationType } from '../../../database/entities/notification.entity';
import { UsersService } from '../../../modules/users/users.service';
import { TutorMatchingService } from '../../../modules/tutor-matching/tutor-matching.service';
import { PlanFeature, FeatureType } from '../../../database/entities/plan-feature.entity';
import { UserType } from '../../../database/entities/user.entity';
import { StoryMakerScoringService } from './story-maker-scoring.service';
import { GeminiAiService } from '../../../common/services/gemini-ai.service';


@Injectable()
export class StoryMakerService {
  private readonly logger = new Logger(StoryMakerService.name);

  constructor(
    @InjectRepository(StoryMaker)
    private readonly storyMakerRepository: Repository<StoryMaker>,
    @InjectRepository(StoryMakerParticipation)
    private readonly participationRepository: Repository<StoryMakerParticipation>,
    @InjectRepository(StoryMakerSubmission)
    private readonly submissionRepository: Repository<StoryMakerSubmission>,
    @InjectRepository(StoryMakerEvaluation)
    private readonly evaluationRepository: Repository<StoryMakerEvaluation>,
    private readonly fileRegistryService: FileRegistryService,
    private readonly currentUserService: CurrentUserService,
    private readonly dataSource: DataSource,
    private readonly asyncNotificationHelper: AsyncNotificationHelperService,
    private readonly usersService: UsersService,
    private readonly tutorMatchingService: TutorMatchingService,
    private readonly storyMakerScoringService: StoryMakerScoringService,
    private readonly geminiAiService: GeminiAiService,
  ) {}

  /**
   * Get the plan feature ID for the story maker module
   * This is used to identify the story maker module in the tutor matching system
   * @returns The story maker module feature ID or null if not found
   */
  private async getStoryMakerModuleFeatureId(): Promise<string | null> {
    try {
      // Get the repository for PlanFeature
      const planFeatureRepository = this.dataSource.getRepository(PlanFeature);

      // Find the story maker module feature (part of HEC_PLAY)
      const playFeature = await planFeatureRepository.findOne({
        where: { type: FeatureType.HEC_PLAY },
      });

      if (!playFeature) {
        this.logger.warn('Story maker module feature (HEC_PLAY) not found');
        return null;
      }

      this.logger.log(`Found story maker module feature with ID: ${playFeature.id}`);
      return playFeature.id;
    } catch (error) {
      this.logger.error(`Error getting story maker module feature ID: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Get available story maker games for the current student
   * @param paginationDto Pagination parameters
   * @returns List of available games with played status
   */
  async getAvailableGames(paginationDto: PaginationDto): Promise<StoryMakerGameListResponseDto> {
    try {
      const { page = 1, limit = 10 } = paginationDto;
      const skip = (page - 1) * limit;

      const studentId = this.currentUserService.getCurrentUserId();
      if (!studentId) {
        this.logger.warn('Attempted to get available games without a valid student ID');
        return { games: [], total_count: 0 };
      }

      const [storyMakers, totalCount] = await this.storyMakerRepository.findAndCount({
        where: { isActive: true },
        skip,
        take: limit,
        order: { createdAt: 'DESC' },
      });

      const participations = await this.participationRepository.find({
        where: { studentId },
        select: ['storyMakerId'],
      });

      // Create a set of story maker IDs that the student has already played
      const playedStoryMakerIds = new Set(participations.map((p) => p.storyMakerId));

      const games: StoryMakerGameListItemDto[] = [];

      for (const storyMaker of storyMakers) {
        try {
          const pictureUrl = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.STORY_MAKER, storyMaker.id);

          games.push({
            id: storyMaker.id,
            title: storyMaker.title,
            instruction: storyMaker.instruction,
            picture: pictureUrl || '',
            is_played: playedStoryMakerIds.has(storyMaker.id),
          });
        } catch (error) {
          // Log the error but continue processing other games
          this.logger.error(`Failed to process story maker with ID ${storyMaker.id} (${storyMaker.title}): ${error.message}`, error.stack);
        }
      }

      return {
        games,
        total_count: totalCount,
      };
    } catch (error) {
      this.logger.error(`Failed to retrieve available story maker games: ${error.message}`, error.stack);
      // Return empty result instead of throwing technical error to the student
      return { games: [], total_count: 0 };
    }
  }

  /**
   * Get a specific story maker game by ID with enhanced details for students
   *
   * This endpoint returns story details with different information based on the student's interaction:
   *
   * Scenario 1: Student hasn't played the game yet
   * - Returns basic story details with is_played: false
   * - No submission content, evaluation, or score is returned
   *
   * Scenario 2: Student submitted but tutor hasn't evaluated yet
   * - Returns story details with is_played: true
   * - Returns the student's latest submission content
   * - No evaluation details or score yet
   *
   * Scenario 3: Student submitted and tutor evaluated with score
   * - Returns story details with is_played: true
   * - Returns the student's submission content
   * - Returns evaluation details (corrections, feedback)
   * - Returns the score assigned by the tutor
   *
   * Scenario 4: Student submitted again after evaluation
   * - Returns story details with is_played: true
   * - Returns the student's latest submission content (the new one)
   * - No evaluation details for the latest submission
   * - Still returns the score from the first submission
   *
   * @param id The ID of the story maker game
   * @returns Enhanced game details including submission, evaluation, and score when available
   */
  async getGameById(id: string): Promise<StoryMakerGameDetailDto> {
    try {
      // Get current student ID
      const studentId = this.currentUserService.getCurrentUserId();
      if (!studentId) {
        this.logger.warn('Attempted to get game details without a valid student ID');
        throw new NotFoundException('Game not found');
      }

      // Get the story maker
      const storyMaker = await this.storyMakerRepository.findOne({
        where: { id, isActive: true },
      });

      if (!storyMaker) {
        this.logger.warn(`Story maker with ID ${id} not found or not active`);
        throw new NotFoundException('Game not found');
      }

      // Validate that the story has image analysis - required for proper evaluation
      if (!storyMaker.imageAnalysis) {
        this.logger.warn(`Story maker ${id} (${storyMaker.title}) is missing image analysis - triggering background analysis`);

        // Trigger background image analysis
        this.triggerImageAnalysisForStory(storyMaker).catch((error: any) => {
          this.logger.error(`Failed to trigger image analysis for story ${id}: ${error.message}`, error.stack);
        });

        // Return appropriate error to inform the client
        throw new BadRequestException({
          message: 'This story is being prepared for play. Image analysis is in progress. Please try again in a few moments.',
          code: 'IMAGE_ANALYSIS_PENDING',
          retryAfter: 30 // Suggest retry after 30 seconds
        });
      }

      // Check if the student has already played this game and get participation details
      const participation = await this.participationRepository.findOne({
        where: { studentId, storyMakerId: id },
        relations: ['submissions', 'submissions.evaluations'],
      });

      // Get the picture URL
      const pictureUrl = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.STORY_MAKER, storyMaker.id);

      // Prepare the simplified, user-friendly response
      const response: any = {
        id: storyMaker.id,
        title: storyMaker.title,
        instruction: storyMaker.instruction,
        picture: pictureUrl || '',
        deadline: storyMaker.deadline,
        is_played: !!participation,
        current_score: participation?.score || null,
        evaluation: null, // Will be populated if evaluation exists
        status: 'not_started', // Will be updated based on participation
        latest_submission: null, // Will be populated with latest submitted content if exists
      };

      // If the student has played this game, determine current status and evaluation
      if (participation) {
        // Sort submissions by date (newest first), handling null submittedAt for drafts
        const sortedSubmissions = participation.submissions.sort((a, b) => {
          if (!a.submittedAt) return 1; // Drafts go to end
          if (!b.submittedAt) return -1;
          return new Date(b.submittedAt).getTime() - new Date(a.submittedAt).getTime();
        });

        const submittedSubmissions = sortedSubmissions.filter(s => s.submittedAt);
        const draftSubmissions = sortedSubmissions.filter(s => !s.submittedAt);

        // Determine current status
        if (draftSubmissions.length > 0) {
          response.status = 'drafting';
        } else if (submittedSubmissions.length > 0) {
          const latestSubmitted = submittedSubmissions[0];
          response.status = latestSubmitted.isEvaluated ? 'completed' : 'submitted';
        } else {
          response.status = 'started';
        }

        // Get the latest submitted submission (regardless of evaluation status)
        const latestSubmittedSubmission = submittedSubmissions.length > 0 ? submittedSubmissions[0] : null;

        if (latestSubmittedSubmission) {
          response.latest_submission = {
            id: latestSubmittedSubmission.id,
            content: latestSubmittedSubmission.content,
            submitted_at: latestSubmittedSubmission.submittedAt,
            is_evaluated: latestSubmittedSubmission.isEvaluated,
            word_count: this.countWords(latestSubmittedSubmission.content),
            character_count: latestSubmittedSubmission.content.length,
          };
        }

        // Get the latest evaluated submission for showing results
        const latestEvaluatedSubmission = submittedSubmissions.find(s => s.isEvaluated && s.evaluations?.length > 0);

        if (latestEvaluatedSubmission) {
          const evaluation = latestEvaluatedSubmission.evaluations[0];

          // All evaluations now use 0-100 scale, composite score is average
          const maxScore = 100;

          // Create user-friendly evaluation response
          response.evaluation = {
            score: evaluation.totalScore,
            max_score: maxScore,
            percentage: evaluation.totalScore, // totalScore is already a percentage (0-100)
            feedback: this.createUserFriendlyFeedback(evaluation),
            evaluated_at: evaluation.evaluatedAt,
            // Detailed breakdown using new 9 criteria
            details: {
              content_task_fulfillment: evaluation.contentTaskFulfillment,
              organization_coherence: evaluation.organizationCoherence,
              grammar_accuracy: evaluation.grammarAccuracy,
              vocabulary_lexical: evaluation.vocabularyLexical,
              sentence_fluency_style: evaluation.sentenceFluencyStyle,
              clarity_cohesion: evaluation.clarityCohesion,
              creativity: evaluation.creativity,
              critical_thinking: evaluation.criticalThinking,
              expressiveness: evaluation.expressiveness,
              word_count: evaluation.wordCount,
            }
          };
        }
      }

      return response;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error; // Rethrow NotFoundException for proper 404 response
      }

      this.logger.error(`Failed to retrieve story maker game with ID ${id}: ${error.message}`, error.stack);
      throw new NotFoundException('Game not found'); // Convert all other errors to 404 for student-facing API
    }
  }

  /**
   * Get all submissions made by the current student for a specific story maker game
   * @param id The ID of the story maker game
   * @returns List of submissions with evaluations
   */
  async getStudentSubmissions(id: string): Promise<any> {
    try {
      // Get current student ID
      const studentId = this.currentUserService.getCurrentUserId();
      if (!studentId) {
        this.logger.warn('Attempted to get submissions without a valid student ID');
        throw new NotFoundException("We couldn't find your information. Please try logging in again.");
      }

      // Get the story maker
      const storyMaker = await this.storyMakerRepository.findOne({
        where: { id, isActive: true },
      });

      if (!storyMaker) {
        this.logger.warn(`Story maker with ID ${id} not found or not active`);
        throw new NotFoundException('Game not found');
      }

      // Get the participation with submissions and evaluations
      const participation = await this.participationRepository.findOne({
        where: { studentId, storyMakerId: id },
        relations: ['submissions', 'submissions.evaluations'],
      });

      if (!participation) {
        return {
          story_maker: {
            id: storyMaker.id,
            title: storyMaker.title,
          },
          participation: null,
          submissions: [],
        };
      }

      // Sort submissions by date (newest first)
      const sortedSubmissions = participation.submissions.sort((a, b) => b.submittedAt.getTime() - a.submittedAt.getTime());

      // Map submissions to DTOs
      const submissionDtos = sortedSubmissions.map((submission) => {
        const evaluation = submission.evaluations && submission.evaluations.length > 0 ? submission.evaluations[0] : null;

        const submissionDto: any = {
          id: submission.id,
          content: submission.content,
          submitted_at: submission.submittedAt,
          is_evaluated: submission.isEvaluated,
          created_at: submission.createdAt,
        };

        if (evaluation) {
          submissionDto.evaluation = {
            id: evaluation.id,
            ai_feedback: evaluation.aiFeedback,
            content_task_fulfillment: evaluation.contentTaskFulfillment,
            organization_coherence: evaluation.organizationCoherence,
            grammar_accuracy: evaluation.grammarAccuracy,
            vocabulary_lexical: evaluation.vocabularyLexical,
            sentence_fluency_style: evaluation.sentenceFluencyStyle,
            clarity_cohesion: evaluation.clarityCohesion,
            creativity: evaluation.creativity,
            critical_thinking: evaluation.criticalThinking,
            expressiveness: evaluation.expressiveness,
            popularity_score: evaluation.popularityScore,
            total_score: evaluation.totalScore,
            word_count: evaluation.wordCount,
            grammar_error_count: evaluation.grammarErrorCount,
            evaluated_at: evaluation.evaluatedAt,
          };
        }

        return submissionDto;
      });

      // Get the picture URL
      const pictureUrl = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.STORY_MAKER, storyMaker.id);

      // Return the result
      return {
        story_maker: {
          id: storyMaker.id,
          title: storyMaker.title,
          instruction: storyMaker.instruction,
          picture: pictureUrl || '',
          deadline: storyMaker.deadline,
        },
        participation: {
          id: participation.id,
          first_submitted_at: participation.firstSubmittedAt,
          is_evaluated: participation.isEvaluated,
          score: participation.score,
          evaluated_at: participation.evaluatedAt,
        },
        submissions: submissionDtos,
        can_submit: submissionDtos.length === 0 || submissionDtos[0].is_evaluated,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to get student submissions: ${error.message}`, error.stack);
      throw new NotFoundException('Failed to get submissions');
    }
  }

  /**
   * Submit a story for a specific story maker game
   * @param id The ID of the story maker game
   * @param content The content of the story. Max length is 50,000 characters.
   * @returns Success message
   */
  async submitStory(id: string, content: string): Promise<{ message: string }> {
    // Start a transaction
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Validate content
      if (!content || content.trim() === '') {
        this.logger.warn(`Student attempted to submit empty story for story maker ${id}`);
        throw new BadRequestException("Don't forget to write your story before submitting!");
      }

      // Get current student ID
      const studentId = this.currentUserService.getCurrentUserId();
      if (!studentId) {
        this.logger.warn('Attempted to submit story without a valid student ID');
        throw new UnauthorizedException("We couldn't find your information. Please try logging in again.");
      }

      // Verify the user is a student by checking their type
      const student = await this.usersService.findById(studentId);
      if (!student) {
        this.logger.warn(`User with ID ${studentId} not found during story submission`);
        throw new NotFoundException('User not found');
      }

      if (student.type !== UserType.STUDENT) {
        this.logger.warn(`User with ID ${studentId} and name ${student.name} is not a student`);
        throw new ForbiddenException('Only students can submit stories');
      }

      // Get the story maker within the transaction
      const storyMaker = await queryRunner.manager.findOne(StoryMaker, {
        where: { id, isActive: true },
      });

      if (!storyMaker) {
        this.logger.warn(`Story maker with ID ${id} not found or not active during submission attempt`);
        throw new NotFoundException('This game is not available right now. Please try another one!');
      }

      // Word limit feature has been removed - no word count validation needed

      // Check if the student has already participated in this story maker
      // First get the participation with a lock but without relations to avoid the "FOR UPDATE cannot be applied to the nullable side of an outer join" error
      let participation = await queryRunner.manager.findOne(StoryMakerParticipation, {
        where: { studentId, storyMakerId: id },
        lock: { mode: 'pessimistic_write' }, // Prevent race conditions
      });

      // If participation exists, fetch the submissions separately
      if (participation) {
        const submissions = await queryRunner.manager.find(StoryMakerSubmission, {
          where: { participationId: participation.id },
          order: { submittedAt: 'DESC' },
        });
        participation.submissions = submissions;
      }

      const now = new Date();

      // If no participation exists, create one (first time submission)
      if (!participation) {
        participation = queryRunner.manager.create(StoryMakerParticipation, {
          studentId,
          storyMakerId: id,
          isEvaluated: false,
          firstSubmittedAt: now,
        });

        await queryRunner.manager.save(participation);
      } else {
        // Check if there's a deadline and if it has passed
        if (storyMaker.deadline) {
          const deadlineDate = new Date(participation.firstSubmittedAt);
          deadlineDate.setDate(deadlineDate.getDate() + storyMaker.deadline);

          if (now > deadlineDate) {
            this.logger.warn(`Student ${studentId} attempted to submit after deadline for story maker ${id} (${storyMaker.title})`);
            throw new BadRequestException('The deadline for this story has passed. You can no longer submit.');
          }
        }

        // Check if the student has any submissions
        if (participation.submissions && participation.submissions.length > 0) {
          // Get the latest submission
          const latestSubmission = participation.submissions.sort((a, b) => b.submittedAt.getTime() - a.submittedAt.getTime())[0];

          // Check if the latest submission has been evaluated
          if (!latestSubmission.isEvaluated) {
            this.logger.warn(`Student ${studentId} attempted to resubmit before evaluation for story maker ${id} (${storyMaker.title})`);
            throw new BadRequestException("Your previous submission is still waiting for evaluation. You can submit again after it's evaluated.");
          }
        }
      }

      // Create a new submission record
      const submission = queryRunner.manager.create(StoryMakerSubmission, {
        participationId: participation.id,
        content,
        submittedAt: now,
        isEvaluated: false,
      });

      await queryRunner.manager.save(submission);

      // Commit the transaction
      await queryRunner.commitTransaction();

      this.logger.log(`Student ${studentId} successfully submitted story for story maker ${id} (${storyMaker.title})`);

      // Send notification to the assigned tutor or all admin users
      try {
        // Get the story maker module feature ID
        const storyMakerModuleId = await this.getStoryMakerModuleFeatureId();

        // Create HTML content for the notification
        const htmlContent = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #4a6ee0;">New Story Submission</h2>
            <p>A student has submitted a story that needs your evaluation.</p>
            <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 15px 0;">
              <p><strong>Student:</strong> ${student.name}</p>
              <p><strong>Story Title:</strong> ${storyMaker.title}</p>
              <p><strong>Submission Time:</strong> ${new Date().toLocaleString()}</p>
            </div>
            <p>Please review and evaluate this submission at your earliest convenience.</p>
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
              <p>This is an automated message from the HEC system.</p>
              <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
            </div>
          </div>
        `;

        // Notification options
        const notificationOptions = {
          relatedEntityId: submission.id,
          relatedEntityType: RelatedEntityType.STORY_MAKER_SUBMISSION,
          htmlContent: htmlContent,
          sendEmail: true,
          sendInApp: true,
          sendPush: true,
          sendMobile: false,
          sendSms: false,
          sendRealtime: true,
        };

        if (storyMakerModuleId) {
          // Find the assigned tutor for this student and the story maker module
          const studentTutorMapping = await this.tutorMatchingService.getStudentTutorForModule(studentId, storyMakerModuleId);

          if (studentTutorMapping) {
            // Send notification to the assigned tutor
            await this.asyncNotificationHelper.notifyAsync(
              studentTutorMapping.tutorId,
              NotificationType.STORY_SUBMISSION,
              'New Story Submission Requires Evaluation',
              `Student ${student.name} has submitted a story "${storyMaker.title}" that needs evaluation.`,
              notificationOptions,
            );

            this.logger.log(`Sent story submission notification to tutor ${studentTutorMapping.tutorId} for story maker ${id} (${storyMaker.title})`);
            return { message: 'Great job! Your story has been sent to your tutor for review.' };
          } else {
            this.logger.warn(`No tutor assigned for student ${studentId} and story maker module ${storyMakerModuleId} (${storyMaker.title})`);

            // Try to get any available tutor for the story maker module
            const availableTutors = await this.tutorMatchingService.getAvailableTutorsForModule(storyMakerModuleId);

            if (availableTutors && availableTutors.length > 0) {
              // Auto-assign the first available tutor
              const assignResult = await this.tutorMatchingService.assignTutor({
                studentId: studentId,
                tutorId: availableTutors[0].id,
                planFeatureId: storyMakerModuleId,
                notes: 'Auto-assigned during story maker submission',
              });

              if (assignResult) {
                // Send notification to the newly assigned tutor
                await this.asyncNotificationHelper.notifyAsync(
                  availableTutors[0].id,
                  NotificationType.STORY_SUBMISSION,
                  'New Story Submission Requires Evaluation',
                  `Student ${student.name} has submitted a story "${storyMaker.title}" that needs evaluation.`,
                  notificationOptions,
                );

                this.logger.log(`Auto-assigned and notified tutor ${availableTutors[0].id} for student ${studentId} and story maker ${id} (${storyMaker.title})`);
                return { message: 'Great job! Your story has been sent to your tutor for review.' };
              }
            }
          }
        }

        // If we get here, either there's no module ID, no assigned tutor, or auto-assignment failed
        // Fall back to notifying all admins
        const adminUsers = await this.usersService.getAllAdminUsers();

        if (adminUsers && adminUsers.length > 0) {
          // Get admin user IDs
          const adminUserIds = adminUsers.map((admin) => admin.id);

          // Send notification to all admins
          await this.asyncNotificationHelper.notifyManyAsync(
            adminUserIds.map(adminId => ({
              userId: adminId,
              type: NotificationType.STORY_SUBMISSION,
              title: 'New Story Submission Requires Evaluation',
              message: `Student ${student.name} has submitted a story "${storyMaker.title}" that needs evaluation.`,
              options: notificationOptions,
            }))
          );

          this.logger.log(`Sent story submission notifications to ${adminUserIds.length} admin users`);
        } else {
          this.logger.warn('No admin users found to notify about story submission');
        }
      } catch (error) {
        // Just log the error but don't fail the submission
        this.logger.error(`Failed to send notification for story submission: ${error.message}`, error.stack);
      }

      return { message: 'Great job! Your story has been sent to your tutor for review.' };
    } catch (error) {
      // Rollback the transaction in case of error
      await queryRunner.rollbackTransaction();

      // If it's already a NestJS exception, just rethrow it
      if (error instanceof BadRequestException || error instanceof UnauthorizedException || error instanceof ForbiddenException || error instanceof NotFoundException) {
        throw error;
      }

      if (error.code === '23505') {
        // PostgreSQL unique constraint violation
        this.logger.warn(`Concurrent submission detected for story maker ${id}`);
        throw new BadRequestException("You've already sent your story for this game. Try another game!");
      }

      this.logger.error(`Failed to submit story for story maker ${id}: ${error.message}`, error.stack);
      throw new BadRequestException('Oops! Something went wrong. Please try again.');
    } finally {
      // Release the query runner
      await queryRunner.release();
    }
  }

  /**
   * Update story content (draft) - consistent with diary/novel pattern
   */
  async updateStory(storyMakerId: string, content: string): Promise<any> {
    const studentId = this.currentUserService.getCurrentUserId();

    try {
      this.logger.log(`Student ${studentId} saving draft for story maker ${storyMakerId}`);

      // Get or create participation
      let participation = await this.getOrCreateParticipation(storyMakerId, studentId);

      // Get or create draft submission
      let submission = await this.submissionRepository.findOne({
        where: {
          participationId: participation.id,
          status: 'DRAFT'
        }
      });

      if (!submission) {
        submission = this.submissionRepository.create({
          participationId: participation.id,
          content,
          status: 'DRAFT',
          wordCountDraft: this.countWords(content),
          characterCountDraft: content.length,
        });
      } else {
        submission.content = content;
        submission.wordCountDraft = this.countWords(content);
        submission.characterCountDraft = content.length;
        submission.updatedAt = new Date();
      }

      await this.submissionRepository.save(submission);

      this.logger.log(`Draft saved successfully for story maker ${storyMakerId}`);

      return {
        content: submission.content,
        last_saved_at: submission.updatedAt,
        word_count: submission.wordCountDraft,
        character_count: submission.characterCountDraft,
        auto_save_count: submission.autoSaveCount,
        is_submitted: false,
      };

    } catch (error) {
      this.logger.error(`Failed to save draft: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to save draft. Please try again.');
    }
  }

  /**
   * Auto-save story content (lightweight background saving)
   */
  async autoSave(storyMakerId: string, content: string): Promise<{ saved_at: Date }> {
    const studentId = this.currentUserService.getCurrentUserId();

    try {
      // Get or create participation
      let participation = await this.getOrCreateParticipation(storyMakerId, studentId);

      // Get or create draft submission
      let submission = await this.submissionRepository.findOne({
        where: {
          participationId: participation.id,
          status: 'DRAFT'
        }
      });

      if (!submission) {
        submission = this.submissionRepository.create({
          participationId: participation.id,
          content,
          status: 'DRAFT',
          wordCountDraft: this.countWords(content),
          characterCountDraft: content.length,
          autoSaveCount: 1,
          lastAutoSavedAt: new Date(),
        });
      } else {
        submission.content = content;
        submission.lastAutoSavedAt = new Date();
        submission.autoSaveCount += 1;
        submission.wordCountDraft = this.countWords(content);
        submission.characterCountDraft = content.length;
      }

      await this.submissionRepository.save(submission);

      return { saved_at: submission.lastAutoSavedAt };

    } catch (error) {
      this.logger.error(`Auto-save failed: ${error.message}`);
      // Don't throw error for auto-save failures
      return { saved_at: new Date() };
    }
  }

  /**
   * Get current story content - consistent with diary/novel pattern
   */
  async getStory(storyMakerId: string): Promise<any> {
    const studentId = this.currentUserService.getCurrentUserId();

    try {
      // Get participation
      const participation = await this.participationRepository.findOne({
        where: { studentId, storyMakerId }
      });

      if (!participation) {
        return {
          content: '',
          last_saved_at: null,
          word_count: 0,
          character_count: 0,
          auto_save_count: 0,
          is_submitted: false,
        };
      }

      // Get draft submission
      const submission = await this.submissionRepository.findOne({
        where: {
          participationId: participation.id,
          status: 'DRAFT'
        }
      });

      if (!submission) {
        return {
          content: '',
          last_saved_at: null,
          word_count: 0,
          character_count: 0,
          auto_save_count: 0,
          is_submitted: false,
        };
      }

      return {
        content: submission.content,
        last_saved_at: submission.updatedAt,
        word_count: submission.wordCountDraft,
        character_count: submission.characterCountDraft,
        auto_save_count: submission.autoSaveCount,
        is_submitted: false,
      };

    } catch (error) {
      this.logger.error(`Failed to get draft: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to retrieve draft.');
    }
  }

  /**
   * Submit final story for AI evaluation (new method replacing tutor evaluation)
   */
  async submitFinalStory(storyMakerId: string, content: string): Promise<any> {
    const studentId = this.currentUserService.getCurrentUserId();
    const queryRunner = this.dataSource.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      this.logger.log(`Student ${studentId} submitting final story for story maker ${storyMakerId}`);

      // Get or create participation
      let participation = await this.participationRepository.findOne({
        where: { studentId, storyMakerId }
      });

      if (!participation) {
        // Create participation if it doesn't exist
        participation = queryRunner.manager.create(StoryMakerParticipation, {
          studentId,
          storyMakerId,
          participatedAt: new Date(),
        });
        await queryRunner.manager.save(participation);
        this.logger.log(`Created new participation for student ${studentId} and story maker ${storyMakerId}`);
      }

      // Get or create draft submission
      let submission = await this.submissionRepository.findOne({
        where: {
          participationId: participation.id,
          status: 'DRAFT'
        }
      });

      if (!submission) {
        // Create draft submission if it doesn't exist
        submission = queryRunner.manager.create(StoryMakerSubmission, {
          participationId: participation.id,
          content: content,
          status: 'DRAFT',
          wordCountDraft: this.countWords(content),
          characterCountDraft: content.length,
        });
        await queryRunner.manager.save(submission);
        this.logger.log(`Created new draft submission for participation ${participation.id}`);
      }

      // Update to submitted status
      submission.content = content;
      submission.status = 'SUBMITTED';
      submission.submittedAt = new Date();
      submission.isEvaluated = false;

      await queryRunner.manager.save(submission);

      // Send submission confirmation notification
      await this.sendSubmissionNotification(studentId, storyMakerId);

      // Commit the transaction
      await queryRunner.commitTransaction();

      this.logger.log(`Story submitted successfully for story maker ${storyMakerId}`);

      // Trigger AI evaluation (async)
      this.triggerAIEvaluation(submission).catch(error => {
        this.logger.error(`AI evaluation failed: ${error.message}`, error.stack);
      });

      return {
        message: 'Your story has been submitted and is being evaluated!',
        submission_id: submission.id,
        submitted_at: submission.submittedAt
      };

    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Failed to submit final story: ${error.message}`, error.stack);

      if (error instanceof BadRequestException) {
        throw error;
      }

      throw new BadRequestException('Failed to submit story. Please try again.');
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Trigger AI evaluation for a submitted story
   */
  private async triggerAIEvaluation(submission: StoryMakerSubmission): Promise<void> {
    try {
      this.logger.log(`Starting AI evaluation for submission ${submission.id}`);

      // Perform comprehensive scoring
      const score = await this.storyMakerScoringService.scoreStorySubmission(submission);

      // Save evaluation to database
      const evaluation = await this.storyMakerScoringService.saveEvaluation(submission.id, score);

      // Send evaluation complete notification
      await this.sendEvaluationCompleteNotification(submission.participationId, evaluation, score);

      this.logger.log(`AI evaluation completed for submission ${submission.id} with total score ${score.totalScore}`);

    } catch (error) {
      this.logger.error(`AI evaluation failed for submission ${submission.id}: ${error.message}`, error.stack);
      // Don't throw - this should not fail the submission process
    }
  }

  /**
   * Create user-friendly feedback from AI evaluation
   */
  private createUserFriendlyFeedback(evaluation: StoryMakerEvaluation): any {
    const score = evaluation.totalScore;
    const maxScore = 100; // Updated to match new 0-100 scale
    const percentage = Math.round(score); // totalScore is already a percentage (0-100)

    // Determine overall message based on score
    let overallMessage = '';
    let encouragement = '';

    if (percentage >= 85) {
      overallMessage = 'Excellent work! 🌟';
      encouragement = 'You\'re a fantastic storyteller!';
    } else if (percentage >= 70) {
      overallMessage = 'Great job! 👏';
      encouragement = 'Keep up the good work!';
    } else if (percentage >= 50) {
      overallMessage = 'Good start! 📝';
      encouragement = 'You\'re on the right track!';
    } else if (percentage >= 30) {
      overallMessage = 'Nice beginning! ✨';
      encouragement = 'Every great story starts with a first sentence!';
    } else {
      overallMessage = 'Keep practicing! 💪';
      encouragement = 'Writing takes practice - you\'re learning!';
    }

    // Create actionable suggestions
    const suggestions = [];

    if (evaluation.creativity < 50) {
      suggestions.push('Add more creative details to make your story unique');
    }

    if (evaluation.vocabularyLexical < 50) {
      suggestions.push('Try using more varied and interesting words');
    }

    if (evaluation.grammarAccuracy < 50) {
      suggestions.push('Check your spelling and grammar before submitting');
    }

    if (evaluation.organizationCoherence < 50) {
      suggestions.push('Try organizing your story with a clear beginning, middle, and end');
    }

    if (evaluation.contentTaskFulfillment < 50) {
      suggestions.push('Make sure your story relates to the picture and follows the instructions');
    }

    if (suggestions.length === 0) {
      suggestions.push('Keep writing amazing stories!');
    }

    // Highlight strengths
    const strengths = [];

    if (evaluation.creativity >= 75) {
      strengths.push('Great imagination!');
    }

    if (evaluation.sentenceFluencyStyle >= 75) {
      strengths.push('Nice writing style!');
    }

    if (evaluation.grammarAccuracy >= 75) {
      strengths.push('Good spelling and grammar!');
    }

    if (evaluation.vocabularyLexical >= 75) {
      strengths.push('Great word choices!');
    }

    if (evaluation.contentTaskFulfillment >= 75) {
      strengths.push('You followed the instructions well!');
    }

    if (evaluation.organizationCoherence >= 75) {
      strengths.push('Your story flows nicely!');
    }

    return {
      message: overallMessage,
      encouragement,
      strengths: strengths.length > 0 ? strengths : ['You\'re learning and improving!'],
      suggestions: suggestions.slice(0, 3), // Limit to 3 suggestions
      simple_feedback: evaluation.aiFeedback ? this.simplifyAiFeedback(evaluation.aiFeedback) : encouragement
    };
  }

  /**
   * Simplify AI feedback for younger students
   */
  private simplifyAiFeedback(aiFeedback: string): string {
    // Replace technical terms with simpler ones
    return aiFeedback
      .replace(/proofread/gi, 'check')
      .replace(/grammar/gi, 'spelling')
      .replace(/sentence structure/gi, 'how you write')
      .replace(/vocabulary/gi, 'words')
      .replace(/sophisticated/gi, 'fancy')
      .replace(/assessment/gi, 'checking')
      .replace(/typos/gi, 'spelling mistakes');
  }

  /**
   * Get detailed evaluation history for a student for a specific story maker game
   */
  async getEvaluationHistory(id: string): Promise<any> {
    const studentId = this.currentUserService.getCurrentUserId();
    if (!studentId) {
      throw new NotFoundException('Game not found');
    }

    try {
      // Get the participation with submissions and evaluations
      const participation = await this.participationRepository.findOne({
        where: { studentId, storyMakerId: id },
        relations: ['submissions', 'submissions.evaluations', 'storyMaker'],
      });

      if (!participation) {
        return {
          story_info: null,
          evaluations: [],
          total_count: 0,
          summary: null,
        };
      }

      // Get only evaluated submissions
      const evaluatedSubmissions = participation.submissions
        .filter(s => s.isEvaluated && s.evaluations?.length > 0)
        .sort((a, b) => new Date(b.submittedAt).getTime() - new Date(a.submittedAt).getTime());

      // Map evaluations to detailed response format
      const evaluationsResponse = evaluatedSubmissions.map((submission, index) => {
        const evaluation = submission.evaluations[0];

        return {
          submission_id: submission.id,
          submission_number: evaluatedSubmissions.length - index, // 1, 2, 3, etc.
          submitted_at: submission.submittedAt,
          evaluated_at: evaluation.evaluatedAt,
          content: submission.content,
          word_count: evaluation.wordCount,

          // User-friendly evaluation
          evaluation: {
            score: evaluation.totalScore,
            max_score: 100,
            percentage: Math.round(evaluation.totalScore), // totalScore is already a percentage
            feedback: this.createUserFriendlyFeedback(evaluation),

            // Detailed breakdown
            breakdown: {
              content_task_fulfillment: {
                score: evaluation.contentTaskFulfillment,
                max: 100,
                description: 'How well the story addresses the prompt'
              },
              creativity: {
                score: evaluation.creativity,
                max: 100,
                description: 'Originality and imaginative elements'
              },
              grammar_accuracy: {
                score: evaluation.grammarAccuracy,
                max: 100,
                description: evaluation.grammarErrorCount > 0
                  ? `${evaluation.grammarErrorCount} spelling/grammar issues found`
                  : 'Good spelling and grammar'
              },
              vocabulary: {
                score: evaluation.vocabularyLexical,
                max: 100,
                description: 'Word choice and variety'
              },
              organization: {
                score: evaluation.organizationCoherence,
                max: 100,
                description: 'Story structure and flow'
              },
              popularity: {
                score: evaluation.popularityScore,
                max: 5,
                description: evaluation.popularityScore > 0
                  ? `Earned ${evaluation.popularityScore} points from likes`
                  : 'No likes yet'
              }
            },

            // Raw AI data for advanced analysis
            ai_details: {
              feedback: evaluation.aiFeedback,
              grammar_errors: evaluation.aiEvaluationData?.grammarErrors || [],
              processing_time: evaluation.aiEvaluationData?.processingTime || 0,
            }
          }
        };
      });

      // Calculate summary statistics
      const summary = evaluationsResponse.length > 0 ? {
        total_submissions: evaluationsResponse.length,
        average_score: Math.round(evaluationsResponse.reduce((sum, e) => sum + e.evaluation.score, 0) / evaluationsResponse.length),
        best_score: Math.max(...evaluationsResponse.map(e => e.evaluation.score)),
        latest_score: evaluationsResponse[0]?.evaluation.score || 0,
        improvement: evaluationsResponse.length > 1
          ? evaluationsResponse[0].evaluation.score - evaluationsResponse[evaluationsResponse.length - 1].evaluation.score
          : 0,
        total_words_written: evaluationsResponse.reduce((sum, e) => sum + e.word_count, 0),
      } : null;

      return {
        story_info: {
          id: participation.storyMaker.id,
          title: participation.storyMaker.title,
        },
        evaluations: evaluationsResponse,
        total_count: evaluationsResponse.length,
        summary,
      };
    } catch (error) {
      this.logger.error(`Failed to retrieve evaluation history for story maker ${id}: ${error.message}`, error.stack);
      throw new NotFoundException('Game not found');
    }
  }

  /**
   * Helper method to get or create participation
   */
  private async getOrCreateParticipation(storyMakerId: string, studentId: string): Promise<StoryMakerParticipation> {
    let participation = await this.participationRepository.findOne({
      where: { studentId, storyMakerId }
    });

    if (!participation) {
      participation = this.participationRepository.create({
        studentId,
        storyMakerId,
      });
      await this.participationRepository.save(participation);
    }

    return participation;
  }

  /**
   * Helper method to count words
   */
  private countWords(text: string): number {
    if (!text || text.trim().length === 0) {
      return 0;
    }
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * Trigger image analysis for a story that is missing analysis
   * This method runs in the background to populate missing image analysis
   */
  private async triggerImageAnalysisForStory(storyMaker: StoryMaker): Promise<void> {
    try {
      this.logger.log(`Starting background image analysis for story ${storyMaker.id} (${storyMaker.title})`);

      // Get the image file from the file registry
      const imageFileData = await this.fileRegistryService.getFileBuffer(FileEntityType.STORY_MAKER, storyMaker.id);

      if (!imageFileData) {
        this.logger.error(`No image file found for story ${storyMaker.id} - cannot perform analysis`);
        return;
      }

      // Extract buffer and use the actual MIME type from the file
      const { buffer: imageBuffer, mimeType: actualMimeType } = imageFileData;
      const mimeType = actualMimeType || this.determineMimeType(storyMaker.picture) || 'image/jpeg';

      // Perform image analysis using Gemini AI
      const imageAnalysis = await this.geminiAiService.analyzeImage(imageBuffer, mimeType);

      // Update the story maker with the analysis results
      await this.storyMakerRepository.update(storyMaker.id, {
        imageAnalysis: {
          objects: imageAnalysis.objects,
          scene: imageAnalysis.scene,
          mood: imageAnalysis.mood,
          themes: imageAnalysis.themes,
          colors: imageAnalysis.colors,
          setting: imageAnalysis.setting,
          characters: imageAnalysis.characters,
          emotions: imageAnalysis.emotions,
          description: imageAnalysis.description,
          relevanceKeywords: imageAnalysis.relevanceKeywords,
        }
      });

      this.logger.log(`Successfully completed background image analysis for story ${storyMaker.id} (${storyMaker.title})`);
      this.logger.debug(`Analysis result: ${JSON.stringify(imageAnalysis, null, 2)}`);

    } catch (error) {
      this.logger.error(`Background image analysis failed for story ${storyMaker.id}: ${error.message}`, error.stack);

      // Set a fallback analysis to prevent repeated failures
      await this.storyMakerRepository.update(storyMaker.id, {
        imageAnalysis: {
          objects: ['creative elements'],
          scene: 'A creative scene perfect for storytelling',
          mood: 'inspiring and imaginative',
          themes: ['creativity', 'imagination', 'storytelling'],
          colors: ['vibrant', 'inspiring'],
          setting: 'creative storytelling environment',
          characters: ['creative storyteller'],
          emotions: ['inspiration', 'wonder', 'creativity'],
          description: 'An inspiring image that encourages creative storytelling and imagination. Analysis was completed with fallback data due to technical limitations.',
          relevanceKeywords: ['creative', 'story', 'imagination', 'narrative', 'adventure', 'discovery'],
        }
      });

      this.logger.log(`Applied fallback image analysis for story ${storyMaker.id} to prevent repeated failures`);
    }
  }

  /**
   * Determine MIME type from file path/extension
   */
  private determineMimeType(filePath: string): string | null {
    if (!filePath) return null;

    const extension = filePath.toLowerCase().split('.').pop();

    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'webp':
        return 'image/webp';
      default:
        return 'image/jpeg'; // Default fallback
    }
  }

  /**
   * Send submission confirmation notification
   */
  private async sendSubmissionNotification(studentId: string, storyMakerId: string): Promise<void> {
    try {
      await this.asyncNotificationHelper.notifyAsync(
        studentId,
        NotificationType.STORY_MAKER_SUBMITTED,
        'Story Submitted Successfully! 🎉',
        'Your story is being evaluated by our AI system. Results will be available shortly.',
        {
          relatedEntityId: storyMakerId,
          relatedEntityType: RelatedEntityType.STORY_MAKER_SUBMISSION,
          sendEmail: false,
          sendPush: true,
          sendInApp: true,
        }
      );
    } catch (error) {
      this.logger.error(`Failed to send submission notification: ${error.message}`);
    }
  }

  /**
   * Send evaluation complete notification with detailed results
   */
  private async sendEvaluationCompleteNotification(participationId: string, evaluation: StoryMakerEvaluation, score: any): Promise<void> {
    try {
      // Get student ID from participation
      const participation = await this.participationRepository.findOne({
        where: { id: participationId },
        relations: ['storyMaker']
      });

      if (!participation) return;



      await this.asyncNotificationHelper.notifyAsync(
        participation.studentId,
        NotificationType.STORY_MAKER_EVALUATED,
        'Your Story Has Been Evaluated! 🎉',
        `Great job! You scored ${score.totalScore} points. Check out your detailed results!`,
        {
          relatedEntityId: participation.storyMaker.id,
          relatedEntityType: RelatedEntityType.STORY_MAKER_EVALUATION,
          htmlContent: this.generateEvaluationEmailContent(participation.storyMaker.id,evaluation, score),
          sendEmail: true,
          sendPush: true,
          sendInApp: true,
        }
      );
    } catch (error) {
      this.logger.error(`Failed to send evaluation notification: ${error.message}`);
    }
  }

  /**
   * Generate rich email content for evaluation results
   */
  private generateEvaluationEmailContent(storyMakerId:string, evaluation: StoryMakerEvaluation, score: any): string {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #4CAF50;">🎉 Your Story Maker Results Are In!</h2>

        <div style="background: #f5f5f5; padding: 20px; border-radius: 10px; margin: 20px 0;">
          <h3>Score Breakdown:</h3>
          <div style="display: flex; justify-content: space-between; margin: 10px 0;">
            <span>📝 Sentences Written:</span>
            <strong>${score.sentenceScore} points</strong>
          </div>
          <div style="display: flex; justify-content: space-between; margin: 10px 0;">
            <span>🎨 Creativity:</span>
            <strong>${score.creativityScore}/5 points</strong>
          </div>
          <div style="display: flex; justify-content: space-between; margin: 10px 0;">
            <span>✍️ Writing Quality:</span>
            <strong>${score.sentencePowerScore}/3 points</strong>
          </div>
          <div style="display: flex; justify-content: space-between; margin: 10px 0;">
            <span>📊 Participation:</span>
            <strong>${score.participationScore}/5 points</strong>
          </div>
          <div style="display: flex; justify-content: space-between; margin: 10px 0;">
            <span>✅ Accuracy:</span>
            <strong>${score.accuracyScore}/3 points</strong>
          </div>
          <div style="display: flex; justify-content: space-between; margin: 10px 0;">
            <span>❤️ Popularity:</span>
            <strong>${score.popularityScore}/5 points</strong>
          </div>
          <hr>
          <div style="display: flex; justify-content: space-between; margin: 15px 0; font-size: 18px;">
            <span><strong>🏆 Total Score:</strong></span>
            <strong style="color: #4CAF50;">${score.totalScore} points</strong>
          </div>
        </div>

        ${evaluation.aiFeedback ? `
          <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h4>🤖 AI Feedback:</h4>
            <p>${evaluation.aiFeedback}</p>
          </div>
        ` : ''}

        <div style="text-align: center; margin: 30px 0;">
          <a href="${process.env.FRONTEND_URL}/story-maker/${storyMakerId}/submission"
             style="background: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">
            Write Another Story
          </a>
        </div>
      </div>
    `;
  }

  /**
   * Get all reviewed story makers as shared stories (latest submission per user per story only)
   */
  async getSharedStories(paginationDto: PaginationDto): Promise<any> {
    const { page = 1, limit = 10 } = paginationDto;
    const skip = (page - 1) * limit;

    try {
      // Get latest evaluated submission per user per story using raw query
      const latestSubmissionsQuery = `
        SELECT DISTINCT ON (p.student_id, p.story_maker_id) 
               s.id, s.content, s.submitted_at, s.is_evaluated,
               p.student_id, p.story_maker_id
        FROM story_maker_submission s
        INNER JOIN story_maker_participation p ON s.participation_id = p.id
        WHERE s.status = 'SUBMITTED' AND s.is_evaluated = true
        ORDER BY p.student_id, p.story_maker_id, s.submitted_at DESC
      `;
      
      const latestSubmissions = await this.dataSource.query(latestSubmissionsQuery);
      
      // Apply pagination to the unique submissions
      const paginatedSubmissionIds = latestSubmissions
        .slice(skip, skip + limit)
        .map(s => s.id);
      
      if (paginatedSubmissionIds.length === 0) {
        return {
          stories: [],
          total_count: 0,
          page,
          limit,
        };
      }
      
      // Get full submission details for paginated results
      const submissions = await this.submissionRepository
        .createQueryBuilder('submission')
        .leftJoinAndSelect('submission.participation', 'participation')
        .leftJoinAndSelect('participation.storyMaker', 'storyMaker')
        .leftJoinAndSelect('participation.student', 'student')
        .leftJoinAndSelect('submission.evaluations', 'evaluations')
        .where('submission.id IN (:...ids)', { ids: paginatedSubmissionIds })
        .orderBy('submission.submittedAt', 'DESC')
        .getMany();

      const sharedStories = await Promise.all(
        submissions.map(async (submission) => {
          const evaluation = submission.evaluations?.[0];
          const storyMaker = submission.participation.storyMaker;
          const student = submission.participation.student;

          // Get picture URL
          const pictureUrl = await this.fileRegistryService.getFileUrlWithFallback(
            FileEntityType.STORY_MAKER,
            storyMaker.id
          );

          return {
            id: submission.id,
            story_maker: {
              id: storyMaker.id,
              title: storyMaker.title,
              instruction: storyMaker.instruction,
              picture: pictureUrl || '',
            },
            student: {
              id: student.id,
              name: student.name,
            },
            content: submission.content,
            word_count: this.countWords(submission.content),
            character_count: submission.content.length,
            submitted_at: submission.submittedAt,
            evaluation: evaluation ? {
              total_score: evaluation.totalScore,
              max_score: 100,
              percentage: Math.round(evaluation.totalScore),
              creativity_score: evaluation.creativity,
              grammar_accuracy_score: evaluation.grammarAccuracy,
              vocabulary_score: evaluation.vocabularyLexical,
              popularity_score: evaluation.popularityScore,
              evaluated_at: evaluation.evaluatedAt,
            } : null,
          };
        })
      );

      return {
        stories: sharedStories,
        total_count: latestSubmissions.length,
        page,
        limit,
      };
    } catch (error) {
      this.logger.error(`Failed to retrieve shared stories: ${error.message}`, error.stack);
      return {
        stories: [],
        total_count: 0,
        page,
        limit,
      };
    }
  }

  /**
   * Backward compatibility aliases
   */
  async saveDraft(storyMakerId: string, content: string): Promise<any> {
    return this.updateStory(storyMakerId, content);
  }

  async getDraft(storyMakerId: string): Promise<any> {
    return this.getStory(storyMakerId);
  }
}
