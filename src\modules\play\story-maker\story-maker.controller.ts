import { Controller, Get, Post, Put, Patch, Delete, Body, UseGuards, Query, Param, ParseUUIDPipe, Req } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiQuery, ApiParam, ApiBody } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../../common/guards/jwt.guard';
import { StudentGuard } from '../../../common/guards/student.guard';
import { StudentTutorGuard } from '../../../common/guards/student-tutor.guard';
import { Public } from '../../../common/decorators/public-api.decorator';
import { StoryMakerService } from './story-maker.service';
import { StoryMakerLikeService } from './story-maker-like.service';
import { StoryMakerPopularityService } from './story-maker-popularity.service';
import { ApiResponse } from '../../../common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiErrorResponse } from '../../../common/decorators/api-response.decorator';
import { StoryMakerGameListResponseDto, StoryMakerGameDetailDto } from '../../../database/models/story-maker/story-maker-student.dto';
import {
  StoryMakerLikeResponseDto,
  StoryMakerLikeCountResponseDto,
  StoryMakerLikeDetailsResponseDto
} from '../../../database/models/story-maker/story-maker-like.dto';
import {
  StoryMakerDraftDto,
  StoryMakerAutoSaveDto,
  StoryMakerDraftResponseDto,
  StoryMakerFinalSubmissionDto,
  StoryMakerEvaluationResponseDto,
  StoryMakerSubmissionConfirmationDto,
  StoryMakerAutoSaveResponseDto
} from '../../../database/models/story-maker/story-maker-draft.dto';
import { PaginationDto } from '../../../common/models/pagination.dto';

@ApiTags('Play-StoryMaker')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard, StudentGuard)
@Controller('play/story-maker/play')
export class StoryMakerController {
  constructor(
    private readonly storyMakerService: StoryMakerService,
    private readonly storyMakerLikeService: StoryMakerLikeService,
    private readonly storyMakerPopularityService: StoryMakerPopularityService,
  ) {}

  @Get('list')
  @ApiOperation({
    summary: 'Get available story maker games',
    description: 'Returns a list of active story maker games. Games already played by the student will be marked as played.',
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (1-based)', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  @ApiOkResponseWithType(StoryMakerGameListResponseDto, 'Story maker games retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  async getAvailableGames(@Query() paginationDto: PaginationDto): Promise<ApiResponse<StoryMakerGameListResponseDto>> {
    const result = await this.storyMakerService.getAvailableGames(paginationDto);

    // Check if any games were found
    const message = result.games.length === 0 ? 'No story maker games are available right now. Please check back later!' : 'Story maker games retrieved successfully';

    return ApiResponse.success(result, message);
  }

  @Public()
  @Get('shared-stories')
  @ApiOperation({
    summary: 'Get all reviewed story makers as shared stories',
    description: 'Returns all story maker submissions that have been evaluated and can be shared publicly',
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (1-based)', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  @ApiOkResponseWithType(Object, 'Shared stories retrieved successfully')
  async getSharedStories(@Query() paginationDto: PaginationDto): Promise<ApiResponse<any>> {
    const result = await this.storyMakerService.getSharedStories(paginationDto);
    return ApiResponse.success(result, 'Shared stories retrieved successfully');
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get a specific story maker game with enhanced details',
    description: `Returns details of a specific story maker game with enhanced information based on the student's interaction:

    Scenario 1: Student hasn't played the game yet
    - Returns basic story details with is_played: false
    - No submission content, evaluation, or score is returned
    - latest_submission: null

    Scenario 2: Student submitted but hasn't been evaluated yet
    - Returns story details with is_played: true
    - latest_submission: includes content, word count, character count, submission date
    - No evaluation details or score yet

    Scenario 3: Student submitted and has been evaluated
    - Returns story details with is_played: true
    - latest_submission: includes the most recent submitted content with metadata
    - evaluation: includes detailed scoring breakdown with relevance score (if image analysis available)
    - Returns the comprehensive AI evaluation results

    Scenario 4: Student submitted again after evaluation
    - Returns story details with is_played: true
    - latest_submission: shows the newest submission content and metadata
    - evaluation: shows results from the most recent evaluated submission
    - Supports multiple submissions with independent evaluations`,
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the story maker game',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponseWithType(StoryMakerGameDetailDto, 'Story maker game retrieved successfully')
  @ApiErrorResponse(400, 'Story is being prepared - image analysis in progress. Includes retryAfter field.')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Game not found')
  async getGameById(@Param('id', ParseUUIDPipe) id: string): Promise<ApiResponse<StoryMakerGameDetailDto>> {
    const result = await this.storyMakerService.getGameById(id);
    return ApiResponse.success(result, 'Story game retrieved successfully');
  }

  @Get(':id/submissions')
  @ApiOperation({
    summary: 'Get student submissions for a story',
    description: 'Returns all submissions made by the student for a specific story maker game, including evaluations.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the story maker game',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponseWithType(Object, 'Submissions retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Game not found or no submissions')
  async getSubmissions(@Param('id', ParseUUIDPipe) id: string): Promise<ApiResponse<any>> {
    const result = await this.storyMakerService.getStudentSubmissions(id);

    const message = result.submissions.length === 0 ? "You haven't submitted any stories for this game yet." : 'Your submissions retrieved successfully';

    return ApiResponse.success(result, message);
  }

  @Get(':id/evaluations')
  @ApiOperation({
    summary: 'Get evaluation history for a story',
    description: 'Returns detailed evaluation history for all submissions made by the student for a specific story maker game.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the story maker game',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponseWithType(Object, 'Evaluation history retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Game not found or no evaluations')
  async getEvaluationHistory(@Param('id', ParseUUIDPipe) id: string): Promise<ApiResponse<any>> {
    const result = await this.storyMakerService.getEvaluationHistory(id);

    const message = result.evaluations.length === 0 ? "No evaluations found for this story yet." : 'Evaluation history retrieved successfully';

    return ApiResponse.success(result, message);
  }



  @Put(':id')
  @ApiOperation({
    summary: 'Update story content (draft)',
    description: 'Update story content as draft. Can be called multiple times. No evaluation performed. Consistent with diary/novel pattern.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the story maker game',
    type: 'string',
    format: 'uuid',
  })
  @ApiBody({
    type: StoryMakerDraftDto,
    description: 'The story content to save as draft',
  })
  @ApiOkResponseWithType(StoryMakerDraftResponseDto, 'Story updated successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Game not found')
  async updateStory(@Param('id', ParseUUIDPipe) id: string, @Body() draftDto: StoryMakerDraftDto): Promise<ApiResponse<StoryMakerDraftResponseDto>> {
    const result = await this.storyMakerService.updateStory(id, draftDto.content);
    return ApiResponse.success(result, 'Story updated successfully');
  }

  @Post(':id/submit')
  @ApiOperation({
    summary: 'Submit story for AI evaluation',
    description: 'Submit the story for AI evaluation. This triggers immediate scoring and cannot be undone. Consistent with diary/novel pattern.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the story maker game',
    type: 'string',
    format: 'uuid',
  })
  @ApiBody({
    type: StoryMakerFinalSubmissionDto,
    description: 'The final story content to submit',
  })
  @ApiOkResponseWithType(StoryMakerSubmissionConfirmationDto, 'Story submitted and evaluation started')
  @ApiErrorResponse(400, 'Invalid input or no draft found')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Game not found')
  async submitStory(@Param('id', ParseUUIDPipe) id: string, @Body() submissionDto: StoryMakerFinalSubmissionDto): Promise<ApiResponse<StoryMakerSubmissionConfirmationDto>> {
    const result = await this.storyMakerService.submitFinalStory(id, submissionDto.content);
    return ApiResponse.success(result, 'Story submitted successfully');
  }



  @Get(':id/draft')
  @ApiOperation({
    summary: 'Get story draft content',
    description: 'Retrieve the current draft content for editing. Returns empty content if no draft exists.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the story maker game',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponseWithType(StoryMakerDraftResponseDto, 'Draft content retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Game not found')
  async getDraftContent(@Param('id', ParseUUIDPipe) id: string): Promise<ApiResponse<StoryMakerDraftResponseDto>> {
    const result = await this.storyMakerService.getStory(id);
    return ApiResponse.success(result, 'Draft content retrieved successfully');
  }

  @Patch(':id/auto-save')
  @ApiOperation({
    summary: 'Auto-save story content',
    description: 'Automatically save story content in background. Called frequently during typing.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the story maker game',
    type: 'string',
    format: 'uuid',
  })
  @ApiBody({
    type: StoryMakerAutoSaveDto,
    description: 'The content to auto-save',
  })
  @ApiOkResponseWithType(StoryMakerAutoSaveResponseDto, 'Content auto-saved')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  async autoSave(@Param('id', ParseUUIDPipe) id: string, @Body() autoSaveDto: StoryMakerAutoSaveDto): Promise<ApiResponse<StoryMakerAutoSaveResponseDto>> {
    const result = await this.storyMakerService.autoSave(id, autoSaveDto.content);
    return ApiResponse.success(result, 'Content auto-saved');
  }

  // ===== LIKE SYSTEM ENDPOINTS (Following Diary Pattern) =====

  @Post('submissions/:id/like')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Like a story submission',
    description: 'Adds a like to the specified story submission (only available to students and tutors)',
  })
  @ApiParam({
    name: 'id',
    description: 'ID of the story submission',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponseWithType(StoryMakerLikeResponseDto, 'Story submission liked successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(403, 'Only students and tutors can like story submissions')
  @ApiErrorResponse(404, 'Story submission not found')
  async likeStorySubmission(@Req() req: any, @Param('id', ParseUUIDPipe) id: string): Promise<ApiResponse<StoryMakerLikeResponseDto>> {
    const like = await this.storyMakerLikeService.addLike(id, req.user.id, req.user.type);
    return ApiResponse.success(like, 'Story submission liked successfully');
  }

  @Delete('submissions/:id/like')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Unlike a story submission',
    description: 'Removes a like from the specified story submission (only available to students and tutors)',
  })
  @ApiParam({
    name: 'id',
    description: 'ID of the story submission',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponseWithType(Object, 'Story submission unliked successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(403, 'Only students and tutors can unlike story submissions')
  @ApiErrorResponse(404, 'Like not found')
  async unlikeStorySubmission(@Req() req: any, @Param('id', ParseUUIDPipe) id: string): Promise<ApiResponse<{ message: string }>> {
    await this.storyMakerLikeService.removeLike(id, req.user.id);
    return ApiResponse.success({ message: 'Like removed successfully' }, 'Story submission unliked successfully');
  }

  @Get('submissions/:id/likes')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get story submission like details',
    description: 'Get the number of likes and whether current user has liked the specified story submission',
  })
  @ApiParam({
    name: 'id',
    description: 'ID of the story submission',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponseWithType(StoryMakerLikeCountResponseDto, 'Story submission likes retrieved successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(403, 'Only students and tutors can view like details')
  @ApiErrorResponse(404, 'Story submission not found')
  async getStorySubmissionLikes(@Req() req: any, @Param('id', ParseUUIDPipe) id: string): Promise<ApiResponse<StoryMakerLikeCountResponseDto>> {
    const [likeCount, hasLiked] = await Promise.all([
      this.storyMakerLikeService.getLikeCount(id),
      this.storyMakerLikeService.hasUserLiked(id, req.user.id)
    ]);
    return ApiResponse.success({ count: likeCount, hasLiked }, 'Story submission likes retrieved successfully');
  }

  @Get('submissions/:id/like-count')
  @ApiOperation({
    summary: 'Get story submission like count',
    description: 'Get the number of likes for the specified story submission (public endpoint)',
  })
  @ApiParam({
    name: 'id',
    description: 'ID of the story submission',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponseWithType(StoryMakerLikeCountResponseDto, 'Story submission like count retrieved successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(404, 'Story submission not found')
  async getStorySubmissionLikeCount(@Param('id', ParseUUIDPipe) id: string): Promise<ApiResponse<StoryMakerLikeCountResponseDto>> {
    const count = await this.storyMakerLikeService.getLikeCount(id);
    return ApiResponse.success({ count, hasLiked: false }, 'Story submission like count retrieved successfully');
  }

  @Get('submissions/:id/popularity')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get story submission popularity statistics',
    description: 'Get detailed popularity statistics including 24-hour like counts and scoring thresholds',
  })
  @ApiParam({
    name: 'id',
    description: 'ID of the story submission',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponseWithType(StoryMakerLikeDetailsResponseDto, 'Popularity statistics retrieved successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(404, 'Story submission not found')
  async getStorySubmissionPopularity(@Param('id', ParseUUIDPipe) id: string): Promise<ApiResponse<any>> {
    const stats = await this.storyMakerPopularityService.getPopularityStatistics(id);
    return ApiResponse.success(stats, 'Popularity statistics retrieved successfully');
  }

}
