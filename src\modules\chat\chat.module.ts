import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MulterModule } from '@nestjs/platform-express';
import { ChatService } from './chat.service';
import { AdminChatService } from './admin-chat.service';
import { VirtualAdminService } from './virtual-admin.service';
import { AdminConversationService } from './admin-conversation.service';
import { AdminConversationManagerService } from './admin-conversation-manager.service';
import { ChatGateway } from './chat.gateway';
import { ChatController } from './chat.controller';
import { AdminChatController } from './admin-chat.controller';
import { Conversation } from '../../database/entities/conversation.entity';
import { AdminConversation } from '../../database/entities/admin-conversation.entity';
import { AdminConversationParticipant } from '../../database/entities/admin-conversation-participant.entity';
import { Message } from '../../database/entities/message.entity';
import { MessageAttachment } from '../../database/entities/message-attachment.entity';
import { MessageRegistry } from '../../database/entities/message-registry.entity';
import { VirtualAdmin } from '../../database/entities/virtual-admin.entity';
import { User } from '../../database/entities/user.entity';
import { StudentTutorMapping } from '../../database/entities/student-tutor-mapping.entity';
import { StudentFriendship } from '../../database/entities/student-friendship.entity';
import { NotificationModule } from '../notification/notification.module';

import { CommonModule } from '../../common/common.module';
import { DeeplinkModule } from '../../common/utils/deeplink.module';
import { SocketTimezoneTransformer } from '../../common/transformers/socket-timezone.transformer';
import { TimezoneService } from '../../common/services/timezone.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([Conversation, AdminConversation, AdminConversationParticipant, Message, MessageAttachment, MessageRegistry, VirtualAdmin, User, StudentTutorMapping, StudentFriendship]),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: { expiresIn: '1d' },
      }),
    }),
    NotificationModule,
    CommonModule,
    DeeplinkModule,
    MulterModule.register({
      limits: {
        fileSize: 10 * 1024 * 1024, // 10MB
      },
    }),
  ],
  controllers: [ChatController, AdminChatController],
  providers: [ChatService, AdminChatService, VirtualAdminService, AdminConversationService, AdminConversationManagerService, ChatGateway, SocketTimezoneTransformer, TimezoneService],
  exports: [ChatService, AdminChatService, VirtualAdminService, AdminConversationService, AdminConversationManagerService],
})
export class ChatModule {}
