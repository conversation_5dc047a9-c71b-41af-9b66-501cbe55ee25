# Content Management System

## 📚 Content Overview

The HEC platform's content management system allows administrators to create, manage, and moderate all educational content including story prompts, diary themes, game configurations, and user-generated content.

## 🎨 Story Maker Content Management

### Creating Story Prompts

#### Step-by-Step Process
1. **Navigate to Content Management**
   - Admin Dashboard → Content → Story Maker → "Create New Story"

2. **Upload Story Image**
   ```
   Requirements:
   - Format: JPG, PNG, WebP
   - Resolution: 800x600 minimum (1920x1080 recommended)
   - File Size: Maximum 5MB
   - Content: Age-appropriate, educational, inspiring
   ```

3. **Configure Story Settings**
   ```javascript
   {
     "title": "The Magical Forest Adventure",
     "description": "A mysterious forest with glowing trees and hidden paths",
     "difficulty_level": "intermediate", // beginner, intermediate, advanced
     "age_group": "8-12", // 6-8, 8-12, 12-16
     "themes": ["adventure", "nature", "mystery"],
     "estimated_time": "15-20 minutes",
     "learning_objectives": [
       "Creative storytelling",
       "Descriptive writing",
       "Character development"
     ]
   }
   ```

4. **Set Scoring Criteria**
   - **Creativity Weight**: 30% (originality, imagination)
   - **Grammar Weight**: 20% (spelling, punctuation)
   - **Relevance Weight**: 25% (connection to image)
   - **Completeness Weight**: 25% (story structure, length)

### Managing Existing Stories

#### Story Library Dashboard
- **Total Stories**: 150+ active prompts
- **Categories**: Adventure, Fantasy, Real-life, Historical, Science
- **Status Tracking**: Active, Draft, Archived, Under Review
- **Performance Metrics**: Completion rate, average scores, popularity

#### Bulk Operations
```bash
# Export story data
GET /admin/stories/export?format=csv&date_range=last_30_days

# Import new stories
POST /admin/stories/import
Content-Type: multipart/form-data
File: stories_batch.csv

# Update multiple stories
PATCH /admin/stories/bulk-update
{
  "story_ids": [1, 2, 3, 4, 5],
  "updates": {
    "difficulty_level": "intermediate",
    "age_group": "8-12"
  }
}
```

### Content Moderation Workflow

#### Automated Screening
- **Image Analysis**: AI scans for inappropriate content
- **Text Analysis**: Checks descriptions for policy violations
- **Duplicate Detection**: Identifies similar existing content
- **Quality Assessment**: Evaluates image resolution and clarity

#### Manual Review Process
1. **Initial Submission** → Auto-screening
2. **Flagged Content** → Manual review queue
3. **Admin Review** → Approve/Reject/Request changes
4. **Approved Content** → Live on platform
5. **Rejected Content** → Notification with feedback

## 📖 Diary Theme Management

### Creating Custom Themes

#### Theme Components
```css
/* Theme Structure */
.diary-theme-forest {
  --primary-color: #2d5a27;
  --secondary-color: #8fbc8f;
  --accent-color: #ffd700;
  --background-image: url('/themes/forest-bg.jpg');
  --font-family: 'Georgia', serif;
  --border-style: 2px solid #8fbc8f;
}
```

#### Theme Configuration
```javascript
{
  "theme_id": "forest_adventure",
  "name": "Forest Adventure",
  "description": "A magical forest theme with nature elements",
  "category": "nature",
  "premium": false,
  "components": {
    "background": {
      "type": "image",
      "url": "/themes/forest-bg.jpg",
      "fallback_color": "#2d5a27"
    },
    "colors": {
      "primary": "#2d5a27",
      "secondary": "#8fbc8f",
      "accent": "#ffd700",
      "text": "#1a1a1a"
    },
    "typography": {
      "heading_font": "Georgia, serif",
      "body_font": "Arial, sans-serif",
      "font_sizes": {
        "title": "24px",
        "heading": "18px",
        "body": "14px"
      }
    },
    "decorative_elements": [
      "leaf_borders",
      "tree_corners",
      "flower_bullets"
    ]
  }
}
```

### Theme Library Management

#### Categories and Organization
- **Seasonal Themes**: Spring, Summer, Fall, Winter
- **Subject Themes**: Science, History, Literature, Art
- **Mood Themes**: Happy, Calm, Energetic, Thoughtful
- **Special Occasions**: Holidays, Celebrations, Events
- **Premium Themes**: Advanced designs for subscribers

#### Theme Performance Analytics
```javascript
{
  "theme_usage_stats": {
    "most_popular": [
      {"theme": "rainbow_dreams", "usage": 2847},
      {"theme": "space_adventure", "usage": 2156},
      {"theme": "ocean_depths", "usage": 1923}
    ],
    "least_used": [
      {"theme": "vintage_library", "usage": 45},
      {"theme": "desert_sunset", "usage": 67}
    ],
    "user_ratings": {
      "average_rating": 4.2,
      "total_ratings": 15847
    }
  }
}
```

## 🎮 Block Game Configuration

### Game Type Management

#### Sentence Building Games
```javascript
{
  "game_type": "sentence_building",
  "difficulty_levels": {
    "beginner": {
      "word_count": "3-5 words",
      "sentence_types": ["simple"],
      "grammar_focus": ["subject-verb"],
      "time_limit": "no_limit"
    },
    "intermediate": {
      "word_count": "5-8 words",
      "sentence_types": ["simple", "compound"],
      "grammar_focus": ["subject-verb-object", "adjectives"],
      "time_limit": "3_minutes"
    },
    "advanced": {
      "word_count": "8-12 words",
      "sentence_types": ["complex", "compound-complex"],
      "grammar_focus": ["all_grammar_rules"],
      "time_limit": "2_minutes"
    }
  }
}
```

#### Word Bank Management
```javascript
{
  "word_categories": {
    "nouns": {
      "animals": ["cat", "dog", "elephant", "butterfly"],
      "objects": ["book", "chair", "computer", "bicycle"],
      "people": ["teacher", "student", "doctor", "artist"]
    },
    "verbs": {
      "action": ["run", "jump", "swim", "dance"],
      "thinking": ["think", "believe", "understand", "remember"],
      "being": ["is", "are", "was", "were"]
    },
    "adjectives": {
      "descriptive": ["beautiful", "large", "colorful", "smooth"],
      "emotional": ["happy", "sad", "excited", "calm"]
    }
  }
}
```

### Game Difficulty Algorithms

#### Adaptive Difficulty System
```python
def calculate_next_difficulty(user_performance):
    """
    Adjusts game difficulty based on user performance
    """
    accuracy = user_performance['accuracy_rate']
    speed = user_performance['completion_time']
    consistency = user_performance['consistency_score']
    
    if accuracy > 0.9 and speed < 60 and consistency > 0.8:
        return "increase_difficulty"
    elif accuracy < 0.6 or consistency < 0.4:
        return "decrease_difficulty"
    else:
        return "maintain_difficulty"
```

#### Scoring Configuration
```javascript
{
  "scoring_system": {
    "base_points": 10,
    "accuracy_multiplier": {
      "100%": 2.0,
      "90-99%": 1.5,
      "80-89%": 1.2,
      "70-79%": 1.0,
      "below_70%": 0.5
    },
    "speed_bonus": {
      "under_30s": 5,
      "30-60s": 3,
      "60-120s": 1,
      "over_120s": 0
    },
    "streak_bonus": {
      "5_correct": 2,
      "10_correct": 5,
      "20_correct": 10
    }
  }
}
```

## 🏆 Achievement System Management

### Badge Configuration

#### Achievement Categories
```javascript
{
  "writing_achievements": {
    "first_entry": {
      "name": "First Steps",
      "description": "Write your first diary entry",
      "icon": "pencil-start.svg",
      "points": 10,
      "rarity": "common"
    },
    "prolific_writer": {
      "name": "Prolific Writer",
      "description": "Write 100 diary entries",
      "icon": "book-stack.svg",
      "points": 500,
      "rarity": "epic",
      "requirements": {
        "diary_entries": 100
      }
    }
  },
  "game_achievements": {
    "story_master": {
      "name": "Story Master",
      "description": "Score 95+ on 10 Story Maker games",
      "icon": "crown-story.svg",
      "points": 200,
      "rarity": "rare",
      "requirements": {
        "story_maker_scores": {
          "min_score": 95,
          "count": 10
        }
      }
    }
  }
}
```

#### Progress Tracking System
```javascript
{
  "progress_metrics": {
    "writing_improvement": {
      "grammar_score_trend": "increasing",
      "vocabulary_growth": "+15_words_per_month",
      "creativity_index": "4.2/5.0",
      "consistency_rating": "daily_writer"
    },
    "engagement_metrics": {
      "login_streak": "14_days",
      "feature_usage": {
        "diary": "daily",
        "story_maker": "3x_weekly",
        "block_games": "2x_weekly"
      }
    }
  }
}
```

## 📊 Content Analytics Dashboard

### Performance Metrics

#### Content Engagement Analytics
```javascript
{
  "story_maker_analytics": {
    "total_submissions": 15847,
    "average_completion_rate": 0.78,
    "most_popular_themes": [
      "adventure", "fantasy", "animals"
    ],
    "average_story_length": 156,
    "creativity_score_distribution": {
      "excellent": 0.25,
      "good": 0.45,
      "average": 0.25,
      "needs_improvement": 0.05
    }
  },
  "diary_analytics": {
    "total_entries": 45623,
    "average_entry_length": 234,
    "most_used_themes": [
      "rainbow_dreams", "space_adventure", "nature_walk"
    ],
    "writing_improvement_rate": 0.15
  }
}
```

#### User Content Quality Metrics
- **Grammar Improvement**: Track error reduction over time
- **Vocabulary Expansion**: Monitor new word usage
- **Creativity Scores**: Measure originality and imagination
- **Engagement Levels**: Time spent, return visits, completion rates

### Content Moderation Dashboard

#### Automated Moderation Results
```javascript
{
  "moderation_stats": {
    "total_content_reviewed": 1247,
    "auto_approved": 1156,
    "flagged_for_review": 78,
    "rejected": 13,
    "false_positives": 2,
    "moderation_accuracy": 0.984
  },
  "common_issues": [
    "inappropriate_language",
    "off_topic_content",
    "copyright_concerns",
    "quality_standards"
  ]
}
```

## 🔄 Content Lifecycle Management

### Content Publishing Workflow

#### Draft → Review → Publish Process
1. **Content Creation**
   - Admin creates new content
   - Auto-save drafts every 30 seconds
   - Preview functionality available

2. **Quality Assurance**
   - Automated checks (grammar, appropriateness)
   - Peer review (optional)
   - Educational value assessment

3. **Approval Process**
   - Senior admin approval required
   - Educational team review for learning objectives
   - Technical review for implementation

4. **Publishing**
   - Scheduled release (optional)
   - Feature flags for gradual rollout
   - Performance monitoring post-launch

### Content Updates and Versioning

#### Version Control System
```javascript
{
  "content_version": {
    "story_id": "forest_adventure_v2",
    "version_history": [
      {
        "version": "1.0",
        "created": "2024-01-15",
        "changes": "Initial creation"
      },
      {
        "version": "1.1", 
        "created": "2024-02-10",
        "changes": "Updated image quality, added learning objectives"
      },
      {
        "version": "2.0",
        "created": "2024-03-05",
        "changes": "Complete redesign, new scoring algorithm"
      }
    ]
  }
}
```

## 📋 Content Management Checklist

### Daily Tasks
- [ ] Review flagged content in moderation queue
- [ ] Check content performance metrics
- [ ] Respond to content-related user feedback
- [ ] Monitor system performance for content delivery

### Weekly Tasks
- [ ] Analyze content engagement statistics
- [ ] Review and update featured content
- [ ] Plan new content creation based on user requests
- [ ] Update content categories and tags

### Monthly Tasks
- [ ] Comprehensive content performance review
- [ ] Archive outdated or low-performing content
- [ ] Plan seasonal content updates
- [ ] Review and update content policies
- [ ] Analyze user feedback for content improvements

### Quarterly Tasks
- [ ] Major content library reorganization
- [ ] Educational effectiveness assessment
- [ ] Content strategy planning for next quarter
- [ ] Technology updates for content management system

---

**Next Steps**: After setting up content management, proceed to [Analytics Guide](analytics-guide.md) to monitor content performance, or explore [Security Settings](security.md) to configure content moderation policies.

*For content creation best practices and educational guidelines, refer to the Content Creation Handbook or contact the educational team.*