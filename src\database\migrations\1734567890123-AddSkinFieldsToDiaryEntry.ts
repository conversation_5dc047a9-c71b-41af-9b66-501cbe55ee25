import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddSkinFieldsToDiaryEntry1734567890123 implements MigrationInterface {
  name = 'AddSkinFieldsToDiaryEntry1734567890123';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if student_skin_id column exists before adding
    const studentSkinIdExists = await queryRunner.hasColumn('diary_entry', 'student_skin_id');
    if (!studentSkinIdExists) {
      await queryRunner.addColumn(
        'diary_entry',
        new TableColumn({
          name: 'student_skin_id',
          type: 'uuid',
          isNullable: true,
        }),
      );
    }

    // Check if skin_type column exists before adding
    const skinTypeExists = await queryRunner.hasColumn('diary_entry', 'skin_type');
    if (!skinTypeExists) {
      await queryRunner.addColumn(
        'diary_entry',
        new TableColumn({
          name: 'skin_type',
          type: 'varchar',
          length: '20',
          default: "'global'",
          isNullable: false,
        }),
      );
    }

    // Add foreign key constraint for student_skin_id if it doesn't exist
    try {
      await queryRunner.query(`
        ALTER TABLE "diary_entry" 
        ADD CONSTRAINT "FK_diary_entry_student_skin_id" 
        FOREIGN KEY ("student_skin_id") 
        REFERENCES "student_diary_skin"("id") 
        ON DELETE SET NULL
      `);
    } catch (error) {
      // Constraint might already exist, ignore error
      console.log('Foreign key constraint FK_diary_entry_student_skin_id might already exist');
    }

    // Add check constraint for skin_type if it doesn't exist
    try {
      await queryRunner.query(`
        ALTER TABLE "diary_entry" 
        ADD CONSTRAINT "CHK_diary_entry_skin_type" 
        CHECK ("skin_type" IN ('global', 'student'))
      `);
    } catch (error) {
      // Constraint might already exist, ignore error
      console.log('Check constraint CHK_diary_entry_skin_type might already exist');
    }

    // Add check constraint to ensure only one skin type is set if it doesn't exist
    try {
      await queryRunner.query(`
        ALTER TABLE "diary_entry" 
        ADD CONSTRAINT "CHK_diary_entry_skin_exclusivity" 
        CHECK (
          ("skin_type" = 'global' AND "skin_id" IS NOT NULL AND "student_skin_id" IS NULL) OR
          ("skin_type" = 'student' AND "skin_id" IS NULL AND "student_skin_id" IS NOT NULL)
        )
      `);
    } catch (error) {
      // Constraint might already exist, ignore error
      console.log('Check constraint CHK_diary_entry_skin_exclusivity might already exist');
    }

    // Create indexes if they don't exist
    try {
      await queryRunner.query(`
        CREATE INDEX "IDX_diary_entry_student_skin_id" 
        ON "diary_entry" ("student_skin_id")
      `);
    } catch (error) {
      // Index might already exist, ignore error
      console.log('Index IDX_diary_entry_student_skin_id might already exist');
    }

    try {
      await queryRunner.query(`
        CREATE INDEX "IDX_diary_entry_skin_type" 
        ON "diary_entry" ("skin_type")
      `);
    } catch (error) {
      // Index might already exist, ignore error
      console.log('Index IDX_diary_entry_skin_type might already exist');
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_diary_entry_skin_type"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_diary_entry_student_skin_id"`);

    // Drop constraints
    await queryRunner.query(`ALTER TABLE "diary_entry" DROP CONSTRAINT IF EXISTS "CHK_diary_entry_skin_exclusivity"`);
    await queryRunner.query(`ALTER TABLE "diary_entry" DROP CONSTRAINT IF EXISTS "CHK_diary_entry_skin_type"`);
    await queryRunner.query(`ALTER TABLE "diary_entry" DROP CONSTRAINT IF EXISTS "FK_diary_entry_student_skin_id"`);

    // Drop columns
    await queryRunner.dropColumn('diary_entry', 'skin_type');
    await queryRunner.dropColumn('diary_entry', 'student_skin_id');
  }
}