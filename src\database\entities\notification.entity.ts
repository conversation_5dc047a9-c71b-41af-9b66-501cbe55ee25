import { <PERSON><PERSON><PERSON>, <PERSON>um<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Join<PERSON><PERSON>um<PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { User } from './user.entity';

/**
 * Types of notifications in the system
 * @enum {string}
 */
export enum NotificationType {
  /** Notification for diary entry submission */
  DIARY_SUBMISSION = 'diary_submission',
  /** Notification for diary entry update */
  DIARY_UPDATE = 'diary_update',
  /** Notification for diary entry review */
  DIARY_REVIEW = 'diary_review',
  /** Notification for diary feedback */
  DIARY_FEEDBACK = 'diary_feedback',
  /** Notification for mission creation */
  MISSION_CREATED = 'mission_created',
  /** Notification for mission submission */
  MISSION_SUBMISSION = 'mission_submission',
  MISSION_SUBMISSION_UPDATED = 'mission_submission_updated',
  /** Notification for mission feedback */
  MISSION_FEEDBACK = 'mission_feedback',
  /** Notification for mission correction */
  MISSION_CORRECTION = 'mission_correction',
  /** Notification for mission review completion */
  MISSION_REVIEW_COMPLETE = 'mission_review_complete',
  /** Notification for mission confirmation */
  MISSION_CONFIRMED = 'mission_confirmed',
  /** Notification for tutor greeting */
  TUTOR_GREETING = 'tutor_greeting',
  /** Notification for tutor assignment */
  TUTOR_ASSIGNMENT = 'tutor_assignment',
  /** Notification for tutor verification */
  TUTOR_VERIFICATION = 'tutor_verification',
  /** Notification for chat message */
  CHAT_MESSAGE = 'chat_message',
  /** Notification for Q&A assignment */
  QA_ASSIGNMENT = 'qa_assignment',
  /** Notification for Q&A submission */
  QA_SUBMISSION = 'qa_submission',
  /** Notification for Q&A review */
  QA_REVIEW = 'qa_review',
  /** Notification for Q&A feedback */
  QA_FEEDBACK = 'qa_feedback',
  /** Notification for Q&A Mission submission */
  QA_MISSION_SUBMISSION = 'qa_mission_submission',
  /** Notification for Q&A Mission review */
  QA_MISSION_REVIEW = 'qa_mission_review',
  /** Notification for Q&A Mission feedback */
  QA_MISSION_FEEDBACK = 'qa_mission_feedback',
  /** Notification for essay submission */
  ESSAY_SUBMISSION = 'essay_submission',
  /** Notification for essay review */
  ESSAY_REVIEW = 'essay_review',
  /** Notification for essay feedback */
  ESSAY_FEEDBACK = 'essay_feedback',
  /** Notification for story submission */
  STORY_SUBMISSION = 'story_submission',
  /** Notification for story review */
  STORY_REVIEW = 'story_review',
  /** Notification for novel submission */
  NOVEL_SUBMISSION = 'novel_submission',
  /** Notification for novel entry update */
  NOVEL_UPDATE = 'novel_update',
  /** Notification for novel review */
  NOVEL_REVIEW = 'novel_review',
  /** Notification for novel feedback */
  NOVEL_FEEDBACK = 'novel_feedback',
  /** Notification for award winner */
  AWARD_WINNER = 'award_winner',
  /** System notification */
  SYSTEM = 'system',
  /** Notification for story maker submission */
  STORY_MAKER_SUBMITTED = 'story_maker_submitted',
  /** Notification for story maker evaluation complete */
  STORY_MAKER_EVALUATED = 'story_maker_evaluated',
  /** Notification for story maker like */
  STORY_MAKER_LIKED = 'story_maker_liked',
  /** Notification for story maker share */
  STORY_MAKER_SHARED = 'story_maker_shared',
  /** Notification for story maker achievement */
  STORY_MAKER_ACHIEVEMENT = 'story_maker_achievement',
  /** Notification for story maker popularity update */
  STORY_MAKER_POPULARITY_UPDATE = 'story_maker_popularity_update',
  /** Notification for story maker hall of fame */
  STORY_MAKER_HALL_OF_FAME = 'story_maker_hall_of_fame',
  /** Notification for diary friend share */
  DIARY_FRIEND_SHARE = 'diary_friend_share',
  /** Notification for diary follow request */
  DIARY_FOLLOW_REQUEST = 'diary_follow_request',
  /** Notification for diary follow request accepted */
  DIARY_FOLLOW_ACCEPTED = 'diary_follow_accepted',
  /** Notification for diary follow request rejected */
  DIARY_FOLLOW_REJECTED = 'diary_follow_rejected',
}

@Entity()
export class Notification extends AuditableBaseEntity {
  @Column({ name: 'user_id' })
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({
    name: 'type',
    type: 'enum',
    enum: NotificationType,
  })
  type: NotificationType;

  @Column({ name: 'title' })
  title: string;

  @Column({ name: 'message', type: 'text' })
  message: string;

  @Column({ name: 'related_entity_id', nullable: true })
  relatedEntityId: string;

  @Column({ name: 'related_entity_type', nullable: true })
  relatedEntityType: string;

  @Column({ name: 'is_read', default: false })
  isRead: boolean;

  @Column({ name: 'read_at', nullable: true })
  readAt: Date;
}
