import { Injectable } from '@nestjs/common';
import { Plan } from '../../database/entities/plan.entity';

@Injectable()
export class EmailTemplateService {
  /**
   * Generate subscription welcome email template
   */
  generateSubscriptionWelcomeTemplate(plan: Plan, endDate: Date): string {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2c3e50;">Welcome to ${plan.name}! 🎉</h2>
        <p>Thank you for subscribing to our premium plan. Your subscription is now active and ready to use!</p>
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #495057; margin-top: 0;">Your Subscription Details:</h3>
          <ul style="color: #6c757d;">
            <li><strong>Plan:</strong> ${plan.name}</li>
            <li><strong>Type:</strong> ${plan.subscriptionType}</li>
            <li><strong>Valid Until:</strong> ${endDate.toLocaleDateString()}</li>
            <li><strong>Features:</strong> ${plan.planFeatures?.length || 0} premium features included</li>
            <li><strong>Auto-Renewal:</strong> Enabled</li>
          </ul>
        </div>
        <p>Start exploring all the amazing features your new plan has to offer!</p>
        <p style="color: #6c757d; font-size: 14px;">Thank you for choosing our premium services.</p>
      </div>
    `;
  }

  /**
   * Generate subscription payment confirmation template
   */
  generatePaymentConfirmationTemplate(plan: Plan, endDate: Date): string {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2c3e50;">Payment Completed Successfully! 🎉</h2>
        <p>Your pending payment has been completed and your plan is now active.</p>
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #495057; margin-top: 0;">Your Active Plan:</h3>
          <ul style="color: #6c757d;">
            <li><strong>Plan:</strong> ${plan.name}</li>
            <li><strong>Type:</strong> ${plan.subscriptionType}</li>
            <li><strong>Valid Until:</strong> ${endDate.toLocaleDateString()}</li>
            <li><strong>Features:</strong> ${plan.planFeatures?.length || 0} premium features included</li>
            <li><strong>Auto-Renewal:</strong> Enabled</li>
          </ul>
        </div>
        <p>Start exploring all the amazing features your plan has to offer!</p>
        <p style="color: #6c757d; font-size: 14px;">Thank you for choosing our services.</p>
      </div>
    `;
  }

  /**
   * Generate plan renewal confirmation template
   */
  generateRenewalConfirmationTemplate(plan: Plan, endDate: Date): string {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2c3e50;">Plan Renewed Successfully! 🎉</h2>
        <p>Your subscription has been automatically renewed.</p>
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #495057; margin-top: 0;">Renewal Details:</h3>
          <ul style="color: #6c757d;">
            <li><strong>Plan:</strong> ${plan.name}</li>
            <li><strong>Type:</strong> ${plan.subscriptionType}</li>
            <li><strong>Valid Until:</strong> ${endDate.toLocaleDateString()}</li>
            <li><strong>Features:</strong> ${plan.planFeatures?.length || 0} premium features included</li>
            <li><strong>Next Renewal:</strong> ${endDate.toLocaleDateString()}</li>
          </ul>
        </div>
        <p>Continue enjoying all the amazing features of your plan!</p>
        <p style="color: #6c757d; font-size: 14px;">Thank you for your continued trust in our services.</p>
      </div>
    `;
  }

  /**
   * Generate tutor assignment email template for students
   */
  generateStudentTutorAssignmentTemplate(tutorName: string, tutorId: string, tutorProfileUrl: string, modules: string[]): string {
    let modulesList = '';
    modules.forEach((moduleName) => {
      modulesList += `
        <li style="background-color: #f0f8ff; margin: 8px 0; padding: 12px 15px; border-radius: 6px; border-left: 3px solid #1a73e8;">
          <span style="color: #333; font-weight: 500;">📖 ${moduleName}</span>
        </li>`;
    });

    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 8px; background-color: #ffffff;">
        <div style="text-align: center; margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, #1a73e8 0%, #0d47a1 100%); border-radius: 8px; color: white;">
          <h1 style="margin: 0; font-size: 28px; font-weight: bold;">🎓 Welcome to Your HEC Learning Journey!</h1>
          <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">Your Path to Success Begins Here</p>
        </div>

        <div style="margin-bottom: 25px;">
          <h2 style="color: #1a73e8; font-size: 22px; margin-bottom: 15px; border-bottom: 2px solid #1a73e8; padding-bottom: 8px;">
            👨‍🏫 Meet Your Expert Tutor
          </h2>
          <div style="background-color: #f8f9ff; padding: 20px; border-radius: 8px; border-left: 4px solid #1a73e8;">
            <p style="font-size: 18px; margin: 0 0 10px 0; color: #333;">
              <strong style="color: #1a73e8;">${tutorName}</strong> will be your dedicated tutor and learning partner.
            </p>
            <p style="margin: 0; color: #666; font-size: 14px;">
              Your tutor is an experienced educator who will provide personalized guidance, feedback, and support throughout your learning journey.
            </p>
          </div>
        </div>

        <div style="margin-bottom: 25px;">
          <h3 style="color: #1a73e8; font-size: 18px; margin-bottom: 15px;">📚 Your Learning Modules:</h3>
          <ul style="list-style: none; padding: 0; margin: 0;">
            ${modulesList}
          </ul>
        </div>

        <div style="margin-bottom: 25px;">
          <h3 style="color: #1a73e8; font-size: 18px; margin-bottom: 15px;">🎯 Your Learning Experience:</h3>
          <div style="background-color: #f8f9ff; padding: 20px; border-radius: 8px; border: 1px solid #1a73e8;">
            <ul style="margin: 0; padding-left: 20px; color: #555;">
              <li style="margin-bottom: 12px;">✨ Expert guidance tailored to your learning style</li>
              <li style="margin-bottom: 12px;">📝 Detailed feedback on all your submissions</li>
              <li style="margin-bottom: 12px;">📊 Regular progress tracking and reviews</li>
              <li style="margin-bottom: 12px;">💬 Direct communication with your tutor</li>
              <li style="margin-bottom: 0;">🤝 Continuous support throughout your journey</li>
            </ul>
          </div>
        </div>

        <div style="text-align: center; margin: 30px 0;">
          <table role="presentation" style="margin: 0 auto;">
            <tr>
              <td>
                <a href="${tutorProfileUrl}/${tutorId}"
                   style="background: linear-gradient(135deg, #1a73e8 0%, #0d47a1 100%);
                          color: white;
                          padding: 16px 32px;
                          text-decoration: none;
                          border-radius: 30px;
                          font-weight: bold;
                          font-size: 16px;
                          display: inline-block;
                          box-shadow: 0 4px 15px rgba(26, 115, 232, 0.3);
                          transition: all 0.3s ease;
                          text-align: center;">
                  👤 Meet Your Tutor
                </a>
              </td>
            </tr>
          </table>
        </div>

        <div style="background-color: #f0f7ff; padding: 25px; border-radius: 12px; margin-top: 25px; text-align: center; border: 1px solid #1a73e8;">
          <h3 style="color: #1a73e8; margin: 0 0 10px 0; font-size: 20px;">Ready to Begin?</h3>
          <p style="margin: 0; color: #555; font-size: 15px; line-height: 1.5;">
            Log in to your HEC dashboard to connect with your tutor and start your personalized learning experience. Your tutor is excited to help you achieve your learning goals!
          </p>
        </div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #666; font-size: 13px; text-align: center;">
          <p style="margin: 0 0 5px 0;">This is an automated message from the HEC Learning System</p>
          <p style="margin: 0;">&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
        </div>
      </div>
    `;
  }

  /**
   * Generate tutor assignment email template for tutors
   */
  generateTutorAssignmentTemplate(studentName: string, studentId: string, studentProfileUrl: string, modules: string[]): string {
    let modulesList = '';
    modules.forEach((moduleName) => {
      modulesList += `
        <li style="background-color: #f0f8ff; margin: 8px 0; padding: 12px 15px; border-radius: 6px; border-left: 3px solid #4CAF50;">
          <span style="color: #333; font-weight: 500;">📖 ${moduleName}</span>
        </li>`;
    });

    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 8px; background-color: #ffffff;">
        <div style="text-align: center; margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%); border-radius: 8px; color: white;">
          <h1 style="margin: 0; font-size: 28px; font-weight: bold;">🎓 New Student Assignment</h1>
          <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">Welcome Your New Student</p>
        </div>

        <div style="margin-bottom: 25px;">
          <h2 style="color: #333; font-size: 22px; margin-bottom: 15px; border-bottom: 2px solid #4CAF50; padding-bottom: 8px;">
            👨‍🎓 Your New Student
          </h2>
          <div style="background-color: #f0f8f0; padding: 20px; border-radius: 8px; border-left: 4px solid #4CAF50;">
            <p style="font-size: 18px; margin: 0 0 10px 0; color: #333;">
              <strong style="color: #4CAF50;">${studentName}</strong> has been assigned to you for tutoring.
            </p>
            <p style="margin: 0; color: #666; font-size: 14px;">
              You will be guiding this student through their learning journey across <strong>${modules.length} ${modules.length === 1 ? 'module' : 'modules'}</strong>.
            </p>
          </div>
        </div>

        <div style="margin-bottom: 25px;">
          <h3 style="color: #333; font-size: 18px; margin-bottom: 15px;">📚 Modules You'll Be Teaching:</h3>
          <ul style="list-style: none; padding: 0; margin: 0;">
            ${modulesList}
          </ul>
        </div>

        <div style="margin-bottom: 25px;">
          <h3 style="color: #333; font-size: 18px; margin-bottom: 15px;">🎯 Your Responsibilities as a Tutor:</h3>
          <div style="background-color: #fff8f0; padding: 15px; border-radius: 8px; border: 1px solid #ffd700;">
            <ul style="margin: 0; padding-left: 20px; color: #555;">
              <li style="margin-bottom: 8px;"><strong>Review & Feedback:</strong> Carefully review student's diary entries and submissions</li>
              <li style="margin-bottom: 8px;"><strong>Provide Corrections:</strong> Offer constructive feedback and corrections to help improve their skills</li>
              <li style="margin-bottom: 8px;"><strong>Answer Questions:</strong> Be available to respond to any questions or concerns the student may have</li>
              <li style="margin-bottom: 8px;"><strong>Track Progress:</strong> Monitor the student's learning progress and provide encouragement</li>
              <li style="margin-bottom: 0;"><strong>Maintain Communication:</strong> Keep regular contact to ensure the student stays engaged and motivated</li>
            </ul>
          </div>
        </div>

        <div style="text-align: center; margin: 30px 0;">
          <a href="${studentProfileUrl}/${studentId}"
             style="background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
                    color: white;
                    padding: 15px 30px;
                    text-decoration: none;
                    border-radius: 25px;
                    font-weight: bold;
                    font-size: 16px;
                    display: inline-block;
                    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
                    transition: all 0.3s ease;">
            👤 View Student Profile
          </a>
        </div>

        <div style="background-color: #f9f9f9; padding: 20px; border-radius: 8px; margin-top: 25px; text-align: center;">
          <p style="margin: 0 0 10px 0; color: #333; font-weight: 500;">Ready to start mentoring?</p>
          <p style="margin: 0; color: #666; font-size: 14px;">
            Log in to the HEC system to begin interacting with your student and start making a positive impact on their learning journey.
          </p>
        </div>

        <div style="background-color: #e8f5e8; padding: 15px; border-radius: 8px; margin-top: 20px; text-align: center; border: 1px solid #4CAF50;">
          <p style="margin: 0; color: #2e7d32; font-weight: 500; font-size: 16px;">
            🌟 Thank you for your dedication to helping our students succeed! 🌟
          </p>
        </div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px; text-align: center;">
          <p style="margin: 0 0 5px 0;">This is an automated message from the HEC system.</p>
          <p style="margin: 0;">&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
        </div>
      </div>
    `;
  }
}
