import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../../database/entities/user.entity';
import { DEFAULT_TIMEZONE, SUPPORTED_TIMEZONES, SupportedTimezone } from '../../constants/timezone.constants';

@Injectable()
export class TimezoneService {
  private readonly logger = new Logger(TimezoneService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  /**
   * Get user's timezone from database or cache
   */
  async getUserTimezone(userId: string): Promise<string> {

    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
        select: ['timezone']
      });

      const timezone = user?.timezone || DEFAULT_TIMEZONE;
      return timezone;
    } catch (error) {
      this.logger.error(`Failed to get timezone for user ${userId}:`, error);
      return DEFAULT_TIMEZONE;
    }
  }

  /**
   * Convert UTC date to user's timezone
   */
  convertUTCToUserTimezone(utcDate: Date, timezone: string): string {
    try {
      // Get the date in user's timezone
      const options: Intl.DateTimeFormatOptions = {
        timeZone: timezone,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      };
      
      const formatter = new Intl.DateTimeFormat('en-CA', options);
      const parts = formatter.formatToParts(utcDate);
      
      const year = parts.find(p => p.type === 'year')?.value;
      const month = parts.find(p => p.type === 'month')?.value;
      const day = parts.find(p => p.type === 'day')?.value;
      const hour = parts.find(p => p.type === 'hour')?.value;
      const minute = parts.find(p => p.type === 'minute')?.value;
      const second = parts.find(p => p.type === 'second')?.value;
      
      // Get timezone offset
      const offset = this.getTimezoneOffsetString(utcDate, timezone);
      
      return `${year}-${month}-${day}T${hour}:${minute}:${second}.000${offset}`;
    } catch (error) {
      this.logger.error(`Failed to convert UTC to timezone ${timezone}:`, error);
      return utcDate.toISOString();
    }
  }

  /**
   * Get timezone offset string (e.g., +09:00, -05:00)
   */
  private getTimezoneOffsetString(date: Date, timezone: string): string {
    try {
      const utcDate = new Date(date.toLocaleString('en-US', { timeZone: 'UTC' }));
      const tzDate = new Date(date.toLocaleString('en-US', { timeZone: timezone }));
      const offsetMs = tzDate.getTime() - utcDate.getTime();
      const offsetHours = Math.floor(Math.abs(offsetMs) / (1000 * 60 * 60));
      const offsetMinutes = Math.floor((Math.abs(offsetMs) % (1000 * 60 * 60)) / (1000 * 60));
      const sign = offsetMs >= 0 ? '+' : '-';
      return `${sign}${offsetHours.toString().padStart(2, '0')}:${offsetMinutes.toString().padStart(2, '0')}`;
    } catch (error) {
      return '+00:00';
    }
  }

  /**
   * Convert user timezone date to UTC
   */
  convertUserTimezoneToUTC(dateString: string, timezone: string): Date {
    try {
      // Handle different input formats
      let inputDate: Date;
      
      if (dateString.includes('T')) {
        // ISO format with time
        inputDate = new Date(dateString);
      } else {
        // Date only format (YYYY-MM-DD)
        const [year, month, day] = dateString.split('-').map(Number);
        inputDate = new Date(year, month - 1, day);
      }
      
      if (isNaN(inputDate.getTime())) {
        throw new Error('Invalid date format');
      }
      
      // Create a date in the user's timezone
      const userTimezoneDate = new Date(inputDate.toLocaleString('en-US', { timeZone: timezone }));
      const utcDate = new Date(inputDate.toLocaleString('en-US', { timeZone: 'UTC' }));
      
      // Calculate the offset and adjust
      const offset = userTimezoneDate.getTime() - utcDate.getTime();
      return new Date(inputDate.getTime() - offset);
    } catch (error) {
      this.logger.error(`Failed to convert timezone ${timezone} to UTC:`, error);
      return new Date(dateString);
    }
  }

  /**
   * Get timezone offset in milliseconds
   */
  private getTimezoneOffset(timezone: string, date: Date): number {
    try {
      const utcDate = new Date(date.toLocaleString('en-US', { timeZone: 'UTC' }));
      const tzDate = new Date(date.toLocaleString('en-US', { timeZone: timezone }));
      return utcDate.getTime() - tzDate.getTime();
    } catch (error) {
      return 0;
    }
  }

  /**
   * Handle edge cases for timezone conversion
   */
  private handleTimezoneEdgeCases(date: Date, timezone: string): Date {
    try {
      // Handle DST transitions
      const beforeDST = new Date(date.getTime() - 24 * 60 * 60 * 1000);
      const afterDST = new Date(date.getTime() + 24 * 60 * 60 * 1000);
      
      const offsetBefore = this.getTimezoneOffset(timezone, beforeDST);
      const offsetCurrent = this.getTimezoneOffset(timezone, date);
      const offsetAfter = this.getTimezoneOffset(timezone, afterDST);
      
      // If offset changed, we might be in a DST transition
      if (offsetBefore !== offsetCurrent || offsetCurrent !== offsetAfter) {
        this.logger.warn(`DST transition detected for ${timezone} on ${date.toISOString()}`);
      }
      
      return date;
    } catch (error) {
      this.logger.error(`Error handling timezone edge cases:`, error);
      return date;
    }
  }

  /**
   * Get date range for user's timezone (start and end of day)
   */
  getUserDateRange(dateString: string, timezone: string): { start: Date; end: Date } {
    try {
      // Parse date string (YYYY-MM-DD format)
      const [year, month, day] = dateString.split('-').map(Number);
      if (!year || !month || !day) {
        throw new Error('Invalid date format. Expected YYYY-MM-DD');
      }
      
      // Create start and end of day in user's timezone
      const startOfDayLocal = `${dateString}T00:00:00`;
      const endOfDayLocal = `${dateString}T23:59:59.999`;
      
      // Convert to UTC
      const startUTC = this.convertUserTimezoneToUTC(startOfDayLocal, timezone);
      const endUTC = this.convertUserTimezoneToUTC(endOfDayLocal, timezone);
      
      return { start: startUTC, end: endUTC };
    } catch (error) {
      this.logger.error(`Failed to get date range for ${dateString} in ${timezone}:`, error);
      // Fallback to current day in UTC
      const now = new Date();
      const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const endOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59, 999);
      return { start: startOfDay, end: endOfDay };
    }
  }

  /**
   * Validate timezone
   */
  isValidTimezone(timezone: string): boolean {
    return SUPPORTED_TIMEZONES.includes(timezone as SupportedTimezone);
  }

}