# Transcription Module API Documentation

## Overview

The Transcription module provides English sentence copying functionality using public domain classic children's books. Students practice writing by copying sentences from selected texts, improving their grammar, vocabulary, and writing accuracy through structured practice sessions.

## Educational Benefits

- **Grammar Reinforcement**: Students internalize grammatical structures through repeated copying
- **Vocabulary Expansion**: Learners encounter words in context from classic literature
- **Spelling & Mechanics**: Repeated copying solidifies correct spelling and punctuation
- **Pattern Recognition**: Helps students identify recurring linguistic structures
- **Confidence Building**: Provides safe, guided environment for writing practice

## UI Flows for All Users

### Student User Flow

#### 1. Entry Point
```
Main Dashboard → Transcription Practice Card → [Start Practice]
```

#### 2. Book Selection Flow
```
┌─────────────────────────────────┐
│ 📚 Choose Your Story            │
├─────────────────────────────────┤
│ ┌─────────────────────────────┐ │
│ │ 🧜♀️ The Little Mermaid      │ │
│ │ Hans Christian Andersen     │ │
│ │ 12 sentences • Easy level   │ │
│ │ [Select] 📖                 │ │
│ └─────────────────────────────┘ │
│ ┌─────────────────────────────┐ │
│ │ 🐰 Alice in Wonderland      │ │
│ │ Lewis Carroll               │ │
│ │ 25 sentences • Medium       │ │
│ │ [Select] 📖                 │ │
│ └─────────────────────────────┘ │
└─────────────────────────────────┘
```

#### 3. Practice Session Flow
```
┌─────────────────────────────────┐
│ 📝 Sentence 1 of 12             │
├─────────────────────────────────┤
│ From: The Little Mermaid        │
│                                 │
│ ┌─────────────────────────────┐ │
│ │ "Far out in the ocean the   │ │
│ │  water is as blue as the    │ │
│ │  petals of the cornflower." │ │
│ └─────────────────────────────┘ │
│                                 │
│ Type the sentence:              │
│ ┌─────────────────────────────┐ │
│ │ Far out in the ocean the    │ │
│ │ water is as blue as the     │ │
│ │ petals of the cornflower.   │ │
│ └─────────────────────────────┘ │
│                                 │
│ [Check] ✓ [Skip] → [Help] ?     │
└─────────────────────────────────┘
```

#### 4. Feedback Flow
```
┌─────────────────────────────────┐
│ 🎉 Great Job!                   │
├─────────────────────────────────┤
│ ✅ Perfect copy!                │
│                                 │
│ You practiced:                  │
│ • Descriptive adjectives        │
│ • Compound sentences            │
│ • Quotation marks               │
│                                 │
│ Time: 1m 23s                    │
│                                 │
│ [Next Sentence] → [Finish] 🏁   │
└─────────────────────────────────┘
```

#### 5. Session Complete Flow
```
┌─────────────────────────────────┐
│ 🏆 Session Complete!            │
├─────────────────────────────────┤
│ The Little Mermaid              │
│                                 │
│ 📊 Your Results:                │
│ • 12/12 sentences completed     │
│ • 10 correct on first try       │
│ • 2 needed corrections          │
│ • Total time: 15m 30s           │
│                                 │
│ 🎯 Skills Practiced:            │
│ • Past tense verbs ✓            │
│ • Descriptive language ✓        │
│ • Punctuation ✓                 │
│                                 │
│ [Practice More] 📚 [Home] 🏠    │
└─────────────────────────────────┘
```

#### 6. Progress View Flow
```
┌─────────────────────────────────┐
│ 📈 My Progress                  │
├─────────────────────────────────┤
│ This Week: 3 sessions           │
│ Accuracy: 85% ↗️                │
│                                 │
│ Recent Sessions:                │
│ ┌─────────────────────────────┐ │
│ │ 🧜♀️ The Little Mermaid      │ │
│ │ 12/12 • 83% accuracy        │ │
│ │ Jan 27, 2025                │ │
│ └─────────────────────────────┘ │
│ ┌─────────────────────────────┐ │
│ │ 🐰 Alice in Wonderland      │ │
│ │ 8/25 • In Progress          │ │
│ │ Jan 26, 2025                │ │
│ └─────────────────────────────┘ │
│                                 │
│ [View Details] 📊               │
└─────────────────────────────────┘
```

### Admin User Flow

#### 1. Content Management Dashboard
```
┌─────────────────────────────────┐
│ 📚 Transcription Management     │
├─────────────────────────────────┤
│ Books Library: 31 books         │
│ Active Sessions: 45             │
│                                 │
│ Quick Actions:                  │
│ [+ Add New Book] 📖             │
│ [📊 View Analytics]             │
│ [⚙️ Settings]                   │
│                                 │
│ Recent Activity:                │
│ • 156 sentences copied today    │
│ • 23 new sessions started       │
│ • 89% average accuracy          │
└─────────────────────────────────┘
```

#### 2. Add Book Flow
```
┌─────────────────────────────────┐
│ ➕ Add New Book                 │
├─────────────────────────────────┤
│ Title: [Alice in Wonderland]    │
│ Author: [Lewis Carroll]         │
│ Year: [1865]                    │
│                                 │
│ Content Source:                 │
│ ○ Upload HTML file              │
│ ● Paste text content            │
│                                 │
│ ┌─────────────────────────────┐ │
│ │ Chapter 1. Down the Rabbit- │ │
│ │ Hole. Alice was beginning   │ │
│ │ to get very tired...        │ │
│ └─────────────────────────────┘ │
│                                 │
│ [Extract Sentences] ⚡          │
│ [Save Book] 💾                  │
└─────────────────────────────────┘
```

#### 3. Sentence Extraction Flow
```
┌─────────────────────────────────┐
│ ⚡ Extracting Sentences...      │
├─────────────────────────────────┤
│ Processing: Alice in Wonderland │
│                                 │
│ ✅ Found 47 sentences           │
│ ✅ Classified difficulty levels │
│ ✅ Identified grammar patterns  │
│                                 │
│ Preview:                        │
│ 1. "Alice was beginning to get  │
│    very tired..." (Medium)      │
│ 2. "So she was considering in   │
│    her own mind..." (Hard)      │
│                                 │
│ [Approve & Save] ✓              │
│ [Edit Sentences] ✏️             │
└─────────────────────────────────┘
```

#### 4. Analytics Dashboard Flow
```
┌─────────────────────────────────┐
│ 📊 System Analytics             │
├─────────────────────────────────┤
│ Usage Overview (Last 30 days):  │
│ • 1,247 practice sessions       │
│ • 15,623 sentences copied       │
│ • 87% average accuracy          │
│                                 │
│ Popular Books:                  │
│ 1. Alice in Wonderland (234)    │
│ 2. The Little Mermaid (189)     │
│ 3. Snow White (156)             │
│                                 │
│ Common Error Patterns:          │
│ • Spelling: 45%                 │
│ • Punctuation: 32%              │
│ • Capitalization: 23%           │
│                                 │
│ [Export Report] 📄              │
└─────────────────────────────────┘
```

### Tutor User Flow

#### 1. Student Monitoring Dashboard
```
┌─────────────────────────────────┐
│ 👨‍🏫 My Students - Transcription  │
├─────────────────────────────────┤
│ Active Students: 12             │
│                                 │
│ ┌─────────────────────────────┐ │
│ │ 👦 Alex Johnson             │ │
│ │ Last session: 2 hours ago   │ │
│ │ Progress: 85% accuracy      │ │
│ │ [View Details] 👁️           │ │
│ └─────────────────────────────┘ │
│ ┌─────────────────────────────┐ │
│ │ 👧 Sarah Chen               │ │
│ │ Last session: 1 day ago     │ │
│ │ Progress: 92% accuracy      │ │
│ │ [View Details] 👁️           │ │
│ └─────────────────────────────┘ │
│                                 │
│ [Class Overview] 📋             │
└─────────────────────────────────┘
```

#### 2. Student Detail View Flow
```
┌─────────────────────────────────┐
│ 👦 Alex Johnson - Progress      │
├─────────────────────────────────┤
│ Overall Stats:                  │
│ • 15 sessions completed         │
│ • 187 sentences practiced       │
│ • 85% average accuracy          │
│ • 12m average session time      │
│                                 │
│ Recent Sessions:                │
│ ┌─────────────────────────────┐ │
│ │ The Little Mermaid          │ │
│ │ 12/12 sentences • 83%       │ │
│ │ Common errors: spelling     │ │
│ │ [Review Errors] 🔍          │ │
│ └─────────────────────────────┘ │
│                                 │
│ Improvement Areas:              │
│ • Focus on punctuation          │
│ • Practice compound sentences   │
│                                 │
│ [Send Feedback] 💬              │
└─────────────────────────────────┘
```

#### 3. Error Analysis Flow
```
┌─────────────────────────────────┐
│ 🔍 Error Analysis - Alex        │
├─────────────────────────────────┤
│ Session: The Little Mermaid     │
│ Date: Jan 27, 2025              │
│                                 │
│ Sentence 3 Errors:              │
│ Original: "Far out in the ocean │
│           the water is blue."   │
│ Student:  "Far out in the ocean │
│           the water is bleu."   │
│                                 │
│ ❌ Spelling: "blue" → "bleu"    │
│ ⏱️ Time: 2m 15s (slow)          │
│                                 │
│ Recommendation:                 │
│ Practice words with 'ue' ending │
│                                 │
│ [Next Error] → [Send Note] 📝   │
└─────────────────────────────────┘
```

#### 4. Class Overview Flow
```
┌─────────────────────────────────┐
│ 📋 Class Overview               │
├─────────────────────────────────┤
│ Class Average: 88% accuracy     │
│ Most Active: Sarah Chen (5 hrs) │
│ Needs Attention: Mike Liu       │
│                                 │
│ Weekly Progress:                │
│ ████████░░ 80% completion       │
│                                 │
│ Common Class Challenges:        │
│ 1. Punctuation (45% errors)     │
│ 2. Complex sentences (32%)      │
│ 3. Capitalization (23%)         │
│                                 │
│ Recommended Focus:              │
│ • Assign punctuation practice   │
│ • Review compound sentences     │
│                                 │
│ [Generate Report] 📊            │
│ [Assign Practice] 📝            │
└─────────────────────────────────┘
```

## Navigation Flow Summary

### Student Journey
```
Dashboard → Book Selection → Practice Session → Feedback → Progress View
    ↑                                                           ↓
    ←─────────────── Session Complete ←─────────────────────────┘
```

### Admin Journey
```
Dashboard → Add Book → Extract Sentences → Monitor Analytics
    ↑                                           ↓
    ←─────── Content Management ←───────────────┘
```

### Tutor Journey
```
Student List → Student Details → Error Analysis → Feedback
    ↑                                               ↓
    ←─────────── Class Overview ←──────────────────┘
```

## Database Schema

### Entities

#### Book Entity
- **id**: UUID (Primary Key)
- **title**: Book title (e.g., "Alice's Adventures in Wonderland")
- **author**: Author name (e.g., "Lewis Carroll")
- **description**: Book description
- **publicationYear**: Year of publication
- **content**: Full book content for sentence extraction
- **isActive**: Whether book is available for practice

#### Sentence Entity
- **id**: UUID (Primary Key)
- **content**: The sentence text for copying
- **orderIndex**: Order within the book
- **difficultyLevel**: easy/medium/hard classification
- **grammarPattern**: Identified grammar pattern (past_tense, compound, etc.)
- **bookId**: Reference to parent book

#### TranscriptionSession Entity
- **id**: UUID (Primary Key)
- **studentId**: Reference to student user
- **startedAt**: Session start timestamp
- **completedAt**: Session completion timestamp
- **totalAttempts**: Number of sentences attempted
- **correctAttempts**: Number of correct attempts
- **isCompleted**: Session completion status

#### TranscriptionAttempt Entity
- **id**: UUID (Primary Key)
- **sessionId**: Reference to practice session
- **sentenceId**: Reference to sentence being copied
- **userInput**: Student's typed sentence
- **isCorrect**: Whether the copy was accurate
- **errors**: JSON object containing error details
- **timeSpentSeconds**: Time taken for the attempt
- **attemptedAt**: Attempt timestamp

## API Endpoints

### Admin Endpoints

#### Create Book
```http
POST /transcription/admin/books
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
  "title": "Alice's Adventures in Wonderland",
  "author": "Lewis Carroll",
  "description": "A classic children's story about Alice's journey down the rabbit hole.",
  "publicationYear": 1865,
  "content": "Chapter 1. Down the Rabbit-Hole. Alice was beginning to get very tired..."
}
```

**Response:**
```json
{
  "success": true,
  "message": "Book created successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-************",
    "title": "Alice's Adventures in Wonderland",
    "author": "Lewis Carroll",
    "description": "A classic children's story...",
    "publicationYear": 1865,
    "sentenceCount": 0,
    "isActive": true,
    "createdAt": "2025-01-27T10:00:00Z"
  },
  "statusCode": 201
}
```

#### Get All Books (Admin)
```http
GET /transcription/admin/books
Authorization: Bearer {jwt_token}
```

#### Extract Sentences from Book
```http
POST /transcription/admin/books/{bookId}/sentences
Authorization: Bearer {jwt_token}
```

**Response:**
```json
{
  "success": true,
  "message": "Sentences extracted successfully",
  "data": [
    {
      "id": "sentence-uuid-1",
      "content": "Alice was beginning to get very tired of sitting by her sister on the bank.",
      "orderIndex": 1,
      "difficultyLevel": "medium",
      "grammarPattern": "past_tense",
      "bookId": "123e4567-e89b-12d3-a456-************"
    }
  ]
}
```

#### Delete Book
```http
DELETE /transcription/admin/books/{bookId}
Authorization: Bearer {jwt_token}
```

### Student Endpoints

#### Get Available Books
```http
GET /transcription/books
Authorization: Bearer {jwt_token}
```

**Response:**
```json
{
  "success": true,
  "message": "Books retrieved successfully",
  "data": [
    {
      "id": "123e4567-e89b-12d3-a456-************",
      "title": "Alice's Adventures in Wonderland",
      "author": "Lewis Carroll",
      "description": "A classic children's story...",
      "publicationYear": 1865,
      "sentenceCount": 25,
      "isActive": true,
      "createdAt": "2025-01-27T10:00:00Z"
    }
  ]
}
```

#### Get Book Sentences
```http
GET /transcription/books/{bookId}/sentences
Authorization: Bearer {jwt_token}
```

#### Start Practice Session
```http
POST /transcription/sessions
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
  "bookId": "123e4567-e89b-12d3-a456-************"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Session started successfully",
  "data": {
    "id": "session-uuid-1",
    "studentId": "student-uuid-1",
    "startedAt": "2025-01-27T10:00:00Z",
    "completedAt": null,
    "totalAttempts": 0,
    "correctAttempts": 0,
    "isCompleted": false
  },
  "statusCode": 201
}
```

#### Submit Sentence Copy
```http
POST /transcription/sessions/{sessionId}/attempts
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
  "sentenceId": "sentence-uuid-1",
  "userInput": "Alice was begining to get very tired of sitting by her sister on the bank.",
  "timeSpentSeconds": 45
}
```

**Response:**
```json
{
  "success": true,
  "message": "Attempt submitted successfully",
  "data": {
    "id": "attempt-uuid-1",
    "sentenceId": "sentence-uuid-1",
    "userInput": "Alice was begining to get very tired of sitting by her sister on the bank.",
    "isCorrect": false,
    "errors": [
      {
        "type": "spelling",
        "position": 2,
        "expected": "beginning",
        "actual": "begining"
      }
    ],
    "timeSpentSeconds": 45,
    "attemptedAt": "2025-01-27T10:05:00Z",
    "sentence": {
      "id": "sentence-uuid-1",
      "content": "Alice was beginning to get very tired of sitting by her sister on the bank.",
      "orderIndex": 1,
      "difficultyLevel": "medium",
      "grammarPattern": "past_tense",
      "bookId": "123e4567-e89b-12d3-a456-************"
    }
  },
  "statusCode": 201
}
```

#### Get Session Details
```http
GET /transcription/sessions/{sessionId}
Authorization: Bearer {jwt_token}
```

#### Get Session Attempts
```http
GET /transcription/sessions/{sessionId}/attempts
Authorization: Bearer {jwt_token}
```

#### Get Student Progress
```http
GET /transcription/my-progress
Authorization: Bearer {jwt_token}
```

### Tutor Endpoints

#### Get Student Sessions
```http
GET /transcription/tutor/students/{studentId}/sessions
Authorization: Bearer {jwt_token}
```

#### Get Session Attempts for Review
```http
GET /transcription/tutor/sessions/{sessionId}/attempts
Authorization: Bearer {jwt_token}
```

## Error Validation

### Sentence Validation Algorithm

The system performs character-by-character comparison and identifies:

1. **Spelling Errors**: Word-level comparison with position tracking
2. **Punctuation Errors**: End-of-sentence punctuation validation
3. **Capitalization Errors**: Proper capitalization checking
4. **Missing Words**: Detection of omitted words
5. **Extra Words**: Detection of additional words

### Error Response Format

```json
{
  "type": "spelling|punctuation|capitalization",
  "position": 2,
  "expected": "beginning",
  "actual": "begining"
}
```

## Educational Features

### Difficulty Classification
- **Easy**: 8 words or fewer
- **Medium**: 9-15 words
- **Hard**: 16+ words

### Grammar Pattern Recognition
- **past_tense**: Contains "was", "were"
- **compound**: Contains "and", "but"
- **descriptive**: Contains "very", "quite"
- **simple**: Default pattern

### Progress Tracking
- Session completion rates
- Accuracy percentages
- Time spent per sentence
- Common error patterns
- Grammar pattern mastery

## Integration Points

### Existing Systems
- **User Management**: Student/tutor/admin roles
- **Authentication**: JWT-based security
- **File Storage**: Book content storage
- **Audit Logging**: Activity tracking

### Future Enhancements
- **Awards Integration**: Achievement tracking
- **Notification System**: Progress updates
- **Analytics Dashboard**: Detailed progress visualization
- **Gamification**: Badges and streaks

## Usage Examples

### Complete Practice Flow

1. **Student selects book**: `GET /transcription/books`
2. **Start session**: `POST /transcription/sessions`
3. **Get sentences**: `GET /transcription/books/{bookId}/sentences`
4. **Submit attempts**: `POST /transcription/sessions/{sessionId}/attempts`
5. **View progress**: `GET /transcription/my-progress`

### Admin Content Management

1. **Upload book**: `POST /transcription/admin/books`
2. **Extract sentences**: `POST /transcription/admin/books/{bookId}/sentences`
3. **Monitor usage**: `GET /transcription/admin/books`

### Tutor Monitoring

1. **View student sessions**: `GET /transcription/tutor/students/{studentId}/sessions`
2. **Review attempts**: `GET /transcription/tutor/sessions/{sessionId}/attempts`
3. **Provide feedback**: Based on error patterns and progress data

## Mobile Responsive Considerations

### Mobile Student Flow
- **Larger touch targets** for practice buttons
- **Swipe gestures** for navigation between sentences
- **Auto-zoom prevention** on input fields
- **Portrait-optimized** sentence display
- **Thumb-friendly** button placement

### Tablet Optimizations
- **Split-screen view** showing original and input side-by-side
- **Larger text display** for better readability
- **Touch-friendly** error correction interface

### Accessibility Features
- **Screen reader support** for sentence content
- **High contrast mode** for error highlighting
- **Keyboard navigation** for all interactions
- **Voice input support** for typing assistance
- **Adjustable font sizes** for different reading levels

This module provides a comprehensive sentence copying practice system that aligns with the educational benefits outlined in the original proposal while maintaining consistency with the existing HEC backend architecture and providing intuitive user flows for all user types.