import { Test, TestingModule } from '@nestjs/testing';
import { LoggerService } from './logger.service';
import { SecureTestDataFactory } from '../../../test/fixtures/factories/secure-test-data.factory';

describe('LoggerService Security Tests', () => {
  let service: LoggerService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [LoggerService],
    }).compile();

    service = module.get<LoggerService>(LoggerService);
  });

  describe('Log Injection Prevention', () => {
    it('should sanitize log inputs to prevent injection', () => {
      const maliciousInputs = SecureTestDataFactory.getMaliciousInputs();
      const logInjectionInputs = [
        'user\n\r[ADMIN] Unauthorized access granted',
        'test\r\n[ERROR] System compromised',
        'input\n[CRITICAL] Security breach detected',
      ];

      logInjectionInputs.forEach(input => {
        // Mock console methods to capture output
        const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
        
        service.log(input);
        
        // Verify that newlines and carriage returns are removed
        const loggedMessage = consoleSpy.mock.calls[0]?.[0] || '';
        expect(loggedMessage).not.toContain('\n');
        expect(loggedMessage).not.toContain('\r');
        expect(loggedMessage).not.toMatch(/\[ADMIN\]/);
        expect(loggedMessage).not.toMatch(/\[ERROR\]/);
        expect(loggedMessage).not.toMatch(/\[CRITICAL\]/);
        
        consoleSpy.mockRestore();
      });
    });

    it('should not log sensitive data', () => {
      const sensitiveData = {
        password: 'secret123',
        token: 'jwt-token-123',
        apiKey: 'api-key-456',
        creditCard: '1234-5678-9012-3456',
      };

      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      service.log('User data', sensitiveData);
      
      const loggedMessage = consoleSpy.mock.calls[0]?.[0] || '';
      expect(loggedMessage).not.toContain('secret123');
      expect(loggedMessage).not.toContain('jwt-token-123');
      expect(loggedMessage).not.toContain('api-key-456');
      expect(loggedMessage).not.toContain('1234-5678-9012-3456');
      
      consoleSpy.mockRestore();
    });

    it('should handle special characters safely', () => {
      const specialChars = ['<script>', '${jndi:ldap}', '{{7*7}}', '<%=7*7%>'];
      
      specialChars.forEach(char => {
        const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
        
        service.log(`Test with ${char}`);
        
        const loggedMessage = consoleSpy.mock.calls[0]?.[0] || '';
        // Should escape or remove dangerous patterns
        expect(loggedMessage).not.toContain('<script>');
        expect(loggedMessage).not.toContain('${jndi:');
        expect(loggedMessage).not.toContain('{{');
        expect(loggedMessage).not.toContain('<%=');
        
        consoleSpy.mockRestore();
      });
    });
  });

  describe('Error Logging Security', () => {
    it('should sanitize error messages', () => {
      const maliciousError = new Error('Database error\n[ADMIN] Full access granted');
      
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      service.error('Operation failed', maliciousError.stack);
      
      const loggedMessage = consoleSpy.mock.calls[0]?.[0] || '';
      expect(loggedMessage).not.toContain('\n[ADMIN]');
      expect(loggedMessage).not.toMatch(/\[ADMIN\]/);
      
      consoleSpy.mockRestore();
    });

    it('should not expose stack traces in production', () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';
      
      const error = new Error('Test error');
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      service.error('Test error', error.stack);
      
      const loggedMessage = consoleSpy.mock.calls[0]?.[0] || '';
      // In production, should not log full stack traces
      expect(loggedMessage).not.toContain('at Object.');
      expect(loggedMessage).not.toContain('.spec.ts:');
      
      consoleSpy.mockRestore();
      process.env.NODE_ENV = originalEnv;
    });
  });

  describe('Context Logging Security', () => {
    it('should sanitize context data', () => {
      const maliciousContext = {
        userId: 'TEST123',
        action: 'login\n[ADMIN] Privilege escalation',
        ip: '***********',
      };
      
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      service.log('User action', maliciousContext);
      
      const loggedMessage = consoleSpy.mock.calls[0]?.[0] || '';
      expect(loggedMessage).not.toContain('\n[ADMIN]');
      expect(loggedMessage).not.toMatch(/\[ADMIN\]/);
      
      consoleSpy.mockRestore();
    });

    it('should validate log levels', () => {
      const validLevels = ['log', 'error', 'warn', 'debug', 'verbose'];
      
      validLevels.forEach(level => {
        expect(() => {
          (service as any)[level]('Test message');
        }).not.toThrow();
      });
    });
  });

  describe('Performance and Rate Limiting', () => {
    it('should handle high volume logging safely', () => {
      const startTime = Date.now();
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      // Log 1000 messages
      for (let i = 0; i < 1000; i++) {
        service.log(`Test message ${i}`);
      }
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Should complete within reasonable time (less than 1 second)
      expect(duration).toBeLessThan(1000);
      expect(consoleSpy).toHaveBeenCalledTimes(1000);
      
      consoleSpy.mockRestore();
    });

    it('should prevent log bombing attacks', () => {
      const largMessage = 'A'.repeat(10000); // 10KB message
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      service.log(largMessage);
      
      const loggedMessage = consoleSpy.mock.calls[0]?.[0] || '';
      // Should truncate very large messages
      expect(loggedMessage.length).toBeLessThan(5000);
      
      consoleSpy.mockRestore();
    });
  });
});