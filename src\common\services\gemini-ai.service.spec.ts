import { Test, TestingModule } from '@nestjs/testing';
import { GeminiAiService } from './gemini-ai.service';
import { ConfigService } from '@nestjs/config';
import { LoggerService } from './logger.service';

describe('GeminiAiService', () => {
  let service: GeminiAiService;
  let mockConfigService: jest.Mocked<ConfigService>;
  let mockLoggerService: jest.Mocked<LoggerService>;

  beforeEach(async () => {
    const mockConfig = {
      get: jest.fn(),
    };

    const mockLogger = {
      log: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GeminiAiService,
        {
          provide: ConfigService,
          useValue: mockConfig,
        },
        {
          provide: LoggerService,
          useValue: mockLogger,
        },
      ],
    }).compile();

    service = module.get<GeminiAiService>(GeminiAiService);
    mockConfigService = module.get(ConfigService);
    mockLoggerService = module.get(LoggerService);
  });

  describe('Core Functionality', () => {
    it('should be defined', () => {
      expect(service).toBeDefined();
    });

    it('should analyze text content successfully', async () => {
      const testContent = 'This is a sample text for analysis.';
      mockConfigService.get.mockReturnValue('test-api-key');

      // Mock the Gemini API response
      jest.spyOn(service as any, 'callGeminiApi').mockResolvedValue({
        analysis: 'Positive sentiment',
        score: 0.8,
        suggestions: ['Good writing style'],
      });

      const result = await service.analyzeText(testContent);

      expect(result).toBeDefined();
      expect(result.analysis).toBe('Positive sentiment');
      expect(result.score).toBe(0.8);
    });

    it('should generate writing suggestions', async () => {
      const testContent = 'Short text.';
      mockConfigService.get.mockReturnValue('test-api-key');

      jest.spyOn(service as any, 'callGeminiApi').mockResolvedValue({
        suggestions: [
          'Consider expanding your ideas',
          'Add more descriptive details',
        ],
      });

      const result = await service.generateSuggestions(testContent);

      expect(result).toBeDefined();
      expect(Array.isArray(result.suggestions)).toBe(true);
      expect(result.suggestions.length).toBeGreaterThan(0);
    });

    it('should evaluate essay quality', async () => {
      const essayContent = 'This is a well-written essay with good structure.';
      mockConfigService.get.mockReturnValue('test-api-key');

      jest.spyOn(service as any, 'callGeminiApi').mockResolvedValue({
        score: 85,
        feedback: 'Well structured essay',
        areas_for_improvement: ['Grammar', 'Vocabulary'],
      });

      const result = await service.evaluateEssay(essayContent);

      expect(result).toBeDefined();
      expect(result.score).toBe(85);
      expect(result.feedback).toBeDefined();
    });
  });

  describe('Security & Validation', () => {
    it('should validate API key configuration', () => {
      mockConfigService.get.mockReturnValue(null);

      expect(() => service.validateConfiguration()).toThrow('Gemini API key not configured');
    });

    it('should sanitize input content', () => {
      const maliciousContent = '<script>alert("xss")</script>Test content';
      const sanitized = service.sanitizeInput(maliciousContent);

      expect(sanitized).not.toContain('<script>');
      expect(sanitized).toContain('Test content');
    });

    it('should handle rate limiting', async () => {
      mockConfigService.get.mockReturnValue('test-api-key');
      
      jest.spyOn(service as any, 'callGeminiApi').mockRejectedValue({
        status: 429,
        message: 'Rate limit exceeded',
      });

      await expect(service.analyzeText('test')).rejects.toThrow('Rate limit exceeded');
      expect(mockLoggerService.warn).toHaveBeenCalledWith(
        expect.stringContaining('Rate limit')
      );
    });

    it('should validate content length limits', () => {
      const longContent = 'a'.repeat(10001); // Assuming 10k limit
      
      expect(() => service.validateContentLength(longContent)).toThrow(
        'Content exceeds maximum length'
      );
    });
  });

  describe('Error Handling', () => {
    it('should handle API connection errors', async () => {
      mockConfigService.get.mockReturnValue('test-api-key');
      
      jest.spyOn(service as any, 'callGeminiApi').mockRejectedValue(
        new Error('Network error')
      );

      await expect(service.analyzeText('test')).rejects.toThrow('Network error');
      expect(mockLoggerService.error).toHaveBeenCalled();
    });

    it('should handle invalid API responses', async () => {
      mockConfigService.get.mockReturnValue('test-api-key');
      
      jest.spyOn(service as any, 'callGeminiApi').mockResolvedValue(null);

      await expect(service.analyzeText('test')).rejects.toThrow(
        'Invalid API response'
      );
    });

    it('should handle authentication errors', async () => {
      mockConfigService.get.mockReturnValue('invalid-key');
      
      jest.spyOn(service as any, 'callGeminiApi').mockRejectedValue({
        status: 401,
        message: 'Invalid API key',
      });

      await expect(service.analyzeText('test')).rejects.toThrow('Invalid API key');
    });
  });

  describe('Edge Cases', () => {
    it('should handle null/undefined inputs', async () => {
      await expect(service.analyzeText(null)).rejects.toThrow();
      await expect(service.analyzeText(undefined)).rejects.toThrow();
      await expect(service.analyzeText('')).rejects.toThrow();
    });

    it('should handle empty content', async () => {
      await expect(service.analyzeText('   ')).rejects.toThrow(
        'Content cannot be empty'
      );
    });

    it('should handle special characters and unicode', async () => {
      const unicodeContent = '测试内容 🚀 émojis and spëcial chars';
      mockConfigService.get.mockReturnValue('test-api-key');

      jest.spyOn(service as any, 'callGeminiApi').mockResolvedValue({
        analysis: 'Unicode content processed',
        score: 0.7,
      });

      const result = await service.analyzeText(unicodeContent);
      expect(result).toBeDefined();
    });

    it('should handle concurrent requests', async () => {
      mockConfigService.get.mockReturnValue('test-api-key');
      
      jest.spyOn(service as any, 'callGeminiApi').mockResolvedValue({
        analysis: 'Concurrent test',
        score: 0.5,
      });

      const promises = Array(5).fill(0).map(() => 
        service.analyzeText('concurrent test')
      );

      const results = await Promise.all(promises);
      expect(results).toHaveLength(5);
      results.forEach(result => {
        expect(result.analysis).toBe('Concurrent test');
      });
    });
  });
});