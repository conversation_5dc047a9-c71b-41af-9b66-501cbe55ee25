import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { JwtService } from '@nestjs/jwt';
import { DataSource } from 'typeorm';
import { BadRequestException, UnauthorizedException, ConflictException, NotFoundException } from '@nestjs/common';
import { AuthService } from './auth.service';
import { UsersService } from '../users/users.service';
import EmailService from '../../common/services/email.service';
import { ProfilePictureService } from '../../common/services/profile-picture.service';
import { DiaryService } from '../diary/diary.service';
import { AsyncNotificationHelperService } from '../notification/async-notification-helper.service';
import { DeeplinkService } from '../../common/utils/deeplink.service';
import { TokenBlacklistService } from '../../common/services/token-blacklist.service';
import { EmailTemplateService } from '../../common/services/email-template.service';
import { PlansService } from '../plans/plans.service';
import { Role } from '../../database/entities/role.entity';
import { User, UserType } from '../../database/entities/user.entity';
import { UserPlan } from '../../database/entities/user-plan.entity';
import { PasswordReset } from '../../database/entities/password-reset.entity';
import { EmailVerification } from '../../database/entities/email-verification.entity';
import { TutorApproval, TutorApprovalStatus } from '../../database/entities/tutor-approval.entity';
import { Diary } from '../../database/entities/diary.entity';
import { LoginUserDto, RegisterDto, ForgotPasswordDto, ResetPasswordDto, ChangePasswordDto } from '../../database/models/users.dto';
import { SecureTestDataFactory } from '../../../test/fixtures/factories/secure-test-data.factory';

describe('AuthService', () => {
  let service: AuthService;
  let usersService: jest.Mocked<UsersService>;
  let jwtService: jest.Mocked<JwtService>;
  let emailService: jest.Mocked<EmailService>;
  let profilePictureService: jest.Mocked<ProfilePictureService>;
  let userRepository: any;
  let emailVerificationRepository: any;
  let passwordResetRepository: any;
  let tutorApprovalRepository: any;
  let dataSource: any;

  beforeEach(async () => {
    const mockUserRepository = {
      findOne: jest.fn(),
      find: jest.fn(),
      create: jest.fn(),
      save: jest.fn(),
    };

    const mockEmailVerificationRepository = {
      findOne: jest.fn(),
      save: jest.fn(),
      delete: jest.fn(),
    };

    const mockPasswordResetRepository = {
      findOne: jest.fn(),
      save: jest.fn(),
      delete: jest.fn(),
    };

    const mockTutorApprovalRepository = {
      findOne: jest.fn(),
      save: jest.fn(),
    };

    const mockDataSource = {
      query: jest.fn(),
      createQueryRunner: jest.fn().mockReturnValue({
        connect: jest.fn(),
        startTransaction: jest.fn(),
        commitTransaction: jest.fn(),
        rollbackTransaction: jest.fn(),
        release: jest.fn(),
        manager: {
          getRepository: jest.fn().mockImplementation((entity) => {
            if (entity === User) return mockUserRepository;
            if (entity === Role) return { findOne: jest.fn(), create: jest.fn(), save: jest.fn() };
            return { save: jest.fn(), create: jest.fn(), delete: jest.fn() };
          }),
        },
      }),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: UsersService,
          useValue: {
            findByUserId: jest.fn(),
            findByEmail: jest.fn(),
            getAllAdminUsers: jest.fn(),
          },
        },
        {
          provide: EmailService,
          useValue: {
            sendVerificationLink: jest.fn(),
            sendPasswordResetLink: jest.fn(),
            sendUserId: jest.fn(),
            sendPasswordChangeNotification: jest.fn(),
          },
        },
        {
          provide: JwtService,
          useValue: {
            sign: jest.fn(),
            decode: jest.fn(),
          },
        },
        {
          provide: ProfilePictureService,
          useValue: {
            hasProfilePicture: jest.fn(),
            getProfilePictureDirectUrl: jest.fn(),
          },
        },
        {
          provide: DataSource,
          useValue: mockDataSource,
        },
        {
          provide: DiaryService,
          useValue: {
            createDiary: jest.fn(),
          },
        },
        {
          provide: AsyncNotificationHelperService,
          useValue: {
            notifyAsync: jest.fn(),
            notifyManyAsync: jest.fn(),
          },
        },
        {
          provide: DeeplinkService,
          useValue: {
            getLinkHtml: jest.fn(),
          },
        },
        {
          provide: TokenBlacklistService,
          useValue: {
            blacklistToken: jest.fn(),
          },
        },
        {
          provide: EmailTemplateService,
          useValue: {
            generateSubscriptionWelcomeTemplate: jest.fn(),
            generatePaymentConfirmationTemplate: jest.fn(),
          },
        },
        {
          provide: PlansService,
          useValue: {
            subscribeWithFreePayment: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(Role),
          useValue: { findOne: jest.fn(), find: jest.fn(), create: jest.fn(), save: jest.fn() },
        },
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
        {
          provide: getRepositoryToken(UserPlan),
          useValue: { findOne: jest.fn(), find: jest.fn(), create: jest.fn(), save: jest.fn() },
        },
        {
          provide: getRepositoryToken(PasswordReset),
          useValue: mockPasswordResetRepository,
        },
        {
          provide: getRepositoryToken(EmailVerification),
          useValue: mockEmailVerificationRepository,
        },
        {
          provide: getRepositoryToken(TutorApproval),
          useValue: mockTutorApprovalRepository,
        },
        {
          provide: getRepositoryToken(Diary),
          useValue: { findOne: jest.fn(), find: jest.fn(), create: jest.fn(), save: jest.fn() },
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    usersService = module.get(UsersService);
    jwtService = module.get(JwtService);
    emailService = module.get(EmailService);
    profilePictureService = module.get(ProfilePictureService);
    userRepository = module.get(getRepositoryToken(User));
    emailVerificationRepository = module.get(getRepositoryToken(EmailVerification));
    passwordResetRepository = module.get(getRepositoryToken(PasswordReset));
    tutorApprovalRepository = module.get(getRepositoryToken(TutorApproval));
    dataSource = module.get(DataSource);
  });

  describe('validateUser', () => {
    it('should validate user with correct credentials', async () => {
      const testCredentials = SecureTestDataFactory.createSecureTestCredentials();
      const mockUser = {
        id: 'user-1',
        userId: testCredentials.userId,
        verifyPassword: jest.fn().mockReturnValue(true),
      };

      usersService.findByUserId.mockResolvedValue(mockUser as any);

      const result = await service.validateUser(testCredentials.userId, testCredentials.password);

      expect(result).toEqual(mockUser);
      expect(usersService.findByUserId).toHaveBeenCalledWith(testCredentials.userId);
      expect(mockUser.verifyPassword).toHaveBeenCalledWith(testCredentials.password);
    });

    it('should return null for invalid credentials', async () => {
      const testCredentials = SecureTestDataFactory.createSecureTestCredentials();
      const mockUser = {
        id: 'user-1',
        userId: testCredentials.userId,
        verifyPassword: jest.fn().mockReturnValue(false),
      };

      usersService.findByUserId.mockResolvedValue(mockUser as any);

      const result = await service.validateUser(testCredentials.userId, 'mock-wrong-password');

      expect(result).toBeNull();
    });

    it('should return null for non-existent user', async () => {
      usersService.findByUserId.mockResolvedValue(null);

      const result = await service.validateUser('TESTNONEXISTENT', 'mock-password-123');

      expect(result).toBeNull();
    });
  });

  describe('login', () => {
    const testUser = SecureTestDataFactory.createMockUser();
    const mockUser = {
      id: testUser.id,
      userId: testUser.userId,
      email: testUser.email,
      type: UserType.STUDENT,
      isActive: true,
      isConfirmed: true,
      userRoles: [{ role: { name: 'student' } }],
      verifyPassword: jest.fn().mockReturnValue(true),
      toDto: jest.fn().mockReturnValue({
        id: testUser.id,
        userId: testUser.userId,
        email: testUser.email,
        type: 'student',
      }),
      lastLoginAt: new Date(),
      refreshToken: '',
      refreshTokenExpiry: new Date(0),
    };

    it('should login user successfully', async () => {
      const testCredentials = SecureTestDataFactory.createSecureTestCredentials();
      const loginDto: LoginUserDto = {
        userId: testCredentials.userId,
        password: testCredentials.password,
      };

      usersService.findByUserId.mockResolvedValue(mockUser as any);
      userRepository.save.mockResolvedValue(mockUser);
      jwtService.sign.mockReturnValue('jwt-token');
      jwtService.decode.mockReturnValue({ exp: Math.floor(Date.now() / 1000) + 3600 });
      profilePictureService.hasProfilePicture.mockResolvedValue(false);
      dataSource.query.mockResolvedValue([]);

      const result = await service.login(loginDto);

      expect(result).toHaveProperty('access_token', 'jwt-token');
      expect(result).toHaveProperty('user');
      expect(userRepository.save).toHaveBeenCalled();
    });

    it('should throw UnauthorizedException for invalid user ID', async () => {
      const testCredentials = SecureTestDataFactory.createSecureTestCredentials();
      const loginDto: LoginUserDto = {
        userId: 'TESTINVALID',
        password: testCredentials.password,
      };

      usersService.findByUserId.mockResolvedValue(null);

      await expect(service.login(loginDto)).rejects.toThrow(UnauthorizedException);
    });

    it('should throw UnauthorizedException for invalid password', async () => {
      const testCredentials = SecureTestDataFactory.createSecureTestCredentials();
      const loginDto: LoginUserDto = {
        userId: testCredentials.userId,
        password: 'mock-wrong-password',
      };

      const mockUserWithWrongPassword = {
        ...mockUser,
        verifyPassword: jest.fn().mockReturnValue(false),
      };

      usersService.findByUserId.mockResolvedValue(mockUserWithWrongPassword as any);

      await expect(service.login(loginDto)).rejects.toThrow(UnauthorizedException);
    });

    it('should throw UnauthorizedException for unconfirmed user', async () => {
      const testCredentials = SecureTestDataFactory.createSecureTestCredentials();
      const loginDto: LoginUserDto = {
        userId: testCredentials.userId,
        password: testCredentials.password,
      };

      const unconfirmedUser = {
        ...mockUser,
        isConfirmed: false,
      };

      usersService.findByUserId.mockResolvedValue(unconfirmedUser as any);

      await expect(service.login(loginDto)).rejects.toThrow(UnauthorizedException);
    });

    it('should throw UnauthorizedException for inactive user', async () => {
      const testCredentials = SecureTestDataFactory.createSecureTestCredentials();
      const loginDto: LoginUserDto = {
        userId: testCredentials.userId,
        password: testCredentials.password,
      };

      const inactiveUser = {
        ...mockUser,
        isActive: false,
      };

      usersService.findByUserId.mockResolvedValue(inactiveUser as any);

      await expect(service.login(loginDto)).rejects.toThrow(UnauthorizedException);
    });
  });

  describe('register', () => {
    it('should register student successfully', async () => {
      const testData = SecureTestDataFactory.createMockUser({ type: UserType.STUDENT });
      const testCredentials = SecureTestDataFactory.createSecureTestCredentials();
      const registerDto: RegisterDto = {
        userId: testData.userId,
        email: testData.email,
        password: testCredentials.password,
        confirmPassword: testCredentials.password,
        phoneNumber: testData.phoneNumber,
        gender: testData.gender,
        type: UserType.STUDENT,
        agreedToTerms: true,
      };

      usersService.findByEmail.mockResolvedValue(null);
      usersService.findByUserId.mockResolvedValue(null);
      emailService.sendVerificationLink.mockResolvedValue(true);

      const result = await service.register(registerDto);

      expect(result).toEqual({
        success: true,
        message: 'Registration successful. Please check your email for verification link.',
        userId: expect.any(String),
      });
    });

    it('should throw ConflictException for existing email', async () => {
      const testData = SecureTestDataFactory.createMockUser();
      const testCredentials = SecureTestDataFactory.createSecureTestCredentials();
      const registerDto: RegisterDto = {
        userId: testData.userId,
        email: testData.email,
        password: testCredentials.password,
        confirmPassword: testCredentials.password,
        phoneNumber: testData.phoneNumber,
        gender: testData.gender,
        type: UserType.STUDENT,
        agreedToTerms: true,
      };

      const existingUser = { id: 'existing-user', email: testData.email };
      usersService.findByEmail.mockResolvedValue(existingUser as any);

      await expect(service.register(registerDto)).rejects.toThrow(ConflictException);
    });

    it('should throw BadRequestException for password mismatch', async () => {
      const testData = SecureTestDataFactory.createMockUser();
      const testCredentials = SecureTestDataFactory.createSecureTestCredentials();
      const registerDto: RegisterDto = {
        userId: testData.userId,
        email: testData.email,
        password: testCredentials.password,
        confirmPassword: 'mock-different-password',
        phoneNumber: testData.phoneNumber,
        gender: testData.gender,
        type: UserType.STUDENT,
        agreedToTerms: true,
      };

      usersService.findByEmail.mockResolvedValue(null);
      usersService.findByUserId.mockResolvedValue(null);

      await expect(service.register(registerDto)).rejects.toThrow(BadRequestException);
    });
  });

  describe('verifyEmail', () => {
    it('should verify email successfully', async () => {
      const token = 'verification-token';
      const mockVerification = {
        token,
        userId: 'user-1',
        expirationTime: new Date(Date.now() + 300000), // 5 minutes from now
        isUsed: false,
      };

      const mockUser = {
        id: 'user-1',
        type: UserType.STUDENT,
        isConfirmed: false,
        userRoles: [{ role: { name: 'student' } }],
        toDto: jest.fn().mockReturnValue({ id: 'user-1', type: 'student' }),
      };

      emailVerificationRepository.findOne.mockResolvedValue(mockVerification);
      userRepository.findOne.mockResolvedValue(mockUser);
      userRepository.save.mockResolvedValue({ ...mockUser, isConfirmed: true });
      emailVerificationRepository.save.mockResolvedValue({ ...mockVerification, isUsed: true });
      jwtService.sign.mockReturnValue('jwt-token');
      profilePictureService.hasProfilePicture.mockResolvedValue(false);

      const result = await service.verifyEmail(token);

      expect(result).toHaveProperty('success', true);
      expect(result).toHaveProperty('access_token', 'jwt-token');
      expect(userRepository.save).toHaveBeenCalled();
    });

    it('should throw BadRequestException for invalid token', async () => {
      const token = 'invalid-token';

      emailVerificationRepository.findOne.mockResolvedValue(null);

      await expect(service.verifyEmail(token)).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException for expired token', async () => {
      const token = 'expired-token';
      const mockVerification = {
        token,
        userId: 'user-1',
        expirationTime: new Date(Date.now() - 300000), // 5 minutes ago
        isUsed: false,
      };

      emailVerificationRepository.findOne.mockResolvedValue(mockVerification);

      await expect(service.verifyEmail(token)).rejects.toThrow(BadRequestException);
    });
  });

  describe('forgotPassword', () => {
    it('should send password reset email for valid email', async () => {
      const testUser = SecureTestDataFactory.createMockUser();
      const forgotPasswordDto: ForgotPasswordDto = {
        identifier: testUser.email,
      };

      const mockUser = {
        id: testUser.id,
        email: testUser.email,
      };

      userRepository.findOne.mockResolvedValue(mockUser);
      passwordResetRepository.delete.mockResolvedValue({ affected: 1 });
      passwordResetRepository.save.mockResolvedValue({ id: 'reset-1' });
      emailService.sendPasswordResetLink.mockResolvedValue(true);

      const result = await service.forgotPassword(forgotPasswordDto);

      expect(result).toEqual({
        success: true,
        message: 'If your email is registered, you will receive a password reset link.',
      });
      expect(emailService.sendPasswordResetLink).toHaveBeenCalled();
    });

    it('should return success message for non-existent email', async () => {
      const forgotPasswordDto: ForgotPasswordDto = {
        identifier: '<EMAIL>',
      };

      userRepository.findOne.mockResolvedValue(null);

      const result = await service.forgotPassword(forgotPasswordDto);

      expect(result).toEqual({
        success: true,
        message: 'If your email is registered, you will receive a password reset link.',
      });
    });
  });

  describe('resetPassword', () => {
    it('should reset password successfully', async () => {
      const testCredentials = SecureTestDataFactory.createSecureTestCredentials();
      const resetPasswordDto: ResetPasswordDto = {
        token: 'mock-reset-token',
        newPassword: testCredentials.password,
      };

      const mockReset = {
        token: 'mock-reset-token',
        userId: 'user-1',
        expirationTime: new Date(Date.now() + 300000), // 5 minutes from now
        isUsed: false,
      };

      const mockUser = {
        id: 'user-1',
        setPassword: jest.fn(),
      };

      passwordResetRepository.findOne.mockResolvedValue(mockReset);
      userRepository.findOne.mockResolvedValue(mockUser);
      userRepository.save.mockResolvedValue(mockUser);
      passwordResetRepository.save.mockResolvedValue({ ...mockReset, isUsed: true });

      const result = await service.resetPassword(resetPasswordDto);

      expect(result).toEqual({
        success: true,
        message: 'Password has been reset successfully. You can now login with your new password.',
      });
      expect(mockUser.setPassword).toHaveBeenCalledWith(testCredentials.password);
    });

    it('should throw BadRequestException for invalid token', async () => {
      const testCredentials = SecureTestDataFactory.createSecureTestCredentials();
      const resetPasswordDto: ResetPasswordDto = {
        token: 'mock-invalid-token',
        newPassword: testCredentials.password,
      };

      passwordResetRepository.findOne.mockResolvedValue(null);

      await expect(service.resetPassword(resetPasswordDto)).rejects.toThrow(BadRequestException);
    });
  });

  describe('changePassword', () => {
    it('should change password successfully', async () => {
      const oldCredentials = SecureTestDataFactory.createSecureTestCredentials();
      const newCredentials = SecureTestDataFactory.createSecureTestCredentials();
      const changePasswordDto: ChangePasswordDto = {
        currentPassword: oldCredentials.password,
        newPassword: newCredentials.password,
        confirmNewPassword: newCredentials.password,
      };

      const mockRequest = {
        user: { sub: 'user-1' },
      };

      const testUser = SecureTestDataFactory.createMockUser();
      const mockUser = {
        id: testUser.id,
        userId: testUser.userId,
        type: UserType.STUDENT,
        userRoles: [{ role: { name: 'student' } }],
        verifyPassword: jest.fn()
          .mockReturnValueOnce(true) // current password check
          .mockReturnValueOnce(false), // new password different check
        setPassword: jest.fn(),
        toDto: jest.fn().mockReturnValue({ id: testUser.id, type: 'student' }),
        refreshToken: '',
        refreshTokenExpiry: new Date(0),
        lastLoginAt: new Date(),
      };

      userRepository.findOne.mockResolvedValue(mockUser);
      userRepository.save.mockResolvedValue(mockUser);
      jwtService.sign.mockReturnValue('new-jwt-token');
      jwtService.decode.mockReturnValue({ exp: Math.floor(Date.now() / 1000) + 3600 });
      profilePictureService.hasProfilePicture.mockResolvedValue(false);
      emailService.sendPasswordChangeNotification.mockResolvedValue(true);
      dataSource.query.mockResolvedValue([]);

      const result = await service.changePassword(changePasswordDto, mockRequest as any);

      expect(result).toHaveProperty('access_token', 'new-jwt-token');
      expect(mockUser.setPassword).toHaveBeenCalledWith(newCredentials.password);
    });

    it('should throw BadRequestException for incorrect current password', async () => {
      const newCredentials = SecureTestDataFactory.createSecureTestCredentials();
      const changePasswordDto: ChangePasswordDto = {
        currentPassword: 'mock-wrong-password',
        newPassword: newCredentials.password,
        confirmNewPassword: newCredentials.password,
      };

      const mockRequest = {
        user: { sub: 'user-1' },
      };

      const testUser = SecureTestDataFactory.createMockUser();
      const mockUser = {
        id: testUser.id,
        verifyPassword: jest.fn().mockReturnValue(false),
      };

      userRepository.findOne.mockResolvedValue(mockUser);

      await expect(service.changePassword(changePasswordDto, mockRequest as any))
        .rejects.toThrow(BadRequestException);
    });
  });

  describe('logout', () => {
    it('should logout user successfully', async () => {
      const testUser = SecureTestDataFactory.createMockUser();
      const userId = testUser.id;
      const accessToken = 'mock-jwt-token';

      const mockUser = {
        id: userId,
        userId: testUser.userId,
        refreshToken: 'mock-refresh-token',
        refreshTokenExpiry: new Date(),
      };

      userRepository.findOne.mockResolvedValue(mockUser);
      userRepository.save.mockResolvedValue({
        ...mockUser,
        refreshToken: '',
        refreshTokenExpiry: new Date(0),
      });

      await service.logout(userId, accessToken);

      expect(userRepository.save).toHaveBeenCalled();
    });

    it('should throw NotFoundException for non-existent user', async () => {
      const userId = 'non-existent';

      userRepository.findOne.mockResolvedValue(null);

      await expect(service.logout(userId)).rejects.toThrow(NotFoundException);
    });
  });

  describe('checkTutorApprovalStatus', () => {
    it('should return approved status for approved tutor', async () => {
      const userId = 'tutor-1';
      const mockApproval = {
        userId,
        status: TutorApprovalStatus.APPROVED,
        createdAt: new Date(),
      };

      tutorApprovalRepository.findOne.mockResolvedValue(mockApproval);

      const result = await service.checkTutorApprovalStatus(userId);

      expect(result).toEqual({
        isApproved: true,
        status: TutorApprovalStatus.APPROVED,
      });
    });

    it('should return pending status for tutor without approval record', async () => {
      const userId = 'tutor-1';

      tutorApprovalRepository.findOne.mockResolvedValue(null);

      const result = await service.checkTutorApprovalStatus(userId);

      expect(result).toEqual({
        isApproved: false,
        status: TutorApprovalStatus.PENDING,
        message: 'Your tutor account is pending approval. Please wait for an administrator to approve your account.',
      });
    });
  });
});