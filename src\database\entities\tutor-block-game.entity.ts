import { <PERSON><PERSON><PERSON>, Column, OneToMany, ManyToOne, Jo<PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { User } from './user.entity';
import { ModuleType } from './tutor-waterfall-set.entity';

@Entity()
export class TutorBlockGame extends AuditableBaseEntity {
  @Column()
  title: string;

  @Column()
  score: number;

  @Column({ name: 'tutor_id' })
  tutorId: string;

  @Column({ name: 'student_id' })
  studentId: string;

  @Column({ name: 'module_type', type: 'enum', enum: ModuleType })
  moduleType: ModuleType;

  @Column({ name: 'entry_id' })
  entryId: string;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'tutor_id' })
  tutor: User;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @Join<PERSON><PERSON>umn({ name: 'student_id' })
  student: User;
}