import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddQANotificationTypes1692261600000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // First transaction: Create and modify enums
        await queryRunner.query(`
            BEGIN;
            
            -- Create notification_type_enum if it doesn't exist
            DO $$
            BEGIN
                IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'notification_type_enum') THEN
                    CREATE TYPE notification_type_enum AS ENUM (
                        'qa_assignment', 'qa_mission_submission', 'qa_mission_review', 'qa_mission_feedback'
                    );
                END IF;
            END$$;

            -- Create outbox_notification_type_enum if it doesn't exist
            DO $$
            BEGIN
                IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'outbox_notification_type_enum') THEN
                    CREATE TYPE outbox_notification_type_enum AS ENUM (
                        'qa_submission', 'qa_reviewed'
                    );
                END IF;
            END$$;

            -- Add new values to notification_type_enum
            DO $$
            BEGIN
                BEGIN
                    ALTER TYPE notification_type_enum ADD VALUE IF NOT EXISTS 'qa_assignment';
                EXCEPTION WHEN duplicate_object THEN NULL;
                END;
                
                BEGIN
                    ALTER TYPE notification_type_enum ADD VALUE IF NOT EXISTS 'qa_mission_submission';
                EXCEPTION WHEN duplicate_object THEN NULL;
                END;
                
                BEGIN
                    ALTER TYPE notification_type_enum ADD VALUE IF NOT EXISTS 'qa_mission_review';
                EXCEPTION WHEN duplicate_object THEN NULL;
                END;
                
                BEGIN
                    ALTER TYPE notification_type_enum ADD VALUE IF NOT EXISTS 'qa_mission_feedback';
                EXCEPTION WHEN duplicate_object THEN NULL;
                END;
            END$$;

            -- Add new values to outbox_notification_type_enum
            DO $$
            BEGIN
                BEGIN
                    ALTER TYPE outbox_notification_type_enum ADD VALUE IF NOT EXISTS 'qa_submission';
                EXCEPTION WHEN duplicate_object THEN NULL;
                END;
                
                BEGIN
                    ALTER TYPE outbox_notification_type_enum ADD VALUE IF NOT EXISTS 'qa_reviewed';
                EXCEPTION WHEN duplicate_object THEN NULL;
                END;
            END$$;

            COMMIT;
        `);

        // Second transaction: Update the data
        await queryRunner.query(`
            BEGIN;
            
            -- Update related_entity_type in notification table
            DO $$
            BEGIN
                UPDATE notification
                SET related_entity_type = 'qa_assignment'
                WHERE type = 'qa_assignment' 
                AND related_entity_type = 'qa_assignment_set';
            END$$;

            COMMIT;
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Note: PostgreSQL doesn't support removing enum values
        // We'll just log a warning
        console.warn('Warning: Cannot remove enum values in PostgreSQL. Skipping reversion.');
    }
}
