import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveAllWaterfallForeignKeys1756300000000 implements MigrationInterface {
  name = 'RemoveAllWaterfallForeignKeys1756300000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Get all foreign key constraints for waterfall question tables
    const constraints = await queryRunner.query(`
      SELECT 
        tc.constraint_name,
        tc.table_name
      FROM information_schema.table_constraints tc
      JOIN information_schema.key_column_usage kcu 
        ON tc.constraint_name = kcu.constraint_name
      WHERE tc.constraint_type = 'FOREIGN KEY'
        AND kcu.column_name = 'set_id'
        AND tc.table_name IN (
          'waterfall_question', 
          'waterfall_true_false_question', 
          'waterfall_multiple_choice_question'
        )
    `);

    // Drop all found foreign key constraints
    for (const constraint of constraints) {
      await queryRunner.query(`
        ALTER TABLE "${constraint.table_name}" 
        DROP CONSTRAINT IF EXISTS "${constraint.constraint_name}"
      `);
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Re-add foreign key constraints (this might fail if there are tutor questions)
    await queryRunner.query(`
      ALTER TABLE "waterfall_question" 
      ADD CONSTRAINT "FK_waterfall_question_set_id" 
      FOREIGN KEY ("set_id") REFERENCES "waterfall_set"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "waterfall_true_false_question" 
      ADD CONSTRAINT "FK_waterfall_true_false_question_set_id" 
      FOREIGN KEY ("set_id") REFERENCES "waterfall_set"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "waterfall_multiple_choice_question" 
      ADD CONSTRAINT "FK_waterfall_multiple_choice_question_set_id" 
      FOREIGN KEY ("set_id") REFERENCES "waterfall_set"("id") ON DELETE CASCADE
    `);
  }
}