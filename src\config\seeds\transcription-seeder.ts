import { Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Book } from '../../database/entities/book.entity';
import { Sentence } from '../../database/entities/sentence.entity';

@Injectable()

export class TranscriptionSeeder {
  constructor(
    @InjectRepository(Book)
    private bookRepository: Repository<Book>,
    @InjectRepository(Sentence)
    private sentenceRepository: Repository<Sentence>
  ) {}

  async run(): Promise<void> {
    console.log('🌱 Seeding transcription data...');

    // Sample books data
    const booksData = [
      {
        title: "The Little Mermaid",
        author: "<PERSON>",
        description: "A classic fairy tale about a young mermaid who dreams of becoming human.",
        publicationYear: 1837,
        content: `Far out in the ocean the water is as blue as the petals of the cornflower. There dwelt the Sea King. He had been a widower for many years. His aged mother kept house for him. She was a wise woman. She took great care of the little sea princesses. They were six beautiful children. The youngest was the most beautiful of all. Her skin was as clear as a rose petal. Her eyes were as blue as the deepest sea. She had no feet. Her body ended in a fish's tail. All day long they played in the great halls of the castle. Living flowers grew out of the walls. When the windows were opened the fish swam in. The fish swam up to the little princesses. They ate out of their hands. They let themselves be petted.`,
        sentences: [
          { content: "Far out in the ocean the water is as blue as the petals of the cornflower.", orderIndex: 1, difficultyLevel: "medium", grammarPattern: "descriptive" },
          { content: "There dwelt the Sea King.", orderIndex: 2, difficultyLevel: "easy", grammarPattern: "simple" },
          { content: "He had been a widower for many years.", orderIndex: 3, difficultyLevel: "medium", grammarPattern: "past_tense" },
          { content: "His aged mother kept house for him.", orderIndex: 4, difficultyLevel: "easy", grammarPattern: "simple" },
          { content: "She was a wise woman.", orderIndex: 5, difficultyLevel: "easy", grammarPattern: "simple" },
          { content: "She took great care of the little sea princesses.", orderIndex: 6, difficultyLevel: "medium", grammarPattern: "past_tense" },
          { content: "They were six beautiful children.", orderIndex: 7, difficultyLevel: "easy", grammarPattern: "descriptive" },
          { content: "The youngest was the most beautiful of all.", orderIndex: 8, difficultyLevel: "medium", grammarPattern: "descriptive" },
          { content: "Her skin was as clear as a rose petal.", orderIndex: 9, difficultyLevel: "medium", grammarPattern: "descriptive" },
          { content: "Her eyes were as blue as the deepest sea.", orderIndex: 10, difficultyLevel: "medium", grammarPattern: "descriptive" },
          { content: "She had no feet.", orderIndex: 11, difficultyLevel: "easy", grammarPattern: "simple" },
          { content: "Her body ended in a fish's tail.", orderIndex: 12, difficultyLevel: "medium", grammarPattern: "past_tense" }
        ]
      },
      {
        title: "Alice's Adventures in Wonderland",
        author: "Lewis Carroll",
        description: "A young girl falls down a rabbit hole into a fantasy world populated by peculiar creatures.",
        publicationYear: 1865,
        content: `Alice was beginning to get very tired of sitting by her sister on the bank. She had nothing to do. Once or twice she had peeped into the book her sister was reading. There were no pictures or conversations in it. What is the use of a book without pictures or conversations? She was considering in her own mind whether the pleasure of making a daisy chain would be worth the trouble of getting up and picking the daisies. The hot day made her feel very sleepy and stupid. Suddenly a White Rabbit with pink eyes ran close by her. There was nothing so very remarkable in that. Alice did not think it so very much out of the way to hear the Rabbit say to itself Oh dear! Oh dear! I shall be late!`,
        sentences: [
          { content: "Alice was beginning to get very tired of sitting by her sister on the bank.", orderIndex: 1, difficultyLevel: "hard", grammarPattern: "compound" },
          { content: "She had nothing to do.", orderIndex: 2, difficultyLevel: "easy", grammarPattern: "simple" },
          { content: "Once or twice she had peeped into the book her sister was reading.", orderIndex: 3, difficultyLevel: "hard", grammarPattern: "past_tense" },
          { content: "There were no pictures or conversations in it.", orderIndex: 4, difficultyLevel: "medium", grammarPattern: "compound" },
          { content: "What is the use of a book without pictures or conversations?", orderIndex: 5, difficultyLevel: "medium", grammarPattern: "compound" },
          { content: "She was considering in her own mind whether the pleasure of making a daisy chain would be worth the trouble.", orderIndex: 6, difficultyLevel: "hard", grammarPattern: "compound" },
          { content: "The hot day made her feel very sleepy and stupid.", orderIndex: 7, difficultyLevel: "medium", grammarPattern: "descriptive" },
          { content: "Suddenly a White Rabbit with pink eyes ran close by her.", orderIndex: 8, difficultyLevel: "medium", grammarPattern: "descriptive" },
          { content: "There was nothing so very remarkable in that.", orderIndex: 9, difficultyLevel: "medium", grammarPattern: "descriptive" },
          { content: "Alice did not think it so very much out of the way to hear the Rabbit say to itself Oh dear!", orderIndex: 10, difficultyLevel: "hard", grammarPattern: "compound" }
        ]
      },
      {
        title: "Snow White",
        author: "Brothers Grimm",
        description: "A beautiful princess flees to the forest and finds refuge with seven dwarfs.",
        publicationYear: 1812,
        content: `Once upon a time in winter a queen sat sewing at her window. The frame was made of black ebony wood. As she sewed she looked up at the snow. She pricked her finger with the needle. Three drops of blood fell on the snow. The red looked so beautiful against the white snow and the black wood. She thought to herself I wish I had a child as white as snow. I wish the child had lips as red as blood. I wish the child had hair as black as this wood. Soon after she had a little daughter. The child was as white as snow. Her lips were as red as blood. Her hair was as black as ebony wood. She was called Snow White.`,
        sentences: [
          { content: "Once upon a time in winter a queen sat sewing at her window.", orderIndex: 1, difficultyLevel: "medium", grammarPattern: "past_tense" },
          { content: "The frame was made of black ebony wood.", orderIndex: 2, difficultyLevel: "easy", grammarPattern: "descriptive" },
          { content: "As she sewed she looked up at the snow.", orderIndex: 3, difficultyLevel: "medium", grammarPattern: "compound" },
          { content: "She pricked her finger with the needle.", orderIndex: 4, difficultyLevel: "easy", grammarPattern: "past_tense" },
          { content: "Three drops of blood fell on the snow.", orderIndex: 5, difficultyLevel: "easy", grammarPattern: "past_tense" },
          { content: "The red looked so beautiful against the white snow and the black wood.", orderIndex: 6, difficultyLevel: "medium", grammarPattern: "descriptive" },
          { content: "She thought to herself I wish I had a child as white as snow.", orderIndex: 7, difficultyLevel: "hard", grammarPattern: "compound" },
          { content: "Soon after she had a little daughter.", orderIndex: 8, difficultyLevel: "easy", grammarPattern: "past_tense" },
          { content: "The child was as white as snow.", orderIndex: 9, difficultyLevel: "easy", grammarPattern: "descriptive" },
          { content: "Her lips were as red as blood.", orderIndex: 10, difficultyLevel: "easy", grammarPattern: "descriptive" },
          { content: "Her hair was as black as ebony wood.", orderIndex: 11, difficultyLevel: "easy", grammarPattern: "descriptive" },
          { content: "She was called Snow White.", orderIndex: 12, difficultyLevel: "easy", grammarPattern: "simple" }
        ]
      },
      {
        title: "The Ugly Duckling",
        author: "Hans Christian Andersen",
        description: "A story about a young bird who doesn't fit in until he discovers his true identity.",
        publicationYear: 1843,
        content: `It was summer in the country. The wheat fields were golden yellow. The oats were still green. The hay was stacked in the meadows. There the stork walked on his long red legs. He spoke Egyptian. His mother had taught him that language. Around the fields and meadows there were great forests. In the forests there were deep lakes. It was indeed lovely in the country. In the midst of the sunshine there stood an old manor house. It was surrounded by deep canals. From the walls down to the water there grew great burdock leaves. They were so high that small children could stand upright under the tallest of them.`,
        sentences: [
          { content: "It was summer in the country.", orderIndex: 1, difficultyLevel: "easy", grammarPattern: "simple" },
          { content: "The wheat fields were golden yellow.", orderIndex: 2, difficultyLevel: "easy", grammarPattern: "descriptive" },
          { content: "The oats were still green.", orderIndex: 3, difficultyLevel: "easy", grammarPattern: "descriptive" },
          { content: "The hay was stacked in the meadows.", orderIndex: 4, difficultyLevel: "easy", grammarPattern: "past_tense" },
          { content: "There the stork walked on his long red legs.", orderIndex: 5, difficultyLevel: "medium", grammarPattern: "descriptive" },
          { content: "He spoke Egyptian.", orderIndex: 6, difficultyLevel: "easy", grammarPattern: "simple" },
          { content: "His mother had taught him that language.", orderIndex: 7, difficultyLevel: "medium", grammarPattern: "past_tense" },
          { content: "Around the fields and meadows there were great forests.", orderIndex: 8, difficultyLevel: "medium", grammarPattern: "compound" },
          { content: "In the forests there were deep lakes.", orderIndex: 9, difficultyLevel: "easy", grammarPattern: "simple" },
          { content: "It was indeed lovely in the country.", orderIndex: 10, difficultyLevel: "easy", grammarPattern: "descriptive" }
        ]
      }
    ];

    console.log('🌱 Seeding transcription data...');

    for (const bookData of booksData) {
      // Check if book already exists
      const existingBook = await this.bookRepository.findOne({
        where: { title: bookData.title, author: bookData.author }
      });

      if (existingBook) {
        console.log(`📚 Book "${bookData.title}" already exists, skipping...`);
        continue;
      }

      // Create book without sentences first
      const { sentences: sentencesData, ...bookInfo } = bookData;
      const book = this.bookRepository.create({
        ...bookInfo,
        isActive: true
      });

      const savedBook = await this.bookRepository.save(book);
      console.log(`📚 Created book: ${savedBook.title}`);

      // Create sentences for the book with proper bookId
      if (sentencesData && sentencesData.length > 0) {
        const sentencesToInsert = sentencesData.map(sentenceData => ({
          content: sentenceData.content,
          orderIndex: sentenceData.orderIndex,
          difficultyLevel: sentenceData.difficultyLevel,
          grammarPattern: sentenceData.grammarPattern,
          bookId: savedBook.id
        }));

        await this.sentenceRepository.insert(sentencesToInsert);
        console.log(`📝 Created ${sentencesToInsert.length} sentences for "${savedBook.title}"`);
      }
    }

    console.log('✅ Transcription seeding completed!');
  }
}