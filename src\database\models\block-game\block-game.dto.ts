import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsNumber, IsPositive, IsOptional, IsBoolean, IsArray, ValidateNested, ArrayMinSize, ArrayMaxSize } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { PaginationDto } from '../../../common/models/pagination.dto';

/**
 * DTO for block game sentence with gap functionality
 */
export class BlockGameSentenceDto {
  @ApiProperty({
    description: 'The starting part of the sentence with [[gap]] markers',
    example: 'The fan makes a [[gap]]',
  })
  @IsString()
  @IsNotEmpty()
  starting_part: string;

  @ApiProperty({
    description: 'The expanding part of the sentence with [[gap]] markers',
    example: '[[gap]] sound',
  })
  @IsString()
  @IsNotEmpty()
  expanding_part: string;

  @ApiProperty({
    description: 'Correct answers for gaps in starting part',
    example: ['strange'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  starting_part_answers: string[];

  @ApiProperty({
    description: 'Distractor options for gaps in starting part',
    example: ['loud', 'quiet', 'beautiful'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  starting_part_distractors?: string[];

  @ApiProperty({
    description: 'Correct answers for gaps in expanding part',
    example: ['loud'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  expanding_part_answers: string[];

  @ApiProperty({
    description: 'Distractor options for gaps in expanding part',
    example: ['sound', 'noise', 'music'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  expanding_part_distractors?: string[];

  @ApiProperty({
    description: 'The order of the sentence in the game',
    example: 1,
  })
  @IsNumber()
  @IsPositive()
  sentence_order: number;
}

/**
 * DTO for creating a new block game
 */
export class CreateBlockGameDto {
  @ApiProperty({
    description: 'The title of the block game',
    example: 'Basic Sentence Building',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'The total score for the block game',
    example: 20,
  })
  @IsNumber()
  @IsPositive()
  score: number;

  @ApiProperty({
    description: 'Array of sentences for the block game (max 10 sentences)',
    type: [BlockGameSentenceDto],
    isArray: true,
    example: [
      {
        starting_part: 'The fan makes a [[gap]]',
        expanding_part: '[[gap]] sound',
        starting_part_answers: ['strange'],
        starting_part_distractors: ['loud', 'quiet', 'beautiful'],
        expanding_part_answers: ['loud'],
        expanding_part_distractors: ['sound', 'noise', 'music'],
        sentence_order: 1,
      },
      {
        starting_part: 'I will [[gap]]',
        expanding_part: 'it [[gap]] morning',
        starting_part_answers: ['check'],
        starting_part_distractors: ['see', 'find', 'watch'],
        expanding_part_answers: ['tomorrow'],
        expanding_part_distractors: ['today', 'yesterday', 'now'],
        sentence_order: 2,
      },
    ],
  })
  @IsArray()
  @ArrayMinSize(1, { message: 'At least one sentence must be provided' })
  @ArrayMaxSize(10, { message: 'Maximum 10 sentences are allowed' })
  @ValidateNested({ each: true })
  @Type(() => BlockGameSentenceDto)
  sentences: BlockGameSentenceDto[];
}

/**
 * DTO for updating a block game
 */
export class UpdateBlockGameDto {
  @ApiProperty({
    description: 'The title of the block game',
    example: 'Advanced Sentence Building',
    required: false,
  })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiProperty({
    description: 'The total score for the block game',
    example: 25,
    required: false,
  })
  @IsNumber()
  @IsPositive()
  @IsOptional()
  score?: number;

  @ApiProperty({
    description: 'Whether the block game is active and available to students',
    example: true,
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return undefined;
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean()
  is_active?: boolean;

  @ApiProperty({
    description: 'Array of sentences for the block game (max 10 sentences)',
    type: [BlockGameSentenceDto],
    isArray: true,
    required: false,
  })
  @IsArray()
  @ArrayMinSize(1, { message: 'At least one sentence must be provided' })
  @ArrayMaxSize(10, { message: 'Maximum 10 sentences are allowed' })
  @ValidateNested({ each: true })
  @Type(() => BlockGameSentenceDto)
  @IsOptional()
  sentences?: BlockGameSentenceDto[];
}

/**
 * DTO for block game response
 */
export class BlockGameResponseDto {
  @ApiProperty({
    description: 'The ID of the block game',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'The title of the block game',
    example: 'Basic Sentence Building',
  })
  title: string;

  @ApiProperty({
    description: 'The total score for the block game',
    example: 20,
  })
  score: number;

  @ApiProperty({
    description: 'Whether the block game is active and available to students',
    example: true,
  })
  is_active: boolean;

  @ApiProperty({
    description: 'The number of sentences in the block game',
    example: 5,
  })
  sentence_count: number;

  @ApiProperty({
    description: 'The sentences in the block game with gap information',
    type: [BlockGameSentenceDto],
    isArray: true,
  })
  sentences?: BlockGameSentenceDto[];

  @ApiProperty({
    description: 'When the block game was created',
    example: '2023-01-01T00:00:00.000Z',
  })
  created_at: Date;

  @ApiProperty({
    description: 'When the block game was last updated',
    example: '2023-01-02T00:00:00.000Z',
  })
  updated_at: Date;

  @ApiProperty({
    description: 'Who created the block game',
    example: '<EMAIL>',
  })
  created_by: string;
}

/**
 * DTO for toggling block game active status
 */
export class ToggleBlockGameStatusDto {
  @ApiProperty({
    description: 'Whether the block game should be active',
    example: true,
  })
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return undefined;
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean()
  is_active: boolean;
}

/**
 * DTO for block game query parameters
 */
export class GetBlockGamesQueryDto extends PaginationDto {
  @ApiProperty({
    description: 'Search term for title',
    required: false,
    example: 'sentence',
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({
    description: 'Filter by active status',
    required: false,
    example: true,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return undefined;
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean()
  is_active?: boolean;

  // Inherits sortBy and sortDirection from PaginationDto
}
