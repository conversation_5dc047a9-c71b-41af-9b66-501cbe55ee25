import { MigrationInterface, QueryRunner } from "typeorm";

export class AddShareDateToDiaryShares1692345678901 implements MigrationInterface {
    name = 'AddShareDateToDiaryShares1692345678901'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add the new column
        await queryRunner.query(`ALTER TABLE "diary_share" ADD "share_date" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP`);
        
        // Update existing records
        await queryRunner.query(`UPDATE "diary_share" SET "share_date" = "created_at" WHERE "share_date" IS NULL`);
        
        // Remove the default after migration
        await queryRunner.query(`ALTER TABLE "diary_share" ALTER COLUMN "share_date" DROP DEFAULT`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "diary_share" DROP COLUMN "share_date"`);
    }
}
