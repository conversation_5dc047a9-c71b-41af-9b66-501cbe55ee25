import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { DiarySkin } from '../../database/entities/diary-skin.entity';
import { StudentDiarySkin } from '../../database/entities/student-diary-skin.entity';
import { ShopSkinMapping } from '../../database/entities/shop-skin-mapping.entity';
import { StudentOwnedItem } from '../../database/entities/student-owned-item.entity';
import { ShopItem, ShopItemType } from '../../database/entities/shop-item.entity';

export interface ResolvedSkin {
  id: string;
  name: string;
  description: string;
  templateContent: string;
  previewImagePath: string;
  isActive: boolean;
  isGlobal: boolean;
  createdById: string | null;
  type: 'global' | 'student' | 'shop';
  isAccessible: boolean;
  isPurchased?: boolean;
  isFree?: boolean;
}

@Injectable()
export class SkinResolverService {
  private readonly logger = new Logger(SkinResolverService.name);

  constructor(
    @InjectRepository(DiarySkin)
    private diarySkinRepository: Repository<DiarySkin>,
    @InjectRepository(StudentDiarySkin)
    private studentDiarySkinRepository: Repository<StudentDiarySkin>,
    @InjectRepository(ShopSkinMapping)
    private shopSkinMappingRepository: Repository<ShopSkinMapping>,
    @InjectRepository(StudentOwnedItem)
    private studentOwnedItemRepository: Repository<StudentOwnedItem>,
    @InjectRepository(ShopItem)
    private shopItemRepository: Repository<ShopItem>,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * Resolve a skin by ID for a specific user
   * This method handles all skin types uniformly
   */
  async resolveSkin(skinId: string, userId: string): Promise<ResolvedSkin | null> {
    if (!skinId) {
      return null;
    }

    // Try global skin first
    const globalSkin = await this.resolveGlobalSkin(skinId, userId);
    if (globalSkin) {
      return globalSkin;
    }

    // Try student skin
    const studentSkin = await this.resolveStudentSkin(skinId, userId);
    if (studentSkin) {
      return studentSkin;
    }

    // Try shop skin
    const shopSkin = await this.resolveShopSkin(skinId, userId);
    if (shopSkin) {
      return shopSkin;
    }

    return null;
  }

  /**
   * Get all accessible skins for a user
   */
  async getAccessibleSkins(userId: string): Promise<ResolvedSkin[]> {
    const skins: ResolvedSkin[] = [];

    // Get global skins
    const globalSkins = await this.getAccessibleGlobalSkins(userId);
    skins.push(...globalSkins);

    // Get student skins
    const studentSkins = await this.getStudentSkins(userId);
    skins.push(...studentSkins);

    return skins;
  }

  /**
   * Validate if a user can access a specific skin
   */
  async validateSkinAccess(skinId: string, userId: string): Promise<boolean> {
    const resolvedSkin = await this.resolveSkin(skinId, userId);
    return resolvedSkin?.isAccessible || false;
  }

  private async resolveGlobalSkin(skinId: string, userId: string): Promise<ResolvedSkin | null> {
    const skin = await this.diarySkinRepository.findOne({
      where: { id: skinId, isActive: true },
    });

    if (!skin) {
      return null;
    }

    // Check if it's linked to a shop item
    const shopMapping = await this.shopSkinMappingRepository.findOne({
      where: { diarySkinId: skinId },
      relations: ['shopItem'],
    });

    let isAccessible = true;
    let isPurchased = false;
    let isFree = true;

    if (shopMapping) {
      isFree = shopMapping.shopItem.type === ShopItemType.FREE;
      if (!isFree) {
        // Check if user purchased it
        const ownedItem = await this.studentOwnedItemRepository.findOne({
          where: { studentId: userId, shopItemId: shopMapping.shopItemId },
        });
        isPurchased = !!ownedItem;
        isAccessible = isPurchased;
      }
    }

    return {
      id: skin.id,
      name: skin.name,
      description: skin.description,
      templateContent: skin.templateContent,
      previewImagePath: skin.previewImagePath,
      isActive: skin.isActive,
      isGlobal: true,
      createdById: skin.createdById,
      type: 'global',
      isAccessible,
      isPurchased,
      isFree,
    };
  }

  private async resolveStudentSkin(skinId: string, userId: string): Promise<ResolvedSkin | null> {
    const skin = await this.studentDiarySkinRepository.findOne({
      where: { id: skinId, studentId: userId, isActive: true },
    });

    if (!skin) {
      return null;
    }

    return {
      id: skin.id,
      name: skin.name,
      description: skin.description,
      templateContent: skin.templateContent,
      previewImagePath: skin.previewImagePath,
      isActive: skin.isActive,
      isGlobal: false,
      createdById: skin.studentId,
      type: 'student',
      isAccessible: true, // Student always has access to their own skins
      isFree: true,
    };
  }

  private async resolveShopSkin(skinId: string, userId: string): Promise<ResolvedSkin | null> {
    // Check if it's a shop item directly
    const shopItem = await this.shopItemRepository.findOne({
      where: { id: skinId },
    });

    if (!shopItem) {
      return null;
    }

    const isFree = shopItem.type === ShopItemType.FREE;
    let isPurchased = false;
    let isAccessible = isFree;

    if (!isFree) {
      const ownedItem = await this.studentOwnedItemRepository.findOne({
        where: { studentId: userId, shopItemId: skinId },
      });
      isPurchased = !!ownedItem;
      isAccessible = isPurchased;
    }

    return {
      id: shopItem.id,
      name: shopItem.title,
      description: shopItem.description,
      templateContent: shopItem.metadata || '<div>Default template</div>',
      previewImagePath: shopItem.filePath,
      isActive: shopItem.isActive,
      isGlobal: false,
      createdById: null,
      type: 'shop',
      isAccessible,
      isPurchased,
      isFree,
    };
  }

  private async getAccessibleGlobalSkins(userId: string): Promise<ResolvedSkin[]> {
    const skins = await this.diarySkinRepository.find({
      where: { isActive: true },
    });

    const resolvedSkins: ResolvedSkin[] = [];

    for (const skin of skins) {
      const resolved = await this.resolveGlobalSkin(skin.id, userId);
      if (resolved && resolved.isAccessible) {
        resolvedSkins.push(resolved);
      }
    }

    return resolvedSkins;
  }

  private async getStudentSkins(userId: string): Promise<ResolvedSkin[]> {
    const skins = await this.studentDiarySkinRepository.find({
      where: { studentId: userId, isActive: true },
    });

    return skins.map(skin => ({
      id: skin.id,
      name: skin.name,
      description: skin.description,
      templateContent: skin.templateContent,
      previewImagePath: skin.previewImagePath,
      isActive: skin.isActive,
      isGlobal: false,
      createdById: skin.studentId,
      type: 'student' as const,
      isAccessible: true,
      isFree: true,
    }));
  }
}