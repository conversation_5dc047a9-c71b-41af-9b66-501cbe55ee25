import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';

import { EssayMissionService } from './admin-essay.service';
import { EssaySubmissionService } from './student-essay.service';
import { EssayAwardService } from './essay-award.service';
import { MissionController } from './admin-essay.controller';
import { StudentEssayController } from './student-essay.controller';
import { EssayTaskSubmissionMarking } from 'src/database/entities/essay-task-submission-marking.entity';
import { EssayMissionTasks } from 'src/database/entities/essay-mission-tasks.entity';
import { EssayTaskSubmissions } from 'src/database/entities/essay-task-submissions.entity';
import { EssayTaskSubmissionHistory } from 'src/database/entities/essay-task-submission-history.entity';
import { EssayMission } from 'src/database/entities/essay-mission.entity';
import { TutorEssayController } from './tutor-essay.controller';
import { TutorEssayService } from './tutor-essay.service';
import { UsersModule } from '../users/users.module';
import { NotificationModule } from '../notification/notification.module';
import { DeeplinkModule } from '../../common/utils/deeplink.module';

import { DeeplinkService } from '../../common/utils/deeplink.service';
import { DiaryModule } from '../diary/diary.module';
import { DiaryService } from '../diary/diary.service';
import { DiarySkin } from 'src/database/entities/diary-skin.entity';
import { StudentDiarySkin } from 'src/database/entities/student-diary-skin.entity';
import { EssayModuleSkinPreference } from 'src/database/entities/essay-preferences.entity';
import { DiaryEntry } from 'src/database/entities/diary-entry.entity';
import { DiarySkinService } from '../diary/diary-skin.service';
import { User } from 'src/database/entities/user.entity';
import { SubscriptionFeatureGuard } from '../../common/guards/subscription-feature.guard';
import { AwardsModule } from '../awards/awards.module';
import { PlansModule } from '../plans/plans.module';
import { TutorMatchingModule } from '../tutor-matching/tutor-matching.module';
@Module({
  imports: [
    TypeOrmModule.forFeature(
      [
        EssayMission, 
        EssayMissionTasks, 
        EssayTaskSubmissionHistory, 
        EssayTaskSubmissions, 
        EssayTaskSubmissionMarking, 
        EssayModuleSkinPreference, 
        DiarySkin, 
        User,
        StudentDiarySkin
      ]
    ),
    ConfigModule,
    forwardRef(() => UsersModule),
    NotificationModule,
    DeeplinkModule,
    forwardRef(() => PlansModule),
    forwardRef(() => TutorMatchingModule),
    forwardRef(() => DiaryModule),
    forwardRef(() => AwardsModule),
  ],
  controllers: [MissionController, StudentEssayController, TutorEssayController],
  providers: [
    EssayMissionService,
    JwtService,
    EssaySubmissionService,
    EssayAwardService,
    TutorEssayService,
    DeeplinkService,
    SubscriptionFeatureGuard,
  ],
  exports: [EssayMissionService, EssaySubmissionService, EssayAwardService, TutorEssayService],
})
export class EssayModule {}
