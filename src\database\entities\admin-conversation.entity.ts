import { <PERSON><PERSON><PERSON>, Column, <PERSON>To<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToMany, Index } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { User } from './user.entity';
import { Message } from './message.entity';

/**
 * Status of the admin conversation
 */
export enum AdminConversationStatus {
  ACTIVE = 'active',
  ARCHIVED = 'archived',
  BLOCKED = 'blocked',
}

@Entity()
@Index(['userId'], { unique: true }) // One admin conversation per user
export class AdminConversation extends AuditableBaseEntity {
  @Column({ name: 'user_id' })
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ name: 'virtual_admin_id' })
  virtualAdminId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'virtual_admin_id' })
  virtualAdmin: User;

  @Column({
    name: 'status',
    type: 'enum',
    enum: AdminConversationStatus,
    default: AdminConversationStatus.ACTIVE,
  })
  status: AdminConversationStatus;

  @Column({ name: 'last_message_at', nullable: true })
  lastMessageAt: Date;

  @Column({ name: 'last_message_text', nullable: true, type: 'text' })
  lastMessageText: string;

  @Column({ name: 'last_message_sender_id', nullable: true })
  lastMessageSenderId: string;

  @Column({ name: 'user_unread_count', default: 0 })
  userUnreadCount: number;

  @Column({ name: 'admin_unread_count', default: 0 })
  adminUnreadCount: number;

  @OneToMany(() => Message, (message) => message.adminConversation)
  messages: Message[];
}