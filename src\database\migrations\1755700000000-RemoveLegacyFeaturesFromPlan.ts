import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveLegacyFeaturesFromPlan1755700000000 implements MigrationInterface {
  name = 'RemoveLegacyFeaturesFromPlan1755700000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Remove the legacy_features column from plan table
    await queryRunner.query(`ALTER TABLE "plan" DROP COLUMN IF EXISTS "legacy_features"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Add back the legacy_features column
    await queryRunner.query(`ALTER TABLE "plan" ADD "legacy_features" text`);
  }
}