# Awards & Hall of Fame API Testing Flow

This document outlines the testing flow for the Awards and Hall of Fame Module API endpoints.

## Prerequisites

Before testing the Awards & Hall of Fame APIs:

1. Ensure the HEC backend is running
2. Have valid authentication tokens for different user roles (students, admins)
3. Set up your API testing tool (<PERSON><PERSON> recommended)
4. Ensure test awards and winners are configured in the system

## Awards Module Testing Flow

### Award Management (Admin)

#### Test Case 1: Create Award

1. Authenticate with an admin token
2. Send a POST request to `/api/awards`:
   ```json
   {
     "name": "Diary Excellence Award",
     "description": "Awarded for outstanding diary entries",
     "module": "diary",
     "criteria": "diary_score",
     "frequency": "monthly",
     "rewardPoints": 100,
     "isActive": true,
     "criteriaConfig": {
       "minScore": 85,
       "minEntries": 10
     }
   }
   ```
3. Verify HTTP status code is 201 Created
4. Verify award is created with correct details
5. Verify unique name constraint is enforced

#### Test Case 2: Get All Awards (Admin)

1. Send a GET request to `/api/awards/admin` with filters:
   ```json
   {
     "module": "diary",
     "includeInactive": "false",
     "name": "Excellence",
     "frequency": "monthly",
     "page": 1,
     "limit": 10,
     "sortBy": "createdAt",
     "sortDirection": "DESC"
   }
   ```
2. Verify HTTP status code is 200 OK
3. Verify response contains paginated awards
4. Verify filtering works correctly
5. Verify sorting is applied properly

#### Test Case 3: Update Award

1. Send a PATCH request to `/api/awards/{id}`:
   ```json
   {
     "name": "Updated Award Name",
     "description": "Updated description",
     "rewardPoints": 150,
     "isActive": false
   }
   ```
2. Verify HTTP status code is 200 OK
3. Verify award is updated correctly
4. Verify name uniqueness is still enforced

#### Test Case 4: Delete Award

1. Send a DELETE request to `/api/awards/{id}`
2. Verify HTTP status code is 200 OK
3. If award has winners: verify it's marked inactive instead of deleted
4. If no winners: verify it's completely removed
5. Verify appropriate response message

### Award Criteria Testing

#### Test Case 1: Get Award Criteria

1. Send a GET request to `/api/awards/criteria`
2. Verify HTTP status code is 200 OK
3. Verify response contains all criteria organized by module
4. Verify criteria include id, name, description, and module

#### Test Case 2: Get Module-Specific Criteria

1. Send a GET request to `/api/awards/criteria?module=diary`
2. Verify HTTP status code is 200 OK
3. Verify response contains only diary module criteria
4. Test with invalid module and verify 400 error

### Reward Points Management (Admin)

#### Test Case 1: Create Reward Point Transaction

1. Send a POST request to `/api/awards/reward-points`:
   ```json
   {
     "userId": "123e4567-e89b-12d3-a456-426614174000",
     "points": 50,
     "type": "EARNED",
     "source": "MANUAL_ADMIN",
     "description": "Bonus points for excellent participation",
     "relatedEntityId": "award-id",
     "relatedEntityType": "award"
   }
   ```
2. Verify HTTP status code is 201 Created
3. Verify reward point transaction is recorded
4. Verify user's balance is updated

#### Test Case 2: Get User Reward Point Balance

1. Send a GET request to `/api/awards/reward-points/{userId}`
2. Verify HTTP status code is 200 OK
3. Verify response contains current balance
4. Verify recent transactions are included
5. Verify transaction history is accurate

### Award Winners Management (Admin)

#### Test Case 1: Create Award Winner

1. Send a POST request to `/api/awards/winners`:
   ```json
   {
     "userId": "123e4567-e89b-12d3-a456-426614174000",
     "awardId": "456e7890-e89b-12d3-a456-426614174000",
     "period": "2024-01",
     "score": 95.5,
     "metadata": {
       "entriesSubmitted": 15,
       "averageScore": 88.2
     }
   }
   ```
2. Verify HTTP status code is 201 Created
3. Verify award winner is recorded
4. Verify reward points are automatically created
5. Verify notification is sent to winner

#### Test Case 2: Get All Award Winners

1. Send a GET request to `/api/awards/winners` with pagination
2. Verify HTTP status code is 200 OK
3. Verify response contains paginated winners
4. Test filtering by award ID
5. Verify sorting options work correctly

#### Test Case 3: Get User Awards (Admin)

1. Send a GET request to `/api/awards/winners/{userId}`
2. Verify HTTP status code is 200 OK
3. Verify response contains all awards for the user
4. Verify award details and win dates are included

### Student Award Access

#### Test Case 1: Get Available Awards

1. Authenticate with a student token
2. Send a GET request to `/api/awards/available` with filters:
   ```json
   {
     "module": "diary",
     "name": "Excellence",
     "page": 1,
     "limit": 10,
     "sortBy": "rewardPoints",
     "sortDirection": "DESC"
   }
   ```
3. Verify HTTP status code is 200 OK
4. Verify only active awards are returned
5. Verify filtering and sorting work correctly

#### Test Case 2: Get My Awards

1. Send a GET request to `/api/awards/my-awards`
2. Verify HTTP status code is 200 OK
3. Verify response contains only student's awards
4. Verify award details and win dates are included
5. Verify total reward points are calculated correctly

#### Test Case 3: Get My Reward Points

1. Send a GET request to `/api/awards/my-points`
2. Verify HTTP status code is 200 OK
3. Verify response contains current balance
4. Verify recent transactions are included
5. Verify transaction sources are properly categorized

## Hall of Fame Module Testing Flow

### Module-Specific Hall of Fame

#### Test Case 1: Get Diary Hall of Fame

1. Authenticate with any valid token (student, tutor, admin)
2. Send a GET request to `/api/hall-of-fame/diary?limit=50`
3. Verify HTTP status code is 200 OK
4. Verify response contains diary award winners
5. Verify winners are organized by award type and frequency
6. Verify limit parameter is respected

#### Test Case 2: Get Novel Hall of Fame

1. Send a GET request to `/api/hall-of-fame/novel?limit=25`
2. Verify HTTP status code is 200 OK
3. Verify response contains novel award winners
4. Verify monthly and yearly awards are included
5. Verify winner details include user info and scores

#### Test Case 3: Get Essay Hall of Fame

1. Send a GET request to `/api/hall-of-fame/essay?limit=30`
2. Verify HTTP status code is 200 OK
3. Verify response contains essay award winners
4. Verify award frequency filtering works
5. Verify winner rankings are correct

### Ongoing Awards Testing

#### Test Case 1: Get Ongoing Diary Awards

1. Send a GET request to `/api/hall-of-fame/diary/ongoing`
2. Verify HTTP status code is 200 OK
3. Verify response contains current period winners
4. Test frequency filtering: `/api/hall-of-fame/diary/ongoing?frequency=weekly`
5. Verify only current period data is returned

#### Test Case 2: Get Ongoing Novel Awards

1. Send a GET request to `/api/hall-of-fame/novel/ongoing?frequency=monthly`
2. Verify HTTP status code is 200 OK
3. Verify response contains current month's winners
4. Test yearly frequency filtering
5. Verify award period calculations are correct

#### Test Case 3: Get Ongoing Essay Awards

1. Send a GET request to `/api/hall-of-fame/essay/ongoing?frequency=yearly`
2. Verify HTTP status code is 200 OK
3. Verify response contains current year's winners
4. Test without frequency filter
5. Verify all ongoing awards are returned

## Integration Testing Flow

### Test Case 1: Complete Award Lifecycle

1. **Award Creation (Admin)**
   - Create new award with specific criteria
   - Verify award appears in available awards

2. **Student Achievement**
   - Student completes activities meeting criteria
   - System automatically evaluates achievement
   - Award is granted if criteria are met

3. **Winner Creation**
   - Admin or system creates award winner record
   - Reward points are automatically granted
   - Notification is sent to student

4. **Hall of Fame Update**
   - Winner appears in relevant Hall of Fame
   - Ongoing awards are updated
   - Rankings are recalculated

### Test Case 2: Multi-Module Award System

1. Create awards for different modules (diary, novel, essay)
2. Students achieve awards across multiple modules
3. Verify Hall of Fame correctly segregates by module
4. Verify cross-module reward point accumulation

### Test Case 3: Award Frequency Management

1. Create awards with different frequencies (daily, weekly, monthly, yearly)
2. Verify period calculations are correct
3. Test ongoing awards for each frequency
4. Verify award reset behavior at period boundaries

## Performance Testing Flow

### Test Case 1: Hall of Fame Performance

1. Create large number of award winners (1000+)
2. Test Hall of Fame retrieval performance
3. Verify pagination works efficiently
4. Test sorting performance with large datasets

### Test Case 2: Award Calculation Performance

1. Test award criteria evaluation with many students
2. Verify batch award processing performance
3. Test concurrent award granting
4. Verify database performance under load

### Test Case 3: Reward Points Performance

1. Test reward point balance calculation with many transactions
2. Verify transaction history retrieval performance
3. Test concurrent point transactions
4. Verify balance consistency under load

## Security Testing Flow

### Test Case 1: Access Control

1. Test student access to admin-only endpoints
2. Verify proper role-based access control
3. Test cross-user award access attempts
4. Verify award manipulation prevention

### Test Case 2: Data Integrity

1. Test award criteria tampering attempts
2. Verify reward point transaction integrity
3. Test Hall of Fame data manipulation
4. Verify audit trail completeness

### Test Case 3: Input Validation

1. Test XSS prevention in award descriptions
2. Test SQL injection in search parameters
3. Verify input sanitization for all fields
4. Test malicious file uploads (if applicable)

## Error Handling Testing Flow

### Test Case 1: Award System Errors

1. Test award creation with duplicate names
2. Test invalid criteria configurations
3. Test award granting to non-existent users
4. Verify appropriate error messages

### Test Case 2: Reward Points Errors

1. Test negative point transactions
2. Test insufficient balance scenarios
3. Test invalid transaction types
4. Verify error recovery mechanisms

### Test Case 3: Hall of Fame Errors

1. Test Hall of Fame with no data
2. Test invalid frequency parameters
3. Test non-existent module requests
4. Verify graceful error handling

## Edge Cases Testing Flow

### Test Case 1: Boundary Conditions

1. Test award criteria at exact thresholds
2. Test maximum reward point values
3. Test Hall of Fame with single winner
4. Verify system limits are enforced

### Test Case 2: Time-based Scenarios

1. Test award period transitions
2. Test ongoing awards at period boundaries
3. Test timezone handling for awards
4. Verify timestamp accuracy across operations

### Test Case 3: Data Consistency

1. Test concurrent award granting
2. Verify Hall of Fame consistency during updates
3. Test reward point balance consistency
4. Verify data integrity across operations

## Reporting and Analytics Testing

### Test Case 1: Award Statistics

1. Verify award distribution analytics
2. Test performance trend calculations
3. Verify completion rate statistics
4. Test comparative analysis features

### Test Case 2: Hall of Fame Analytics

1. Test winner trend analysis
2. Verify ranking algorithm accuracy
3. Test performance comparison features
4. Verify historical data accuracy

### Test Case 3: Reward Points Analytics

1. Test point earning pattern analysis
2. Verify spending behavior tracking
3. Test balance trend calculations
4. Verify transaction category analysis

This comprehensive testing flow ensures the Awards and Hall of Fame modules function correctly, providing accurate recognition and motivation systems while maintaining data integrity and security.