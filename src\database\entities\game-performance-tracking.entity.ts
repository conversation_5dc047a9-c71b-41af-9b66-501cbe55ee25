import { <PERSON>ti<PERSON>, Column, <PERSON>T<PERSON><PERSON><PERSON>, Join<PERSON><PERSON>umn } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { User } from './user.entity';

export enum GameType {
  WATERFALL = 'waterfall',
  BLOCK = 'block'
}

export enum GameSource {
  ADMIN = 'admin',
  TUTOR = 'tutor'
}

@Entity()
export class GamePerformanceTracking extends AuditableBaseEntity {
  @Column({ name: 'student_id' })
  studentId: string;

  @Column({ name: 'game_type', type: 'enum', enum: GameType })
  gameType: GameType;

  @Column({ name: 'game_source', type: 'enum', enum: GameSource })
  gameSource: GameSource;

  @Column({ name: 'game_id' })
  gameId: string;

  @Column()
  score: number;

  @Column({ name: 'total_score' })
  totalScore: number;

  @Column({ name: 'completion_time_seconds', nullable: true })
  completionTimeSeconds: number;

  @Column({ name: 'played_at' })
  playedAt: Date;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'student_id' })
  student: User;
}