# Notification and Email Fixes Summary

## Issues Identified

### 1. Multiple In-App Notifications
- **Problem**: Students and tutors were receiving individual in-app notifications for each module assignment during subscription
- **Root Cause**: The code was sending separate notifications for each module instead of consolidating them
- **Impact**: Users received multiple notifications for a single subscription event

### 2. Potential Email Duplication
- **Problem**: Risk of multiple emails being sent for the same subscription event
- **Root Cause**: No deduplication mechanism in the notification outbox service
- **Impact**: Users could receive multiple emails for the same event

### 3. Incomplete Push Notification Logic
- **Problem**: Push notification logic didn't properly handle multiple module assignments
- **Root Cause**: Logic only handled single assignments, incomplete for multiple assignments
- **Impact**: Inconsistent push notification experience

## Fixes Implemented

### 1. Consolidated Student Notifications (`src/modules/plans/plans.service.ts`)

#### Before:
```typescript
// Sent individual notifications for each module
for (const assignment of validAssignments) {
  await this.asyncNotificationHelper.notifyAsync(
    student.id, 
    NotificationType.TUTOR_ASSIGNMENT, 
    `New Tutor for ${assignment.module.name}`, 
    message, 
    options
  );
}
```

#### After:
```typescript
// Send single consolidated in-app notification
await this.asyncNotificationHelper.notifyAsync(
  student.id, 
  NotificationType.TUTOR_ASSIGNMENT, 
  inAppTitle, 
  inAppMessage, 
  options,
  { batchId: batchId }
);
```

### 2. Consolidated Tutor Notifications

#### Before:
```typescript
// Sent individual notifications for each module
for (const moduleName of modules) {
  await this.asyncNotificationHelper.notifyAsync(
    tutor.id, 
    NotificationType.TUTOR_ASSIGNMENT, 
    `New Student for ${moduleName}`, 
    inAppMessage, 
    options
  );
}
```

#### After:
```typescript
// Send single consolidated notification
await this.asyncNotificationHelper.notifyAsync(
  tutor.id, 
  NotificationType.TUTOR_ASSIGNMENT, 
  inAppTitle, 
  inAppMessage, 
  options,
  { batchId: batchId }
);
```

### 3. Deduplication Mechanism (`src/modules/notification/notification-outbox.service.ts`)

Added duplicate detection logic:
```typescript
private async checkForDuplicateNotification(
  userId: string,
  type: OutboxNotificationType,
  options: any,
  metadata: any
): Promise<NotificationOutbox | null> {
  // Check for notifications in the last 5 minutes
  const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
  
  const existingNotification = await this.outboxRepository.findOne({
    where: {
      userId,
      type,
      createdAt: MoreThan(fiveMinutesAgo),
      status: In([OutboxStatus.PENDING, OutboxStatus.PROCESSING, OutboxStatus.COMPLETED])
    }
  });

  // Additional checks for tutor assignments and batch IDs
  if (existingNotification) {
    if (type === OutboxNotificationType.TUTOR_ASSIGNMENT) {
      if (options?.relatedEntityId === existingNotification.options?.relatedEntityId ||
          metadata?.batchId === existingNotification.metadata?.batchId) {
        return existingNotification;
      }
    }
  }
  
  return null;
}
```

### 4. Batch ID Implementation

Added unique batch IDs for subscription events:
```typescript
// Generate unique batch ID for deduplication
const batchId = `subscription_${studentId}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

// Pass batch ID to all notifications
await this.sendConsolidatedTutorAssignmentNotification(student, tutorAssignments, batchId);
await this.sendConsolidatedTutorNotifications(student, tutorAssignments, batchId);
```

### 5. Fixed Push Notification Logic

Enhanced push notification handling for multiple assignments:
```typescript
if (tutorGroups.size === 1) {
  const tutorGroup = Array.from(tutorGroups.values())[0];
  pushTitle = 'New Tutor Assignment';
  if (tutorGroup.modules.length === 1) {
    pushMessage = `You have been assigned ${tutorGroup.tutor.name} as your tutor for ${tutorGroup.modules[0]}.`;
  } else {
    pushMessage = `You have been assigned ${tutorGroup.tutor.name} as your tutor for ${tutorGroup.modules.length} modules.`;
  }
}
```

## Auto-Subscription Consistency

The auto-subscription logic in `auth.service.ts` is already consistent with these fixes because:

1. **Login flow**: `autoSubscribeToUltimateMonthly` → `subscribeWithFreePayment` → `handlePostSubscriptionTasks` → `assignTutorsForPlan` → fixed notification methods

2. **Email verification flow**: Same path as login flow

3. **Added batch ID**: Enhanced `subscribeWithFreePayment` to include batch ID for deduplication

## Expected Results

### For Students:
- **Before**: Multiple in-app notifications (one per module), potentially multiple emails
- **After**: Single consolidated in-app notification, single email, single push notification

### For Tutors:
- **Before**: Multiple in-app notifications (one per module), potentially multiple emails  
- **After**: Single consolidated in-app notification, single email, single push notification

### System Benefits:
- Reduced notification spam
- Better user experience
- Improved system performance
- Consistent notification behavior across all subscription flows

## Testing

Created test file `test/notification-fixes.test.ts` to verify:
- Deduplication mechanism works correctly
- Batch ID generation is unique
- No duplicate notifications are sent for the same event

## Files Modified

1. `src/modules/plans/plans.service.ts` - Fixed notification consolidation logic
2. `src/modules/notification/notification-outbox.service.ts` - Added deduplication mechanism
3. `test/notification-fixes.test.ts` - Added tests for verification
