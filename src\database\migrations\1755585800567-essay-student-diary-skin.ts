import { MigrationInterface, QueryRunner } from "typeorm";

export class EssayStudentDiarySkin1755585800567 implements MigrationInterface {
    name = 'EssayStudentDiarySkin1755585800567'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" ADD "student_diary_skin" uuid`);
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" ADD CONSTRAINT "FK_1d945afdd29b9fd2a227118c983" FOREIGN KEY ("student_diary_skin") REFERENCES "student_diary_skin"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" DROP CONSTRAINT "FK_1d945afdd29b9fd2a227118c983"`);
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" DROP COLUMN "student_diary_skin"`);
    }
}
