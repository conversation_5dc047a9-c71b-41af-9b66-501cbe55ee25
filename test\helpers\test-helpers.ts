import { Repository } from 'typeorm';
import { User, UserType } from '../../src/database/entities/user.entity';
import { Role } from '../../src/database/entities/role.entity';
import { Plan, PlanType, SubscriptionType } from '../../src/database/entities/plan.entity';

// Mock repository factory
export type MockRepository<T = any> = Partial<Record<keyof Repository<T>, jest.Mock>>;

export const createMockRepository = <T = any>(): MockRepository<T> => ({
  find: jest.fn(),
  findOne: jest.fn(),
  findOneBy: jest.fn(),
  create: jest.fn(),
  save: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  remove: jest.fn(),
  createQueryBuilder: jest.fn(() => ({
    leftJoinAndSelect: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    take: jest.fn().mockReturnThis(),
    getMany: jest.fn(),
    getManyAndCount: jest.fn(),
    getOne: jest.fn(),
  })),
});

// Test data factories
export const createTestUser = (overrides: Partial<User> = {}): User => {
  const user = new User();
  user.id = 'test-user-id';
  user.userId = 'TEST001';
  user.email = '<EMAIL>';
  user.name = 'Test User';
  user.type = UserType.STUDENT;
  user.isActive = true;
  user.isConfirmed = true;
  user.phoneNumber = '1234567890';
  user.gender = 'male';
  user.createdAt = new Date();
  user.updatedAt = new Date();
  
  Object.assign(user, overrides);
  return user;
};

export const createTestRole = (name: string = 'student'): Role => {
  const role = new Role();
  role.id = `role-${name}`;
  role.name = name;
  role.createdAt = new Date();
  role.updatedAt = new Date();
  return role;
};

export const createTestPlan = (overrides: Partial<Plan> = {}): Plan => {
  const plan = new Plan();
  plan.id = 'test-plan-id';
  plan.name = 'Test Plan';
  plan.type = PlanType.STANDARD;
  plan.price = 9.99;
  plan.subscriptionType = SubscriptionType.MONTHLY;
  plan.isActive = true;
  plan.createdAt = new Date();
  plan.updatedAt = new Date();
  
  Object.assign(plan, overrides);
  return plan;
};

// Mock services
export const createMockEmailService = () => ({
  sendVerificationLink: jest.fn().mockResolvedValue(true),
  sendPasswordResetLink: jest.fn().mockResolvedValue(true),
  sendUserId: jest.fn().mockResolvedValue(true),
  sendPasswordChangeNotification: jest.fn().mockResolvedValue(true),
  sendEmail: jest.fn().mockResolvedValue(true),
});

export const createMockJwtService = () => ({
  sign: jest.fn().mockReturnValue('mock-jwt-token'),
  decode: jest.fn().mockReturnValue({ exp: Math.floor(Date.now() / 1000) + 3600 }),
  verify: jest.fn().mockReturnValue({ sub: 'test-user-id' }),
});

export const createMockProfilePictureService = () => ({
  hasProfilePicture: jest.fn().mockResolvedValue(false),
  getProfilePictureUrl: jest.fn().mockResolvedValue(null),
  getProfilePictureDirectUrl: jest.fn().mockResolvedValue(null),
  updateProfilePicture: jest.fn().mockResolvedValue(true),
});

export const createMockDataSource = () => ({
  query: jest.fn().mockResolvedValue([]),
  createQueryRunner: jest.fn().mockReturnValue({
    connect: jest.fn(),
    startTransaction: jest.fn(),
    commitTransaction: jest.fn(),
    rollbackTransaction: jest.fn(),
    release: jest.fn(),
    manager: {
      save: jest.fn(),
      create: jest.fn(),
      getRepository: jest.fn(),
    },
  }),
});

// Test utilities
export const expectToThrow = async (fn: () => Promise<any>, errorType: any) => {
  await expect(fn()).rejects.toThrow(errorType);
};

export const mockDate = (date: string | Date) => {
  const mockDate = new Date(date);
  jest.spyOn(global, 'Date').mockImplementation(() => mockDate as any);
  return mockDate;
};

export const restoreDate = () => {
  (global.Date as any).mockRestore();
};