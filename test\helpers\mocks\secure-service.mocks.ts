import { faker } from '@faker-js/faker';

/**
 * Secure mock implementations for external services
 * These mocks prevent security vulnerabilities in tests
 */

export const createSecureEmailMock = () => ({
  sendEmail: jest.fn().mockImplementation((to: string, subject: string, content: string) => {
    // Validate inputs to prevent injection
    if (content.includes('<script>') || content.includes('javascript:')) {
      throw new Error('Malicious content detected');
    }
    return Promise.resolve({ messageId: `mock-${faker.string.alphanumeric(16)}` });
  }),
  
  sendVerificationLink: jest.fn().mockImplementation((email: string, token: string) => {
    // Ensure email format is valid
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new Error('Invalid email format');
    }
    return Promise.resolve(true);
  }),
  
  sendPasswordResetLink: jest.fn().mockImplementation((email: string, token: string) => {
    return Promise.resolve(true);
  }),
  
  validateTemplate: jest.fn().mockReturnValue(true),
  
  sanitizeContent: jest.fn().mockImplementation((content: string) => {
    // Basic sanitization
    return content
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;');
  })
});

export const createSecureFileMock = () => ({
  upload: jest.fn().mockImplementation((path: string, file: any) => {
    // Prevent path traversal
    if (path.includes('../') || path.includes('..\\')) {
      throw new Error('Path traversal detected');
    }
    
    // Validate file type
    const allowedExtensions = ['.jpg', '.jpeg', '.png', '.pdf', '.txt'];
    const hasValidExtension = allowedExtensions.some(ext => path.toLowerCase().endsWith(ext));
    
    if (!hasValidExtension) {
      throw new Error('Invalid file type');
    }
    
    return Promise.resolve({ 
      url: `https://secure-cdn.example.com/${faker.string.alphanumeric(32)}`,
      filename: path.split('/').pop()
    });
  }),
  
  download: jest.fn().mockImplementation((path: string) => {
    if (path.includes('../')) {
      throw new Error('Path traversal detected');
    }
    return Promise.resolve(Buffer.from('mock file content'));
  }),
  
  delete: jest.fn().mockResolvedValue(true),
  
  validatePath: jest.fn().mockImplementation((path: string) => {
    return !path.includes('../') && !path.includes('..\\');
  })
});

export const createSecureLoggerMock = () => ({
  log: jest.fn().mockImplementation((level: string, message: string, meta?: any) => {
    // Sanitize log message to prevent injection
    const sanitizedMessage = message.replace(/[\n\r]/g, '');
    
    // Don't log sensitive data
    const sensitivePatterns = ['password', 'token', 'secret', 'key'];
    const containsSensitive = sensitivePatterns.some(pattern => 
      sanitizedMessage.toLowerCase().includes(pattern)
    );
    
    if (containsSensitive) {
      console.warn('Attempted to log sensitive data');
      return;
    }
    
    console.log(`[${level}] ${sanitizedMessage}`);
  }),
  
  error: jest.fn().mockImplementation((message: string, error?: Error) => {
    const sanitizedMessage = message.replace(/[\n\r]/g, '');
    console.error(`[ERROR] ${sanitizedMessage}`);
  }),
  
  warn: jest.fn().mockImplementation((message: string) => {
    const sanitizedMessage = message.replace(/[\n\r]/g, '');
    console.warn(`[WARN] ${sanitizedMessage}`);
  }),
  
  info: jest.fn().mockImplementation((message: string) => {
    const sanitizedMessage = message.replace(/[\n\r]/g, '');
    console.info(`[INFO] ${sanitizedMessage}`);
  })
});

export const createSecureDatabaseMock = () => ({
  query: jest.fn().mockImplementation((sql: string, params?: any[]) => {
    // Basic SQL injection detection
    const dangerousPatterns = ['DROP', 'DELETE', 'UPDATE', 'INSERT'];
    const upperSql = sql.toUpperCase();
    
    // Allow only SELECT statements in tests
    if (!upperSql.trim().startsWith('SELECT') && 
        dangerousPatterns.some(pattern => upperSql.includes(pattern))) {
      throw new Error('Potentially dangerous SQL detected in test');
    }
    
    return Promise.resolve([]);
  }),
  
  findOne: jest.fn().mockResolvedValue(null),
  find: jest.fn().mockResolvedValue([]),
  save: jest.fn().mockImplementation((entity: any) => Promise.resolve(entity)),
  delete: jest.fn().mockResolvedValue({ affected: 1 }),
  
  createQueryBuilder: jest.fn().mockReturnValue({
    select: jest.fn().mockReturnThis(),
    from: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    orWhere: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    offset: jest.fn().mockReturnThis(),
    getOne: jest.fn().mockResolvedValue(null),
    getMany: jest.fn().mockResolvedValue([]),
    getRawOne: jest.fn().mockResolvedValue({}),
    getRawMany: jest.fn().mockResolvedValue([])
  })
});

export const createSecureJwtMock = () => ({
  sign: jest.fn().mockImplementation((payload: any, options?: any) => {
    // Ensure payload doesn't contain sensitive data
    const payloadStr = JSON.stringify(payload);
    const sensitivePatterns = ['password', 'secret', 'key'];
    
    if (sensitivePatterns.some(pattern => payloadStr.toLowerCase().includes(pattern))) {
      throw new Error('Sensitive data detected in JWT payload');
    }
    
    return `mock.jwt.token.${faker.string.alphanumeric(32)}`;
  }),
  
  verify: jest.fn().mockImplementation((token: string, secret: string) => {
    if (!token.startsWith('mock.jwt.token.')) {
      throw new Error('Invalid token format');
    }
    
    return {
      sub: faker.string.uuid(),
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 3600
    };
  }),
  
  decode: jest.fn().mockImplementation((token: string) => {
    return {
      sub: faker.string.uuid(),
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 3600
    };
  })
});

export const createSecureHttpMock = () => ({
  get: jest.fn().mockImplementation((url: string) => {
    // Validate URL to prevent SSRF
    if (!url.startsWith('https://') && !url.startsWith('http://localhost')) {
      throw new Error('Invalid URL scheme');
    }
    
    return Promise.resolve({
      status: 200,
      data: { message: 'Mock response' }
    });
  }),
  
  post: jest.fn().mockImplementation((url: string, data: any) => {
    // Validate request data
    const dataStr = JSON.stringify(data);
    if (dataStr.includes('<script>') || dataStr.includes('javascript:')) {
      throw new Error('Malicious content detected');
    }
    
    return Promise.resolve({
      status: 201,
      data: { id: faker.string.uuid() }
    });
  })
});