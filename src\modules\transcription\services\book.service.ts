import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Book } from '../../../database/entities/book.entity';
import { CreateBookDto, BookResponseDto } from '../../../database/models/transcription.dto';

@Injectable()
export class BookService {
  constructor(
    @InjectRepository(Book)
    private bookRepository: Repository<Book>,
  ) {}

  async create(createBookDto: CreateBookDto): Promise<BookResponseDto> {
    const book = this.bookRepository.create(createBookDto);
    const savedBook = await this.bookRepository.save(book);
    return this.mapToResponseDto(savedBook);
  }

  async findAll(): Promise<BookResponseDto[]> {
    const books = await this.bookRepository.find({
      where: { isActive: true },
      select: {
        id: true,
        title: true,
        author: true,
        description: true,
        publicationYear: true,
        content: false, // Don't load large content field
        isActive: true,
        createdAt: true
      },
      relations: {
        sentences: true
      },
      order: {
        createdAt: 'DESC'
      }
    });
    return books.map(book => this.mapToResponseDto(book));
  }

  async findById(id: string): Promise<BookResponseDto> {
    const book = await this.bookRepository.findOne({
      where: { id, isActive: true },
      relations: {
        sentences: true
      }
    });
    if (!book) {
      throw new NotFoundException('Book not found');
    }
    return this.mapToResponseDto(book);
  }

  async delete(id: string): Promise<void> {
    const book = await this.bookRepository.findOne({ where: { id } });
    if (!book) {
      throw new NotFoundException('Book not found');
    }
    book.isActive = false;
    await this.bookRepository.save(book);
  }

  private mapToResponseDto(book: Book): BookResponseDto {
    return {
      id: book.id,
      title: book.title,
      author: book.author,
      description: book.description,
      publicationYear: book.publicationYear,
      content: book.content,
      sentenceCount: book.sentences?.length || 0,
      isActive: book.isActive,
      createdAt: book.createdAt,
    };
  }
}