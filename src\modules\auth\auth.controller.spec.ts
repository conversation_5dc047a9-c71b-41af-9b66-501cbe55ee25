import { Test, TestingModule } from '@nestjs/testing';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { SecureTestDataFactory } from '../../../test/fixtures/factories/secure-test-data.factory';

describe('AuthController', () => {
  let controller: AuthController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [
        {
          provide: AuthService,
          useValue: {
            login: jest.fn(),
            register: jest.fn(),
            forgotPassword: jest.fn(),
            resetPassword: jest.fn(),
            verifyEmail: jest.fn(),
            resendVerificationEmail: jest.fn(),
            changePassword: jest.fn(),
            createRole: jest.fn(),
            forgotUserId: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<AuthController>(AuthController);
    authService = module.get<AuthService>(AuthService);
  });

  let authService: any;

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('login', () => {
    it('should login user successfully', async () => {
      const testCredentials = SecureTestDataFactory.createSecureTestCredentials();
      const testUser = SecureTestDataFactory.createMockUser();
      const loginDto = {
        userId: testCredentials.userId,
        password: testCredentials.password,
      };

      const mockLoginResponse = {
        access_token: 'mock-jwt-token',
        user: {
          id: testUser.id,
          userId: testUser.userId,
          email: testUser.email,
          type: 'student',
        },
      };

      authService.login.mockResolvedValue(mockLoginResponse);

      const result = await controller.login(loginDto);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockLoginResponse);
      expect(result.message).toBe('Login successful');
      expect(authService.login).toHaveBeenCalled();
    });
  });

  describe('register', () => {
    it('should register student user successfully', async () => {
      const testUser = SecureTestDataFactory.createMockUser();
      const testCredentials = SecureTestDataFactory.createSecureTestCredentials();
      const registerDto = {
        userId: testUser.userId,
        email: testUser.email,
        password: testCredentials.password,
        confirmPassword: testCredentials.password,
        phoneNumber: testUser.phoneNumber,
        gender: testUser.gender,
        type: 'student',
        agreedToTerms: true,
        toCreateUserDto: jest.fn(),
      } as any;

      const mockUser = {
        userId: testUser.userId,
      };

      authService.register.mockResolvedValue(mockUser);

      const result = await controller.register(registerDto);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockUser);
      expect(result.message).toBe('Registration successful. Please check your email for verification link.');
      expect(authService.register).toHaveBeenCalledWith(registerDto);
    });

    it('should register tutor user successfully', async () => {
      const testUser = SecureTestDataFactory.createMockUser();
      const testCredentials = SecureTestDataFactory.createSecureTestCredentials();
      const registerDto = {
        userId: testUser.userId,
        email: testUser.email,
        password: testCredentials.password,
        confirmPassword: testCredentials.password,
        phoneNumber: testUser.phoneNumber,
        gender: testUser.gender,
        type: 'tutor',
        agreedToTerms: true,
        toCreateUserDto: jest.fn(),
      } as any;

      const mockUser = {
        userId: testUser.userId,
      };

      authService.register.mockResolvedValue(mockUser);

      const result = await controller.register(registerDto);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockUser);
      expect(result.message).toBe('Registration successful. Please check your email for verification link.');
      expect(authService.register).toHaveBeenCalledWith(registerDto);
    });
  });

  describe('verifyEmail', () => {
    it('should verify email successfully', async () => {
      const verifyEmailDto = {
        token: 'mock-verification-token',
      };

      const mockResponse = {
        message: 'Email verified successfully',
      };

      authService.verifyEmail.mockResolvedValue(mockResponse);

      const result = await controller.verifyEmail(verifyEmailDto);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockResponse);
      expect(result.message).toBe('Email verified successfully');
      expect(authService.verifyEmail).toHaveBeenCalledWith('mock-verification-token');
    });
  });

  describe('forgotPassword', () => {
    it('should send password reset email successfully', async () => {
      const testUser = SecureTestDataFactory.createMockUser();
      const forgotPasswordDto = {
        identifier: testUser.email,
      } as any;

      const mockResponse = {
        message: 'Password reset email sent',
      };

      authService.forgotPassword.mockResolvedValue(mockResponse);

      const result = await controller.forgotPassword(forgotPasswordDto);

      expect(result.success).toBe(true);
      expect(result.data).toBeNull();
      expect(result.message).toBe('If your email is registered, you will receive a password reset link.');
      expect(authService.forgotPassword).toHaveBeenCalledWith({ identifier: testUser.email });
    });
  });

  describe('resetPassword', () => {
    it('should reset password successfully', async () => {
      const testCredentials = SecureTestDataFactory.createSecureTestCredentials();
      const resetPasswordDto = {
        token: 'mock-reset-token',
        newPassword: testCredentials.password,
      };

      const mockResponse = {
        message: 'Password reset successfully',
      };

      authService.resetPassword.mockResolvedValue(mockResponse);

      const result = await controller.resetPassword(resetPasswordDto);

      expect(result.success).toBe(true);
      expect(result.data).toBeNull();
      expect(result.message).toBe('Password has been reset successfully. You can now login with your new password.');
      expect(authService.resetPassword).toHaveBeenCalledWith({ token: 'mock-reset-token', newPassword: testCredentials.password });
    });
  });

  describe('changePassword', () => {
    it('should change password successfully', async () => {
      const testUser = SecureTestDataFactory.createMockUser();
      const oldCredentials = SecureTestDataFactory.createSecureTestCredentials();
      const newCredentials = SecureTestDataFactory.createSecureTestCredentials();
      const changePasswordDto = {
        userId: testUser.id,
        currentPassword: oldCredentials.password,
        newPassword: newCredentials.password,
        confirmNewPassword: newCredentials.password,
      } as any;

      const mockReq = {
        user: { sub: testUser.id },
      } as any;

      const mockResponse = {
        access_token: 'mock-new-jwt-token',
        user: {
          id: testUser.id,
          userId: testUser.userId,
          email: testUser.email,
        },
      };

      authService.changePassword.mockResolvedValue(mockResponse);

      const result = await controller.changePassword(changePasswordDto, mockReq);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockResponse);
      expect(result.message).toBe('Password changed successfully. Please use your new password for future logins.');
      expect(authService.changePassword).toHaveBeenCalledWith(changePasswordDto, mockReq);
    });
  });
});
