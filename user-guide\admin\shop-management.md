# Shop Management System

## 🛍️ Shop System Overview

The HEC platform includes a comprehensive shop system where students can purchase diary skins, themes, and other digital items using reward points earned through platform activities. This guide covers all aspects of shop management for administrators.

## 🏪 Shop Dashboard Access

### Navigation Path
1. **Admin Dashboard** → Shop Management
2. **Available Sections**:
   - Shop Items Management
   - Categories & Organization
   - Reward Points Settings
   - Purchase Analytics
   - Student Owned Items
   - Shopping Cart Management

## 📦 Shop Items Management

### Creating New Shop Items

#### Item Types Available
```javascript
{
  "item_types": {
    "diary_skins": {
      "description": "Custom diary themes and backgrounds",
      "price_range": "50-500 points",
      "categories": ["seasonal", "nature", "fantasy", "educational"]
    },
    "profile_decorations": {
      "description": "Avatar frames, badges, and profile enhancements",
      "price_range": "25-200 points",
      "categories": ["frames", "badges", "animations"]
    },
    "writing_tools": {
      "description": "Special fonts, text effects, and writing aids",
      "price_range": "100-300 points",
      "categories": ["fonts", "effects", "templates"]
    },
    "achievement_boosters": {
      "description": "Items that enhance learning experience",
      "price_range": "200-1000 points",
      "categories": ["multipliers", "unlocks", "premium_features"]
    }
  }
}
```

#### Creating Shop Items
```javascript
// POST /api/admin/shop/items
{
  "name": "Magical Forest Diary Skin",
  "description": "Transform your diary with enchanted forest themes featuring animated elements",
  "category_id": "diary_skins_nature",
  "price": 250,
  "currency": "reward_points",
  "item_type": "diary_skin",
  "rarity": "rare", // common, uncommon, rare, epic, legendary
  "availability": {
    "start_date": "2024-12-20T00:00:00Z",
    "end_date": null, // null for permanent availability
    "limited_quantity": false,
    "max_purchases_per_user": 1
  },
  "requirements": {
    "minimum_level": 5,
    "required_achievements": ["first_diary_entry", "creative_writer"],
    "user_type_restrictions": [] // empty for all users
  },
  "metadata": {
    "preview_images": [
      "/shop/previews/magical_forest_1.jpg",
      "/shop/previews/magical_forest_2.jpg"
    ],
    "tags": ["nature", "magical", "animated", "premium"],
    "featured": false,
    "new_item": true
  }
}
```

### Item Configuration Options

#### Pricing Strategies
```javascript
{
  "pricing_models": {
    "fixed_price": {
      "price": 250,
      "currency": "reward_points"
    },
    "tiered_pricing": {
      "student_price": 200,
      "premium_student_price": 150,
      "tutor_discount": 0.8 // 20% discount
    },
    "dynamic_pricing": {
      "base_price": 250,
      "demand_multiplier": 1.2,
      "seasonal_discount": 0.9,
      "bulk_discount": {
        "3_items": 0.95,
        "5_items": 0.9,
        "10_items": 0.85
      }
    }
  }
}
```

#### Availability Controls
```javascript
{
  "availability_settings": {
    "always_available": {
      "start_date": null,
      "end_date": null,
      "stock_unlimited": true
    },
    "limited_time": {
      "start_date": "2024-12-20T00:00:00Z",
      "end_date": "2025-01-20T23:59:59Z",
      "stock_unlimited": true
    },
    "limited_quantity": {
      "total_stock": 100,
      "remaining_stock": 87,
      "max_per_user": 1,
      "restock_date": "2025-02-01T00:00:00Z"
    },
    "exclusive_access": {
      "required_level": 10,
      "required_achievements": ["master_writer"],
      "user_groups": ["premium_subscribers"],
      "invitation_only": false
    }
  }
}
```

## 🏷️ Categories & Organization

### Category Management

#### Creating Categories
```javascript
// POST /api/admin/shop/categories
{
  "name": "Seasonal Themes",
  "description": "Diary skins that change with the seasons",
  "parent_category_id": "diary_skins",
  "display_order": 1,
  "icon": "/icons/seasonal.svg",
  "banner_image": "/banners/seasonal_banner.jpg",
  "is_featured": true,
  "metadata": {
    "color_scheme": "#4a90e2",
    "seasonal_rotation": true,
    "auto_feature_new_items": true
  }
}
```

#### Category Hierarchy
```
Shop Root
├── Diary Skins
│   ├── Seasonal Themes
│   │   ├── Spring Collection
│   │   ├── Summer Vibes
│   │   ├── Autumn Colors
│   │   └── Winter Wonderland
│   ├── Educational Themes
│   │   ├── Science & Nature
│   │   ├── History & Culture
│   │   └── Literature & Arts
│   └── Fantasy & Adventure
├── Profile Decorations
│   ├── Avatar Frames
│   ├── Achievement Badges
│   └── Profile Backgrounds
└── Writing Tools
    ├── Special Fonts
    ├── Text Effects
    └── Writing Templates
```

### Featured Items Management

#### Setting Featured Items
```javascript
// PATCH /api/admin/shop/featured
{
  "featured_items": [
    {
      "item_id": "magical_forest_skin",
      "position": 1,
      "feature_until": "2025-01-15T23:59:59Z",
      "banner_text": "New Arrival!"
    },
    {
      "item_id": "winter_wonderland_skin",
      "position": 2,
      "feature_until": "2024-12-31T23:59:59Z",
      "banner_text": "Limited Time!"
    }
  ],
  "auto_rotation": {
    "enabled": true,
    "rotation_days": 7,
    "criteria": "popularity_and_newness"
  }
}
```

## 💰 Reward Points System

### Points Configuration

#### Earning Mechanisms
```javascript
{
  "reward_point_settings": {
    "diary_activities": {
      "first_entry_of_day": 10,
      "diary_entry_submitted": 5,
      "entry_receives_good_feedback": 15,
      "consecutive_days_writing": {
        "3_days": 20,
        "7_days": 50,
        "30_days": 200
      }
    },
    "game_activities": {
      "story_maker_completion": 8,
      "story_maker_high_score": 25, // 90+ score
      "block_game_completion": 5,
      "block_game_perfect_score": 15,
      "first_game_of_day": 5
    },
    "social_activities": {
      "friend_connection": 10,
      "story_shared": 5,
      "story_liked_by_others": 2,
      "helpful_comment": 8
    },
    "achievement_bonuses": {
      "common_achievement": 25,
      "uncommon_achievement": 50,
      "rare_achievement": 100,
      "epic_achievement": 250,
      "legendary_achievement": 500
    }
  }
}
```

#### Points Management Tools
```javascript
// Admin tools for points management
{
  "admin_actions": {
    "bulk_point_adjustment": {
      "endpoint": "POST /api/admin/reward-points/bulk-adjust",
      "payload": {
        "user_ids": ["user1", "user2", "user3"],
        "adjustment": 100,
        "reason": "Holiday bonus",
        "notify_users": true
      }
    },
    "point_multiplier_events": {
      "endpoint": "POST /api/admin/reward-points/multiplier-event",
      "payload": {
        "multiplier": 2.0,
        "start_date": "2024-12-25T00:00:00Z",
        "end_date": "2024-12-31T23:59:59Z",
        "applicable_activities": ["diary_entry", "games"],
        "description": "Holiday Double Points Week!"
      }
    }
  }
}
```

### Points Analytics

#### Usage Statistics
```javascript
{
  "points_analytics": {
    "total_points_issued": 2847563,
    "total_points_spent": 1923847,
    "points_in_circulation": 923716,
    "average_user_balance": 156,
    "top_earners": [
      {"user_id": "user123", "points_earned": 5847},
      {"user_id": "user456", "points_earned": 4923}
    ],
    "spending_patterns": {
      "most_popular_items": [
        {"item": "rainbow_diary_skin", "purchases": 847},
        {"item": "star_frame", "purchases": 623}
      ],
      "average_purchase_value": 187,
      "purchase_frequency": "2.3 per week per active user"
    }
  }
}
```

## 🛒 Shopping Cart & Purchase Management

### Cart System Configuration

#### Cart Settings
```javascript
{
  "cart_configuration": {
    "max_items_per_cart": 10,
    "cart_expiry_hours": 24,
    "auto_save_enabled": true,
    "guest_cart_enabled": false, // require login
    "bulk_discount_thresholds": {
      "3_items": 0.05, // 5% discount
      "5_items": 0.10, // 10% discount
      "10_items": 0.15 // 15% discount
    }
  }
}
```

#### Purchase Flow Management
```javascript
{
  "purchase_workflow": {
    "validation_steps": [
      "user_authentication",
      "sufficient_points_check",
      "item_availability_check",
      "purchase_limits_check",
      "requirements_verification"
    ],
    "completion_steps": [
      "deduct_points",
      "add_to_owned_items",
      "send_confirmation",
      "update_analytics",
      "trigger_achievements"
    ],
    "failure_handling": {
      "insufficient_points": "show_earning_suggestions",
      "item_unavailable": "suggest_alternatives",
      "purchase_limit_reached": "explain_limits"
    }
  }
}
```

### Purchase Analytics

#### Transaction Monitoring
```javascript
{
  "purchase_analytics": {
    "daily_transactions": {
      "total_purchases": 156,
      "total_points_spent": 28947,
      "average_transaction_value": 185,
      "successful_transactions": 152,
      "failed_transactions": 4
    },
    "popular_items_today": [
      {
        "item": "Autumn Leaves Diary Skin",
        "purchases": 23,
        "revenue_points": 5750
      },
      {
        "item": "Golden Star Frame",
        "purchases": 18,
        "revenue_points": 1800
      }
    ],
    "user_behavior": {
      "cart_abandonment_rate": "12%",
      "average_items_per_purchase": 2.3,
      "repeat_purchase_rate": "67%"
    }
  }
}
```

## 👥 Student Owned Items Management

### Ownership Tracking

#### Viewing User Inventories
```javascript
// GET /api/admin/users/{userId}/owned-items
{
  "user_id": "student123",
  "total_items": 15,
  "total_value": 2847,
  "categories": {
    "diary_skins": {
      "count": 8,
      "items": [
        {
          "item_id": "magical_forest",
          "name": "Magical Forest Skin",
          "purchased_date": "2024-11-15T10:30:00Z",
          "purchase_price": 250,
          "currently_equipped": true
        }
      ]
    },
    "profile_decorations": {
      "count": 5,
      "items": [...]
    },
    "writing_tools": {
      "count": 2,
      "items": [...]
    }
  }
}
```

#### Bulk Operations
```javascript
// Admin bulk operations
{
  "bulk_operations": {
    "grant_items": {
      "endpoint": "POST /api/admin/owned-items/bulk-grant",
      "payload": {
        "user_ids": ["user1", "user2"],
        "item_ids": ["holiday_skin", "winter_frame"],
        "reason": "Holiday gift",
        "notify_users": true
      }
    },
    "revoke_items": {
      "endpoint": "DELETE /api/admin/owned-items/bulk-revoke",
      "payload": {
        "user_ids": ["user123"],
        "item_ids": ["inappropriate_skin"],
        "reason": "Policy violation",
        "refund_points": true
      }
    }
  }
}
```

## 📊 Shop Analytics Dashboard

### Performance Metrics

#### Sales Analytics
```javascript
{
  "shop_performance": {
    "revenue_metrics": {
      "total_points_earned": 2847563,
      "total_points_spent": 1923847,
      "net_points_circulation": 923716,
      "conversion_rate": "23.5%", // visitors to purchasers
      "average_order_value": 187
    },
    "item_performance": {
      "best_sellers": [
        {
          "item": "Rainbow Dreams Diary Skin",
          "sales": 847,
          "revenue": 211750,
          "rating": 4.8
        }
      ],
      "slow_movers": [
        {
          "item": "Vintage Library Skin",
          "sales": 12,
          "revenue": 3000,
          "days_since_last_sale": 15
        }
      ]
    },
    "user_engagement": {
      "active_shoppers": 2156,
      "window_shoppers": 5847,
      "repeat_customers": 1445,
      "customer_lifetime_value": 892
    }
  }
}
```

### Inventory Management

#### Stock Monitoring
```javascript
{
  "inventory_status": {
    "total_items": 156,
    "active_items": 142,
    "draft_items": 8,
    "archived_items": 6,
    "low_stock_alerts": [
      {
        "item": "Limited Edition Holiday Skin",
        "current_stock": 5,
        "threshold": 10,
        "estimated_sellout": "2024-12-23"
      }
    ],
    "restock_needed": [
      {
        "item": "Popular Spring Theme",
        "last_restock": "2024-11-01",
        "demand_score": 8.5
      }
    ]
  }
}
```

## 🎯 Promotional Campaigns

### Campaign Management

#### Creating Promotions
```javascript
// POST /api/admin/shop/promotions
{
  "name": "Holiday Sale 2024",
  "description": "Special discounts on seasonal items",
  "type": "percentage_discount",
  "discount_value": 25, // 25% off
  "applicable_items": {
    "categories": ["seasonal_themes"],
    "specific_items": ["winter_wonderland", "holiday_cheer"],
    "exclude_items": ["premium_exclusive"]
  },
  "conditions": {
    "minimum_purchase": 100,
    "maximum_discount": 500,
    "user_eligibility": "all_users",
    "usage_limit_per_user": 3
  },
  "schedule": {
    "start_date": "2024-12-20T00:00:00Z",
    "end_date": "2024-12-31T23:59:59Z",
    "timezone": "UTC"
  },
  "marketing": {
    "banner_text": "Holiday Sale - Up to 25% Off!",
    "notification_message": "Don't miss our biggest sale of the year!",
    "email_campaign": true
  }
}
```

#### Bundle Offers
```javascript
{
  "bundle_configuration": {
    "name": "Complete Writer's Pack",
    "description": "Everything you need for creative writing",
    "items": [
      {"item_id": "premium_diary_skin", "quantity": 1},
      {"item_id": "special_font_pack", "quantity": 1},
      {"item_id": "writer_badge", "quantity": 1}
    ],
    "pricing": {
      "individual_total": 750,
      "bundle_price": 500,
      "savings": 250,
      "savings_percentage": 33
    },
    "availability": {
      "limited_quantity": 100,
      "time_limited": true,
      "end_date": "2024-12-31T23:59:59Z"
    }
  }
}
```

## 🔧 Shop Configuration

### System Settings

#### Global Shop Settings
```javascript
{
  "shop_configuration": {
    "general_settings": {
      "shop_enabled": true,
      "maintenance_mode": false,
      "currency_name": "HEC Points",
      "currency_symbol": "🌟",
      "tax_rate": 0.0, // no tax on virtual items
      "refund_policy_days": 7
    },
    "display_settings": {
      "items_per_page": 12,
      "enable_search": true,
      "enable_filters": true,
      "show_ratings": true,
      "show_purchase_count": false,
      "default_sort": "popularity"
    },
    "purchase_settings": {
      "require_confirmation": true,
      "allow_gift_purchases": true,
      "enable_wishlist": true,
      "auto_equip_purchased_items": false
    }
  }
}
```

## 📋 Shop Management Checklist

### Daily Tasks
- [ ] Review new purchase transactions
- [ ] Check for failed payment attempts
- [ ] Monitor popular items and stock levels
- [ ] Review customer feedback and ratings
- [ ] Update featured items if needed

### Weekly Tasks
- [ ] Analyze sales performance metrics
- [ ] Review and update item pricing
- [ ] Check promotional campaign performance
- [ ] Plan new item releases
- [ ] Review customer support tickets

### Monthly Tasks
- [ ] Comprehensive sales and revenue analysis
- [ ] Inventory optimization and planning
- [ ] Customer behavior analysis
- [ ] Seasonal content planning
- [ ] Promotional strategy review

---

**Next Steps**: After setting up shop management, proceed to [Payment Integration](payment-integration.md) to configure payment processing, or explore [Promotions Management](promotions-management.md) for advanced marketing campaigns.

*For shop customization and advanced features, refer to the Shop API documentation or contact the development team.*