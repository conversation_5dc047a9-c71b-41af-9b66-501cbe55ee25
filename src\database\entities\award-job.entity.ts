import { Entity, Column } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';

export enum AwardJobStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

export enum AwardJobType {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  MANUAL = 'manual',
}

@Entity()
export class AwardJob extends AuditableBaseEntity {
  @Column({
    name: 'job_type',
    type: 'enum',
    enum: AwardJobType,
  })
  jobType: AwardJobType;

  @Column({
    name: 'status',
    type: 'enum',
    enum: AwardJobStatus,
    default: AwardJobStatus.PENDING,
  })
  status: AwardJobStatus;

  @Column({ name: 'next_run_at', type: 'timestamp', nullable: true })
  nextRunAt: Date;

  @Column({ name: 'last_run_at', type: 'timestamp', nullable: true })
  lastRunAt: Date;

  @Column({ name: 'started_at', type: 'timestamp', nullable: true })
  startedAt: Date;

  @Column({ name: 'completed_at', type: 'timestamp', nullable: true })
  completedAt: Date;

  @Column({ name: 'processed_count', default: 0 })
  processedCount: number;

  @Column({ name: 'error_message', type: 'text', nullable: true })
  errorMessage: string;

  @Column({ name: 'execution_time_ms', nullable: true })
  executionTimeMs: number;

  @Column({ name: 'triggered_by', nullable: true })
  triggeredBy: string; // Admin ID for manual triggers

  @Column({ name: 'summary', type: 'text', nullable: true })
  summary: string; // Plain text summary of the job execution
}