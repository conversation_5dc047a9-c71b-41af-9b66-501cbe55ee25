import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WaterfallSet } from '../../../database/entities/waterfall-set.entity';
import { WaterfallQuestion } from '../../../database/entities/waterfall-question.entity';
import { WaterfallTrueFalseQuestion } from '../../../database/entities/waterfall-true-false-question.entity';
import { WaterfallMultipleChoiceQuestion } from '../../../database/entities/waterfall-multiple-choice-question.entity';
import { TutorWaterfallSet } from '../../../database/entities/tutor-waterfall-set.entity';
import { TutorBlockGame } from '../../../database/entities/tutor-block-game.entity';
import { BlockGame } from '../../../database/entities/block-game.entity';
import { GamePerformanceTracking } from '../../../database/entities/game-performance-tracking.entity';
import { WaterfallParticipation } from '../../../database/entities/waterfall-participation.entity';
import { WaterfallAnswer } from '../../../database/entities/waterfall-answer.entity';
import { WaterfallController } from './waterfall.controller';
import { WaterfallAdminController } from './waterfall-admin.controller';
import { TutorWaterfallController } from '../tutor/tutor-waterfall.controller';
import { WaterfallService } from './waterfall.service';
import { WaterfallAdminService } from './waterfall-admin.service';
import { TutorWaterfallService } from '../tutor/tutor-waterfall.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      WaterfallSet,
      WaterfallQuestion,
      WaterfallTrueFalseQuestion,
      WaterfallMultipleChoiceQuestion,
      TutorWaterfallSet,
      TutorBlockGame,
      BlockGame,
      GamePerformanceTracking,
      WaterfallParticipation,
      WaterfallAnswer,
    ])
  ],
  controllers: [WaterfallController, WaterfallAdminController, TutorWaterfallController],
  providers: [
    WaterfallService,
    WaterfallAdminService,
    TutorWaterfallService,
  ],
  exports: [
    WaterfallService,
    WaterfallAdminService,
    TutorWaterfallService,
  ],
})
export class WaterfallModule {}
