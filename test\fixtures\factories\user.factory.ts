import { faker } from '@faker-js/faker';
import { User, UserType } from '../../../src/database/entities/user.entity';
import { Role } from '../../../src/database/entities/role.entity';

export class UserTestFactory {
  /**
   * Creates a secure mock user for testing
   * Never uses real credentials or sensitive data
   */
  static createMockUser(overrides?: Partial<User>): Partial<User> {
    const timestamp = Date.now();
    return {
      id: faker.string.uuid(),
      userId: `TEST${timestamp}`,
      email: `test-${timestamp}@example.com`,
      passwordHash: `mock-hash-${faker.string.alphanumeric(32)}`,
      firstName: faker.person.firstName(),
      lastName: faker.person.lastName(),
      phoneNumber: faker.phone.number(),
      gender: faker.helpers.arrayElement(['male', 'female', 'other']),
      type: UserType.STUDENT,
      isActive: true,
      isConfirmed: true,
      createdAt: faker.date.recent(),
      updatedAt: faker.date.recent(),
      lastLoginAt: faker.date.recent(),
      refreshToken: '',
      refreshTokenExpiry: new Date(0),
      ...overrides,
    };
  }

  /**
   * Creates secure test credentials that never expose real data
   */
  static createSecureTestCredentials() {
    const timestamp = Date.now();
    return {
      userId: `TEST${timestamp}`,
      email: `test-${timestamp}@example.com`,
      password: `mock-password-${faker.string.alphanumeric(16)}`,
    };
  }

  /**
   * Creates a mock student user
   */
  static createMockStudent(overrides?: Partial<User>): Partial<User> {
    return this.createMockUser({
      type: UserType.STUDENT,
      userId: `STU${Date.now()}`,
      ...overrides,
    });
  }

  /**
   * Creates a mock tutor user
   */
  static createMockTutor(overrides?: Partial<User>): Partial<User> {
    return this.createMockUser({
      type: UserType.TUTOR,
      userId: `TUT${Date.now()}`,
      ...overrides,
    });
  }

  /**
   * Creates a mock admin user
   */
  static createMockAdmin(overrides?: Partial<User>): Partial<User> {
    return this.createMockUser({
      type: UserType.ADMIN,
      userId: `ADM${Date.now()}`,
      ...overrides,
    });
  }

  /**
   * Creates a mock role
   */
  static createMockRole(name: string = 'student'): Partial<Role> {
    return {
      id: faker.string.uuid(),
      name,
      description: `Mock ${name} role for testing`,
      createdAt: faker.date.recent(),
      updatedAt: faker.date.recent(),
    };
  }

  /**
   * Creates mock login credentials that are safe for testing
   */
  static createMockLoginDto() {
    const credentials = this.createSecureTestCredentials();
    return {
      userId: credentials.userId,
      password: credentials.password,
    };
  }

  /**
   * Creates mock registration data
   */
  static createMockRegisterDto(overrides?: any) {
    const credentials = this.createSecureTestCredentials();
    const password = credentials.password;
    
    return {
      userId: credentials.userId,
      email: credentials.email,
      password,
      confirmPassword: password,
      firstName: faker.person.firstName(),
      lastName: faker.person.lastName(),
      phoneNumber: faker.phone.number(),
      gender: faker.helpers.arrayElement(['male', 'female', 'other']),
      type: UserType.STUDENT,
      agreedToTerms: true,
      ...overrides,
    };
  }
}