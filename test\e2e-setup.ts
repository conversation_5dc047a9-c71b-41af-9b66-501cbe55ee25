import 'reflect-metadata';
import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { AppModule } from '../src/app.module';
import { DataSource } from 'typeorm';
import { getTestDatabaseConfig } from './utils/test-database.config';

let app: INestApplication;
let testDataSource: DataSource;

// E2E test specific setup
beforeAll(async () => {
  // Set test environment variables
  process.env.NODE_ENV = 'test';
  process.env.JWT_SECRET = 'test-jwt-secret-e2e';
  process.env.DATABASE_URL = 'sqlite::memory:';
  process.env.PORT = '3001'; // Different port for E2E tests
  
  // Initialize test database
  testDataSource = new DataSource(getTestDatabaseConfig());
  await testDataSource.initialize();
  await testDataSource.runMigrations();
  
  // Create test application
  const moduleFixture: TestingModule = await Test.createTestingModule({
    imports: [AppModule],
  }).compile();

  app = moduleFixture.createNestApplication();
  await app.init();
}, 60000);

// Clean database between tests
beforeEach(async () => {
  // Clear all tables but keep schema
  const entities = testDataSource.entityMetadatas;
  for (const entity of entities) {
    const repository = testDataSource.getRepository(entity.name);
    await repository.clear();
  }
});

// Global test teardown
afterAll(async () => {
  if (app) {
    await app.close();
  }
  if (testDataSource && testDataSource.isInitialized) {
    await testDataSource.destroy();
  }
});

// Mock external services for E2E tests
jest.mock('nodemailer', () => ({
  createTransport: jest.fn(() => ({
    sendMail: jest.fn().mockResolvedValue({ messageId: 'test-message-id' }),
  })),
}));

jest.mock('@aws-sdk/client-s3', () => ({
  S3Client: jest.fn(() => ({
    send: jest.fn(),
  })),
  PutObjectCommand: jest.fn(),
  GetObjectCommand: jest.fn(),
}));

// Export app and data source for use in tests
export { app, testDataSource };