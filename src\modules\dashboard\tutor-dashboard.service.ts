import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User, UserType } from '../../database/entities/user.entity';
import { PlanFeature, FeatureType } from '../../database/entities/plan-feature.entity';
import { StudentTutorMapping, MappingStatus } from '../../database/entities/student-tutor-mapping.entity';
import { DiaryEntryAttendance, AttendanceStatus } from '../../database/entities/diary-entry-attendance.entity';
import { DiaryEntry, DiaryEntryStatus } from '../../database/entities/diary-entry.entity';
import { MissionDiaryEntry, MissionEntryStatus } from '../../database/entities/mission-diary-entry.entity';
import { NovelEntry, NovelEntryStatus } from '../../database/entities/novel-entry.entity';


import { EssayTaskSubmissions } from '../../database/entities/essay-task-submissions.entity';
import { SubmissionStatus as EssaySubmissionStatus } from '../../constants/submission.enum';
import { EssayMissionTasks } from '../../database/entities/essay-mission-tasks.entity';
import { DiaryFeedback } from '../../database/entities/diary-feedback.entity';
import { DiaryCorrection } from '../../database/entities/diary-correction.entity';
import { MissionDiaryEntryFeedback } from '../../database/entities/mission-diary-entry-feedback.entity';
import { NovelFeedback } from '../../database/entities/novel-feedback.entity';
import { NovelCorrection } from '../../database/entities/novel-correction.entity';
import { QATaskSubmissionMarking } from '../../database/entities/qa-task-submission-marking.entity';
import { EssayTaskSubmissionMarking } from '../../database/entities/essay-task-submission-marking.entity';
import {
  TutorStudentCountDto,
  TutorAttendanceStatsDto,
  TutorReviewStatsDto,
  TutorSubmissionStatsDto,
  TutorModuleReviewStatsDto,
  TutorModuleSubmissionStatsDto,
} from '../../database/models/dashboard.dto';
import { QASubmission } from 'src/database/entities/qa-submission.entity';

@Injectable()
export class TutorDashboardService {
  private readonly logger = new Logger(TutorDashboardService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(PlanFeature)
    private readonly planFeatureRepository: Repository<PlanFeature>,
    @InjectRepository(StudentTutorMapping)
    private readonly studentTutorMappingRepository: Repository<StudentTutorMapping>,
    @InjectRepository(DiaryEntryAttendance)
    private readonly diaryEntryAttendanceRepository: Repository<DiaryEntryAttendance>,
    @InjectRepository(DiaryEntry)
    private readonly diaryEntryRepository: Repository<DiaryEntry>,
    @InjectRepository(MissionDiaryEntry)
    private readonly missionDiaryEntryRepository: Repository<MissionDiaryEntry>,
    @InjectRepository(NovelEntry)
    private readonly novelEntryRepository: Repository<NovelEntry>,

    @InjectRepository(QASubmission)
    private readonly qaSubmissionsRepository: Repository<QASubmission>,

    @InjectRepository(EssayTaskSubmissions)
    private readonly essayTaskSubmissionsRepository: Repository<EssayTaskSubmissions>,
    @InjectRepository(EssayMissionTasks)
    private readonly essayMissionTasksRepository: Repository<EssayMissionTasks>,
    @InjectRepository(DiaryFeedback)
    private readonly diaryFeedbackRepository: Repository<DiaryFeedback>,
    @InjectRepository(DiaryCorrection)
    private readonly diaryCorrectionRepository: Repository<DiaryCorrection>,
    @InjectRepository(MissionDiaryEntryFeedback)
    private readonly missionDiaryEntryFeedbackRepository: Repository<MissionDiaryEntryFeedback>,
    @InjectRepository(NovelFeedback)
    private readonly novelFeedbackRepository: Repository<NovelFeedback>,
    @InjectRepository(NovelCorrection)
    private readonly novelCorrectionRepository: Repository<NovelCorrection>,
    @InjectRepository(QATaskSubmissionMarking)
    private readonly qaTaskSubmissionMarkingRepository: Repository<QATaskSubmissionMarking>,
    @InjectRepository(EssayTaskSubmissionMarking)
    private readonly essayTaskSubmissionMarkingRepository: Repository<EssayTaskSubmissionMarking>,
  ) {}

  /**
   * Get total count of active students assigned to the tutor
   */
  async getAssignedStudentCount(tutorId: string): Promise<TutorStudentCountDto> {
    try {
      const totalAssignedStudents = await this.studentTutorMappingRepository
        .createQueryBuilder('mapping')
        .leftJoin('mapping.student', 'student')
        .leftJoin('student.userPlans', 'userPlan')
        .leftJoin('userPlan.plan', 'plan')
        .where('mapping.tutorId = :tutorId', { tutorId })
        .andWhere('mapping.status = :status', { status: MappingStatus.ACTIVE })
        .andWhere('student.type = :userType', { userType: UserType.STUDENT })
        .andWhere('userPlan.isActive = :isActive', { isActive: true })
        .andWhere('plan.isActive = :planActive', { planActive: true })
        .select('COUNT(DISTINCT mapping.studentId)', 'count')
        .getRawOne();

      return {
        totalAssignedStudents: parseInt(totalAssignedStudents.count, 10),
      };
    } catch (error) {
      this.logger.error(`Error getting assigned student count for tutor ${tutorId}: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to get assigned student count');
    }
  }

  /**
   * Get today's attendance statistics for tutor's assigned students
   * Note: Total counts all assigned students, but attendance is only tracked for diary module
   */
  async getTodayAttendanceStats(tutorId: string): Promise<TutorAttendanceStatsDto> {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // Get total count of ALL assigned students (across all modules)
      const totalAssignedStudents = await this.studentTutorMappingRepository
        .createQueryBuilder('mapping')
        .leftJoin('mapping.student', 'student')
        .leftJoin('student.userPlans', 'userPlan')
        .leftJoin('userPlan.plan', 'plan')
        .where('mapping.tutorId = :tutorId', { tutorId })
        .andWhere('mapping.status = :status', { status: MappingStatus.ACTIVE })
        .andWhere('student.type = :userType', { userType: UserType.STUDENT })
        .andWhere('userPlan.isActive = :isActive', { isActive: true })
        .andWhere('plan.isActive = :planActive', { planActive: true })
        .select('COUNT(DISTINCT mapping.studentId)', 'count')
        .getRawOne()
        .then((result) => parseInt(result.count, 10));

      if (totalAssignedStudents === 0) {
        return {
          presentCount: 0,
          absentCount: 0,
          totalStudents: 0,
          attendancePercentage: 0,
        };
      }

      // Get attendance stats for today for assigned students
      const todayEnd = new Date(today);
      todayEnd.setHours(23, 59, 59, 999);

      // Get attendance stats for today (only from diary attendance records)
      const attendanceStats = await this.diaryEntryAttendanceRepository
        .createQueryBuilder('attendance')
        .innerJoin('attendance.student', 'student')
        .innerJoin(StudentTutorMapping, 'mapping', 'mapping.studentId = student.id')
        .leftJoin('student.userPlans', 'userPlan')
        .leftJoin('userPlan.plan', 'plan')
        .select('attendance.status', 'status')
        .addSelect('COUNT(*)', 'count')
        .where('attendance.entryDate >= :todayStart AND attendance.entryDate <= :todayEnd', {
          todayStart: today,
          todayEnd: todayEnd
        })
        .andWhere('mapping.tutorId = :tutorId', { tutorId })
        .andWhere('mapping.status = :status', { status: MappingStatus.ACTIVE })
        .andWhere('student.type = :userType', { userType: UserType.STUDENT })
        .andWhere('userPlan.isActive = :isActive', { isActive: true })
        .andWhere('plan.isActive = :planActive', { planActive: true })
        .groupBy('attendance.status')
        .getRawMany();

      let presentCount = 0;
      let absentCount = 0;

      attendanceStats.forEach((stat) => {
        const count = parseInt(stat.count, 10);
        if (stat.status === AttendanceStatus.PRESENT) {
          presentCount = count;
        } else if (stat.status === AttendanceStatus.ABSENT) {
          absentCount = count;
        }
      });

      // Calculate students with no attendance record for today
      const studentsWithAttendance = presentCount + absentCount;
      const studentsWithoutAttendance = totalAssignedStudents - studentsWithAttendance;

      // For attendance percentage calculation, use total assigned students as denominator
      const attendancePercentage = totalAssignedStudents > 0 ? (presentCount / totalAssignedStudents) * 100 : 0;

      return {
        presentCount,
        absentCount: absentCount + studentsWithoutAttendance, // Include students without attendance as absent
        totalStudents: totalAssignedStudents,
        attendancePercentage: Math.round(attendancePercentage * 100) / 100,
      };
    } catch (error) {
      this.logger.error(`Error getting today's attendance stats for tutor ${tutorId}: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to get attendance statistics');
    }
  }

  /**
   * Get total review and feedback statistics for the tutor
   */
  async getReviewStats(tutorId: string): Promise<TutorReviewStatsDto> {
    try {
      const modules = await this.planFeatureRepository.find({
        where: [{ type: FeatureType.HEC_USER_DIARY }, { type: FeatureType.ENGLISH_NOVEL }, { type: FeatureType.ENGLISH_QA_WRITING }, { type: FeatureType.ENGLISH_ESSAY }],
      });

      const moduleBreakdown: TutorModuleReviewStatsDto[] = [];
      let totalReviews = 0;
      let totalFeedback = 0;

      for (const module of modules) {
        const stats = await this.getModuleReviewStats(tutorId, module);
        moduleBreakdown.push(stats);
        totalReviews += stats.totalReviews;
        totalFeedback += stats.totalFeedback;
      }

      return {
        totalReviews,
        totalFeedback,
        moduleBreakdown,
      };
    } catch (error) {
      this.logger.error(`Error getting review stats for tutor ${tutorId}: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to get review statistics');
    }
  }

  /**
   * Get review statistics for a specific module
   */
  private async getModuleReviewStats(tutorId: string, module: PlanFeature): Promise<TutorModuleReviewStatsDto> {
    let totalReviews = 0;
    let totalFeedback = 0;

    switch (module.type) {
      case FeatureType.HEC_USER_DIARY:
        const diaryStats = await this.getDiaryReviewStats(tutorId);
        totalReviews = diaryStats.reviews;
        totalFeedback = diaryStats.feedback;
        break;

      case FeatureType.ENGLISH_NOVEL:
        const novelStats = await this.getNovelReviewStats(tutorId);
        totalReviews = novelStats.reviews;
        totalFeedback = novelStats.feedback;
        break;

      case FeatureType.ENGLISH_QA_WRITING:
        const qaStats = await this.getQAReviewStats(tutorId);
        totalReviews = qaStats.reviews;
        totalFeedback = qaStats.feedback;
        break;

      case FeatureType.ENGLISH_ESSAY:
        const essayStats = await this.getEssayReviewStats(tutorId);
        totalReviews = essayStats.reviews;
        totalFeedback = essayStats.feedback;
        break;
    }

    return {
      moduleType: module.type,
      moduleName: module.name,
      totalReviews,
      totalFeedback,
    };
  }

  /**
   * Get diary review statistics for tutor (only for currently assigned students)
   */
  private async getDiaryReviewStats(tutorId: string): Promise<{ reviews: number; feedback: number }> {
    // Get currently assigned student IDs for diary module
    const diaryFeature = await this.planFeatureRepository.findOne({
      where: { type: FeatureType.HEC_USER_DIARY },
    });
    
    if (!diaryFeature) {
      return { reviews: 0, feedback: 0 };
    }

    const assignedStudentIds = await this.studentTutorMappingRepository
      .createQueryBuilder('mapping')
      .select('mapping.studentId', 'studentId')
      .where('mapping.tutorId = :tutorId', { tutorId })
      .andWhere('mapping.planFeatureId = :planFeatureId', { planFeatureId: diaryFeature.id })
      .andWhere('mapping.status = :status', { status: MappingStatus.ACTIVE })
      .getRawMany();

    const studentIds = assignedStudentIds.map((item) => item.studentId);
    
    if (studentIds.length === 0) {
      return { reviews: 0, feedback: 0 };
    }

    // Regular diary corrections and feedback (only for assigned students)
    const diaryCorrections = await this.diaryCorrectionRepository
      .createQueryBuilder('correction')
      .innerJoin('correction.diaryEntry', 'entry')
      .innerJoin('entry.diary', 'diary')
      .where('correction.tutorId = :tutorId', { tutorId })
      .andWhere('diary.userId IN (:...studentIds)', { studentIds })
      .getCount();
      
    const missionDiaryCorrections = await this.missionDiaryEntryRepository
      .createQueryBuilder('entry')
      .where('entry.reviewedBy = :tutorId', { tutorId })
      .andWhere('entry.studentId IN (:...studentIds)', { studentIds })
      .getCount();

    const diaryFeedback = await this.diaryFeedbackRepository
      .createQueryBuilder('feedback')
      .innerJoin('feedback.diaryEntry', 'entry')
      .innerJoin('entry.diary', 'diary')
      .where('feedback.tutorId = :tutorId', { tutorId })
      .andWhere('diary.userId IN (:...studentIds)', { studentIds })
      .getCount();

    // Mission diary feedback (only for assigned students)
    const missionFeedback = await this.missionDiaryEntryFeedbackRepository
      .createQueryBuilder('feedback')
      .innerJoin('feedback.missionEntry', 'entry')
      .where('feedback.tutorId = :tutorId', { tutorId })
      .andWhere('entry.studentId IN (:...studentIds)', { studentIds })
      .getCount();

    return {
      reviews: diaryCorrections + missionDiaryCorrections,
      feedback: diaryFeedback + missionFeedback,
    };
  }

  /**
   * Get novel review statistics for tutor (only for currently assigned students)
   */
  private async getNovelReviewStats(tutorId: string): Promise<{ reviews: number; feedback: number }> {
    // Get currently assigned student IDs for novel module
    const novelFeature = await this.planFeatureRepository.findOne({
      where: { type: FeatureType.ENGLISH_NOVEL },
    });
    
    if (!novelFeature) {
      return { reviews: 0, feedback: 0 };
    }

    const assignedStudentIds = await this.studentTutorMappingRepository
      .createQueryBuilder('mapping')
      .select('mapping.studentId', 'studentId')
      .where('mapping.tutorId = :tutorId', { tutorId })
      .andWhere('mapping.planFeatureId = :planFeatureId', { planFeatureId: novelFeature.id })
      .andWhere('mapping.status = :status', { status: MappingStatus.ACTIVE })
      .getRawMany();

    const studentIds = assignedStudentIds.map((item) => item.studentId);
    
    if (studentIds.length === 0) {
      return { reviews: 0, feedback: 0 };
    }

    const corrections = await this.novelCorrectionRepository
      .createQueryBuilder('correction')
      .innerJoin('correction.entry', 'entry')
      .where('correction.tutorId = :tutorId', { tutorId })
      .andWhere('entry.studentId IN (:...studentIds)', { studentIds })
      .getCount();
      
    const feedback = await this.novelFeedbackRepository
      .createQueryBuilder('feedback')
      .innerJoin('feedback.entry', 'entry')
      .where('feedback.tutorId = :tutorId', { tutorId })
      .andWhere('entry.studentId IN (:...studentIds)', { studentIds })
      .getCount();

    return {
      reviews: corrections,
      feedback,
    };
  }

  /**
   * Get QA review statistics for tutor (only for currently assigned students)
   */
  private async getQAReviewStats(tutorId: string): Promise<{ reviews: number; feedback: number }> {
    // Get currently assigned student IDs for QA module
    const qaFeature = await this.planFeatureRepository.findOne({
      where: { type: FeatureType.ENGLISH_QA_WRITING },
    });
    
    if (!qaFeature) {
      return { reviews: 0, feedback: 0 };
    }

    const assignedStudentIds = await this.studentTutorMappingRepository
      .createQueryBuilder('mapping')
      .select('mapping.studentId', 'studentId')
      .where('mapping.tutorId = :tutorId', { tutorId })
      .andWhere('mapping.planFeatureId = :planFeatureId', { planFeatureId: qaFeature.id })
      .andWhere('mapping.status = :status', { status: MappingStatus.ACTIVE })
      .getRawMany();

    const studentIds = assignedStudentIds.map((item) => item.studentId);
    
    if (studentIds.length === 0) {
      return { reviews: 0, feedback: 0 };
    }

    const reviews = await this.qaSubmissionsRepository
      .createQueryBuilder('qa')
      .where('qa.reviewedBy = :tutorId', { tutorId })
      .andWhere('qa.createdBy IN (:...studentIds)', { studentIds })
      .andWhere('qa.corrections IS NOT NULL')
      .getCount();

    const feedback = await this.qaSubmissionsRepository
      .createQueryBuilder('qa')
      .where('qa.reviewedBy = :tutorId', { tutorId })
      .andWhere('qa.createdBy IN (:...studentIds)', { studentIds })
      .andWhere('qa.feedback IS NOT NULL')
      .andWhere('qa.feedback != :empty', { empty: '' })
      .getCount();

    return { reviews, feedback };
  }

  /**
   * Get essay review statistics for tutor (only for currently assigned students)
   */
  private async getEssayReviewStats(tutorId: string): Promise<{ reviews: number; feedback: number }> {
    // Get currently assigned student IDs for essay module
    const essayFeature = await this.planFeatureRepository.findOne({
      where: { type: FeatureType.ENGLISH_ESSAY },
    });
    
    if (!essayFeature) {
      return { reviews: 0, feedback: 0 };
    }

    const assignedStudentIds = await this.studentTutorMappingRepository
      .createQueryBuilder('mapping')
      .select('mapping.studentId', 'studentId')
      .where('mapping.tutorId = :tutorId', { tutorId })
      .andWhere('mapping.planFeatureId = :planFeatureId', { planFeatureId: essayFeature.id })
      .andWhere('mapping.status = :status', { status: MappingStatus.ACTIVE })
      .getRawMany();

    const studentIds = assignedStudentIds.map((item) => item.studentId);
    
    if (studentIds.length === 0) {
      return { reviews: 0, feedback: 0 };
    }

    const markings = await this.essayTaskSubmissionMarkingRepository
      .createQueryBuilder('marking')
      .innerJoin('marking.submission', 'submission')
      .where('marking.createdBy = :tutorId', { tutorId })
      .andWhere('submission.createdBy IN (:...studentIds)', { studentIds })
      .getCount();

    return {
      reviews: markings,
      feedback: markings, // Essay markings serve as both reviews and feedback
    };
  }

  /**
   * Get pending submission statistics for tutor (excluding HECplay)
   */
  async getPendingSubmissionStats(tutorId: string): Promise<TutorSubmissionStatsDto> {
    try {
      const modules = await this.planFeatureRepository.find({
        where: [{ type: FeatureType.HEC_USER_DIARY }, { type: FeatureType.ENGLISH_NOVEL }, { type: FeatureType.ENGLISH_QA_WRITING }, { type: FeatureType.ENGLISH_ESSAY }],
      });

      const moduleBreakdown: TutorModuleSubmissionStatsDto[] = [];
      let totalConfirmedSubmissions = 0;
      let totalPendingSubmissions = 0;

      for (const module of modules) {
        const stats = await this.getModuleSubmissionStats(tutorId, module);
        const pendingOnlyStats = {
          moduleType: stats.moduleType,
          moduleName: stats.moduleName,
          confirmedSubmissions: 0,
          pendingSubmissions: stats.pendingSubmissions,
        };
        moduleBreakdown.push(pendingOnlyStats);
        totalPendingSubmissions += stats.pendingSubmissions;
      }

      return {
        totalConfirmedSubmissions: 0,
        totalPendingSubmissions,
        moduleBreakdown,
      };
    } catch (error) {
      this.logger.error(`Error getting pending submission stats for tutor ${tutorId}: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to get pending submission statistics');
    }
  }

  /**
   * Get submission statistics for tutor (excluding HECplay)
   */
  async getSubmissionStats(tutorId: string): Promise<TutorSubmissionStatsDto> {
    try {
      const modules = await this.planFeatureRepository.find({
        where: [{ type: FeatureType.HEC_USER_DIARY }, { type: FeatureType.ENGLISH_NOVEL }, { type: FeatureType.ENGLISH_QA_WRITING }, { type: FeatureType.ENGLISH_ESSAY }],
      });

      const moduleBreakdown: TutorModuleSubmissionStatsDto[] = [];
      let totalConfirmedSubmissions = 0;
      let totalPendingSubmissions = 0;

      for (const module of modules) {
        const stats = await this.getModuleSubmissionStats(tutorId, module);
        moduleBreakdown.push(stats);
        totalConfirmedSubmissions += stats.confirmedSubmissions;
        totalPendingSubmissions += stats.pendingSubmissions;
      }

      return {
        totalConfirmedSubmissions,
        totalPendingSubmissions,
        moduleBreakdown,
      };
    } catch (error) {
      this.logger.error(`Error getting submission stats for tutor ${tutorId}: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to get submission statistics');
    }
  }

  /**
   * Get submission statistics for a specific module
   */
  private async getModuleSubmissionStats(tutorId: string, module: PlanFeature): Promise<TutorModuleSubmissionStatsDto> {
    let confirmedSubmissions = 0;
    let pendingSubmissions = 0;

    // Get students assigned to this tutor for this module (with active plans)
    const assignedStudentIds = await this.studentTutorMappingRepository
      .createQueryBuilder('mapping')
      .leftJoin('mapping.student', 'student')
      .leftJoin('student.userPlans', 'userPlan')
      .leftJoin('userPlan.plan', 'plan')
      .select('mapping.studentId', 'studentId')
      .where('mapping.tutorId = :tutorId', { tutorId })
      .andWhere('mapping.planFeatureId = :planFeatureId', { planFeatureId: module.id })
      .andWhere('mapping.status = :status', { status: MappingStatus.ACTIVE })
      .andWhere('student.type = :userType', { userType: UserType.STUDENT })
      .andWhere('userPlan.isActive = :isActive', { isActive: true })
      .andWhere('plan.isActive = :planActive', { planActive: true })
      .getRawMany();

    const studentIds = assignedStudentIds.map((item) => item.studentId);

    if (studentIds.length === 0) {
      return {
        moduleType: module.type,
        moduleName: module.name,
        confirmedSubmissions: 0,
        pendingSubmissions: 0,
      };
    }

    switch (module.type) {
      case FeatureType.HEC_USER_DIARY:
        const diaryStats = await this.getTutorDiarySubmissionStats(studentIds, tutorId);
        confirmedSubmissions = diaryStats.confirmed;
        pendingSubmissions = diaryStats.pending;
        break;

      case FeatureType.ENGLISH_NOVEL:
        const novelStats = await this.getTutorNovelSubmissionStats(studentIds, tutorId);
        confirmedSubmissions = novelStats.confirmed;
        pendingSubmissions = novelStats.pending;
        break;

      case FeatureType.ENGLISH_QA_WRITING:
        const qaStats = await this.getTutorQASubmissionStats(studentIds, tutorId);
        confirmedSubmissions = qaStats.confirmed;
        pendingSubmissions = qaStats.pending;
        break;

      case FeatureType.ENGLISH_ESSAY:
        const essayStats = await this.getTutorEssaySubmissionStats(studentIds, tutorId);
        confirmedSubmissions = essayStats.confirmed;
        pendingSubmissions = essayStats.pending;
        break;
    }

    return {
      moduleType: module.type,
      moduleName: module.name,
      confirmedSubmissions,
      pendingSubmissions,
    };
  }

  /**
   * Get diary submission statistics for tutor's students
   * FIXED: Only count latest submission per student per diary date/mission, excluding resubmissions
   * Uses ALL assigned students (like pending reviews API), not just diary module students
   */
  private async getTutorDiarySubmissionStats(studentIds: string[], tutorId?: string): Promise<{ confirmed: number; pending: number }> {
    // Get ALL students assigned to this tutor (matching pending reviews API logic)
    const allAssignedStudents = await this.studentTutorMappingRepository
      .createQueryBuilder('mapping')
      .leftJoin('mapping.student', 'student')
      .leftJoin('student.userPlans', 'userPlan')
      .leftJoin('userPlan.plan', 'plan')
      .select('mapping.studentId', 'studentId')
      .where('mapping.tutorId = :tutorId', { tutorId })
      .andWhere('mapping.status = :status', { status: MappingStatus.ACTIVE })
      .andWhere('student.type = :userType', { userType: UserType.STUDENT })
      .andWhere('userPlan.isActive = :isActive', { isActive: true })
      .andWhere('plan.isActive = :planActive', { planActive: true })
      .getRawMany();

    const allStudentIds = allAssignedStudents.map((item) => item.studentId);
    
    if (allStudentIds.length === 0) {
      return { confirmed: 0, pending: 0 };
    }
    // Regular diary entries - count unique students with latest entry status per date
    const diaryConfirmed = await this.diaryEntryRepository
      .createQueryBuilder('entry')
      .leftJoin('entry.diary', 'diary')
      .where('diary.userId IN (:...allStudentIds)', { allStudentIds })
      .andWhere('entry.status = :status', { status: DiaryEntryStatus.REVIEWED })
      .andWhere('entry.reviewedBy = :tutorId', { tutorId })
      .select('COUNT(DISTINCT CONCAT(diary.userId, \'_\', entry.entryDate))', 'count')
      .getRawOne()
      .then(result => parseInt(result.count, 10));

    // Only count latest pending entries per student per entry date (exactly matching pending reviews API)
    const diaryPending = await this.diaryEntryRepository
      .createQueryBuilder('entry')
      .leftJoin('entry.diary', 'diary')
      .where('diary.userId IN (:...allStudentIds)', { allStudentIds })
      .andWhere('entry.status = :submitStatus', { submitStatus: DiaryEntryStatus.SUBMIT })
      .andWhere('entry.isResubmission != :isResubmission', { isResubmission: true })
      .andWhere('entry.updatedAt = (' +
        'SELECT MAX(e2.updated_at) FROM diary_entry e2 ' +
        'INNER JOIN diary d2 ON e2.diary_id = d2.id ' +
        'WHERE d2.user_id = diary.user_id ' +
        'AND e2.entry_date = entry.entry_date ' +
        'AND e2.status = :submitStatus ' +
        'AND e2.is_resubmission != :isResubmission' +
      ')')
      .select('COUNT(*)', 'count')
      .getRawOne()
      .then(result => parseInt(result.count, 10));

    // Mission diary entries - count unique students per mission with latest status
    const missionConfirmed = await this.missionDiaryEntryRepository
      .createQueryBuilder('entry')
      .where('entry.studentId IN (:...allStudentIds)', { allStudentIds })
      .andWhere('entry.status = :missionReviewedStatus', { missionReviewedStatus: MissionEntryStatus.REVIEWED })
      .andWhere('entry.reviewedBy = :tutorId', { tutorId })
      .select('COUNT(DISTINCT CONCAT(entry.studentId, \'_\', entry.missionId))', 'count')
      .getRawOne()
      .then(result => parseInt(result.count, 10));

    // Count latest pending mission entries per student per mission
    const missionPending = await this.missionDiaryEntryRepository
      .createQueryBuilder('entry')
      .where('entry.studentId IN (:...allStudentIds)', { allStudentIds })
      .andWhere('entry.status = :missionStatus', { missionStatus: MissionEntryStatus.SUBMITTED })
      .andWhere('entry.updatedAt = (' +
        'SELECT MAX(e2.updated_at) FROM mission_diary_entry e2 ' +
        'WHERE e2.student_id = entry.student_id ' +
        'AND e2.mission_id = entry.mission_id ' +
        'AND e2.status = :missionStatus' +
      ')')
      .select('COUNT(*)', 'count')
      .getRawOne()
      .then(result => parseInt(result.count, 10));

    return {
      confirmed: diaryConfirmed + missionConfirmed,
      pending: diaryPending + missionPending,
    };
  }

  /**
   * Get novel submission statistics for tutor's students
   * FIXED: Only count latest submission per student per topic, excluding entries being reviewed by other tutors
   */
  private async getTutorNovelSubmissionStats(studentIds: string[], tutorId?: string): Promise<{ confirmed: number; pending: number }> {
    const confirmed = await this.novelEntryRepository
      .createQueryBuilder('entry')
      .where('entry.studentId IN (:...studentIds)', { studentIds })
      .andWhere('entry.status = :status', { status: NovelEntryStatus.REVIEWED })
      .andWhere('entry.reviewedBy = :tutorId', { tutorId })
      .select('COUNT(DISTINCT CONCAT(entry.studentId, \'_\', entry.topicId))', 'count')
      .getRawOne()
      .then(result => parseInt(result.count, 10));

    // Count latest pending novel entries per student per topic
    const pending = await this.novelEntryRepository
      .createQueryBuilder('entry')
      .where('entry.studentId IN (:...studentIds)', { studentIds })
      .andWhere('entry.status = :novelStatus', { novelStatus: NovelEntryStatus.SUBMITTED })
      .andWhere('entry.isResubmission != :novelResubmission', { novelResubmission: true })
      .andWhere('entry.updatedAt = (' +
        'SELECT MAX(e2.updated_at) FROM novel_entry e2 ' +
        'WHERE e2.student_id = entry.student_id ' +
        'AND e2.topic_id = entry.topic_id ' +
        'AND e2.status = :novelStatus ' +
        'AND e2.is_resubmission != :novelResubmission' +
      ')')
      .select('COUNT(*)', 'count')
      .getRawOne()
      .then(result => parseInt(result.count, 10));

    return { confirmed, pending };
  }

/**
 * Get QA submission statistics for tutor's students
 */
private async getTutorQASubmissionStats(
  studentIds: string[],
  tutorId?: string
): Promise<{ confirmed: number; pending: number }> {
  // Import QASubmissionStatus enum
  const { QASubmissionStatus } = require('../../database/entities/qa-submission.entity');
  
  // QA submissions that have been reviewed by this tutor
  const confirmed = await this.qaSubmissionsRepository
    .createQueryBuilder('qa')
    .where('qa.createdBy IN (:...studentIds)', { studentIds })
    .andWhere('qa.status = :status', { status: QASubmissionStatus.REVIEWED })
    .andWhere('qa.reviewedBy = :tutorId', { tutorId })
    .select('COUNT(DISTINCT CONCAT(qa.createdBy, \'_\', qa.assignmentId))', 'count')
    .getRawOne()
    .then(result => parseInt(result.count, 10));

  // Count latest pending QA submissions per student per assignment
  const pending = await this.qaSubmissionsRepository
    .createQueryBuilder('qa')
    .where('qa.createdBy IN (:...studentIds)', { studentIds })
    .andWhere('qa.status = :qaStatus', { qaStatus: QASubmissionStatus.SUBMITTED })
    .andWhere('qa.updatedAt = (' +
      'SELECT MAX(q2.updated_at) FROM qa_submission q2 ' +
      'WHERE q2.created_by = qa.created_by ' +
      'AND q2.assignment_id = qa.assignment_id ' +
      'AND q2.status = :qaStatus' +
    ')')
    .select('COUNT(*)', 'count')
    .getRawOne()
    .then(result => parseInt(result.count, 10));

  return { confirmed, pending };
}


  /**
   * Get essay submission statistics for tutor's students
   * FIXED: Only count latest submission per student per task, excluding entries being reviewed by other tutors
   */
  private async getTutorEssaySubmissionStats(studentIds: string[], tutorId?: string): Promise<{ confirmed: number; pending: number }> {
    const confirmed = await this.essayTaskSubmissionsRepository
      .createQueryBuilder('submission')
      .innerJoin('submission.submissionMark', 'marking')
      .where('submission.createdBy IN (:...studentIds)', { studentIds })
      .andWhere('submission.status = :status', { status: EssaySubmissionStatus.REVIEWED })
      .andWhere('marking.createdBy = :tutorId', { tutorId })
      .select('COUNT(DISTINCT CONCAT(submission.createdBy, \'_\', submission.taskId))', 'count')
      .getRawOne()
      .then(result => parseInt(result.count, 10));

    // Count latest pending essay submissions per student per task
    const pending = await this.essayTaskSubmissionsRepository
      .createQueryBuilder('submission')
      .where('submission.createdBy IN (:...studentIds)', { studentIds })
      .andWhere('submission.status = :essayStatus', { essayStatus: EssaySubmissionStatus.SUBMITTED })
      .andWhere('submission.updatedAt = (' +
        'SELECT MAX(s2.updated_at) FROM essay_task_submissions s2 ' +
        'WHERE s2.created_by = submission.created_by ' +
        'AND s2.task_id = submission.task_id ' +
        'AND s2.status = :essayStatus' +
      ')')
      .select('COUNT(*)', 'count')
      .getRawOne()
      .then(result => parseInt(result.count, 10));

    return { confirmed, pending };
  }
}
