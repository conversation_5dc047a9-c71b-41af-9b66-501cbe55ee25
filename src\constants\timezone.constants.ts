/**
 * Timezone constants for the HEC application
 */

export const DEFAULT_TIMEZONE = 'Asia/Seoul';

export const SUPPORTED_TIMEZONES = [
  'Asia/Seoul',
  'America/New_York',
  'America/Los_Angeles',
  'Europe/London',
  'Europe/Paris',
  'Asia/Tokyo',
  'Asia/Dhaka',
  'Asia/Shanghai',
  'Australia/Sydney',
  'UTC'
] as const;

export type SupportedTimezone = typeof SUPPORTED_TIMEZONES[number];

export const TIMEZONE_CACHE_TTL = 3600; // 1 hour in seconds