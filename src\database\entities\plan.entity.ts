import { Entity, Column, OneToMany, ManyToMany, JoinTable } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { UserPlan } from './user-plan.entity';
import { PlanFeature } from './plan-feature.entity';

export enum PlanType {
  STARTER = 'starter',
  STANDARD = 'standard',
  PRO = 'pro',
  ULTIMATE = 'ultimate',
}

export enum SubscriptionType {
  MONTHLY = 'monthly',
  YEARLY = 'yearly',
}

@Entity()
export class Plan extends AuditableBaseEntity {
  @Column({ name: 'name', unique: true })
  name: string;

  @Column({
    name: 'type',
    type: 'enum',
    enum: PlanType,
    default: PlanType.STARTER,
  })
  type: PlanType;

  @Column({
    name: 'subscription_type',
    type: 'enum',
    enum: SubscriptionType,
    default: SubscriptionType.MONTHLY,
  })
  subscriptionType: SubscriptionType;

  @Column({ name: 'description', type: 'text' })
  description: string;

  @Column({ name: 'price', type: 'decimal', precision: 10, scale: 2 })
  price: number;

  @Column({ name: 'duration_days', type: 'int' })
  durationDays: number;

  @Column({ name: 'auto_renew', default: false })
  autoRenew: boolean;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'is_applicable_for_promotion', default: false })
  isApplicableForPromotion: boolean;

  @Column({ name: 'promotion_id', nullable: true })
  promotionId: string;

  @OneToMany(() => UserPlan, (userPlan) => userPlan.plan)
  userPlans: UserPlan[];

  @ManyToMany(() => PlanFeature, (planFeature) => planFeature.plans)
  @JoinTable({
    name: 'plan_feature_map',
    joinColumn: {
      name: 'plan_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'feature_id',
      referencedColumnName: 'id',
    },
  })
  planFeatures: PlanFeature[];

  // Helper method to get a simple representation of this plan
  toSimpleObject() {
    const featuresArray = this.planFeatures && this.planFeatures.length > 0
      ? this.planFeatures.map((feature) => feature.toSimpleObject())
      : [];

    return {
      id: this.id,
      name: this.name,
      type: this.type,
      subscriptionType: this.subscriptionType,
      description: this.description,
      price: this.price,
      durationDays: this.durationDays,
      autoRenew: this.autoRenew,
      isActive: this.isActive,
      isApplicableForPromotion: this.isApplicableForPromotion,
      promotionId: this.promotionId,
      features: featuresArray,
      planFeatures: this.planFeatures ? this.planFeatures.map((feature) => feature.toSimpleObject()) : [],
    };
  }
}
