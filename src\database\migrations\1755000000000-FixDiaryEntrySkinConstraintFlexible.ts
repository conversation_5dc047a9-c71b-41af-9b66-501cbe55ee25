import { MigrationInterface, QueryRunner } from 'typeorm';

export class FixDiaryEntrySkinConstraintFlexible1755000000000 implements MigrationInterface {
  name = 'FixDiaryEntrySkinConstraintFlexible1755000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop the existing flexible constraint if it exists
    try {
      await queryRunner.query(`
        ALTER TABLE "diary_entry" 
        DROP CONSTRAINT IF EXISTS "CHK_diary_entry_skin_exclusivity_flexible"
      `);
      console.log('Dropped existing CHK_diary_entry_skin_exclusivity_flexible constraint');
    } catch (error) {
      console.log('Constraint CHK_diary_entry_skin_exclusivity_flexible might not exist, continuing...');
    }

    // Drop any other existing skin constraints
    try {
      await queryRunner.query(`
        ALTER TABLE "diary_entry" 
        DROP CONSTRAINT IF EXISTS "CHK_diary_entry_skin_exclusivity"
      `);
      console.log('Dropped existing CHK_diary_entry_skin_exclusivity constraint');
    } catch (error) {
      console.log('Constraint CHK_diary_entry_skin_exclusivity might not exist, continuing...');
    }

    // Fix data inconsistencies first
    console.log('Fixing data inconsistencies...');

    // 1. Set skin_type to 'global' for entries that have skin_id but no skin_type
    await queryRunner.query(`
      UPDATE diary_entry 
      SET skin_type = 'global' 
      WHERE skin_id IS NOT NULL 
        AND student_skin_id IS NULL 
        AND (skin_type IS NULL OR skin_type = '')
    `);

    // 2. Set skin_type to 'student' for entries that have student_skin_id but no skin_type
    await queryRunner.query(`
      UPDATE diary_entry 
      SET skin_type = 'student' 
      WHERE student_skin_id IS NOT NULL 
        AND skin_id IS NULL 
        AND (skin_type IS NULL OR skin_type = '')
    `);

    // 3. Fix entries that have both skin_id and student_skin_id (prioritize global)
    await queryRunner.query(`
      UPDATE diary_entry 
      SET student_skin_id = NULL, skin_type = 'global'
      WHERE skin_id IS NOT NULL 
        AND student_skin_id IS NOT NULL
    `);

    // 4. Fix entries that have neither skin_id nor student_skin_id - set to a default global skin
    const defaultSkinResult = await queryRunner.query(`
      SELECT id FROM diary_skin WHERE is_active = true AND is_global = true LIMIT 1
    `);

    if (defaultSkinResult && defaultSkinResult.length > 0) {
      const defaultSkinId = defaultSkinResult[0].id;
      await queryRunner.query(`
        UPDATE diary_entry 
        SET skin_id = $1, skin_type = 'global', student_skin_id = NULL
        WHERE skin_id IS NULL 
          AND student_skin_id IS NULL
      `, [defaultSkinId]);
      console.log(`Set default skin ${defaultSkinId} for entries with no skin`);
    }

    // 5. Ensure skin_type is never NULL
    await queryRunner.query(`
      UPDATE diary_entry 
      SET skin_type = 'global' 
      WHERE skin_type IS NULL AND skin_id IS NOT NULL
    `);

    // Add the corrected flexible constraint
    await queryRunner.query(`
      ALTER TABLE "diary_entry" 
      ADD CONSTRAINT "CHK_diary_entry_skin_exclusivity_flexible" 
      CHECK (
        (skin_type = 'global' AND skin_id IS NOT NULL AND student_skin_id IS NULL) OR
        (skin_type = 'student' AND skin_id IS NULL AND student_skin_id IS NOT NULL)
      )
    `);

    console.log('Added corrected CHK_diary_entry_skin_exclusivity_flexible constraint');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the flexible constraint
    await queryRunner.query(`
      ALTER TABLE "diary_entry" 
      DROP CONSTRAINT IF EXISTS "CHK_diary_entry_skin_exclusivity_flexible"
    `);

    // Restore the original constraint (if needed)
    await queryRunner.query(`
      ALTER TABLE "diary_entry" 
      ADD CONSTRAINT "CHK_diary_entry_skin_exclusivity" 
      CHECK (
        (skin_type = 'global' AND skin_id IS NOT NULL AND student_skin_id IS NULL) OR
        (skin_type = 'student' AND skin_id IS NULL AND student_skin_id IS NOT NULL) OR
        (skin_type IS NULL AND skin_id IS NOT NULL AND student_skin_id IS NULL)
      )
    `);
  }
}